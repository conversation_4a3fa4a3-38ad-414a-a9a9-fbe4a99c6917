package version

import (
	"regexp"
	"strings"

	"github.com/hashicorp/go-version"
)

const (
	RegexCoreSegments = `^v?([0-9]+(\.[0-9]+)+)`
)

var (
	semverRegexp *regexp.Regexp
)

func init() {
	semverRegexp = regexp.MustCompile(RegexCoreSegments)
}

func Compare(a, b string) (int, error) {
	a, b = onlyCore(a), onlyCore(b)
	v1, err := version.NewVersion(a)
	if err != nil {
		return 0, err
	}
	v2, err := version.NewVersion(b)
	if err != nil {
		return 0, err
	}
	return v1.Compare(v2), nil
}

// MatchConstraint example ver="1.2", constraint=">= 1.0, < 1.4", should return true
// more examples: https://github.com/hashicorp/go-version/blob/main/constraint_test.go
func MatchConstraint(ver, constraint string) (bool, error) {
	ver = onlyCore(ver)

	v, err := version.NewSemver(ver)
	if err != nil {
		return false, err
	}
	constraints, err := version.NewConstraint(constraint)
	if err != nil {
		return false, err
	}
	return constraints.Check(v.Core()), nil
}

func onlyCore(ver string) string {
	ver = strings.ToLower(ver)
	submatch := semverRegexp.FindStringSubmatch(ver)
	if len(submatch) > 0 {
		return submatch[0]
	}
	return ver
}
