package version

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestCompare(t *testing.T) {
	tests := []struct {
		name  string
		a     string
		b     string
		isErr bool
		want  int
	}{
		{
			name: "a's major is greater than b's",
			a:    "1.0.0",
			b:    "0.10.0",
			want: 1,
		},
		{
			name: "a's minor is greater than b's",
			a:    "0.2.0",
			b:    "0.1.10",
			want: 1,
		},
		{
			name: "b's patch is greater than a's",
			a:    "0.2.0",
			b:    "0.2.10",
			want: -1,
		},
		{
			name: "the same version",
			a:    "0.2.00",
			b:    "0.2.0",
			want: 0,
		},
		{
			name: "pre-release version should be smaller than normal version",
			a:    "0.2.0",
			b:    "0.2.0-beta.2",
			want: 0,
		},
		{
			name: "pre-release version compare",
			a:    "0.2.0-alpha.3",
			b:    "0.2.0-beta.2",
			want: 0,
		},
		{
			name: "same version with/without v prefix",
			a:    "v0.2.0",
			b:    "0.2.0",
			want: 0,
		},
		{
			name: "upper V prefix",
			a:    "V0.2.0",
			b:    "0.2.0",
			want: 0,
		},
		{
			name:  "malformed version",
			a:     "f0.2.0",
			b:     "0.2.0",
			isErr: true,
		},
		{
			name:  "error version",
			a:     "",
			b:     "0.2.0",
			isErr: true,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			got, err := Compare(test.a, test.b)
			if (err != nil && !test.isErr) || (err == nil && test.isErr) {
				t.Errorf("Compare() error = %v, wantErr %v", err, test.isErr)
				return
			}
			if got != test.want {
				t.Errorf("Compare() got = %v, want %v", got, test.want)
			}
		})
	}
}

func TestMatchConstraint(t *testing.T) {
	tests := []struct {
		name   string
		ver    string
		c      string
		want   bool
		hasErr bool
	}{
		{
			name: "between versions",
			ver:  "1.2",
			c:    "> 1.0, < 1.4",
			want: true, hasErr: false,
		},
		{
			name: "less than version",
			ver:  "1.2",
			c:    "< 1.4",
			want: true, hasErr: false,
		},
		{
			name: "same version but beta",
			ver:  "2.1.0-beta",
			c:    ">= 2.1.0-a",
			want: true, hasErr: false,
		},
		{
			name: "same version but no patch number",
			ver:  "1.01",
			c:    "= 1.001.0",
			want: true, hasErr: false,
		},
		{
			name: "version with parentheses brackets",
			ver:  "3.48.0-debug-HEAD.9999999(99999)",
			c:    ">= 3.48.0",
			want: true, hasErr: false,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			got, err := MatchConstraint(test.ver, test.c)
			if err != nil {
				assert.True(t, test.hasErr, "error = %v", err)
				return
			}
			if got != test.want {
				t.Errorf("MatchConstraint() got = %v, want %v", got, test.want)
			}
		})
	}
}
