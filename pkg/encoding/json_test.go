package encoding

import (
	"bytes"
	"testing"
)

func TestJsonEncode(t *testing.T) {
	tests := []struct {
		name     string
		input    any
		expected string
		wantErr  bool
	}{
		{
			name: "simple struct",
			input: struct {
				Name  string `json:"name"`
				Value int    `json:"value"`
			}{
				Name:  "josie & ben",
				Value: 123,
			},
			expected: `{"name":"josie & ben","value":123}` + "\n",
			wantErr:  false,
		},
		{
			name:     "nil input",
			input:    nil,
			expected: "null\n",
			wantErr:  false,
		},
		// Add more test cases as needed
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := JsonEncode(tt.input)
			if (err != nil) != tt.wantErr {
				t.Fatalf("Expected error: %v, got: %v", tt.wantErr, err)
			}
			if !bytes.Equal(result, []byte(tt.expected)) {
				t.<PERSON>("Expected %s, got %s", tt.expected, result)
			}
		})
	}
}
