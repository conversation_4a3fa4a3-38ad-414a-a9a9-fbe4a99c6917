package database

import (
	"fmt"
	"reflect"
	"strings"
)

type DBFields struct {
	Fields []string
}

func (f DBFields) String() string {
	if len(f.<PERSON>) == 0 {
		return ""
	}
	return `"` + strings.Join(f.<PERSON>, `", "`) + `"`
}

func (f DBFields) NamedString() string {
	if len(f.<PERSON>) == 0 {
		return ""
	}
	return ":" + strings.Join(f.<PERSON>, ", :")
}

func (f DBFields) Len() int {
	return len(f.<PERSON>)
}

func (f DBFields) Ignore(ignoreFields ...string) DBFields {
	if len(ignoreFields) == 0 {
		return f
	}

	fields := make([]string, len(f.<PERSON>))
	copy(fields, f.Fields)

	dbf := DBFields{Fields: fields}
	for _, field := range ignoreFields {
		for i, fd := range dbf.Fields {
			if field == fd {
				dbf.Fields = append(dbf.Fields[:i], dbf.Fields[i+1:]...)
				break
			}
		}
	}
	return dbf
}

// GetDBFields reflects on a struct and returns the values of fields with `db` tags,
// or a map[string]interface{} and returns the keys.
// only the exported fields will be mapped to the output DBFields
// a hack from https://github.com/jmoiron/sqlx/issues/255
func GetDBFields(values interface{}, ignoreFields ...string) DBFields {
	ignores := make(map[string]struct{}, len(ignoreFields))
	for _, field := range ignoreFields {
		ignores[field] = struct{}{}
	}
	v := reflect.ValueOf(values)
	for v.Kind() == reflect.Ptr {
		v = v.Elem()
	}
	fields := make([]string, 0)
	if v.Kind() == reflect.Struct {
		fieldType := v.Type()
		for i := 0; i < v.NumField(); i++ {
			// skip unexported fields by filtering only having pkg path if the field is unexported
			if fieldType.Field(i).PkgPath != "" {
				continue
			}
			field := fieldType.Field(i).Tag.Get("db")
			if field == "" || field == "-" {
				continue
			}
			if _, isIgnore := ignores[field]; isIgnore {
				continue
			}
			fields = append(fields, field)
		}
		return DBFields{fields}
	}
	if v.Kind() == reflect.Map {
		for _, keyv := range v.MapKeys() {
			fields = append(fields, keyv.String())
		}
		return DBFields{fields}
	}
	panic(fmt.Errorf("GetDBFields requires a struct or a map, found: %s", v.Kind().String()))
}
