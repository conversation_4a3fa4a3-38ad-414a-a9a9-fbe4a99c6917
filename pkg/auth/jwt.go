//go:generate mockgen -source jwt.go -destination jwt_mock.go -package auth
package auth

import (
	"gopkg.in/dgrijalva/jwt-go.v3"
	"strings"
)

type JWTAuth interface {
	GenerateToken(claims jwt.Claims) (string, error)
	ParseToken(tokenString string, claims jwt.Claims) (*jwt.Token, error)
	ParseUnverifiedToken(tokenString string, claims jwt.Claims) (*UnverifiedToken, error)
}

type jwtAuth struct {
	SignedMethod jwt.SigningMethod
	SignedString any
}

type UnverifiedToken struct {
	*jwt.Token
	parts []string
}

func NewJWTAuth(signedMethod jwt.SigningMethod, signedString any) JWTAuth {
	return &jwtAuth{
		signedMethod,
		signedString,
	}
}

func (j *jwtAuth) GenerateToken(claims jwt.Claims) (string, error) {
	tokenClaim := jwt.NewWithClaims(j.SignedMethod, claims)
	return tokenClaim.SignedString(j.SignedString)
}

func (j *jwtAuth) ParseToken(tokenString string, claims jwt.Claims) (*jwt.Token, error) {
	return jwt.ParseWithClaims(tokenString, claims, func(t *jwt.Token) (interface{}, error) {
		return j.SignedString, nil
	})
}

func (j *jwtAuth) ParseUnverifiedToken(tokenString string, claims jwt.Claims) (*UnverifiedToken, error) {
	token, parts, err := new(jwt.Parser).ParseUnverified(tokenString, claims)
	if err != nil {
		return nil, err
	} else if err = token.Claims.Valid(); err != nil { // validates time based claims
		return nil, err
	}
	return &UnverifiedToken{Token: token, parts: parts}, nil
}

func (u *UnverifiedToken) VerifySignature(key []byte) error {
	return u.Token.Method.Verify(strings.Join(u.parts[0:2], "."), u.parts[2], key)
}
