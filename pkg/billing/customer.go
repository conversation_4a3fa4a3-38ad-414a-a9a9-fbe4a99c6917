package billing

import (
	"encoding/json"
)

type UpdateCustomerRespData struct {
	Identifier string `json:"identifier"`
}

func (r *Response) ToUpdateCustomerRespData() (data *UpdateCustomerRespData, err error) {
	err = json.Unmarshal([]byte(r.Data), &data)
	return
}

type CustomerStatus struct {
	UserID       string
	ContractData *CustomerContractData
	OrdersData   *CustomerOrdersData
}

func (s *CustomerStatus) IsHavingSubscription() bool {
	if s.ContractData != nil && s.ContractData.Contract.IsInSubscription() {
		return true
	}
	if s.OrdersData != nil && s.OrdersData.Orders.HasUnfulfilledSubscription() {
		return true
	}
	return false
}

func (s *CustomerStatus) IsHavingUnfulfilledOrder() bool {
	if s.OrdersData != nil && s.OrdersData.Orders.HasUnfulfilledSubscription() {
		return true
	}
	return false
}
