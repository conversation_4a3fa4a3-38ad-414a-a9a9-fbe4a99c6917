package billing

import (
	"encoding/json"
	"errors"

	"gopkg.in/guregu/null.v3"
)

type CreateOrderRespData struct {
	OrderNumber     string         `json:"order_number"`
	PaymentRequired bool           `json:"payment_required"`
	PaymentRequest  PaymentRequest `json:"payment_request"`
}

type PaymentRequest struct {
	Method string             `json:"method"`
	Body   PaymentRequestBody `json:"body,omitempty"`
	Url    string             `json:"url"`
}

type PaymentRequestBody struct {
	MerchantID string `json:"MerchantID"`
	TradeInfo  string `json:"TradeInfo"`
	TradeSha   string `json:"TradeSha"`
	Version    string `json:"Version"`
}

type OrderTransaction struct {
	CalculateBaseTime   int64 `json:"calculate_base_time"`
	CalculatedExpiredAt int64 `json:"calculated_expired_at"`
}

type OrderInvoice struct {
	CheckCode       string `json:"check_code"`
	MerchantID      string `json:"merchant_id"`
	MerchantOrderNo string `json:"merchant_order_no"`
	InvoiceNumber   string `json:"invoice_number"`
	TotalAmt        int64  `json:"total_amt"`
	InvoiceTransNo  string `json:"invoice_trans_no"`
	RandomNum       string `json:"random_num"`
	CreateTime      string `json:"create_time"`
}

type Order struct {
	Number            string             `json:"number"`
	ParentOrderNumber string             `json:"parent_order_number"`
	PaymentType       PaymentType        `json:"payment_type"`
	FulfilledAt       null.Int           `json:"fulfilled_at"`
	PaidAt            null.Int           `json:"paid_at"`
	TotalPrice        int64              `json:"total_price"`
	Products          []Product          `json:"products"`
	SubscribeProduct  Product            `json:"subscribe_product"`
	Transactions      []OrderTransaction `json:"transactions,omitempty"`
	Invoice           *OrderInvoice      `json:"invoice,omitempty"`
	Status            string             `json:"status"`
	PaymentStatus     PaymentStatus      `json:"payment_status"`
	FulfillmentStatus FulfillmentStatus  `json:"fulfillment_status"`
	PlacedFrom        PlacedFrom         `json:"placed_from"`
	ErrorMessage      string             `json:"error_message"`
}

type PaymentType string

const (
	PaymentTypeFree PaymentType = "free"

	PaymentTypeCreditCard PaymentType = "credit_card"
	PaymentTypeCNS        PaymentType = "cns"
	PaymentTypeAPTG       PaymentType = "aptg"
	PaymentTypeEdenred    PaymentType = "edenred"
	PaymentTypePxpayplus  PaymentType = "pxpayplus"
)

func (s PaymentType) String() string {
	return string(s)
}

// billing.order.status
type Status string

const (
	StatusOpened    Status = "opened"
	StatusClosed    Status = "closed"
	StatusCancelled Status = "cancelled"
	StatusError     Status = "error"
)

func (s Status) String() string {
	return string(s)
}

// billing.order.payment_status
type PaymentStatus string

const (
	PaymentStatusPending  PaymentStatus = "pending"
	PaymentStatusPaid     PaymentStatus = "paid"
	PaymentStatusFailed   PaymentStatus = "failed"
	PaymentStatusRefunded PaymentStatus = "refunded"
)

func (p PaymentStatus) String() string {
	return string(p)
}

func (p PaymentStatus) IsPending() bool {
	return p.String() == PaymentStatusPending.String()
}

func (p PaymentStatus) IsPaid() bool {
	return p.String() == PaymentStatusPaid.String()
}

func (p PaymentStatus) IsFailed() bool {
	return p.String() == PaymentStatusFailed.String()
}

func (p PaymentStatus) IsRefunded() bool {
	return p.String() == PaymentStatusRefunded.String()
}

// billing.order.fulfillment_status
type FulfillmentStatus string

const (
	FulfillmentStatusUnfulfilledYet FulfillmentStatus = "unfulfilled_yet"
	FulfillmentStatusFulfilled      FulfillmentStatus = "fulfilled"
	FulfillmentStatusFulfillError   FulfillmentStatus = "fulfill_error"
	FulfillmentStatusReturned       FulfillmentStatus = "returned"
)

func (f FulfillmentStatus) String() string {
	return string(f)
}

func (f FulfillmentStatus) IsUnfulfilledYet() bool {
	return f.String() == FulfillmentStatusUnfulfilledYet.String()
}

func (f FulfillmentStatus) IsFulfilled() bool {
	return f.String() == FulfillmentStatusFulfilled.String()
}

func (f FulfillmentStatus) IsFulfillError() bool {
	return f.String() == FulfillmentStatusFulfillError.String()
}

func (f FulfillmentStatus) IsReturned() bool {
	return f.String() == FulfillmentStatusReturned.String()
}

type PlacedFrom string

const (
	PlacedFromConsole   PlacedFrom = "console"
	PlacedFromClient    PlacedFrom = "client"
	PlacedFromAutoRenew PlacedFrom = "auto_renew"
	PlacedFromTelecom   PlacedFrom = "telecom"
	PlacedFromSTB       PlacedFrom = "stb"
)

func (p PlacedFrom) String() string {
	return string(p)
}

func (p PlacedFrom) IsConsole() bool {
	return p.String() == PlacedFromConsole.String()
}

func (p PlacedFrom) IsClient() bool {
	return p.String() == PlacedFromClient.String()
}

func (p PlacedFrom) IsAutoRenew() bool {
	return p.String() == PlacedFromAutoRenew.String()
}

func (p PlacedFrom) IsTelecom() bool {
	return p.String() == PlacedFromTelecom.String()
}

func (p PlacedFrom) IsSTB() bool {
	return p.String() == PlacedFromSTB.String()
}

func (o *Order) Product() Product {
	product := o.SubscribeProduct
	if product.Identifier == "" && len(o.Products) > 0 {
		product = o.Products[0]
	}
	return product
}

type CustomerOrderData struct {
	Identifier    string `json:"identifier"`
	Email         string `json:"email"`
	Order         Order  `json:"order"`
	InTrialPeriod bool   `json:"in_trial_period"`
	LastExpiredAt int64  `json:"last_expired_at"`
}

type OrderChangedWebhookData struct {
	CustomerOrderData
	Changes struct {
		Status struct {
			Is  string `json:"is"`
			Was string `json:"was"`
		} `json:"status"`
		PaymentStatus struct {
			Is  string `json:"is"`
			Was string `json:"was"`
		} `json:"payment_status"`
		FulfillmentStatus struct {
			Is  string `json:"is"`
			Was string `json:"was"`
		} `json:"fulfillment_status"`
	} `json:"changes"`
}

func (d *OrderChangedWebhookData) Validate() error {
	// validate order
	order := d.Order
	if order.Number == "" {
		return errors.New("order is empty")
	}

	// validate user identifier
	if d.Identifier == "" {
		return errors.New("user identifier is empty")
	}

	// validate product
	product := order.Product()
	if product.Identifier == "" {
		return errors.New("product is empty")
	}

	// validate payment type
	paymentType := order.PaymentType.String()
	if paymentType == "" {
		return errors.New("payment type is empty")
	}

	return nil
}

func (r *WebhookReqBody) ToOrderChangedWebhookData() (data *OrderChangedWebhookData, err error) {
	err = json.Unmarshal([]byte(r.Data), &data)
	return
}

type Orders []Order

type CustomerOrdersData struct {
	Identifier string `json:"identifier"`
	Orders     Orders `json:"orders"`
}

func (r *Response) ToCustomerOrdersData() (data *CustomerOrdersData, err error) {
	err = json.Unmarshal([]byte(r.Data), &data)
	return
}

// HasUnfulfilledPrimePackageOrder 判斷用戶是否有未啟用 Prime 專屬方案訂單
func (o *Orders) HasUnfulfilledPrimePackageOrder() bool {
	if o == nil || len(*o) == 0 {
		return false
	}
	for _, order := range *o {
		if len(order.Products) > 0 && order.Products[0].IsPrimeOnly() &&
			order.PaymentStatus.IsPaid() && order.FulfillmentStatus.IsUnfulfilledYet() {
			return true
		}
	}
	return false
}

// HasUnfulfilledSubscription check if user has any subscription order not matter it's fulfilled or not
func (o *Orders) HasUnfulfilledSubscription() bool {
	if o == nil || len(*o) == 0 {
		return false
	}
	for _, order := range *o {
		if paid := order.PaymentStatus.IsPaid(); !paid {
			continue
		}
		if !order.FulfillmentStatus.IsUnfulfilledYet() {
			continue
		}
		if subscribed := order.SubscribeProduct; subscribed.Identifier != "" && subscribed.Recurring {
			return true
		}
		if len(order.Products) > 0 {
			for _, product := range order.Products {
				if product.Recurring {
					return true
				}
			}
		}
	}
	return false
}
