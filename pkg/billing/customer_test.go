package billing

import (
	"testing"
)

func TestCustomerStatus_IsHavingUnfulfilledOrder(t *testing.T) {
	tests := []struct {
		name       string
		customer   CustomerStatus
		wantResult bool
	}{
		{
			name: "當 OrdersData 為 nil 時應返回 false",
			customer: CustomerStatus{
				OrdersData: nil,
			},
			wantResult: false,
		},
		{
			name: "當 OrdersData 不為 nil 且有未完成的訂閱時應返回 true",
			customer: CustomerStatus{
				OrdersData: &CustomerOrdersData{
					Orders: Orders{
						{
							PaymentStatus:     PaymentStatusPaid,
							FulfillmentStatus: FulfillmentStatusUnfulfilledYet,
							SubscribeProduct: Product{
								Identifier: "test-product",
								Recurring:  true,
							},
						},
					},
				},
			},
			wantResult: true,
		},
		{
			name: "當 OrdersData 不為 nil 但沒有未完成的訂閱時應返回 false",
			customer: CustomerStatus{
				OrdersData: &CustomerOrdersData{
					Orders: Orders{
						{
							PaymentStatus:     PaymentStatusPaid,
							FulfillmentStatus: FulfillmentStatusFulfilled,
							SubscribeProduct: Product{
								Identifier: "test-product",
								Recurring:  true,
							},
						},
					},
				},
			},
			wantResult: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := tt.customer.IsHavingUnfulfilledOrder()
			if got != tt.wantResult {
				t.Errorf("IsHavingUnfulfilledOrder() = %v, want %v", got, tt.wantResult)
			}
		})
	}
}
