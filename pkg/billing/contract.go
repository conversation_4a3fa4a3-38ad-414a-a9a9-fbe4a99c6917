package billing

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/KKTV/kktv-api-v3/pkg/clock"
	"github.com/KKTV/kktv-api-v3/pkg/datatype"
	zlog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/secret"
)

type APICapabilities struct {
	SimultaneousPlaybackDeviceCount int64    `json:"simultaneous_playback_device_count"`
	FamilyMemberCount               int64    `json:"family_member_count"`
	Roles                           []string `json:"roles"`
}

type GracePeriod struct {
	Start             datatype.DateTime `json:"start"`
	End               datatype.DateTime `json:"end"`
	OrderNumber       string            `json:"order_number"` // the last payment failed order number
	ProductIdentifier string            `json:"product_identifier"`
}

type Contract struct {
	CurrentTransaction struct {
		Product           Product `json:"product"`
		CalculateBaseTime int64   `json:"calculate_base_time"`
	} `json:"current_transaction"`
	NextTransaction struct {
		Product           Product `json:"product"`
		CalculateBaseTime int64   `json:"calculate_base_time"`
	} `json:"next_transaction"`
	LastOrderNumber    string `json:"last_order_number"`
	SubscriptionStatus struct {
		APIChangedState[string]
		// 如果用戶已取消訂閱，但仍在訂閱效期內，訂閱產品資訊回傳在 SubscriptionStatus 內
		SubscribedProduct Product `json:"subscribed_product"`
	} `json:"subscription_status"`
	// 訂閱中的產品 (未取消訂閱)
	SubscribedProduct         Product         `json:"subscribed_product"`
	LastExpiredAt             int64           `json:"last_expired_at"`
	ValuableTransactionCount  int64           `json:"valuable_transaction_count"`
	ValuableTransactionAmount int64           `json:"valuable_transaction_amount"`
	HasFreeTrial              bool            `json:"has_free_trial"`
	HasIntroductoryOffer      bool            `json:"has_introductory_offer"`
	Capabilities              APICapabilities `json:"capabilities"`
	GracePeriod               *GracePeriod    `json:"grace_period"`
	clock                     clock.Clock     `json:"-"`
}

func (c *Contract) Exists() bool {
	return c.LastExpiredAt > 0
}

func (c *Contract) IsInSubscription() bool {
	return c.SubscriptionStatus.Is == "ongoing"
}

func (c *Contract) IsSubscriptionSuspended() bool {
	return c.SubscriptionStatus.Is == ContractSubscriptionStatusSuspend.String()
}

func (c *Contract) IsActive() bool {
	var now time.Time
	if c.clock != nil {
		now = c.clock.Now()
	} else {
		now = time.Now()
	}
	return c.LastExpiredAt > now.Unix()
}

func (c *Contract) Product() Product {
	// 訂閱中產品
	billingProduct := c.SubscribedProduct
	// 如果沒有訂閱中產品 (已取消訂閱或單次型產品)，取得當下生效中的 transaction 產品
	if billingProduct.PaymentType == "" {
		billingProduct = c.CurrentTransaction.Product
	}
	// 如果沒有當下生效中的 transaction，判斷已付款但尚未生效 transaction 產品
	if billingProduct.PaymentType == "" {
		billingProduct = c.NextTransaction.Product
	}
	// 當 contract 狀態轉為取消訂閱時，subscription_status.subscribed_product 為取消訂閱前的訂閱方案
	if billingProduct.PaymentType == "" &&
		c.SubscriptionStatus.Is == ContractSubscriptionStatusSuspend.String() &&
		c.SubscriptionStatus.SubscribedProduct.Identifier != "" {
		billingProduct = c.SubscriptionStatus.SubscribedProduct
	}
	return billingProduct
}

func (c *Contract) PaymentType() string {
	return c.Product().PaymentType
}

func (c *Contract) CurrentProductIdentifier() (result string) {
	if c.IsActive() {
		result = c.CurrentTransaction.Product.Identifier
	}
	return
}

func (c *Contract) IsFamilyPlan() (result bool) {
	return c.IsActive() && c.Capabilities.FamilyMemberCount > 1
}

func (c *Contract) SetClock(ck clock.Clock) {
	c.clock = ck
}

type CustomerContractData struct {
	Identifier string   `json:"identifier"`
	Phone      string   `json:"phone"`
	Contract   Contract `json:"contract"`
}

type ContractState string

const (
	ContractStateActive  ContractState = "active"
	ContractStateExpired ContractState = "expired"
)

func (s ContractState) String() string {
	return string(s)
}

type ContractSubscriptionStatus string

const (
	ContractSubscriptionStatusNa      ContractSubscriptionStatus = "na"
	ContractSubscriptionStatusOngoing ContractSubscriptionStatus = "ongoing"
	ContractSubscriptionStatusSuspend ContractSubscriptionStatus = "suspend"
)

func (s ContractSubscriptionStatus) String() string {
	return string(s)
}

type APIChangedState[T any] struct {
	Is  T `json:"is"`
	Was T `json:"was"`
}

type SubscriptionChanges struct {
	State              APIChangedState[ContractState] `json:"state"`
	SubscriptionStatus APIChangedState[string]        `json:"subscription_status"`
	LastExpiredAt      APIChangedState[int64]         `json:"last_expired_at"`
	SubscribedProduct  APIChangedState[string]        `json:"subscribed_product"`
}

type SubscriptionChangedWebhookData struct {
	CustomerContractData
	Changes SubscriptionChanges `json:"changes"`
	Orders  struct {
		Previous struct {
			Number            string `json:"number"`
			ParentOrderNumber string `json:"parent_order_number"`
		} `json:"previous"`
		LastFulfilled struct {
			Number string `json:"number"`
		} `json:"last_fulfilled"`
	}
}

func (d *SubscriptionChangedWebhookData) LastFulfilledOrderNumber() string {
	return d.Orders.LastFulfilled.Number
}

func (d *SubscriptionChangedWebhookData) PreviousParentOrderNumber() string {
	orderNumber := d.Orders.Previous.Number
	if d.Orders.Previous.ParentOrderNumber != "" {
		orderNumber = d.Orders.Previous.ParentOrderNumber
	}
	return orderNumber

}

func (d *SubscriptionChangedWebhookData) IsStateChangedToActive() bool {
	return d.Changes.State.Is == ContractStateActive
}

func (d *SubscriptionChangedWebhookData) IsStateChangedToExpired() bool {
	return d.Changes.State.Is == ContractStateExpired
}

func (d *SubscriptionChangedWebhookData) IsContractActivatedRecurringProduct() bool {
	return d.Changes.SubscribedProduct.Is != ""
}

func (d *SubscriptionChangedWebhookData) IsContractActivatedNonRecurringProduct() bool {
	return d.Contract.Product().Price > 0 && d.Contract.SubscriptionStatus.Is == "na"
}

func (d *SubscriptionChangedWebhookData) IsSubscriptionChangedToSuspend() bool {
	return d.Changes.SubscriptionStatus.Is == ContractSubscriptionStatusSuspend.String()
}

func (r *Response) ToCustomerContractData() (data *CustomerContractData, err error) {
	err = json.Unmarshal([]byte(r.Data), &data)
	return
}

func (r *WebhookReqBody) ToSubscriptionChangedWebhookData() (data *SubscriptionChangedWebhookData, err error) {
	err = json.Unmarshal([]byte(r.Data), &data)
	return
}

func (r *WebhookReqBody) DecryptData() (err error) {
	var decrypted []byte

	if r.Data == "" {
		return fmt.Errorf("data is empty")
	}

	secretAccessKey := secret.Values.BillingSecretAccessKey
	decrypted, err = decryptRespData(r.Data, []byte(secretAccessKey))
	if err != nil {
		return
	}
	zlog.Info("set parsed response data").
		Str("data", string(decrypted)).
		Send()
	r.Data = string(decrypted)

	return
}
