package billing

import (
	"log"
	"testing"

	"github.com/KKTV/kktv-api-v3/kkapp/datastore"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/secret"
	"github.com/stretchr/testify/assert"
)

func TestClient_ListProducts_IntegrationTest(t *testing.T) {
	secret.Init("test")
	cachePool := datastore.NewRedisPool([]string{"localhost:6379"})
	cacheReader := cache.New(cachePool.Slave())
	cacheWriter := cache.New(cachePool.Master())
	c := NewClient("https://bapi-stag.kktv.me", cacheWriter, cacheReader)

	products, err := c.ListProducts()
	assert.NoError(t, err)
	assert.NotNil(t, products)

	log.Println(products.Map)
	assert.Greater(t, len(products.Products), 1)
}
