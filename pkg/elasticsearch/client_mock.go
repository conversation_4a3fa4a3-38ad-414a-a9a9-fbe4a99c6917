// Code generated by MockGen. DO NOT EDIT.
// Source: client.go

// Package elasticsearch is a generated GoMock package.
package elasticsearch

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockClient is a mock of Client interface.
type MockClient struct {
	ctrl     *gomock.Controller
	recorder *MockClientMockRecorder
}

// MockClientMockRecorder is the mock recorder for MockClient.
type MockClientMockRecorder struct {
	mock *MockClient
}

// NewMockClient creates a new mock instance.
func NewMockClient(ctrl *gomock.Controller) *MockClient {
	mock := &MockClient{ctrl: ctrl}
	mock.recorder = &MockClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockClient) EXPECT() *MockClientMockRecorder {
	return m.recorder
}

// SearchTitle mocks base method.
func (m *MockClient) SearchTitle(query SearchTitlePayload, receiver interface{}) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchTitle", query, receiver)
	ret0, _ := ret[0].(error)
	return ret0
}

// SearchTitle indicates an expected call of SearchTitle.
func (mr *MockClientMockRecorder) SearchTitle(query, receiver interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchTitle", reflect.TypeOf((*MockClient)(nil).SearchTitle), query, receiver)
}
