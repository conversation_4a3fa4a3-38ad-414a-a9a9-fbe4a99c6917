package httpreq

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
	"gopkg.in/guregu/null.v3"
)

func TestGetPlatform(t *testing.T) {
	tests := []struct {
		name     string
		platform *string
		expected string
	}{
		{
			name:     "LowerCase Platform",
			platform: null.StringFrom("ANDROID").Ptr(),
			expected: "android",
		},
		{
			name:     "no platform header",
			platform: nil,
			expected: "",
		},
		{
			name:     "empty platform header",
			platform: null.StringFrom("").Ptr(),
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := httptest.NewRequest(http.MethodGet, "/", nil)
			if tt.platform != nil {
				r.Header.Set(HeaderPlatform, *tt.platform)
			}
			assert.Equal(t, tt.expected, GetPlatform(r))
		})
	}
}
