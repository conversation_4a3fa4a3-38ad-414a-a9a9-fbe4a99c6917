package validator

import (
	"errors"
	"regexp"
)

var (
	twMobileNumRegex = regexp.MustCompile(`^((09)|(\+?886(0)?9))[0-9]{8}$`)
)

func IsTaiwanMobileNum(num string) bool {
	return twMobileNumRegex.MatchString(num)
}

func NormalizeTaiwanMobileNum(num string) (string, error) {
	if !IsTaiwanMobileNum(num) {
		return "", errors.New("not a Taiwanese mobile number")
	}
	return `+8869` + num[len(num)-8:], nil
}
