package chtmod

import "testing"

func TestIsSubscriberArea<PERSON>alid(t *testing.T) {
	type args struct {
		area string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "valid area",
			args: args{
				area: "North",
			},
			want: true,
		},
		{
			name: "invalid area",
			args: args{
				area: "East",
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := IsSubscriberAreaValid(tt.args.area); got != tt.want {
				t.Errorf("IsSubscriberAreaValid() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetExternalProductID(t *testing.T) {
	type args struct {
		itemID   string
		itemType string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "get external product id of multi screen product",
			args: args{
				itemID:   MultiScreenProductID,
				itemType: "",
			},
			want: "225",
		},
		{
			name: "get external product id of multi screen MADE product",
			args: args{
				itemID:   MultiScreenProductID,
				itemType: "MADE",
			},
			want: "225MADE",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetExternalProductID(tt.args.itemID, tt.args.itemType); got != tt.want {
				t.Errorf("GetExternalProductID() = %v, want %v", got, tt.want)
			}
		})
	}
}
