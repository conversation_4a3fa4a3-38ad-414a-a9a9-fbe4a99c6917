package chtmod

import (
	"github.com/KKTV/kktv-api-v3/pkg/datetimer"
	"strings"
	"time"
)

var validSubscriberAreas = []string{"north", "central", "south"}

func IsSubscriberAreaValid(area string) bool {
	area = strings.ToLower(area)
	for _, validSubscriberArea := range validSubscriberAreas {
		if validSubscriberArea == area {
			return true
		}
	}
	return false
}

func GetExternalProductID(itemID, itemType string) string {
	if itemID == MultiScreenProductID {
		return itemID + itemType
	}

	return itemID
}

func IsSubscribing(subscriptionInfo *UserSubscriptionInfo) bool {
	if subscriptionInfo == nil || subscriptionInfo.Order.ItemID == "" {
		return false
	}

	now := time.Now().In(datetimer.LocationTaipei)
	if subscriptionInfo.Order.StartTime == 0 || time.Unix(subscriptionInfo.Order.StartTime, 0).After(now) {
		return false
	}

	return subscriptionInfo.Order.EndTime == 0 || time.Unix(subscriptionInfo.Order.EndTime, 0).After(now)
}
