package ssm

import (
	"context"
	"errors"
	"testing"

	myaws "github.com/KKTV/kktv-api-v3/pkg/aws"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/request"
	"github.com/aws/aws-sdk-go/service/appconfigdata"
	"github.com/aws/aws-sdk-go/service/appconfigdata/appconfigdataiface"
	"github.com/stretchr/testify/suite"
)

type mockAppConfigDataClient struct {
	appconfigdataiface.AppConfigDataAPI

	startConfigurationSessionWithContextErr error
	startConfigurationSessionInput          *appconfigdata.StartConfigurationSessionInput

	getLatestConfigurationWithContextOutput *appconfigdata.GetLatestConfigurationOutput
	getLatestConfigurationWithContextErr    error
}

func (m *mockAppConfigDataClient) StartConfigurationSessionWithContext(ctx aws.Context, input *appconfigdata.StartConfigurationSessionInput, opts ...request.Option) (*appconfigdata.StartConfigurationSessionOutput, error) {
	m.startConfigurationSessionInput = input
	return nil, m.startConfigurationSessionWithContextErr
}

func (m *mockAppConfigDataClient) GetLatestConfigurationWithContext(ctx aws.Context, input *appconfigdata.GetLatestConfigurationInput, opts ...request.Option) (*appconfigdata.GetLatestConfigurationOutput, error) {
	return m.getLatestConfigurationWithContextOutput, m.getLatestConfigurationWithContextErr
}

type AppConfigPollingTestSuite struct {
	suite.Suite
	ac         *appConfigPolling
	mockClient *mockAppConfigDataClient
}

func TestAppConfigPollingTestSuite(t *testing.T) {
	suite.Run(t, new(AppConfigPollingTestSuite))
}

func (suite *AppConfigPollingTestSuite) SetupTest() {
	suite.mockClient = &mockAppConfigDataClient{}
	suite.ac = &appConfigPolling{
		client: suite.mockClient,
		ctx:    context.Background(),
	}
}

func (suite *AppConfigPollingTestSuite) TearDownTest() {
}

type testModel struct {
	Hello string `json:"hello"`
}

func (suite *AppConfigPollingTestSuite) TestPoll() {
	testcases := []struct {
		name           string
		configReceiver any
		given          func()
		then           func(val any, err error)
	}{
		{
			name:           "got config successfully",
			configReceiver: &testModel{},
			given: func() {
				suite.mockClient.getLatestConfigurationWithContextOutput = &appconfigdata.GetLatestConfigurationOutput{
					NextPollIntervalInSeconds:  aws.Int64(120),
					NextPollConfigurationToken: aws.String("nexttoken"),
					Configuration:              []byte(`{"hello": "world"}`),
				}
			},
			then: func(val any, err error) {
				suite.NoError(err)
				suite.Equal("nexttoken", *suite.ac.Token)
				suite.Equal("world", val.(*testModel).Hello)
			},
		},
		{
			name:           "got ErrNoNewData WHEN aws return empty config",
			configReceiver: &testModel{},
			given: func() {
				suite.mockClient.getLatestConfigurationWithContextOutput = &appconfigdata.GetLatestConfigurationOutput{
					NextPollIntervalInSeconds:  aws.Int64(120),
					NextPollConfigurationToken: aws.String("nexttoken"),
					Configuration:              []byte(``),
				}
			},
			then: func(val any, err error) {
				suite.Error(myaws.ErrNoNewData)
				suite.Equal(&testModel{}, val)
				// even got empty config, the token should be updated
				suite.Equal(int64(120), suite.ac.GetNextPollInterval())
				suite.Equal("nexttoken", suite.ac.GetNextToken())
			},
		},
		{
			name:           "got error WHEN aws return error",
			configReceiver: &testModel{},
			given: func() {
				suite.mockClient.getLatestConfigurationWithContextErr = errors.New("appconfigdata error")
			},
			then: func(val any, err error) {
				suite.Error(errors.New("appconfigdata error"))
				suite.Equal(&testModel{}, val)
			},
		},
	}
	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			tc.given()
			err := suite.ac.Poll(tc.configReceiver)
			tc.then(tc.configReceiver, err)
		})
	}

}
