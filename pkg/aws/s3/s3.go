//go:generate mockgen -source s3.go -destination s3_mock.go -package s3
package s3

import (
	"bytes"
	"context"
	"io"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/aws/aws-sdk-go-v2/service/s3/types"
	"github.com/gabriel-vasile/mimetype"
)

type S3Client interface {
	GetObject(ctx context.Context, bucket string, pathKey string) (io.ReadCloser, error)
	PutObject(ctx context.Context, bucket string, pathKey string, body []byte, opts ...PutObjectOption) error
	DeleteObject(ctx context.Context, bucket string, pathKey string) error
	HeadObject(ctx context.Context, bucket string, pathKey string) (*s3.HeadObjectOutput, error)
}

type s3Client struct {
	client *s3.Client
}

func NewS3Client(cfg aws.Config) S3Client {
	client := s3.NewFromConfig(cfg)
	return &s3Client{
		client: client,
	}
}

func (w *s3Client) GetObject(ctx context.Context, bucket string, pathKey string) (io.ReadCloser, error) {
	output, err := w.client.GetObject(ctx, &s3.GetObjectInput{
		Bucket: aws.String(bucket),
		Key:    aws.String(pathKey),
	})
	if err != nil {
		return nil, err
	}
	return output.Body, nil
}

const (
	defaultACL = types.ObjectCannedACLPublicRead
)

func (w *s3Client) PutObject(ctx context.Context, bucket string, pathKey string, body []byte, opts ...PutObjectOption) error {
	mtype := mimetype.Detect(body)

	input := &s3.PutObjectInput{
		Bucket:      aws.String(bucket),
		Key:         aws.String(pathKey),
		Body:        bytes.NewReader(body),
		ACL:         defaultACL,
		ContentType: aws.String(mtype.String()),
	}

	for _, opt := range opts {
		opt(input)
	}

	_, err := w.client.PutObject(ctx, input)
	return err
}

type PutObjectOption func(*s3.PutObjectInput)

func WithACL(acl types.ObjectCannedACL) PutObjectOption {
	return func(input *s3.PutObjectInput) {
		input.ACL = acl
	}
}

func WithContentType(contentType string) PutObjectOption {
	return func(input *s3.PutObjectInput) {
		input.ContentType = aws.String(contentType)
	}
}

func (w *s3Client) DeleteObject(ctx context.Context, bucket string, pathKey string) error {
	_, err := w.client.DeleteObject(ctx, &s3.DeleteObjectInput{
		Bucket: aws.String(bucket),
		Key:    aws.String(pathKey),
	})
	return err
}

func (w *s3Client) HeadObject(ctx context.Context, bucket string, pathKey string) (*s3.HeadObjectOutput, error) {
	return w.client.HeadObject(ctx, &s3.HeadObjectInput{
		Bucket: aws.String(bucket),
		Key:    aws.String(pathKey),
	})
}
