package diff

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"gopkg.in/guregu/null.v3"
)

func TestCompareDBModel(t *testing.T) {
	type VerifiedInfo struct {
		Email           *string    `db:"email"`
		EmailVerifiedAt *null.Time `db:"email_verified_at"`
		Phone           string     `db:"phone"`
		PhoneVerifiedAt time.Time  `db:"phone_verified_at"`
	}
	type User struct {
		ID        string    `db:"id"`
		Name      string    `db:"name"`
		Age       int       `db:"age"`
		IsPrime   bool      `db:"is_prime"`
		nickname  string    `db:"nickname"` // private field should be ignored
		Balance   float32   `db:"balance"`
		CreatedAt time.Time `db:"created_at"`
		UpdatedAt time.Time `db:"updated_at"`
		VerifiedInfo
	}

	now := time.Date(2023, 4, 21, 0, 0, 0, 0, time.UTC)
	oldEmail := "<EMAIL>"
	oldEmailVerifiedAt := null.TimeFromPtr(nil)
	oldUser := User{
		ID:        "test-user-id",
		Name:      "<PERSON>",
		Age:       20,
		IsPrime:   false,
		nickname:  "frog",
		Balance:   20.2,
		CreatedAt: now,
		UpdatedAt: now,
		VerifiedInfo: VerifiedInfo{
			Email:           &oldEmail,
			EmailVerifiedAt: &oldEmailVerifiedAt,
			Phone:           "+886912345678",
			PhoneVerifiedAt: now,
		},
	}

	newEmail := "<EMAIL>"
	newEmailVerifiedAt := null.TimeFrom(now)
	newUser := User{
		ID:        "test-user-id",
		Name:      "Dog",
		Age:       87,
		nickname:  "dog",
		IsPrime:   true,
		Balance:   87.2,
		CreatedAt: now.In(time.FixedZone("Asia/Taipei", 8*60*60)),
		UpdatedAt: now.Add(time.Hour),
		VerifiedInfo: VerifiedInfo{
			Email:           &newEmail,
			EmailVerifiedAt: &newEmailVerifiedAt,
			Phone:           "+886987654321",
			PhoneVerifiedAt: now.Add(time.Hour),
		},
	}

	diff, err := CompareDBModel(&oldUser, newUser, "updated_at")
	want := map[string]Val{
		"name": {
			Old: oldUser.Name,
			New: newUser.Name,
		},
		"age": {
			Old: oldUser.Age,
			New: newUser.Age,
		},
		"is_prime": {
			Old: oldUser.IsPrime,
			New: newUser.IsPrime,
		},
		"balance": {
			Old: oldUser.Balance,
			New: newUser.Balance,
		},
		"verified_info.email": {
			Old: *oldUser.Email,
			New: *newUser.Email,
		},
		"verified_info.email_verified_at": {
			Old: "null",
			New: newUser.EmailVerifiedAt.Time,
		},
		"verified_info.phone": {
			Old: oldUser.Phone,
			New: newUser.Phone,
		},
		"verified_info.phone_verified_at": {
			Old: oldUser.PhoneVerifiedAt,
			New: newUser.PhoneVerifiedAt,
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, want, diff)
}
