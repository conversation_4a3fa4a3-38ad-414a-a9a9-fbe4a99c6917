package diff

import (
	"database/sql/driver"
	"errors"
	"reflect"
	"regexp"
	"strings"
	"time"

	"gopkg.in/guregu/null.v3"
)

type Val struct {
	Old any `json:"old"`
	New any `json:"new"`
}

// CompareDBModel compares two db models and return the difference even if the model contains nested structs
// for time type case, different time zone but same timestamp will be considered as same
// support types: string, int, float, bool, time.Time, null.String, null.Int, null.Float, null.Bool, null.Time
// reference to test case db_model_test.go to see the usage
func CompareDBModel(old, new any, ignores ...string) (map[string]Val, error) {
	if old == nil || new == nil {
		return nil, errors.New("value should not be nil")
	}

	// 取得 pointer 指向的值
	if reflect.TypeOf(old).Kind() == reflect.Ptr {
		old = reflect.ValueOf(old).Elem().Interface()
	}
	if reflect.TypeOf(new).Kind() == reflect.Ptr {
		new = reflect.ValueOf(new).Elem().Interface()
	}

	oldVal := reflect.ValueOf(old)
	newVal := reflect.ValueOf(new)
	if oldVal.Kind() != reflect.Struct || newVal.Kind() != reflect.Struct {
		return nil, errors.New("kind of value should be struct")
	}

	oldType := oldVal.Type()
	newType := newVal.Type()
	if oldType != newType {
		return nil, errors.New("type of value should be same")
	}

	timeType := reflect.TypeOf(time.Time{})
	nullTimeType := reflect.TypeOf(null.Time{})
	diffs := make(map[string]Val)
	for i := 0; i < oldVal.NumField(); i++ {
		// 取得 struct 所有欄位名稱、類型、值
		fieldName := oldType.Field(i).Tag.Get("db")
		if fieldName == "" {
			fieldName = convertStringToSnakeCase(oldType.Field(i).Name)
		}

		fieldType := oldType.Field(i).Type
		oldFieldVal := oldVal.Field(i)
		newFieldVal := newVal.Field(i)

		// 取得 pointer 指向的值
		if fieldType.Kind() == reflect.Pointer {
			fieldType = fieldType.Elem()
			if oldFieldVal.IsNil() || newFieldVal.IsNil() {
				return nil, errors.New("value should not be nil")
			}
			oldFieldVal = oldFieldVal.Elem()
			newFieldVal = newFieldVal.Elem()
		}

		// 忽略 private field
		if !oldFieldVal.CanInterface() || !newFieldVal.CanInterface() {
			continue
		}

		// skip ignore fields
		if len(ignores) > 0 {
			ignore := false
			for _, ignoreField := range ignores {
				if fieldName == ignoreField {
					ignore = true
					break
				}
			}
			if ignore {
				continue
			}
		}

		// time 類別用 Equal 比對
		if fieldType == timeType {
			getDiffOfTimeType(&diffs, fieldName, oldFieldVal.Interface(), newFieldVal.Interface())
		} else if fieldType == nullTimeType {
			getDiffOfNullTimeType(&diffs, fieldName, oldFieldVal.Interface(), newFieldVal.Interface())
		} else if fieldType.Kind() == reflect.Struct && !isNullableType(oldFieldVal.Interface()) {
			// 遞迴處理巢狀 struct
			subStruct, err := CompareDBModel(oldFieldVal.Interface(), newFieldVal.Interface())
			if err != nil {
				return nil, err
			}
			for subFieldName, subFieldVal := range subStruct {
				diffs[fieldName+"."+subFieldName] = subFieldVal
			}
		} else {
			// 判斷欄位值是否相同
			if !reflect.DeepEqual(oldFieldVal.Interface(), newFieldVal.Interface()) {
				if isNullableType(oldFieldVal.Interface()) {
					o, n := getDiffNullableVal(oldFieldVal.Interface(), newFieldVal.Interface())
					if o == nil || n == nil {
						return nil, errors.New("invalid nullable value")
					}
					diffs[fieldName] = Val{
						Old: o,
						New: n,
					}
				} else {
					diffs[fieldName] = Val{
						Old: oldFieldVal.Interface(),
						New: newFieldVal.Interface(),
					}
				}
			}
		}
	}

	return diffs, nil
}

type nullableType interface {
	null.String | null.Int | null.Float | null.Bool | null.Time
	IsZero() bool
	Value() (driver.Value, error)
}

func isNullableType(val any) bool {
	switch val.(type) {
	case null.String, null.Int, null.Float, null.Bool, null.Time:
		return true
	}
	return false
}

// formatNullableVal returns "null" if the nullable value is zero value
func formatNullableVal[T nullableType](t T) any {
	if !t.IsZero() {
		val, _ := t.Value()
		return val
	}
	return "null"
}

func getDiffNullableVal(oldFieldVal, newFieldVal any) (any, any) {
	switch oldFieldVal.(type) {
	case null.String:
		oldNullableVal := formatNullableVal(oldFieldVal.(null.String))
		newNullableVal := formatNullableVal(newFieldVal.(null.String))
		return oldNullableVal, newNullableVal
	case null.Int:
		oldNullableVal := formatNullableVal(oldFieldVal.(null.Int))
		newNullableVal := formatNullableVal(newFieldVal.(null.Int))
		return oldNullableVal, newNullableVal
	case null.Float:
		oldNullableVal := formatNullableVal(oldFieldVal.(null.Float))
		newNullableVal := formatNullableVal(newFieldVal.(null.Float))
		return oldNullableVal, newNullableVal
	case null.Bool:
		oldNullableVal := formatNullableVal(oldFieldVal.(null.Bool))
		newNullableVal := formatNullableVal(newFieldVal.(null.Bool))
		return oldNullableVal, newNullableVal
	case null.Time:
		oldNullableVal := formatNullableVal(oldFieldVal.(null.Time))
		newNullableVal := formatNullableVal(newFieldVal.(null.Time))
		return oldNullableVal, newNullableVal
	}

	return nil, nil
}

func convertStringToSnakeCase(str string) string {
	matchFirstCap := regexp.MustCompile("(.)([A-Z][a-z]+)")
	matchAllCap := regexp.MustCompile("([a-z0-9])([A-Z])")
	snake := matchFirstCap.ReplaceAllString(str, "${1}_${2}")
	snake = matchAllCap.ReplaceAllString(snake, "${1}_${2}")
	return strings.ToLower(snake)
}

func getDiffOfTimeType(diffs *map[string]Val, fieldName string, oldFieldVal any, newFieldVal any) {
	if !oldFieldVal.(time.Time).Equal(newFieldVal.(time.Time)) {
		ot, nt := oldFieldVal.(time.Time), newFieldVal.(time.Time)
		(*diffs)[fieldName] = Val{
			Old: ot.UTC(),
			New: nt.UTC(),
		}
	}
}

func getDiffOfNullTimeType(diffs *map[string]Val, fieldName string, oldFieldVal any, newFieldVal any) {
	oldFieldTimeVal := oldFieldVal.(null.Time).ValueOrZero()
	newFieldTimeVal := newFieldVal.(null.Time).ValueOrZero()
	oldFieldTimeVal, _ = time.Parse(time.RFC3339, oldFieldTimeVal.Format(time.RFC3339))
	newFieldTimeVal, _ = time.Parse(time.RFC3339, newFieldTimeVal.Format(time.RFC3339))

	oldFieldTimeVal = oldFieldTimeVal.UTC()
	newFieldTimeVal = newFieldTimeVal.UTC()
	if !oldFieldTimeVal.Equal(newFieldTimeVal) {
		oldFieldVal, newFieldVal = getDiffNullableVal(oldFieldVal, newFieldVal)
		(*diffs)[fieldName] = Val{
			Old: oldFieldVal,
			New: newFieldVal,
		}
	}
}
