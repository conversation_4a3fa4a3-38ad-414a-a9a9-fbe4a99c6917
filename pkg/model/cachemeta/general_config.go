package cachemeta

type AnnounceForKKIDSunset struct {
	Title   string `json:"title"`
	BtnText string `json:"btn_text"`
	Msg     struct {
		General               string `json:"general"`
		UnverifiedHasPassword string `json:"unverified_has_password"`
	} `json:"msg"`
	Redirect struct {
		Account  string `json:"account"`
		Password string `json:"password"`
	} `json:"redirect"`
}

type PlanLustTitleList struct {
	TitleIds []string `json:"title_ids"`
}

type BlockConfigByPlatform struct {
	Android map[string][]string `json:"android"`
	IOS     map[string][]string `json:"ios"`
	TV      map[string][]string `json:"tv"`
	Web     map[string][]string `json:"web"`
	Mod     map[string][]string `json:"mod"`
}

type HideLustHiddenType string

const (
	HideLustHiddenByReviewVersion HideLustHiddenType = "review_version"
	HideLustHiddenByPlatforms     HideLustHiddenType = "platforms"
)

type ContentControlConfig struct {
	HideLustContent struct {
		HiddenBy HideLustHiddenType `json:"hidden_by"`
		// refer to httpreq.GetPlatform. enum: tvos|ipados|ios|mod|android tv|android|web
		Platforms       []string            `json:"platforms,omitempty"`
		SearchNotAppend map[string][]string `json:"search_not_append,omitempty"`
	} `json:"hide_lust_content"`
}
