package authority

type Authority string

const (
	/*
	 Naming convention: [*feature]:[action]
	 - last part of the authority must be an action
	*/
	UserInfoGet Authority = "user:info:get"

	FreemiumPlay  Authority = "freemium:play"
	FreeTrialPlay Authority = "free_trial:play"
	PremiumPlay   Authority = "premium:play"
	PlanAnimePlay Authority = "plan:anime:play"

	PremiumHeadlineLinkDisplay   Authority = "premium:headline-link:display"
	FreeTrialHeadlineLinkDisplay Authority = "free_trial:headline-link:display"
	ExpiredHeadlineLinkDisplay   Authority = "expired:headline-link:display"
	GuestHeadlineLinkDisplay     Authority = "guest:headline-link:display"
	PrimeHeadlineLinkDisplay     Authority = "prime:headline-link:display"
	PrHeadlineLinkDisplay        Authority = "pr:headline-link:display"
	PlanAnimeHeadlineLinkDisplay Authority = "plan-anime:headline-link:display"

	PremiumEventDisplay   Authority = "premium:event:display"
	FreeTrialEventDisplay Authority = "free_trial:event:display"
	ExpiredEventDisplay   Authority = "expired:event:display"
	GuestEventDisplay     Authority = "guest:event:display"
	PrimeEventDisplay     Authority = "prime:event:display"
	PrEventDisplay        Authority = "pr:event:display"
	PlanAnimeEventDisplay Authority = "plan-anime:event:display"

	PremiumAdsDisplay   Authority = "premium:ads:display"
	FreeTrialAdsDisplay Authority = "free_trial:ads:display"
	ExpiredAdsDisplay   Authority = "expired:ads:display"
	GuestAdsDisplay     Authority = "guest:ads:display"
	PrimeAdsDisplay     Authority = "prime:ads:display"
	PrAdsDisplay        Authority = "pr:ads:display"
	PlanAnimeAdsDisplay Authority = "plan-anime:ads:display"

	AdvancedPlayerFunctionDisplay Authority = "advanced-player-function:display"

	PrimeBannerDisplay Authority = "prime:banner:display"

	MenuItemFreeZoneDisplay   Authority = "menu-item:free:display" // to display the `免費專區` menu item
	SignupSurveyBannerDisplay Authority = "signup-survey-banner:display"
	SettingStreamingUpdate    Authority = "setting-streaming:update"
	TitleExtraHighQualityPlay Authority = "extra:high-quality:play"

	ProductPackageGeneralPurchase   Authority = "product-package:general:purchase"
	ProductPackagePrimeOnlyPurchase Authority = "product-package:prime-only:purchase"
	ProductPackagePreOrderPurchase  Authority = "product-package:pre-order:purchase"

	CouponCodeFreeRedeem    Authority = "coupon-code:free:redeem"
	CouponCodeNonFreeRedeem Authority = "coupon-code:non-free:redeem"

	// FamilyMemberJoin to become a family member via an invitation code
	FamilyMemberJoin Authority = "family:member:join"

	AccountDelete Authority = "account:delete"

	SignupSurveyFormDisplay Authority = "signup-survey-form:display"
)

func (a Authority) String() string {
	return string(a)
}
