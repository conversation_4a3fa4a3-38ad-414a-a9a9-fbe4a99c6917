package dbmeta

import "testing"

func TestEpisode_IsAVOD(t *testing.T) {
	tests := []struct {
		name string
		e    Episode
		want bool
	}{
		{
			name: "meta.play_zone is true",
			e: Episode{
				Meta: Meta{
					"play_zone": true,
				},
			},
			want: true,
		},
		{
			name: "meta.play_zone is false",
			e: Episode{
				Meta: Meta{
					"play_zone": false,
				},
			},
			want: false,
		},
		{
			name: "meta.play_zone is nil",
			e:    Episode{Meta: Meta{}},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.e.IsAVOD(); got != tt.want {
				t.Errorf("Episode.IsAVOD() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestEpisode_CanFreeTrialAccess(t *testing.T) {
	tests := []struct {
		name string
		e    Episode
		want bool
	}{
		{
			name: "meta.free_trial is true",
			e: Episode{
				Meta: Meta{
					"free_trial": true,
				},
			},
			want: true,
		},
		{
			name: "meta.free_trial is false",
			e: Episode{
				Meta: Meta{
					"free_trial": false,
				},
			},
			want: false,
		},
		{
			name: "meta.free_trial is nil",
			e:    Episode{Meta: Meta{}},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.e.CanFreeTrialAccess(); got != tt.want {
				t.Errorf("Episode.CanFreeTrialAccess() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestEpisode_Available(t *testing.T) {
	tests := []struct {
		name string
		e    Episode
		want bool
	}{
		{
			name: "meta.available is true",
			e: Episode{
				Meta: Meta{
					"available": true,
				},
			},
			want: true,
		},
		{
			name: "meta.available is false",
			e: Episode{
				Meta: Meta{
					"available": false,
				},
			},
			want: false,
		},
		{
			name: "meta.available is nil",
			e:    Episode{Meta: Meta{}},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.e.Available(); got != tt.want {
				t.Errorf("Episode.Available() = %v, want %v", got, tt.want)
			}
		})
	}
}
