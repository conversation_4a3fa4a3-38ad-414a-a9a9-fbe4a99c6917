package dbmeta

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
)

// Meta is a map of string to interface, shared by Title, Series, Episode
type Meta map[string]interface{}

func (meta Meta) Value() (driver.Value, error) {
	if len(meta) == 0 {
		return nil, nil
	}

	if jsonValue, err := json.Marshal(meta); err != nil {
		return nil, err
	} else {
		return driver.Value(jsonValue), nil
	}
}

func (meta *Meta) Scan(src interface{}) error {
	var source []byte
	_m := make(map[string]interface{})

	switch src.(type) {
	case []uint8:
		source = []byte(src.([]uint8))
	case nil:
		return nil
	default:
		return errors.New("incompatible type for Meta")
	}
	err := json.Unmarshal(source, &_m)
	if err != nil {
		return err
	}
	*meta = Meta(_m)
	return nil
}

func metaVal[T any](meta Meta, key string, defaultVal T) T {
	if meta == nil {
		return defaultVal
	}
	val, ok := meta[key]
	if !ok {
		return defaultVal
	}
	v, ok := val.(T)
	if !ok {
		return defaultVal
	}
	return v
}
