package dbmeta

type TitlelistPinType string

const ( // for list_type: choice
	TitlelistPinTypeAiring            TitlelistPinType = "airing"             //跟播中
	TitlelistPinTypeNewFinale         TitlelistPinType = "new_finale"         //全集新上架
	TitlelistPinTypeGuaranteedVisible TitlelistPinType = "guaranteed_visible" // 保證曝光
)
const ( //for list_type: title, link
	TitlelistPinTypeFirst TitlelistPinType = "first"
)

func (pt TitlelistPinType) String() string {
	return string(pt)
}

type TitlelistType string

const ( // 片單
	TitlelistTypeChoice    TitlelistType = "choice"
	TitlelistTypeRanking   TitlelistType = "ranking"
	TitlelistTypeAiring    TitlelistType = "airing"
	TitlelistTypeHighlight TitlelistType = "highlight"
)
const ( // Headline
	TitlelistTypeTitle TitlelistType = "title"
	TitlelistTypeLink  TitlelistType = "link"
)

func (t TitlelistType) String() string {
	return string(t)
}
