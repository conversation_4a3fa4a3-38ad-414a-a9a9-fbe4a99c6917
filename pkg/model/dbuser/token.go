package dbuser

import (
	"gopkg.in/guregu/null.v3"
)

type Token struct {
	ID        string    `db:"id"`
	UserID    string    `db:"user_id"`
	Token     string    `db:"token"`
	TokenHash string    `db:"token_hash"`
	SourceIP  string    `db:"source_ip"`
	UserAgent string    `db:"user_agent"`
	ExpiredAt null.Time `db:"expired_at"`
	CreatedAt null.Time `db:"created_at"`
	UpdatedAt null.Time `db:"updated_at"`
}
