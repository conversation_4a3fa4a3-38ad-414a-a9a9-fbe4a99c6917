package dbuser

import (
	"time"

	"gopkg.in/guregu/null.v3"
)

type PaymentInfo struct {
	UserID                 string      `db:"user_id" json:"user_id"`
	Email                  null.String `db:"email" json:"email,omitempty"`
	Phone                  null.String `db:"phone" json:"phone,omitempty"`
	PaymentType            null.String `db:"payment_type" json:"payment_type"`
	CreditCard6NO          null.String `db:"credit_card_6no" json:"credit_card_6no,omitempty"`
	CreditCard4NO          null.String `db:"credit_card_4no" json:"credit_card_4no,omitempty"`
	CreditCardTokenValue   null.String `db:"credit_card_token_value" json:"credit_card_token_value,omitempty"`
	CreditCardTokenTerm    null.String `db:"credit_card_token_term" json:"credit_card_token_term,omitempty"`
	TaxID                  null.String `db:"tax_id" json:"tax_id,omitempty"`
	IapReceiptData         null.String `db:"iap_receipt_data" json:"iap_receipt_data,omitempty"`
	IapReceiptDataHash     null.String `db:"iap_receipt_data_hash" json:"iap_receipt_data_hash,omitempty"`
	IapLatestExpiresDate   null.Time   `db:"iap_latest_expires_date" json:"iap_latest_expires_date,omitempty"`
	IapLatestTransactionID null.String `db:"iap_latest_transaction_id" json:"iap_latest_transaction_id,omitempty"`
	CaringCode             null.String `db:"caring_code" json:"caring_code,omitempty"`
	Recipient              null.String `db:"recipient" json:"recipient,omitempty"`
	RecipientAddress       null.String `db:"recipient_address" json:"recipient_address,omitempty"`
	CarrierType            null.String `db:"carrier_type" json:"carrier_type,omitempty"`
	CarrierValue           null.String `db:"carrier_value" json:"carrier_value,omitempty"`
	TelecomMpID            null.String `db:"telecom_mp_id" json:"telecom_mp_id,omitempty"`
	TstarOrderID           null.String `db:"tstar_order_id" json:"tstar_order_id,omitempty"`
	TstarContractID        null.String `db:"tstar_contract_id" json:"tstar_contract_id,omitempty"`
	ModSubscriberID        null.String `db:"mod_subscriber_id" json:"mod_subscriber_id,omitempty"`
	ModSubscriberArea      null.String `db:"mod_subscriber_area" json:"mod_subscriber_area,omitempty"`
	IabReceiptData         null.String `db:"iab_receipt_data" json:"iab_receipt_data,omitempty"`
	IabOrderID             null.String `db:"iab_order_id" json:"iab_order_id,omitempty"`
	IabLatestOrderID       null.String `db:"iab_latest_order_id" json:"iab_latest_order_id,omitempty"`
	IabLatestExpiresDate   null.Time   `db:"iab_latest_expires_date" json:"iab_latest_expires_date,omitempty"`
	CreatedAt              time.Time   `db:"created_at" json:"created_at,omitempty"`
	UpdatedAt              null.Time   `db:"updated_at" json:"updated_at,omitempty"`
	FamilyID               null.String `db:"family_id" json:"family_id,omitempty"`
}

type PaymentInfoType string

const (
	PaymentInfoTypeCoupon     PaymentInfoType = "coupon"
	PaymentInfoTypeTSTAR      PaymentInfoType = "tstar"
	PaymentInfoTypeAPTG       PaymentInfoType = "aptg"
	PaymentInfoTypeTelecom    PaymentInfoType = "telecom"
	PaymentInfoTypeIAP        PaymentInfoType = "iap"
	PaymentInfoTypeIAB        PaymentInfoType = "iab"
	PaymentInfoTypeCns        PaymentInfoType = "cns"
	PaymentInfoTypeCvs        PaymentInfoType = "cvs_code"
	PaymentInfoTypeMod        PaymentInfoType = "mod"
	PaymentInfoTypeCreditCard PaymentInfoType = "credit_card"
	PaymentInfoTypeBandott    PaymentInfoType = "bandott"
	PaymentInfoTypePXPayPlus  PaymentInfoType = "pxpayplus"
)

func (r PaymentInfoType) String() string {
	return string(r)
}


type PaymentStatus string

const (
	PaymentStatusPending     PaymentStatus = "pending"
	PaymentStatusAuthorized  PaymentStatus = "authorized"
	PaymentStatusPaid        PaymentStatus = "paid"
	PaymentStatusFailed      PaymentStatus = "failed"
	PaymentStatusRefunded    PaymentStatus = "refunded"
)

func (s PaymentStatus) String() string {
	return string(s)
}
