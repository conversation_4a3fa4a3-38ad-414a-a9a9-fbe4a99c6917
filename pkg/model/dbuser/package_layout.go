package dbuser

import (
	"database/sql"

	"gopkg.in/guregu/null.v3"
)

type PackageLayout struct {
	ID                sql.NullInt64 `db:"id"`
	ProductPackagesID sql.NullInt64 `db:"product_packages_id"`
	Title             null.String   `db:"title"`
	Subtitle          null.String   `db:"subtitle"`
	TextColor         null.String   `db:"text_color"`
	CTAText           null.String   `db:"cta_text"` // CTA button
	Description       null.String   `db:"description"`
	Image             null.String   `db:"image"`
	BackgroundColor   null.String   `db:"background_color"`
	BackgroundImage   null.String   `db:"background_image"`
	Terms             null.String   `db:"terms"`            // 方案條款
	DisplayMoreBtn    null.Bool     `db:"display_more_btn"` // 看更多方案
	CreatedAt         null.Time     `db:"created_at"`
	UpdatedAt         null.Time     `db:"updated_at"`
}

type PackageLayouts []*PackageLayout

type PackageLayoutField string

func (f PackageLayoutField) String() string {
	return string(f)
}

const (
	PackageLayoutFieldTitle           PackageLayoutField = "title"
	PackageLayoutFieldSubtitle        PackageLayoutField = "subtitle"
	PackageLayoutFieldTextColor       PackageLayoutField = "text_color"
	PackageLayoutFieldCTAText         PackageLayoutField = "cta_text"
	PackageLayoutFieldDescription     PackageLayoutField = "description"
	PackageLayoutFieldImage           PackageLayoutField = "image"
	PackageLayoutFieldBackgroundColor PackageLayoutField = "background_color"
	PackageLayoutFieldBackgroundImage PackageLayoutField = "background_image"
	PackageLayoutFieldTerms           PackageLayoutField = "terms"
	PackageLayoutFieldDisplayMoreBtn  PackageLayoutField = "display_more_btn"
	PackageLayoutFieldCreatedAt       PackageLayoutField = "created_at"
	PackageLayoutFieldUpdatedAt       PackageLayoutField = "updated_at"
)
