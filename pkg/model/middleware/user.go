package middleware

import (
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"gopkg.in/dgrijalva/jwt-go.v3"
)

type AccessUser struct {
	UserID      string            `json:"user_id"`
	Memberships dbuser.Membership `json:"memberships"`
}

func (a *AccessUser) IsMember() bool {
	return len(a.Memberships) > 0
}

type AccessTokenClaims struct {
	jwt.StandardClaims
	Memberships    dbuser.Membership `json:"memberships"`
	Role           dbuser.Role       `json:"role"`                       // deprecated, this is for legacy codes to check permission
	Type           dbuser.Type       `json:"type"`                       // deprecated, this is for legacy codes to check permission
	HasBoughtPrime bool              `json:"has_bought_prime,omitempty"` // deprecated, this is for legacy codes to check permission
}
