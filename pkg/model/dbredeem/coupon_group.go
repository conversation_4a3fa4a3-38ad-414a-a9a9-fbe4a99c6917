package dbredeem

import (
	"fmt"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp/kkutil"
	"gopkg.in/guregu/null.v3"
)

type CouponProductID int64

const (
	// 無價序號
	CouponProductIDNoPrice CouponProductID = 4
	// 有價序號
	CouponProductIDPrice CouponProductID = 6
)

func (c CouponProductID) Int64() int64 {
	return int64(c)
}

// CouponGroup struct
type CouponGroup struct {
	// ID from coupon_codes, others from coupon_groups
	ID                int64       `db:"id" json:"id"`
	Prefix            string      `db:"prefix" json:"prefix"`
	Price             int64       `db:"price" json:"price"`
	Duration          string      `db:"duration" json:"duration"`
	UsageLimitPerUser null.Int    `db:"usage_limit_per_user" json:"usage_limit_per_user"`
	AllowReuse        bool        `db:"allow_reuse" json:"allow_reuse"`
	ValidSince        time.Time   `db:"valid_since" json:"valid_since"`
	ExpiresAt         time.Time   `db:"expires_at" json:"expires_at"`
	CreatedAt         time.Time   `db:"created_at" json:"created_at"`
	UpdatedAt         time.Time   `db:"updated_at" json:"updated_at"`
	PriceNoTax        int64       `db:"price_no_tax" json:"price_no_tax"`
	Description       string      `db:"description" json:"description"`
	FreeDuration      string      `db:"free_duration" json:"free_duration"`
	Fee               int64       `db:"fee" json:"fee"`
	Channel           null.String `db:"channel" json:"channel"`
	ProductID         null.Int    `db:"product_id" json:"product_id"`
	CampaignGroup     null.String `db:"campaign_group" json:"campaign_group"`
}

func (g *CouponGroup) UsageLimit() int64 {
	var usageLimit int64
	if g.AllowReuse {
		usageLimit = 1
	} else if g.UsageLimitPerUser.Valid {
		usageLimit = g.UsageLimitPerUser.Int64
	}
	return usageLimit
}

func (g *CouponGroup) ParseDisplayCouponDuration() (dYear, dMonth, dDay, fYear, fMonth, fDay int) {
	unit, durationAmount := kkutil.ParseDuration(g.Duration)
	_, freeDurationAmount := kkutil.ParseDuration(g.FreeDuration)

	switch unit {
	case kkutil.DurationUnitDay.String():
		dDay = durationAmount
		fDay = freeDurationAmount
	case kkutil.DurationUnitMonth.String():
		dMonth = durationAmount
		fMonth = freeDurationAmount
	case kkutil.DurationUnitYear.String():
		dYear = durationAmount
		fYear = freeDurationAmount
	}
	return
}

func (g *CouponGroup) DisplayCouponDuration() (couponDuration string) {
	dYear, dMonth, dDay, fYear, fMonth, fDay := g.ParseDisplayCouponDuration()

	totalYears := dYear + fYear
	totalMonths := dMonth + fMonth
	totalDays := dDay + fDay

	if totalDays > 0 {
		couponDuration = fmt.Sprintf("%d days", totalDays)
	}
	if totalMonths > 0 {
		couponDuration = fmt.Sprintf("%d months %d days", totalMonths, totalDays)
	}
	if totalYears > 0 {
		couponDuration = fmt.Sprintf("%d years %d months %d days", totalYears, totalMonths, totalDays)
	}
	return couponDuration
}
