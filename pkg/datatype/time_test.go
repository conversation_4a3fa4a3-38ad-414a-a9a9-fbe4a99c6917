package datatype

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestDateTime_MarshalJSON(t *testing.T) {
	loc := time.FixedZone("UTC+8", 8*60*60)
	d := DateTime(time.Date(2020, 4, 29, 0, 0, 0, 0, loc))

	b, err := json.Marshal(d)
	if err != nil {
		t.Error(err)
	}
	assert.Equal(t, "\"2020-04-28T16:00:00Z\"", string(b))
}

func TestDateTime_UnmarshalJSON(t *testing.T) {
	type testCase struct {
		name     string
		input    string
		expected time.Time
	}

	taipeiLoc := time.FixedZone("UTC+8", 8*60*60)
	testCases := []testCase{
		{
			name:     "iso8601 UTC",
			input:    `"2020-04-28T16:00:00Z"`,
			expected: time.Date(2020, 4, 29, 0, 0, 0, 0, taipeiLoc),
		},
		{
			name:     "iso8601 with timezone+08:00",
			input:    `"2020-04-29T00:00:00+08:00"`,
			expected: time.Date(2020, 4, 29, 0, 0, 0, 0, taipeiLoc),
		},
	}

	for _, tc := range testCases {
		var d DateTime
		err := json.Unmarshal([]byte(tc.input), &d)
		if err != nil {
			t.Error(err)
		}
		assert.True(t, tc.expected.Equal(d.Time()))
	}
}
