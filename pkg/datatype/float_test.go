package datatype

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestMarshal(t *testing.T) {

	testcases := []struct {
		name   string
		input  float64
		expect string
	}{
		{
			name:   "integral float",
			input:  4.0,
			expect: "4.0000",
		},
		{
			name:   "float with 1 precision",
			input:  4.1,
			expect: "4.1000",
		},
		{
			name:   "float with 5 precision THEN should be rounded up",
			input:  4.16666,
			expect: "4.1667",
		},
		{
			name:   "float with 5 precision THEN should be rounded down",
			input:  4.16665,
			expect: "4.1666",
		},
	}
	for _, tc := range testcases {
		t.Run(tc.name, func(t *testing.T) {
			f := RoundedFloat(tc.input)
			j, _ := json.Marshal(f)
			assert.Equal(t, tc.expect, string(j))
		})
	}
}
