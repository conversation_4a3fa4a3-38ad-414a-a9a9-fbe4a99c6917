package mail

import (
	"context"
	"github.com/KKTV/kktv-api-v3/pkg/log"
	"time"

	"github.com/mailgun/mailgun-go/v4"
)

type mgClient struct {
	client *mailgun.MailgunImpl
}

func NewMailGunClient(domain, apiKey string) ServiceProvider {
	return &mgClient{
		client: mailgun.NewMailgun(domain, apiKey),
	}
}

func (mg *mgClient) SendMessage(sender, recipient, subject string, body Body) error {
	b := ""
	if body.Text != nil {
		b = *body.Text
	}
	// FIXME: to support html, refer to mg.client.NewMIMEMessage
	message := mg.client.NewMessage(sender, subject, b, recipient)

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()

	resp, id, err := mg.client.Send(ctx, message)
	if err != nil {
		log.Warn("mailgun: send fail").Str("recipient", recipient).Interface("resp", resp).Err(err).Send()
		return err
	}

	log.Info("mailgun: send success").
		Str("recipient", recipient).Str("sending ID:", id).Interface("body", body).Send()
	return nil
}
