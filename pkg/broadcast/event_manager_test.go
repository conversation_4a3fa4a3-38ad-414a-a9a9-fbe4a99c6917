package broadcast

import (
	"sync"
	"testing"

	"github.com/stretchr/testify/require"
)

type MockListener struct {
	HandleFunc func(data interface{}) error
}

func (m *MockListener) Handle(data interface{}) error {
	return m.HandleFunc(data)
}

func TestEmitCallsHandleOnListeners(t *testing.T) {
	broadcaster := NewEventManager()
	var called bool

	wg := sync.WaitGroup{}
	listener := &MockListener{
		HandleFunc: func(data interface{}) error {
			defer wg.Done()
			called = true
			return nil
		},
	}
	const signal = "testEvent"
	broadcaster.Register(signal, listener)
	testcases := []struct {
		name       string
		emitEvent  string
		wantCalled bool
	}{
		{
			name:       "matched event, THEN called",
			emitEvent:  signal,
			wantCalled: true,
		},
		{
			name:       "unmatched event, THEN not called",
			emitEvent:  "unmatchedEvent",
			wantCalled: false,
		},
	}
	r := require.New(t)
	for _, tc := range testcases {
		t.Run(tc.name, func(t *testing.T) {
			called = false
			if tc.wantCalled {
				wg.Add(1)
			}
			broadcaster.Emit(tc.emitEvent, nil)

			wg.Wait()
			r.Equal(tc.wantCalled, called)
		})
	}
}
