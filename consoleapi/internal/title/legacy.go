//go:generate mockgen -source legacy.go -destination legacy_mock.go -package title
package title

import (
	"github.com/KKTV/kktv-api-v3/kkapp/kksearch"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
)

type legacyHelper interface {
	RefreshCache(titleID string) error
}

type legacy struct {

}

func (l *legacy) RefreshCache(titleID string) error {
	dbmeta.Title2Redis(titleID)
	err := kksearch.SyncTitles([]string{titleID})
	return err
}
