package user

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"github.com/KKTV/kktv-api-v3/consoleapi/internal/errs"
	"github.com/KKTV/kktv-api-v3/consoleapi/internal/pkg/model/dbuser"
	"github.com/KKTV/kktv-api-v3/consoleapi/internal/rest"
	"github.com/KKTV/kktv-api-v3/kkapp/amplitude"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/auditing"
	"github.com/KKTV/kktv-api-v3/pkg/log"
	usermodel "github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/KKTV/kktv-api-v3/pkg/render"
	"github.com/KKTV/kktv-api-v3/pkg/validator"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/go-zoo/bone"
	"gopkg.in/guregu/null.v3"
)

type Handler struct {
	repo    Repository
	logRepo auditing.LogRepository
}

func NewHandler(repo Repository, logRepo auditing.LogRepository) *Handler {
	return &Handler{
		repo:    repo,
		logRepo: logRepo,
	}
}

func (h *Handler) Get(w http.ResponseWriter, r *http.Request) {
	userID := bone.GetValue(r, "id")
	user, err := h.repo.GetDetailByID(userID)
	if err != nil {
		log.Error("ConsoleUserHandler: repo get user by id fail").Str("user_id", userID).Err(err).Send()
		render.JSONInternalServerErr(w, rest.Error(errs.InternalError.Code, errs.InternalError.Message))
		return
	}
	if user == nil {
		render.JSONNotFound(w, rest.Error(errs.UserNotFound.Code, errs.UserNotFound.Message))
		return
	}
	info := new(Info)
	info.CopyFromModel(user)
	render.JSONOk(w, rest.Ok(info))
}

type searchReq struct {
	Target string `json:"target" validate:"required,oneof=user_id email phone mod_id"`
	Email  string `json:"email" validate:"omitempty,required_if=Target email,email"`
	Phone  string `json:"phone" validate:"required_if=Target phone"`
	ModID  string `json:"mod_id" validate:"required_if=Target mod_id"`
	UserID string `json:"user_id" validate:"required_if=Target user_id"`
}

// Deprecated: LegacySearch is deprecated, use Search instead
func (h *Handler) LegacySearch(w http.ResponseWriter, r *http.Request) {
	var mayUserID []string
	var mayEmail []string
	var mayPhone []string
	var mayModID []string

	q := r.URL.Query().Get("q")
	targets := strings.Split(q, " ")

	for _, word := range targets {
		if strings.Contains(word, "@") {
			mayEmail = append(mayEmail, strings.ToLower(strings.TrimSpace(word)))
		} else if _, err := strconv.Atoi(word); err == nil {
			// number
			mayPhone = append(mayPhone, "%"+strings.TrimSpace(word)+"%")
			mayModID = append(mayModID, strings.TrimSpace(word))
		} else {
			mayUserID = append(mayUserID, strings.TrimSpace(word))
		}
	}

	log.Debug("search for").
		Strs("user_id", mayUserID).Strs("email", mayEmail).Strs("phone", mayPhone).Strs("kkid_identifier", targets).Send()

	data := make(map[string]interface{})
	var users []*dbuser.UserInfo
	var err error
	if len(mayEmail) == 0 && len(mayPhone) == 0 {
		users, err = h.repo.ListByID(mayUserID)
	} else if len(mayEmail) > 0 || len(mayPhone) > 0 || len(mayUserID) > 0 {
		users, err = h.repo.Search(mayUserID, mayEmail, mayPhone, mayModID, targets)
	}
	if err != nil {
		log.Error("user repo search fail").Err(err).Send()
	}

	var result []*Info
	for _, user := range users {
		u := new(Info)
		u.CopyFromModel(user)
		result = append(result, u)
	}

	data["users"] = result

	response := rest.Ok(data)
	render.JSONOk(w, response)
}

func (h *Handler) Search(w http.ResponseWriter, r *http.Request) {
	var req searchReq
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		render.JSONBadRequest(w, rest.Error(errs.InvalidParameter.Code, errs.InvalidParameter.Message))
		return
	}
	if err := validator.Validate(req); err != nil {
		render.JSONBadRequest(w, rest.Error(errs.InvalidParameter.Code, err.Error()))
		return
	}

	var internalErr error
	defer func() {
		if internalErr != nil {
			log.Error("ConsoleUserHandler: Search fail").Err(internalErr).
				Interface("query", req).Send()
			render.JSONInternalServerErr(w, errs.InternalError)
		}
	}()

	records := make([]*usermodel.User, 0)
	switch req.Target {
	case "user_id":
		u, err := h.repo.GetByID(req.UserID)
		if err != nil {
			internalErr = fmt.Errorf("repo get by id: %w", err)
			return
		} else if u != nil {
			records = append(records, u)
		}

	case "email":
		val := strings.ToLower(req.Email)
		users, err := h.repo.ListByEmail(val)
		if err != nil {
			internalErr = fmt.Errorf("repo get by email: %w", err)
			return
		} else if len(users) > 0 {
			records = append(records, users...)
		}
		users, err = h.repo.ListByPaymentEmail(val)
		if err != nil {
			internalErr = fmt.Errorf("repo get by payment email: %w", err)
			return
		} else if len(users) > 0 {
			records = slice.UnionBy(func(u *usermodel.User) string {
				return u.ID
			}, records, users)
		}

	case "phone":
		phone, err := validator.NormalizeTaiwanMobileNum(req.Phone)
		if err != nil {
			render.JSONBadRequest(w, rest.Error(errs.InvalidParameter.Code, "invalid phone number"))
			return
		}
		users, err := h.repo.ListByPhone(phone)
		if err != nil {
			internalErr = fmt.Errorf("repo get by phone: %w", err)
			return
		} else if len(users) > 0 {
			records = append(records, users...)
		}

	case "mod_id":
		users, err := h.repo.ListByModID(req.ModID)
		if err != nil {
			internalErr = fmt.Errorf("repo get by mod_id: %w", err)
			return
		} else if len(users) > 0 {
			records = append(records, users...)
		}

	default:
		render.JSONBadRequest(w, rest.Error(errs.InvalidParameter.Code, "invalid search field"))
		return
	}

	var result []*ListedUser
	for _, user := range records {
		u := new(ListedUser)
		u.CopyFromModel(user)
		result = append(result, u)
	}
	data := map[string]any{
		"items": result,
	}
	render.JSONOk(w, rest.Ok(data))
}

func (h *Handler) Put(w http.ResponseWriter, r *http.Request) {
	var err error
	var userID string
	var req *ModifyUser
	userID = bone.GetValue(r, "id")

	if userID == "" {
		render.JSONBadRequest(w, rest.Error(errs.InvalidParameter.Code, errs.InvalidParameter.Message))
		return
	}

	jsDecoder := json.NewDecoder(r.Body)
	err = jsDecoder.Decode(&req)
	if err != nil {
		log.Error("ConsoleUserHandler: Put: json decode fail").Err(err).Send()
		render.JSONInternalServerErr(w, rest.Error(errs.InternalError.Code, errs.InternalError.Message))
		return
	}

	req.ID = userID
	user, err := h.repo.GetByID(userID)
	if err != nil {
		log.Error("ConsoleUserHandler: Put: repo get user by id fail").Str("user_id", userID).Err(err).Send()
		render.JSONInternalServerErr(w, rest.Error(errs.InternalError.Code, errs.InternalError.Message))
		return
	}
	originUser := new(ModifyUser)
	originUser.CopyFromModel(user)

	if user == nil {
		render.JSONNotFound(w, rest.Error(errs.UserNotFound.Code, errs.UserNotFound.Message))
		return
	}

	err = h.repo.Update(req)
	if err != nil {
		log.Error("ConsoleUserHandler: Put: repo update user fail").Str("user_id", userID).Err(err).Send()
		render.JSONInternalServerErr(w, rest.Error(errs.InternalError.Code, errs.InternalError.Message))
		return
	}
	logDiffs, err := auditing.GetDiffFields(originUser, req)
	if err != nil {
		log.Warn("ConsoleUserHandler: Put: get diff fields failed").
			Interface("origin", originUser).Interface("new", req).
			Err(err).Send()
	}

	auditBuilder := auditing.NewLogBuilder().ByConsole().
		TargetUpdated("user", user.ID).
		DetailDiff(logDiffs...).
		Note(req.Reason)

	auditLogs := auditBuilder.Build()
	if err := h.logRepo.Insert(auditLogs...); err != nil {
		log.Error("ConsoleUserHandler: Put: insert audit log failed").Interface("logs", auditLogs).Err(err).Send()
		render.JSONInternalServerErr(w, rest.Error(errs.InternalError.Code, errs.InternalError.Message))
		return
	}

	ms := []string{}
	prevMS := []string{}
	for _, m := range req.Membership {
		ms = append(ms, string(m.Role))
	}
	for _, u := range originUser.Membership {
		prevMS = append(prevMS, string(u.Role))
	}
	event, _ := amplitude.NewAccountTypeChangedV2(userID, ms, prevMS, req.Reason)
	go event.Send()

	logMessage := fmt.Sprintf("NewMembership:%v, OldMembership:%v, ExpiredAt:%s, UserID:%s", ms, prevMS, req.ExpiredAt, userID)
	info := new(dbmeta.AuditLogInfo)
	info.Before = originUser
	info.After = req
	info.Reason = req.Reason
	dbmeta.ConsoleLogWithInfo(r, "update_user", logMessage, info)

	render.JSONOk(w, nil)
}
func (h *Handler) PutPaymentInfo(w http.ResponseWriter, r *http.Request) {
	var (
		req    *ModifyPaymentInfo
		err    error
		userID string
	)
	userID = bone.GetValue(r, "id")

	if userID == "" {
		render.JSONBadRequest(w, rest.Error(errs.InvalidParameter.Code, errs.InvalidParameter.Message))
		return
	}

	jsDecoder := json.NewDecoder(r.Body)
	err = jsDecoder.Decode(&req)
	if err != nil {
		log.Error("ConsoleUserHandler: PutPaymentInfo: json decode fail").Err(err).Send()
		render.JSONInternalServerErr(w, rest.Error(errs.InternalError.Code, errs.InternalError.Message))
		return
	}
	paymentInfoModel, err := h.repo.GetPaymentInfoByUserID(userID)
	if err != nil {
		log.Error("ConsoleUserHandler: PutPaymentInfo: json decode fail").Err(err).Send()
		render.JSONInternalServerErr(w, rest.Error(errs.InternalError.Code, errs.InternalError.Message))
		return
	}
	originPaymentInfo := ModifyPaymentInfo{
		UserID:           paymentInfoModel.UserID,
		RecipientAddress: paymentInfoModel.RecipientAddress,
		CarrierType:      paymentInfoModel.CarrierType,
		CarrierValue:     paymentInfoModel.CarrierValue,
		CaringCode:       paymentInfoModel.CaringCode,
	}
	if req.CarrierType.Valid {
		req.CaringCode = null.NewString("", false)
	}
	req.UserID = userID
	err = h.repo.UpdatePaymentInfo(req)
	if err != nil {
		log.Error("ConsoleUserHandler: PutPaymentInfo: update payment fail").Err(err).Send()
		render.JSONInternalServerErr(w, rest.Error(errs.InternalError.Code, errs.InternalError.Message))
		return
	}

	logDiffs, err := auditing.GetDiffFields(originPaymentInfo, *req)
	if err != nil {
		log.Warn("ConsoleUserHandler: PutPaymentInfo: get diff fields failed").
			Interface("origin", originPaymentInfo).Interface("new", req).
			Err(err).Send()
	}

	auditBuilder := auditing.NewLogBuilder().ByConsole().
		TargetUpdated("user", req.UserID).
		DetailDiff(logDiffs...).
		Note(fmt.Sprintf("console user update: %s", req.Reason))

	auditLogs := auditBuilder.Build()
	if err := h.logRepo.Insert(auditLogs...); err != nil {
		log.Error("ConsoleUserHandler: PutPaymentInfo: insert audit log failed").Interface("logs", auditLogs).Err(err).Send()
		render.JSONInternalServerErr(w, rest.Error(errs.InternalError.Code, errs.InternalError.Message))
		return
	}
	info := new(dbmeta.AuditLogInfo)
	info.Before = originPaymentInfo
	info.After = req
	info.Reason = req.Reason
	dbmeta.ConsoleLogWithInfo(r, "update_payment_info", "", info)

	render.JSONOk(w, nil)
}

func (h *Handler) GetMemberRoles(w http.ResponseWriter, r *http.Request) {
	var data []EnumItem
	roles := GetMemberRoleList()
	for _, role := range roles {
		data = append(data, EnumItem{Key: role.String(), Value: role.String()})
	}
	data = append(data, EnumItem{Key: usermodel.RoleGuest.String(), Value: usermodel.RoleGuest.String()})
	response := rest.Ok(data)
	render.JSONOk(w, response)
}
