//go:generate mockgen -source repository.go -destination repository_mock.go -package metatitlelist
package titlelist

import (
	"github.com/KKTV/kktv-api-v3/kkapp/datastore"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	"github.com/KKTV/kktv-api-v3/pkg/database"
)

type Repository interface {
	Create(titlelist *dbmeta.TitleList) error
	Update(titlelist *dbmeta.TitleList) error
}

type repository struct {
	dbReader database.DB
	dbWriter database.DB
}

func NewRepository(dbPoolMeta *datastore.DBPool) Repository {
	return &repository{
		dbReader: dbPoolMeta.Slave().Unsafe(),
		dbWriter: dbPoolMeta.Master().Unsafe(),
	}
}

func (r *repository) Create(titlelist *dbmeta.TitleList) error {
	dbFields := database.GetDBFields(titlelist, "id", "title_name", "title_type")
	sql := `INSERT INTO meta_titlelist (` + dbFields.String() + `) VALUES (` + dbFields.NamedString() + `) RETURNING id;`
	stmt, err := r.dbWriter.PrepareNamed(sql)
	var ID int64
	if err != nil {
		return err
	}
	err = stmt.Get(&ID, titlelist)
	if err != nil {
		return err
	}
	titlelist.ID = ID
	return nil
}

func (r *repository) Update(titlelist *dbmeta.TitleList) error {
	sql := `UPDATE meta_titlelist SET
			caption=:caption, summary=:summary, enabled=:enabled, "order"=:order,
			title_id=:title_id, visible_since=:visible_since, visible_until=:visible_until, topic=:topic, uri=:uri, url=:url, list_type=:list_type,
			trailer_autoplay_enabled=:trailer_autoplay_enabled, trailer_episode_id=:trailer_episode_id, dominant_color=:dominant_color, meta=:meta WHERE id=:id;`

	_, err := r.dbWriter.NamedExec(sql, titlelist)
	if err != nil {
		return err
	}
	return nil
}
