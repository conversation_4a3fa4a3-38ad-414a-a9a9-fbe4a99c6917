package titlelist

import (
	"crypto/sha1"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/KKTV/kktv-api-v3/consoleapi/internal/errs"
	"github.com/KKTV/kktv-api-v3/consoleapi/internal/rest"
	"github.com/KKTV/kktv-api-v3/pkg/clock"
	"github.com/KKTV/kktv-api-v3/pkg/httpreq"
	"github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/render"
	"github.com/KKTV/kktv-api-v3/pkg/validator"
)

type Handler struct {
	repo  Repository
	clock clock.Clock
}

func NewHandler(repo Repository, clock clock.Clock) *Handler {
	return &Handler{
		repo:  repo,
		clock: clock,
	}
}

func (h *Handler) Create(w http.ResponseWriter, r *http.Request) {
	var err error
	var req TitleList

	jsdecoder := json.NewDecoder(r.Body)
	err = jsdecoder.Decode(&req)
	if err != nil {
		render.JSONBadRequest(w, rest.Error(errs.InvalidParameter.Code, "request body parse fail."))
		return
	}

	err = validator.Validate(req)
	if err != nil {
		render.JSONBadRequest(w, rest.Error(errs.InvalidParameter.Code, err.Error()))
		return
	}

	if req.DominantColor.Valid {
		req.DominantColor.String = "#" + req.DominantColor.String
	}

	if req.Meta.ShareId == "" {
		req.Meta.ShareId = h.generateShareId()
	}

	model, err := req.ToModel()
	if err != nil {
		render.JSONInternalServerErr(w, rest.Error(errs.InternalError.Code, err.Error()))
		return
	}
	err = h.repo.Create(model)
	if err != nil {
		render.JSONInternalServerErr(w, rest.Error(errs.InternalError.Code, err.Error()))
		return
	}
	req.ID = model.ID
	render.JSONOk(w, rest.Ok(req))
}

func (h *Handler) Update(w http.ResponseWriter, r *http.Request) {
	var err error
	var req TitleList

	if err := httpreq.PayloadBinding(&req, r); err != nil {
		render.JSONBadRequest(w, rest.Error(errs.InvalidParameter.Code, err.Error()))
		return
	}

	if req.DominantColor.Valid {
		req.DominantColor.String = "#" + req.DominantColor.String
	}

	if req.Meta.ShareId == "" {
		req.Meta.ShareId = h.generateShareId()
	}

	model, err := req.ToModel()
	if err != nil {
		log.Error("ConsoleTitlelistHandler: Update: request convert to model fail").Err(err).Interface("req", req).Send()
		render.JSONInternalServerErr(w, rest.Error(errs.InternalError.Code, errs.InternalError.Message))
		return
	}
	err = h.repo.Update(model)
	if err != nil {
		log.Error("ConsoleTitlelistHandler: Update: repo.Update fail").Err(err).Interface("model", model).Send()
		render.JSONInternalServerErr(w, rest.Error(errs.InternalError.Code, errs.InternalError.Message))
		return
	}
	render.JSONOk(w, rest.Ok(req))
}

func (h *Handler) generateShareId() string {
	hashStr := h.clock.Now().String()
	hasher := sha1.New()
	hasher.Write([]byte(hashStr))

	return fmt.Sprintf("%x", hasher.Sum(nil))[0:10]
}
