package productpackage

import (
	"github.com/KKTV/kktv-api-v3/consoleapi/internal/pkg/urls"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"gopkg.in/guregu/null.v3"
)

type packageLayout struct {
	ID              int64  `json:"id"`
	Title           string `json:"title"`
	Subtitle        string `json:"subtitle"`
	TextColor       string `json:"text_color"`
	Description     string `json:"description"`
	Image           string `json:"image"`
	BackgroundColor string `json:"background_color"`
	BackgroundImage string `json:"background_image"`
	DisplayMoreBtn  bool   `json:"display_more_btn"`
	CTAText         string `json:"cta_text"`
	Terms           string `json:"terms"`
}

type packageViewModel struct {
	ID                int                      `json:"id"`
	Platform          string                   `json:"platform"`
	Price             string                   `json:"price"`
	Duration          string                   `json:"duration"`
	Title             string                   `json:"title"`
	Description       string                   `json:"description"`
	ButtonText        string                   `json:"button_text"`
	Label             *string                  `json:"label"`
	CreatedAt         int64                    `json:"created_at"`
	UpdatedAt         *int64                   `json:"updated_at"`
	ProductIDs        []int                    `json:"product_ids"`
	BillingProductIDs []string                 `json:"billing_product_ids"`
	Active            bool                     `json:"active"`
	Highlight         string                   `json:"highlight"`
	AutoRenew         bool                     `json:"auto_renew"`
	Sort              int                      `json:"sort"`
	Promotion         *string                  `json:"promotion"`
	Targets           []packageTargetViewModel `json:"targets"`
	Category          []string                 `json:"category"`
	PayDuration       *string                  `json:"pay_duration"`
	PackageLayout     *packageLayout           `json:"package_layout"`
}

type packageTargetViewModel struct {
	Condition packageTargetConditionViewModel `json:"condition"`
	Display   packageTargetDisplayViewModel   `json:"display"`
}

type packageTargetConditionViewModel struct {
	Identities     []string                              `json:"identities"`
	LatestPackages *packageTargetLatestPackagesViewModel `json:"latest_packages,omitempty"`
}

type packageTargetLatestPackagesViewModel struct {
	IDs      []int64 `json:"ids"`
	Operator string  `json:"operator"`
}

type packageTargetDisplayViewModel struct {
	Title           string      `json:"title"`
	Price           float64     `json:"price"`
	Duration        string      `json:"duration"`
	Highlight       string      `json:"highlight"`
	ButtonText      string      `json:"button_text"`
	Description     string      `json:"description"`
	OriginPriceDesc string      `json:"origin_price_desc"`
	Label           null.String `json:"label"`
}

func (v *packageViewModel) ConvertFromModel(m dbuser.ProductPackage) {
	v.ID = m.ID
	v.Platform = m.Platform
	v.Price = m.Price
	v.Duration = m.Duration
	v.Title = m.Title
	v.Description = m.Description
	v.ButtonText = m.ButtonText
	v.Label = m.Label.Ptr()
	v.CreatedAt = m.CreatedAt.Unix()
	if m.UpdatedAt.Valid {
		u := m.UpdatedAt.ValueOrZero().Unix()
		v.UpdatedAt = &u
	}

	v.ProductIDs = m.ProductIDs
	if m.BillingProductIds != nil {
		for _, strID := range *m.BillingProductIds {
			v.BillingProductIDs = append(v.BillingProductIDs, strID)
		}
	} else {
		v.BillingProductIDs = []string{}
	}
	v.Active = m.Active
	v.Highlight = m.Highlight
	v.AutoRenew = m.AutoRenew
	v.Sort = m.Sort
	v.Promotion = m.Promotion.Ptr()
	v.Targets = []packageTargetViewModel{}

	if m.Targets != nil && len(*m.Targets) > 0 {
		for _, target := range *m.Targets {
			viewTarget := packageTargetViewModel{
				Condition: packageTargetConditionViewModel{
					Identities: target.Condition.Identities,
				},
				Display: packageTargetDisplayViewModel{
					Title:           target.Display.Title,
					Price:           target.Display.Price,
					Duration:        target.Display.Duration,
					Highlight:       target.Display.Highlight,
					ButtonText:      target.Display.ButtonText,
					Description:     target.Display.Description,
					OriginPriceDesc: target.Display.OriginPriceDesc,
					Label:           target.Display.Label,
				},
			}

			if target.Condition.LatestPackages != nil {
				viewTarget.Condition.LatestPackages = &packageTargetLatestPackagesViewModel{
					IDs:      target.Condition.LatestPackages.IDs,
					Operator: string(target.Condition.LatestPackages.Operator),
				}
			}

			v.Targets = append(v.Targets, viewTarget)
		}
	}

	v.Category = m.Category
	v.PayDuration = m.PayDuration.Ptr()

	if m.Layout != nil && m.Layout.ID.Valid {
		v.PackageLayout = &packageLayout{
			ID:              m.Layout.ID.Int64,
			Title:           m.Layout.Title.ValueOrZero(),
			Subtitle:        m.Layout.Subtitle.ValueOrZero(),
			TextColor:       m.Layout.TextColor.ValueOrZero(),
			Description:     m.Layout.Description.ValueOrZero(),
			BackgroundColor: m.Layout.BackgroundColor.ValueOrZero(),
			DisplayMoreBtn:  m.Layout.DisplayMoreBtn.ValueOrZero(),
			CTAText:         m.Layout.CTAText.ValueOrZero(),
			Terms:           m.Layout.Terms.ValueOrZero(),
		}
		if m.Layout.Image.Valid {
			v.PackageLayout.Image = urls.Image(m.Layout.Image.String)
		}
		if m.Layout.BackgroundImage.Valid {
			v.PackageLayout.BackgroundImage = urls.Image(m.Layout.BackgroundImage.String)
		}
	} else {
		v.PackageLayout = nil
	}
}
