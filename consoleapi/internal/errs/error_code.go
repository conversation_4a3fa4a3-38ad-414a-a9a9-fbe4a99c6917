package errs

import (
	"github.com/KKTV/kktv-api-v3/consoleapi/internal/rest"
)

var (
	InvalidParameter = rest.Err{Code: "400.000", Message: "invalid parameter"}

	ResourceNotFound       = rest.Err{Code: "404.000", Message: "resource not found"}
	UserNotFound           = rest.Err{Code: "404.001", Message: "user not found"}
	ProductPackageNotFound = rest.Err{Code: "404.002", Message: "product package not found"}

	InternalError    = rest.Err{Code: "500.000", Message: "internal error"}
	InternalErrorLLM = rest.Err{Code: "500.001", Message: "llm service error"}
)
