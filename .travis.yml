git:
  submodules: false

dist: focal
language: go
os: linux

env:
  global:
    - PATH=$PATH:/tmp/
    - PROJECT_NAME="$(echo $TRAVIS_REPO_SLUG | cut -d'/' -f2)"
    - PACKAGE_NAME="$PROJECT_NAME-$TRAVIS_COMMIT"
    - AWS_REGION=ap-northeast-1
    - REDISMETA=localhost:6379
    - PGPORT=5432
    - PGUSER=postgres
    - PGHOST=localhost

before_install:
  - sed -i 's/**************:/https:\/\/github.com\//' .gitmodules
  - git submodule update --init --recursive
  - git -C apitest pull origin main # update to latest

jobs:
  include:
    - stage: deploy
      language: go
      go:
        - "1.23.x"

      services:
        - redis-server
        - docker

      addons:
        postgresql: "13"
        apt:
          update: true
          packages:
            - postgresql-13

      cache:
        directories:
          - $HOME/.cache/go-build
          - $HOME/gopath/pkg/mod

      before_install:
        # Use trust instead of peer authentication: https://gist.github.com/mustafa-travisci/529d77d29c07e2b37f0f19042ea1ef38
        - sudo sed -i -e '/local.*peer/s/postgres/all/' -e 's/peer\|md5/trust/g' /etc/postgresql/*/main/pg_hba.conf
        - sudo service postgresql restart
        - sleep 1
        - postgres --version
        # install the golang-migrate tool to prepare db schema for integration test
        - go install -tags 'postgres' github.com/golang-migrate/migrate/v4/cmd/migrate@latest
        - nvm install lts/carbon
        - nvm use lts/carbon
        - make setup
        - curl -Lo /tmp/apex "https://kktv-packages.s3-ap-northeast-1.amazonaws.com/apex/apex"
        - chmod 755 /tmp/apex
        - sudo apt-get -y install jq awscli

      install:
        - make install
        - cd console && npm install && cd ..

      before_script:
        - |
          if [[ "$TRAVIS_BRANCH" = "master" || "$TRAVIS_BRANCH" = "release/*" || -n "$TRAVIS_TAG" ]]; then
            make download-geolite2
          else
            make download-geolite2-from-s3
          fi

      script:
        - |
          if [[ "$TRAVIS_BRANCH" = "master" || -n "$TRAVIS_TAG" ]]; then
            make prod-build-console
          elif [[ (-n $MANUAL_DEPLOY_TEST_BRANCH && "$TRAVIS_BRANCH" = "$MANUAL_DEPLOY_TEST_BRANCH") || "$TRAVIS_BRANCH" = release/*  ]]; then
            make test-build-console
          fi
        - make fast-test
        - make integration-test

      before_deploy:
        - >-
          if [[ "$TRAVIS_BRANCH" = "master" || -n "$TRAVIS_TAG" ]]; then
            export ALIAS=prod
          else
            export ALIAS=test
          fi
        - echo "start db schema migrate on $ALIAS"
        - if ! [ -x "$(command -v aws)" ]; then curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip" ; unzip awscliv2.zip ; sudo ./aws/install ; fi
        - make db-migrate-$ALIAS DB=dbmeta
        - make db-migrate-$ALIAS DB=dbuser
        - make db-migrate-$ALIAS DB=dbredeem

      deploy:
        - provider: script
          skip_cleanup: true
          script: make test-deploy
          on:
            branch: release/test
        - provider: script
          skip_cleanup: true
          script: make prod-deploy
          on:
            tags: true
        - provider: script
          skip_cleanup: true
          script: make test-deploy
          on:
            all_branches: true
            condition: -n $MANUAL_DEPLOY_TEST_BRANCH && "$TRAVIS_BRANCH" = "$MANUAL_DEPLOY_TEST_BRANCH"

      after_deploy:
        - >-
          if [[ -n "$TRAVIS_TAG" ]]; then
            AMPLITUDE_AUTH_TOKEN=$AMPLITUDE_AUTH_TOKEN \
            GITHUB_TOKEN=$GITHUB_TOKEN \
            RELEASE_TAG=$TRAVIS_TAG make amplitude-release
          fi

    - stage: "Post-deploy regression tests"
      if: tag IS present

      language: node_js
      node_js:
        - "18.16.0"
      cache: yarn

      install:
        - cd apitest
        - yarn --frozen-lockfile

      script:
        - yarn test


notifications:
  slack:
    - rooms:
        - kkculture:0qUPIrQZyO80KCU4UEr9OS3v#kktv-ci-cd
      on_failure: change
      on_success: never
      on_pull_requests: false
      template:
        - "<!subteam^S043UHBG5UK>"
        - "Build <%{build_url}|#%{build_number}> (<%{compare_url}|%{commit}>) of %{repository_slug}@%{branch} by %{author} %{result} in %{duration}"
        - "Message: %{message}"
        - "Commit message: %{commit_message}"
      if: branch = master || branch = release/test || tag IS present
    - rooms:
        - kkculture:0qUPIrQZyO80KCU4UEr9OS3v#kktv-ci-cd
      on_success: always
      on_failure: never
