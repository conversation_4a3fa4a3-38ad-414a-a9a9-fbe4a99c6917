{"name": "kktv-api-v3", "description": "KKTV V3 API", "role": "arn:aws:iam::************:role/kktv-prod-lambda-vpc-basic", "vpc": {"securityGroups": ["sg-08334e6c"], "subnets": ["subnet-d6b951a0", "subnet-1e010447"]}, "environment": {"ENV": "prod", "SSO_ALLOWED_DOMAIN": "kkculture.com", "API_DOC_SSO_ALLOWED_ACCOUNTS": "", "REDISMETA": "prod-meta.r3iujk.ng.0001.apne1.cache.amazonaws.com:6379,prod-meta-ro.r3iujk.ng.0001.apne1.cache.amazonaws.com:6379", "REDISUSER": "prod-ud.r3iujk.ng.0001.apne1.cache.amazonaws.com:6379,prod-ud-ro.r3iujk.ng.0001.apne1.cache.amazonaws.com:6379", "REDISRS": "prod-meta.r3iujk.ng.0001.apne1.cache.amazonaws.com:6379,prod-meta-ro.r3iujk.ng.0001.apne1.cache.amazonaws.com:6379", "REDISPLAYBACK": "prod-pb.r3iujk.ng.0001.apne1.cache.amazonaws.com:6379,prod-pb-ro.r3iujk.ng.0001.apne1.cache.amazonaws.com:6379", "DBUSER": "postgres://martian:<EMAIL>:5432/kktv_users,postgres://martian:<EMAIL>:5432/kktv_users", "DBMETA": "postgres://editor:<EMAIL>:5432/meta", "DBREDEEM": "postgres://sinner:<EMAIL>:5432/kktv_redeem", "SEARCHHOST": "http://search-kktv-prod-elasticsearch-qfcah6vl35rg77mkwwfzlucwsi.ap-northeast-1.es.amazonaws.com", "BILLINGHOST": "https://bapi.kktv.me", "NEWEBPAY_PAYMENT_URL": "https://core.newebpay.com/MPG/mpg_gateway", "NEXMO_API_KEY": "********", "NEXMO_API_SECRET": "3lJGuzEZDvzxN2Ci", "NEXMO_PHONE_NUMBER": "KKTV", "MAILGUN_DOMAIN": "kktv.me", "MAILGUN_API_KEY": "************************************", "CLIENT_WEB_HOST": "https://www.kktv.me", "JWT_AUTH_SIGNED_STRING": "cffyrt3VQWPAUujwU5xvldKq9Wu6X7TKxMaHXVDnexwoXcZGOtg3sUAsTzAMfXCT", "LICENSE_URL_WIDEVINE": "https://license.kktv.com.tw", "LICENSE_URL_PLAYREADY": "https://license.kktv.com.tw", "LICENSE_URL_FAIRPLAY": "https://license.kktv.com.tw", "KKS_BV_LICENSE_URL": "https://drm.platform.blendvision.com/api/v3/drm/license", "KKS_BV_TENANT_ID": "4ab7a051-475d-4498-8db6-9020772487ba", "KKS_BV_TENANT_ID_FOR_LINETV": "00037e2b-b3fd-4d44-a3cb-4f227a518142", "KKBOX_APP_ID": "KKTV", "KKBOX_APP_SECRET": "3cabd6aaad5921baa15b24aaa8e2c3d75191891b", "KKBOX_BASE_URL": "https://api-member.kkbox.com.tw", "THEATER_CDN_SIGN_KEY": "6aWbx7JfdDDcRwa1", "EZPAY_API_HOST": "https://inv.ezpay.com.tw", "EZPAY_MERCHANT_ID": "38586812", "EZPAY_HASH_KEY": "cMZ0CU4LLcFTM0U2WDhN4R8UUNJnwGKI", "EZPAY_HASH_IV": "tKI5ENRqZwkMxePu", "MOD_API_KEY": "Qnl7ORVK3t2hzkneQYnsd1oOslFZwSDH2Rf0Adpd", "MOD_CLIENT_USERNAME": "kktv", "MOD_CLIENT_PASSWORD": "0igQS3FP+DiqNc&OYeqVY1w_GIRaDwHoP5ZRaDnGE^dH%Qpefl", "MOD_PROXY_URL": "http://**************/v2", "SLACK_TOKEN": "*********************************************************", "GENAI_GEMINI_API_KEY": "AIzaSyBamHZu2G7m9TIn2GclAQujG7L2CYxZC3I", "AWS_S3_BUCKET_KKTV_API": "kktv-prod-api", "API_DOC_OAUTH_CALLBACK": "https://api.kktv.com.tw/auth/doc/callback?provider=google", "GOOGLE_APP_ID": "**********-rec4u6f2j1t6pv6m690cqlfhc2q2jur2.apps.googleusercontent.com", "GOOGLE_APP_SECRET": "wUGfIt3oLdj1YPYWLmrV7LWW"}, "timeout": 30, "memory": 1600}