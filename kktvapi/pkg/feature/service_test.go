package feature

import (
	"testing"

	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/key"
	"github.com/KKTV/kktv-api-v3/pkg/model/cachemeta"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"
)

func TestRefreshAppConfig(t *testing.T) {
	r := require.New(t)
	ctrl := gomock.NewController(t)
	mockCacheReader := cache.NewMockCacher(ctrl)

	testcases := []struct {
		name       string
		given      func()
		thenAssert func(error)
	}{
		{
			name: "read app config successfully",
			given: func() {
				config := cachemeta.AppConfiguration{
					EnablingMembership: &cachemeta.EnablingMembership{
						Condition: cachemeta.TriggerCondition[[]string]{
							TriggerType: "ALL",
						},
					},
				}
				mockCacheReader.EXPECT().HGet(
					key.MetaGetServiceGeneralConfig(),
					key.MetaServiceGeneralConfigHashKeys.AppConfig,
					gomock.Any()).
					SetArg(2, config).Return(nil)
			},
			thenAssert: func(err error) {
				r.NoError(err)
				r.NotNil(appConfig)
			},
		},
	}
	for _, tc := range testcases {
		t.Run(tc.name, func(t *testing.T) {
			appConfig = &configuration{}
			tc.given()
			err := RefreshAppConfig(mockCacheReader)
			tc.thenAssert(err)
		})
	}
}
