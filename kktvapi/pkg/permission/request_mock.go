// Code generated by MockGen. DO NOT EDIT.
// Source: request.go

// Package permission is a generated GoMock package.
package permission

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockRequest is a mock of Request interface.
type MockRequest struct {
	ctrl     *gomock.Controller
	recorder *MockRequestMockRecorder
}

// MockRequestMockRecorder is the mock recorder for MockRequest.
type MockRequestMockRecorder struct {
	mock *MockRequest
}

// NewMockRequest creates a new mock instance.
func NewMockRequest(ctrl *gomock.Controller) *MockRequest {
	mock := &MockRequest{ctrl: ctrl}
	mock.recorder = &MockRequestMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRequest) EXPECT() *MockRequestMockRecorder {
	return m.recorder
}

// delegate mocks base method.
func (m *MockRequest) delegate(permissionService *service) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "delegate", permissionService)
	ret0, _ := ret[0].(error)
	return ret0
}

// delegate indicates an expected call of delegate.
func (mr *MockRequestMockRecorder) delegate(permissionService interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "delegate", reflect.TypeOf((*MockRequest)(nil).delegate), permissionService)
}
