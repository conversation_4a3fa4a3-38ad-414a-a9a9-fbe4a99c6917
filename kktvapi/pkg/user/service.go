package user

import (
	"sync"
	"time"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/user"
	"github.com/KKTV/kktv-api-v3/pkg/billing"
	"github.com/KKTV/kktv-api-v3/pkg/datatype"
)

var (
	srv     Service
	onceSrv sync.Once
)

type Service interface {
	GetUserServiceStatus(userID string) (status UserServiceStatus, err error)
}

type service struct {
	orderRepo     user.OrderRepository
	billingClient billing.Client
}

func NewService(orderRepo user.OrderRepository, billingClient billing.Client) Service {
	return &service{
		orderRepo:     orderRepo,
		billingClient: billingClient,
	}
}

type UserServiceStatus struct {
	MembershipRole   string            `json:"membership_role"`
	ExpiredAt        datatype.DateTime `json:"expired_at"`
	NextPaymentAt    datatype.DateTime `json:"next_payment_at"`
	IsInSubscription bool              `json:"is_in_subscription"`
}

func (s *service) GetUserServiceStatus(userID string) (status UserServiceStatus, err error) {
	// legacyLatestOrder, err := h.orderRepo.GetLastFulfilledByUserId(userID)
	// if err != nil {
	// 	log.Println("error", err)
	// }
	// log.Println("legacyLatestOrder", legacyLatestOrder)

	// cs, err := h.billingClient.GetCustomerStatus("61ebf3ca-fe66-4324-941b-1ad8d5660d61")
	// if err != nil {
	// 	log.Println("error", err)
	// }
	// zlog.Info("customerStatus.contractData").Interface("contractData", cs.ContractData).Send()
	// zlog.Info("customerStatus.ordersData").Interface("ordersData", cs.OrdersData).Send()

	// Set the date to 2025/07/30
	targetDate := time.Date(2025, 7, 30, 0, 0, 0, 0, time.Local)

	return UserServiceStatus{
		MembershipRole:   "premium",
		ExpiredAt:        datatype.DateTime(targetDate),
		NextPaymentAt:    datatype.DateTime(targetDate),
		IsInSubscription: true,
	}, nil
}
