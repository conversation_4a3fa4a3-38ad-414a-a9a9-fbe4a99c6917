package serials

import (
	"errors"
	"github.com/KKTV/kktv-api-v3/pkg/rand"
	"testing"
	"time"

	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/clock"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
)

type GeneratorTestSuite struct {
	suite.Suite
	gen Generator

	mockClock       *clock.MockClock
	mockCacheReader *cache.MockCacher
	mockCacheWriter *cache.MockCacher
	mockRand        *rand.MockRand

	ctrl *gomock.Controller
}

func TestGeneratorTestSuite(t *testing.T) {
	suite.Run(t, new(GeneratorTestSuite))
}

func (suite *GeneratorTestSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
	suite.mockClock = clock.NewMockClock(suite.ctrl)
	suite.mockCacheReader = cache.NewMockCacher(suite.ctrl)
	suite.mockCacheWriter = cache.NewMockCacher(suite.ctrl)
	suite.mockRand = rand.NewMockRand(suite.ctrl)

	suite.gen = &generator{
		clock:       suite.mockClock,
		cacheReader: suite.mockCacheReader,
		cacheWriter: suite.mockCacheWriter,
		rand:        suite.mockRand,
	}
}

func (suite *GeneratorTestSuite) TearDownTest() {
	suite.ctrl.Finish()
}

func (suite *GeneratorTestSuite) TestOrderIDs() {

	now := time.Date(2020, 4, 29, 0, 0, 0, 0, time.UTC)
	suite.mockClock.EXPECT().Now().Return(now).AnyTimes()

	testcases := []struct {
		name            string
		num             int
		paymentType     string
		paymentTypeCode string
		given           func()
		thenAssert      func(ids []string)
	}{
		{
			name:            "get serial from redis",
			num:             3,
			paymentType:     "credit_card",
			paymentTypeCode: "00",
			given: func() {
				suite.mockCacheReader.EXPECT().Exists(gomock.Any()).Return(true, nil)
				suite.mockCacheWriter.EXPECT().IncrBy(gomock.Any(), int64(3)).
					Return(int64(15), nil)
			},
			thenAssert: func(ids []string) {
				suite.Equal([]string{"KT0020200429000013", "KT0020200429000014", "KT0020200429000015"}, ids)
			},
		},
		{
			name:            "get serial by random",
			num:             2,
			paymentType:     "cvs_code",
			paymentTypeCode: "03",
			given: func() {
				suite.mockCacheReader.EXPECT().Exists(gomock.Any()).Return(false, errors.New("connection error"))
				suite.mockRand.EXPECT().RandomNumber(orderIDSerialLen).Return("000013", nil)
				suite.mockRand.EXPECT().RandomNumber(orderIDSerialLen).Return("000014", nil)
			},
			thenAssert: func(ids []string) {
				suite.Equal([]string{"KT0320200429000013", "KT0320200429000014"}, ids)
			},
		},
	}
	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			tc.given()
			ids := suite.gen.OrderIDs(tc.num, tc.paymentType, tc.paymentTypeCode)
			tc.thenAssert(ids)
		})
	}
}
