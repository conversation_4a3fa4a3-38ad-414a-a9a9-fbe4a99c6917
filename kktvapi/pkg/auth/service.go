package auth

import (
	crand "crypto/rand"
	"encoding/base64"
	"strings"

	"github.com/KKTV/kktv-api-v3/pkg/clock"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/KKTV/kktv-api-v3/pkg/rand"
	"gopkg.in/guregu/null.v3"
)

type Service interface {
	NewAuthedApp(name string, redirectURIs []string) *dbuser.AuthedApp
}

type service struct {
	rand  rand.Rand
	clock clock.Clock
}

func NewService() Service {
	return &service{
		rand:  rand.New(),
		clock: clock.New(),
	}
}

const (
	appIDLength   = 20
	secretLength  = 32 // 32 bytes -> 44 chars base64
	signKeyLength = 47 // 47 bytes -> 64 chars base64
)

func (s *service) NewAuthedApp(name string, redirectURIs []string) *dbuser.AuthedApp {
	appID := strings.ToUpper(s.rand.RandomString(appIDLength))

	secretBytes := generateEncodedBytes(secretLength)
	appSecret := base64.StdEncoding.EncodeToString(secretBytes)

	signKeyBytes := generateEncodedBytes(signKeyLength)
	signKey := base64.StdEncoding.EncodeToString(signKeyBytes)

	return &dbuser.AuthedApp{
		Name:         name,
		Status:       dbuser.AuthedAppStatusActive,
		AppID:        appID,
		AppSecret:    appSecret,
		SignKey:      null.StringFrom(signKey),
		RedirectURIs: redirectURIs,
		CreatedAt:    s.clock.Now(),
		UpdatedAt:    s.clock.Now(),
	}
}

func generateEncodedBytes(size int) []byte {
	bytes := make([]byte, size)
	crand.Read(bytes)

	// In base64 encoding, every 3 bytes are encoded as 4 characters
	// To ensure a '+' character in the output, we need to set specific byte patterns
	// 0xFB at position 0 of a 3-byte group encodes to '+'
	// 0xEF at position 1 of a 3-byte group encodes to '+'
	// 0xBF at position 2 of a 3-byte group encodes to '+'

	// Choose a position that's not at the beginning but ensures a '+' character
	// We'll use position 3 (the start of the second 3-byte group)
	// This ensures we have a '+' character but not at the beginning
	plusPosition := 3
	if plusPosition < size {
		bytes[plusPosition] = 0xFB // Will encode to '+' at position 0 of a 3-byte group
	}

	return bytes
}
