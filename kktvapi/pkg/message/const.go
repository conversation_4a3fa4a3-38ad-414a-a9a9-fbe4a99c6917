package message

import (
	"fmt"
	"mime"
)

const (
	Charset = "UTF-8"
	SenderName = "KKTV會員中心"
	SenderMail = "<EMAIL>"
	SenderSMS  = "KKTV"
)

var (
	// sender contains chinese characters must encoded using MIME encoded-word syntax
	// reference: https://docs.aws.amazon.com/ses/latest/APIReference/API_SendEmail.html
	mailSender = fmt.Sprintf("%s <%s>", mime.BEncoding.Encode(Charset, SenderName), SenderMail)
)
