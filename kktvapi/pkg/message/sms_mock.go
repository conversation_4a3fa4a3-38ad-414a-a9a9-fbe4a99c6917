// Code generated by MockGen. DO NOT EDIT.
// Source: sms.go

// Package message is a generated GoMock package.
package message

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockSMSer is a mock of SMSer interface.
type MockSMSer struct {
	ctrl     *gomock.Controller
	recorder *MockSMSerMockRecorder
}

// MockSMSerMockRecorder is the mock recorder for MockSMSer.
type MockSMSerMockRecorder struct {
	mock *MockSMSer
}

// NewMockSMSer creates a new mock instance.
func NewMockSMSer(ctrl *gomock.Controller) *MockSMSer {
	mock := &MockSMSer{ctrl: ctrl}
	mock.recorder = &MockSMSerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSMSer) EXPECT() *MockSMSerMockRecorder {
	return m.recorder
}

// SendMessage mocks base method.
func (m *MockSMSer) SendMessage(recipient, content string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendMessage", recipient, content)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendMessage indicates an expected call of SendMessage.
func (mr *MockSMSerMockRecorder) SendMessage(recipient, content interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendMessage", reflect.TypeOf((*MockSMSer)(nil).SendMessage), recipient, content)
}
