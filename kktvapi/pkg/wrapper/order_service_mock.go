// Code generated by MockGen. DO NOT EDIT.
// Source: order_service.go

// Package wrapper is a generated GoMock package.
package wrapper

import (
	reflect "reflect"

	database "github.com/KKTV/kktv-api-v3/pkg/database"
	dbuser "github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	gomock "github.com/golang/mock/gomock"
)

// MockOrderService is a mock of OrderService interface.
type MockOrderService struct {
	ctrl     *gomock.Controller
	recorder *MockOrderServiceMockRecorder
}

// MockOrderServiceMockRecorder is the mock recorder for MockOrderService.
type MockOrderServiceMockRecorder struct {
	mock *MockOrderService
}

// NewMockOrderService creates a new mock instance.
func NewMockOrderService(ctrl *gomock.Controller) *MockOrderService {
	mock := &MockOrderService{ctrl: ctrl}
	mock.recorder = &MockOrderServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockOrderService) EXPECT() *MockOrderServiceMockRecorder {
	return m.recorder
}

// GetLastFulfilledByUserId mocks base method.
func (m *MockOrderService) GetLastFulfilledByUserId(userId string) (*dbuser.Order, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLastFulfilledByUserId", userId)
	ret0, _ := ret[0].(*dbuser.Order)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLastFulfilledByUserId indicates an expected call of GetLastFulfilledByUserId.
func (mr *MockOrderServiceMockRecorder) GetLastFulfilledByUserId(userId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLastFulfilledByUserId", reflect.TypeOf((*MockOrderService)(nil).GetLastFulfilledByUserId), userId)
}

// Insert mocks base method.
func (m *MockOrderService) Insert(order *dbuser.Order) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Insert", order)
	ret0, _ := ret[0].(error)
	return ret0
}

// Insert indicates an expected call of Insert.
func (mr *MockOrderServiceMockRecorder) Insert(order interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Insert", reflect.TypeOf((*MockOrderService)(nil).Insert), order)
}

// WithTx mocks base method.
func (m *MockOrderService) WithTx(tx database.Tx) OrderService {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithTx", tx)
	ret0, _ := ret[0].(OrderService)
	return ret0
}

// WithTx indicates an expected call of WithTx.
func (mr *MockOrderServiceMockRecorder) WithTx(tx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithTx", reflect.TypeOf((*MockOrderService)(nil).WithTx), tx)
}
