// Code generated by MockGen. DO NOT EDIT.
// Source: package_billing_order_service.go

// Package wrapper is a generated GoMock package.
package wrapper

import (
	reflect "reflect"

	dbuser "github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	gomock "github.com/golang/mock/gomock"
)

// MockPackageBillingOrderService is a mock of PackageBillingOrderService interface.
type MockPackageBillingOrderService struct {
	ctrl     *gomock.Controller
	recorder *MockPackageBillingOrderServiceMockRecorder
}

// MockPackageBillingOrderServiceMockRecorder is the mock recorder for MockPackageBillingOrderService.
type MockPackageBillingOrderServiceMockRecorder struct {
	mock *MockPackageBillingOrderService
}

// NewMockPackageBillingOrderService creates a new mock instance.
func NewMockPackageBillingOrderService(ctrl *gomock.Controller) *MockPackageBillingOrderService {
	mock := &MockPackageBillingOrderService{ctrl: ctrl}
	mock.recorder = &MockPackageBillingOrderServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPackageBillingOrderService) EXPECT() *MockPackageBillingOrderServiceMockRecorder {
	return m.recorder
}

// GetByBillingOrderNumber mocks base method.
func (m *MockPackageBillingOrderService) GetByBillingOrderNumber(billingOrderNumber string) (*dbuser.PackageBillingOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByBillingOrderNumber", billingOrderNumber)
	ret0, _ := ret[0].(*dbuser.PackageBillingOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByBillingOrderNumber indicates an expected call of GetByBillingOrderNumber.
func (mr *MockPackageBillingOrderServiceMockRecorder) GetByBillingOrderNumber(billingOrderNumber interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByBillingOrderNumber", reflect.TypeOf((*MockPackageBillingOrderService)(nil).GetByBillingOrderNumber), billingOrderNumber)
}
