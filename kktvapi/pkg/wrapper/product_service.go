//go:generate mockgen -source product_service.go -destination product_service_mock.go -package wrapper
package wrapper

import (
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/product"
	"github.com/KKTV/kktv-api-v3/pkg/database"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
)

type ProductService interface {
	GetByName(name string) (*dbuser.Product, error)
	GetByMODExternalProductID(externalProductID string) (*dbuser.Product, error)
}

type productService struct {
	productRepo product.Repository
}

func NewProductService(dbReader, dbWriter database.DB) ProductService {
	return &productService{
		productRepo: product.NewRepositoryWith(dbReader, dbWriter),
	}
}

func (p *productService) GetByName(name string) (*dbuser.Product, error) {
	return p.productRepo.GetByName(name)
}

func (p *productService) GetByMODExternalProductID(externalProductID string) (*dbuser.Product, error) {
	return p.productRepo.GetByMODExternalProductID(externalProductID)
}
