//go:generate mockgen -source payment_info_service.go -destination payment_info_service_mock.go -package wrapper
package wrapper

import (
	"database/sql"
	"errors"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/user"
	"github.com/KKTV/kktv-api-v3/pkg/database"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
)

type PaymentInfoService interface {
	GetByUserID(uid string) (*dbuser.PaymentInfo, error)
	GetByMODSubscriberID(modSubscriberID string) (*dbuser.PaymentInfo, error)
	Upsert(paymentInfo *dbuser.PaymentInfo) (err error)
	WithTx(tx database.Tx) PaymentInfoService
}

type paymentInfoService struct {
	paymentInfoRepository user.PaymentInfoRepository
}

func NewPaymentInfoService(db database.DB) PaymentInfoService {
	return &paymentInfoService{
		paymentInfoRepository: user.NewPaymentInfoRepositoryWith(db),
	}
}

func (s *paymentInfoService) GetByUserID(uid string) (*dbuser.PaymentInfo, error) {
	pi, err := s.paymentInfoRepository.GetPaymentInfoByUserID(uid) //TODO refactor: repo should not return sql.ErrNoRows
	if errors.Is(err, sql.ErrNoRows) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}
	return pi, nil
}

func (s *paymentInfoService) GetByMODSubscriberID(modSubscriberID string) (*dbuser.PaymentInfo, error) {
	return s.paymentInfoRepository.GetByMODSubscriberID(modSubscriberID)
}

func (s *paymentInfoService) Upsert(paymentInfo *dbuser.PaymentInfo) (err error) {
	return s.paymentInfoRepository.Upsert(paymentInfo)
}

func (s *paymentInfoService) WithTx(tx database.Tx) PaymentInfoService {
	return &paymentInfoService{
		paymentInfoRepository: s.paymentInfoRepository.WithTx(tx),
	}
}
