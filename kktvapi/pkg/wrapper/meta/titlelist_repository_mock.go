// Code generated by MockGen. DO NOT EDIT.
// Source: titlelist_repository.go

// Package meta is a generated GoMock package.
package meta

import (
	reflect "reflect"
	time "time"

	dbmeta "github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	gomock "github.com/golang/mock/gomock"
)

// MockTitlelistRepository is a mock of TitlelistRepository interface.
type MockTitlelistRepository struct {
	ctrl     *gomock.Controller
	recorder *MockTitlelistRepositoryMockRecorder
}

// MockTitlelistRepositoryMockRecorder is the mock recorder for MockTitlelistRepository.
type MockTitlelistRepositoryMockRecorder struct {
	mock *MockTitlelistRepository
}

// NewMockTitlelistRepository creates a new mock instance.
func NewMockTitlelistRepository(ctrl *gomock.Controller) *MockTitlelistRepository {
	mock := &MockTitlelistRepository{ctrl: ctrl}
	mock.recorder = &MockTitlelistRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTitlelistRepository) EXPECT() *MockTitlelistRepositoryMockRecorder {
	return m.recorder
}

// ListOnlyTitlesList mocks base method.
func (m *MockTitlelistRepository) ListOnlyTitlesList(displayTime time.Time) ([]*dbmeta.TitleList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListOnlyTitlesList", displayTime)
	ret0, _ := ret[0].([]*dbmeta.TitleList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListOnlyTitlesList indicates an expected call of ListOnlyTitlesList.
func (mr *MockTitlelistRepositoryMockRecorder) ListOnlyTitlesList(displayTime interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListOnlyTitlesList", reflect.TypeOf((*MockTitlelistRepository)(nil).ListOnlyTitlesList), displayTime)
}
