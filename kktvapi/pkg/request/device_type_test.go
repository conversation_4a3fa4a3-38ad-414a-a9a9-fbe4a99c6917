package request

import (
	"net/http"
	"testing"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/platform"
	"github.com/stretchr/testify/assert"
)

func TestGetDeviceTypeFromUserAgent(t *testing.T) {
	testcases := []struct {
		name    string
		assert  func(platform.DeviceType)
		request func() *http.Request
	}{
		{
			name: "Given user agent is iOS, then assert success",
			request: func() *http.Request {
				req, _ := http.NewRequest("GET", "https://google.com", nil)
				req.Header.Set("User-Agent", "MyApp/1.1.1233 (iOS; iPhone XS; Version 13.3 (Build 17C45)) CFNetwork/1121.2.1 Darvin/19.3.0")
				return req
			},
			assert: func(dt platform.DeviceType) {
				assert.Equal(t, platform.DeviceTypeMobileApp, dt)
			},
		},
	}

	for _, tc := range testcases {
		r := tc.request()
		platform := GetDeviceTypeFromUserAgent(r)
		tc.assert(platform)
	}
}
