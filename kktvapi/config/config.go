package config

import (
	"context"
	"log"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/caarlos0/env/v7"
	"github.com/joho/godotenv"
)

var (
	Env                      string
	Debug                    bool
	Port                     int
	MMDB                     string
	RedisMeta                string
	RedisUser                string
	RedisRS                  string
	RedisPlayback            string
	DbUser                   string
	DbMeta                   string
	DbRedeem                 string
	SearchHost               string
	BillingAPIHost           string
	NewebpayPaymentURL       string
	********                 string
	NexmoSecret              string
	NexmoPhoneNumber         string
	MailgunDomain            string
	MailgunAPIKey            string
	ClientWebHost            string
	JWTAuthSignedString      string
	LicenseUrlPlayready      string
	LicenseUrlWidevine       string
	LicenseUrlFairplay       string
	KKSBVLicenseUrl          string
	KKSBVTenantID            string
	KKSBVTenantIDForLineTV   string
	KKBOXMemAppID            string
	KKBOXMemAppSecret        string
	KKBOXMemBaseURL          string
	TheaterCDNSignKey        string
	EzpayApiHost             string
	EzpayMerchantID          string
	EzpayHashKey             string
	EzpayHashIV              string
	MODAPIKey                string
	MODClientUsername        string
	MODClientPassword        string
	MODProxyUrl              string
	GenAIGeminiAPIKey        string
	AWSConfig                aws.Config
	AwsS3BucketKKTVApi       string
	SSOAllowedDomain         []string
	APIDocSSOAllowedAccounts []string
	APIDocOAuthCallback      string
	GoogleAppID              string
	GoogleAppSecret          string
)

type Config struct {
	Env                      string   `env:"ENV,********"`
	Debug                    bool     `env:"DEBUG" envDefault:"false"`
	Port                     int      `env:"PORT,********" envDefault:"3000"`
	MMDB                     string   `env:"MMDB,********" envDefault:".GeoLite2-Country.mmdb"`
	RedisMeta                string   `env:"REDISMETA,********"`
	RedisUser                string   `env:"REDISUSER,********"`
	RedisRS                  string   `env:"REDISRS,********"`
	RedisPlayback            string   `env:"REDISPLAYBACK,********"`
	DbUser                   string   `env:"DBUSER,********"`
	DbMeta                   string   `env:"DBMETA,********"`
	DbRedeem                 string   `env:"DBREDEEM,********"`
	SearchHost               string   `env:"SEARCHHOST,********"`
	BillingAPIHost           string   `env:"BILLINGHOST,********"`
	NewebpayPaymentURL       string   `env:"NEWEBPAY_PAYMENT_URL,********"`
	********                 string   `env:"NEXMO_API_KEY,********"`
	NexmoSecret              string   `env:"NEXMO_API_SECRET,********"`
	NexmoPhoneNumber         string   `env:"NEXMO_PHONE_NUMBER,********"`
	MailgunDomain            string   `env:"MAILGUN_DOMAIN,********"`
	MailgunAPIKey            string   `env:"MAILGUN_API_KEY,********"`
	ClientWebHost            string   `env:"CLIENT_WEB_HOST,********"`
	JWTAuthSignedString      string   `env:"JWT_AUTH_SIGNED_STRING,********"`
	LicenseUrlPlayready      string   `env:"LICENSE_URL_PLAYREADY,********"`
	LicenseUrlWidevine       string   `env:"LICENSE_URL_WIDEVINE,********"`
	LicenseUrlFairplay       string   `env:"LICENSE_URL_FAIRPLAY,********"`
	KKSBVLicenseUrl          string   `env:"KKS_BV_LICENSE_URL,********"`
	KKSBVTenantID            string   `env:"KKS_BV_TENANT_ID,********"`
	KKSBVTenantIDForLineTV   string   `env:"KKS_BV_TENANT_ID_FOR_LINETV,********"`
	KKBOXMemAppID            string   `env:"KKBOX_APP_ID,********"`
	KKBOXMemAppSecret        string   `env:"KKBOX_APP_SECRET,********"`
	KKBOXMemBaseURL          string   `env:"KKBOX_BASE_URL,********"`
	TheaterCDNSignKey        string   `env:"THEATER_CDN_SIGN_KEY,********"`
	EzpayApiHost             string   `env:"EZPAY_API_HOST,********"`
	EzpayMerchantID          string   `env:"EZPAY_MERCHANT_ID,********"`
	EzpayHashKey             string   `env:"EZPAY_HASH_KEY,********"`
	EzpayHashIV              string   `env:"EZPAY_HASH_IV,********"`
	MODAPIKey                string   `env:"MOD_API_KEY,********"`
	MODClientUsername        string   `env:"MOD_CLIENT_USERNAME,********"`
	MODClientPassword        string   `env:"MOD_CLIENT_PASSWORD,********"`
	MODProxyURL              string   `env:"MOD_PROXY_URL,********"`
	GenAIGemini              string   `env:"GENAI_GEMINI_API_KEY,********"`
	AWSDefaultRegion         string   `env:"AWS_DEFAULT_REGION" envDefault:"ap-northeast-1"`
	AwsS3BucketKKTVApi       string   `env:"AWS_S3_BUCKET_KKTV_API"`
	SSOAllowedDomain         []string `env:"SSO_ALLOWED_DOMAIN,********" envDefault:"kkculture.com" envSeparator:","`
	APIDocSSOAllowedAccounts []string `env:"API_DOC_SSO_ALLOWED_ACCOUNTS" envSeparator:","`
	APIDocOAuthCallback      string   `env:"API_DOC_OAUTH_CALLBACK"`
	// https://console.developers.google.com/apis/credentials?project=kktv-backend&organizationId=************
	GoogleAppID     string `env:"GOOGLE_APP_ID,********"`
	GoogleAppSecret string `env:"GOOGLE_APP_SECRET,********"`
}

func Init() error {
	_ = godotenv.Load()

	var cfg Config
	if err := env.Parse(&cfg); err != nil {
		return err
	}

	Env = cfg.Env
	Debug = cfg.Debug
	RedisMeta = cfg.RedisMeta
	RedisUser = cfg.RedisUser
	RedisRS = cfg.RedisRS
	RedisPlayback = cfg.RedisPlayback
	DbUser = cfg.DbUser
	DbMeta = cfg.DbMeta
	DbRedeem = cfg.DbRedeem
	SearchHost = cfg.SearchHost
	BillingAPIHost = cfg.BillingAPIHost
	NewebpayPaymentURL = cfg.NewebpayPaymentURL
	Port = cfg.Port
	MMDB = cfg.MMDB
	******** = cfg.********
	NexmoSecret = cfg.NexmoSecret
	NexmoPhoneNumber = cfg.NexmoPhoneNumber
	MailgunDomain = cfg.MailgunDomain
	MailgunAPIKey = cfg.MailgunAPIKey
	JWTAuthSignedString = cfg.JWTAuthSignedString
	LicenseUrlPlayready = cfg.LicenseUrlPlayready
	LicenseUrlWidevine = cfg.LicenseUrlWidevine
	LicenseUrlFairplay = cfg.LicenseUrlFairplay
	ClientWebHost = cfg.ClientWebHost
	KKSBVLicenseUrl = cfg.KKSBVLicenseUrl
	KKSBVTenantID = cfg.KKSBVTenantID
	KKBOXMemAppID = cfg.KKBOXMemAppID
	KKBOXMemAppSecret = cfg.KKBOXMemAppSecret
	KKBOXMemBaseURL = cfg.KKBOXMemBaseURL
	TheaterCDNSignKey = cfg.TheaterCDNSignKey
	EzpayApiHost = cfg.EzpayApiHost
	EzpayMerchantID = cfg.EzpayMerchantID
	EzpayHashKey = cfg.EzpayHashKey
	EzpayHashIV = cfg.EzpayHashIV
	MODAPIKey = cfg.MODAPIKey
	MODClientUsername = cfg.MODClientUsername
	MODClientPassword = cfg.MODClientPassword
	MODProxyUrl = cfg.MODProxyURL
	GenAIGeminiAPIKey = cfg.GenAIGemini
	AwsS3BucketKKTVApi = cfg.AwsS3BucketKKTVApi
	SSOAllowedDomain = cfg.SSOAllowedDomain
	APIDocOAuthCallback = cfg.APIDocOAuthCallback
	APIDocSSOAllowedAccounts = cfg.APIDocSSOAllowedAccounts
	GoogleAppID = cfg.GoogleAppID
	GoogleAppSecret = cfg.GoogleAppSecret
	KKSBVTenantIDForLineTV = cfg.KKSBVTenantIDForLineTV

	// 新增 AWS Config 初始化
	var err error
	AWSConfig, err = config.LoadDefaultConfig(context.TODO(), config.WithRegion(cfg.AWSDefaultRegion))
	if err != nil {
		log.Fatalf("failed to load AWS config: %v", err)
	}

	return nil
}
