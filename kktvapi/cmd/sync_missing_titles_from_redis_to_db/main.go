// [Usage]
// 1. Execute the command as shown below
// `$ GOOS=linux GOARCH=386 go build -o sync_missing_titles_from_redis_to_db kktvapi/cmd/sync_missing_titles_from_redis_to_db/main.go `
// 2. Copy file by SSH command
// `$ scp sync_missing_titles_from_redis_to_db {jumper-machine}:{your-jumper-machine-folder}`
// Finally, execute this file by `./sync_missing_titles_from_redis_to_db`

package main

import (
	"bufio"
	"fmt"
	"os"
	"strconv"
	"sync"
	"time"

	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/key"
	plog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/jmoiron/sqlx"
	"github.com/lib/pq"
)

// 設定批次大小和工作執行緒數量
const batchSize = 10000
const workerCount = 5

// 指定需要同步的使用者 ID，如果不設置，將同步所有符合條件的使用者
var user_ids = []string{
	// "002a177c-2828-4520-a253-e55bc15fe4af",
}

func init() {
	// 初始化配置，從 .env 檔案和環境變數中加載
	if err := config.Init(); err != nil {
		plog.Fatal("Failed to initialize config").Err(err).Send()
	}

	// 註冊 User 資料庫和 Redis 快取池
	container.RegisterUserDB(config.DbUser)
	container.RegisterUserCache(config.RedisUser)
	plog.Init(config.Env)
}

func main() {
	startTime := time.Now()
	plog.Info("Execution started").Send()

	currentTime := time.Now().Format("20060102150405")
	gapFileName := fmt.Sprintf("%s_gap.txt", currentTime)

	// 獲取 Redis 和資料庫連接池
	dbreader := container.DBPoolUser().Slave()
	dbwriter := container.DBPoolUser().Master() // 資料庫的寫操作需要使用 Master
	cachePoolUser := container.CachePoolUser()
	userCacheReader := cache.New(cachePoolUser.Slave())

	// 打開或創建 gap.txt 檔案
	gapFile, err := os.OpenFile(gapFileName, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		plog.Fatal(fmt.Sprintf("Failed to open file: %s", gapFileName)).Err(err).Send()
		return
	}
	defer gapFile.Close()
	gapWriter := bufio.NewWriter(gapFile)

	// 第一步：批量查詢資料庫中所有 user_playlists 的 user_id 及其對應的 title_ids
	var dbTitleMap map[string][]string
	if len(user_ids) > 0 {
		// 如果指定了 user_ids，僅處理這些使用者
		dbTitleMap, err = getUserPlaylistsForSpecificUsers(dbreader, user_ids)
		if err != nil {
			plog.Fatal("Failed to fetch user playlists for specific users").Err(err).Send()
			return
		}
	} else {
		// 否則，查詢所有使用者的播放列表
		dbTitleMap, err = getAllUserPlaylistsInBatches(dbreader)
		if err != nil {
			plog.Fatal("Failed to fetch user playlists from database").Err(err).Send()
			return
		}
	}

	// 第二步：比對 Redis 中的資料，找出新增的 title_id 並插入資料庫
	err = processUserPlaylists(dbwriter, userCacheReader, dbTitleMap, gapWriter)
	if err != nil {
		plog.Fatal("Failed during the synchronization process").Err(err).Send()
		return
	}

	// 完成後刷新 gap.txt
	if err := gapWriter.Flush(); err != nil {
		plog.Warn("Failed to flush gap.txt writer").Err(err).Send()
	}

	executionTime := time.Since(startTime)
	plog.Info(fmt.Sprintf("Execution completed in %s", executionTime)).Send()
}

// getAllUserPlaylistsInBatches 批量查詢資料庫中所有 user_playlists 的 user_id 及其對應的 title_ids
func getAllUserPlaylistsInBatches(dbreader *sqlx.DB) (map[string][]string, error) {
	userPlaylists := make(map[string][]string)
	offset := 0
	batchCount := 0

	for {
		batchStartTime := time.Now()

		query := `
            SELECT up.user_id, pt.title_id
            FROM playlist_titles pt
            INNER JOIN user_playlists up ON pt.playlist_id = up.id
            ORDER BY up.user_id
            LIMIT $1 OFFSET $2
        `
		rows, err := dbreader.Queryx(query, batchSize, offset)
		if err != nil {
			return nil, err
		}
		defer rows.Close()

		batchProcessed := 0
		for rows.Next() {
			var userID, titleID string
			if err := rows.Scan(&userID, &titleID); err != nil {
				return nil, err
			}
			userPlaylists[userID] = append(userPlaylists[userID], titleID)
			batchProcessed++
		}

		batchCount++
		batchDuration := time.Since(batchStartTime)
		plog.Info(fmt.Sprintf("Processed batch %d with %d records, duration: %s", batchCount, batchProcessed, batchDuration)).Send()

		// 如果本批次處理的記錄數小於批次大小，說明已經處理完所有資料
		if batchProcessed < batchSize {
			break
		}

		// 增加 offset，繼續下一批查詢
		offset += batchSize
	}

	return userPlaylists, nil
}

// processUserPlaylists 處理資料庫和 Redis 中的 user_playlists 資料，並將新增的 title_id 插入資料庫
func processUserPlaylists(dbwriter *sqlx.DB, cacheReader cache.Cacher, dbTitleMap map[string][]string, gapWriter *bufio.Writer) error {
	var wg sync.WaitGroup
	userChan := make(chan string, len(dbTitleMap))
	resultChan := make(chan error, len(dbTitleMap))

	// 啟動 workerCount 個 Goroutine 處理資料
	for i := 0; i < workerCount; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for userID := range userChan {
				redisTitleScores, err := getRedisTitleScores(cacheReader, key.UserFavoriteTitles(userID))
				if err != nil {
					plog.Warn(fmt.Sprintf("Failed to get Redis title count for userID: %s", userID)).Err(err).Send()
					resultChan <- err
					continue
				}

				// 獲取資料庫中已存在的 title_id
				dbTitles := dbTitleMap[userID]

				// 找出在 Redis 中存在但在 DB 中不存在的 title_id
				missingTitleScores := findMissingTitleScores(redisTitleScores, dbTitles)
				if len(missingTitleScores) > 0 {
					plog.Warn(fmt.Sprintf("Found missing titles for userID: %s, Redis titles not in DB: %v", userID, missingTitleScores)).Send()
					_, err := gapWriter.WriteString(fmt.Sprintf("userID: %s, Missing titles: %v\n", userID, missingTitleScores))
					if err != nil {
						plog.Warn("Failed to write to gap.txt").Err(err).Send()
						resultChan <- err
						continue
					}

					// 批量插入缺失的 title_id 和對應的 score 作為 created_at
					err = insertBatchTitlesIntoDB(dbwriter, userID, missingTitleScores)
					if err != nil {
						plog.Warn(fmt.Sprintf("Failed to insert missing titles for userID: %s", userID)).Err(err).Send()
						resultChan <- err
						continue
					}
					plog.Info(fmt.Sprintf("Successfully inserted missing titles for userID: %s", userID)).Send()
				}
				resultChan <- nil
			}
		}()
	}

	// 將 userID 發送到 channel 中
	for userID := range dbTitleMap {
		userChan <- userID
	}
	close(userChan)

	// 等待所有工作執行緒完成
	wg.Wait()
	close(resultChan)

	// 檢查 resultChan 中的錯誤
	for err := range resultChan {
		if err != nil {
			return err
		}
	}

	return nil
}

// getRedisTitleScores 從 Redis 中獲取 title_id 和對應的 score
func getRedisTitleScores(cacheReader cache.Cacher, key string) ([][2]string, error) {
	data, err := cacheReader.ZRevRangeWithScores(key, 0, -1)
	if err != nil {
		if err == cache.ErrCacheMiss {
			return nil, nil
		}
		return nil, err
	}
	var titleScores [][2]string
	for _, item := range data {
		titleScores = append(titleScores, [2]string{item[0], item[1]}) // item[0] 是 title_id, item[1] 是 score
	}
	return titleScores, nil
}

// insertBatchTitlesIntoDB 批量插入缺失的 title_id 並使用 score 作為 created_at
func insertBatchTitlesIntoDB(dbwriter *sqlx.DB, userID string, titleScores [][2]string) error {
	query := `
        INSERT INTO playlist_titles (playlist_id, title_id, created_at)
        SELECT up.id, unnest($2::varchar[]), unnest($3::timestamptz[])
        FROM user_playlists up
        WHERE up.user_id = $1
        ON CONFLICT (playlist_id, title_id) DO NOTHING
    `
	titleIDs := make([]string, len(titleScores))
	createdAts := make([]time.Time, len(titleScores))
	for i, ts := range titleScores {
		titleIDs[i] = ts[0]
		// 將 score 轉換為 time.Time
		scoreFloat, err := strconv.ParseFloat(ts[1], 64)
		if err != nil {
			return fmt.Errorf("failed to parse score %s: %w", ts[1], err)
		}
		createdAts[i] = time.Unix(int64(scoreFloat), 0).UTC()
	}

	_, err := dbwriter.Exec(query, userID, pq.Array(titleIDs), pq.Array(createdAts))
	return err
}

// findMissingTitleScores 找出 Redis 中存在但 DB 不存在的 title_id
func findMissingTitleScores(redisTitleScores [][2]string, dbTitleIDs []string) [][2]string {
	dbTitleMap := make(map[string]struct{})
	for _, id := range dbTitleIDs {
		dbTitleMap[id] = struct{}{}
	}

	var missingTitleScores [][2]string
	for _, ts := range redisTitleScores {
		if _, exists := dbTitleMap[ts[0]]; !exists {
			missingTitleScores = append(missingTitleScores, ts)
		}
	}
	return missingTitleScores
}

func getUserPlaylistsForSpecificUsers(dbreader *sqlx.DB, userIDs []string) (map[string][]string, error) {
	userPlaylists := make(map[string][]string)

	query := `
        SELECT up.user_id, pt.title_id
        FROM playlist_titles pt
        INNER JOIN user_playlists up ON pt.playlist_id = up.id
        WHERE up.user_id = ANY($1)
        ORDER BY up.user_id
    `
	rows, err := dbreader.Queryx(query, pq.Array(userIDs))
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	for rows.Next() {
		var userID, titleID string
		if err := rows.Scan(&userID, &titleID); err != nil {
			return nil, err
		}
		userPlaylists[userID] = append(userPlaylists[userID], titleID)
	}

	return userPlaylists, nil
}
