// [Usage]
// 1. Execute the command as shown below
// `$ GOOS=linux GOARCH=386 go build -o lower_user_email kktvapi/cmd/lower_user_email/main.go `
// 2. Copy file by SSH command
// `$ scp lower_user_email {jumper-machine}:{your-jumper-machine-folder}`
// Finally, execute this file by `./lower_user_email`
package main

import (
	"strings"

	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/user"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/auditing"
	"github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/jinzhu/copier"
	"gopkg.in/guregu/null.v3"
)

func init() {
	// 這邊需要把config.DbUser改成db string，因為跑的時候吃不到 .env
	// kkboxmember/client.go 的config也可以更改
	container.RegisterUserDB(config.DbUser)
	log.Init(config.Env)
}

func main() {
	var (
		users []*dbuser.User
	)
	dbreader := container.DBPoolUser().Slave().Unsafe()
	dbwriter := container.DBPoolUser().Master()
	userRepo := user.NewUserRepository()
	logRepo := auditing.NewRepository(dbwriter)

	allUpperEmailUserSql := `select id, email from users
	where email ~ '[A-Z]' and revoked_at is null;`

	// step1. 撈出 email 有大寫的所有會員
	if err := dbreader.Select(&users, allUpperEmailUserSql); err != nil {
		log.Error("lowerUserEmail: get all members").Err(err).Send()
		return
	}
	// step2. lower them
	for _, user := range users {
		var originUser dbuser.User
		_ = copier.Copy(&originUser, user)
		newEmail := null.NewString(strings.ToLower(user.Email.String), true)

		fields := map[dbuser.UsersField]interface{}{
			dbuser.UsersFieldEmail: newEmail,
		}

		if affected, err := userRepo.UpdateByFields(user.ID, fields); err != nil {
			log.Warn("lowerUserEmail: update by fields").
				Err(err).
				Str("user_id", user.ID).
				Interface("fields", fields).
				Send()
		} else if affected {
			user.Email = newEmail

			logDiffs, err := auditing.GetDiffFields(&originUser, user)
			if err != nil {
				log.Warn("lowerUserEmail: get diff fields failed").
					Interface("origin", originUser).Interface("new", user).
					Err(err).Send()
			}

			auditBuilder := auditing.NewLogBuilder().BySystem().
				TargetUpdated("user", user.ID).
				DetailDiff(logDiffs...).
				Note("2024/06/07 Discuss: Lower all email because login sql has performance issue.")

			auditLogs := auditBuilder.Build()
			if err := logRepo.Insert(auditLogs...); err != nil {
				log.Warn("lowerUserEmail: insert audit log failed").Interface("logs", auditLogs).Err(err).Send()
			}
		}
	}

}
