openapi: 3.0.1
info:
  title: KKTV API
  description: |-
    KKTV API documentation.

    - API server:
      - prod: https://api.kktv.me
      - test: https://test-api.kktv.me
      - staging: https://staging-api.kktv.me
    - DOC
      - [legacy] https://test-api.kktv.me/v3/d0c/
      - https://test-api.kktv.me/doc/
    - CMS
      - prod: https://api.kktv.com.tw/v3/console/
      - test: https://test-api.kktv.com.tw/v3/console/



    # Request header
    Please bring the **Platform** and **App version** into the request header for tracking purpose

    - **X-KKTV-Platfrom**: available for `Web` | `Android` | `iOS` | `tvOS` | `Android TV` | `MOD`
    - **X-KKTV-App-Version**: the client build version.

    e.g. to bring the **x-kktv-device** into request header for get title for SEO API

    ```bash
    curl --location --request GET 'https://api.kktv.me/v4/seo/titles/06000794' \
    --header 'X-KKTV-App-Version: 3.37.0' \
    --header 'X-KKTV-Platfrom: Android'
    ```
  contact:
    email: <EMAIL>
  license:
    name: KKTV
    url: 'https://www.kktv.me'
  version: '{unknown}'
servers:
  - url: 'https://api.kktv.me'
  - url: 'https://test-api.kktv.me'
tags:
  - name: REMOTE_CONFIG
    description: |
      Remote config for user
  - name: SEO
    description: |
      For Google Search engine optimization

      ## v4 Error codes

      | code | description |
      | ---- | ----------- |
      | 404.1  | title not found |
      | 500.0  | unknown error |
  - name: PAGE
    description: |
      # Page layout

      ## layout style

      The layout style of page content contains following：

      | style id | desc | sample |
      | --- | --- | --- |
      | watch_history | 使用者觀看紀錄 | |
      | headline | headline 片單 | |
      | title_list | 自動或手動片單 | |
      | big_title_list | 寬版面大圖片單(新劇跟播用) | |
      | highlight_title_list | highlight 片單  | |
      | ranking_title_list | 排行榜片單 |  |
      | filter | browseTab page 中可以使用的 filter | |
      | announcement | 公告 | |
      | event | 活動 | |

      ## v4 Error codes

      | code | description |
      | ---- | ----------- |
      | 404.1  | BrowseKey not found |
      | 400.1  | The request browse page is not supported |
      | 500.0  | unknown error |
  - name: COLD_START
    description: |
      cold start to enhance recommendation

      ## v4 Error codes

      | code | description |
      | ---- | ----------- |
      | 403.1  | Forbidden to access because the use has completed cold-start process before |
      | 500.0  | unknown error |
  - name: TITLE
    description: titles
  - name: TITLE_LIST
    description: title list
  - name: FAVORITE
    description: user's favorite
  - name: WATCH_HISTORY
    description: user's watch history
  - name: PLAYBACK
    description: the playback function
  - name: COLLECTION
    description: collection
  - name: PAYMENT
    description: payment
  - name: PRODUCT
    description: product
  - name: AUTH
    description: authorization
  - name: USER
    description: |
      ## v4 Error codes

      | code       | message                                |
      | ---------- | -------------------------------------- |
      | 400.1      | Invalid parameter                      |
      | 400.2      | Invalid account format                 |
      | 400.3      | The Account has already exists         |
      | 400.4      | The Account is unverified              |
      | 400.5      | The OTP is incorrect                   |
      | 400.6      | The OTP has expired                    |
      | 400.7      | Invalid password format                |
      | 400.8      | Password confirmation doesn't match    |
      | 400.9      | The OTP requests too much times        |
      | 400.10     | There are duplicate accounts           |
      | 400.11     | Wrong password                         |
      | 400.12     | User's account is unchangeable         |
      | 400.13     | User has already submitted the survey  |
      | 401.0      | User not login                         |
      | 401.1      | Login failed                           |
      | 500.0      | Unknown error                          |
  - name: TEXT_CONTENT
    description: |
      ## v4 Error codes

      | code  | description    |
      | ----- | -------------- |
      | 401.0 | Invalid Token  |
      | 404.1 | User not found |
      | 500.0 | Unknown error  |
  - name: TRIAL
    description: |
      ## v4 Error codes

      | code | description |
      | ---- | ----------- |
      | 400.0 | Invalid request |
      | 404.1 | Episode not found |
      | 404.2 | Title not found |
      | 403.0 | Permission denied |
      | 500.0 | Unknown error |
  - name: VENDOR-LINETV
    description: |
      ## Error codes
components:
  schemas:
    ErrInfo:
      type: object
      title: v4ErrInfo
      x-examples:
        example-200: null
        example-404:
          code: '404.1'
          message: Title not found
      nullable: true
      properties:
        code:
          type: string
        message:
          type: string
    V3ErrStatus:
      type: object
      nullable: true
      title: v3ErrStatus
      x-examples:
        example-200: null
        example-404:
          code: '404.1'
          message: Title not found
      properties:
        type:
          type: string
        subtype:
          type: string
        info:
          type: string
    DateTime:
      type: string
      title: DateTime
      format: date-time
      description: iso8601 date time format
      example: '2025-06-20T10:07:16Z'
    PaymenType:
      title: PaymenType
      type: string
      enum:
        - credit_card
        - iab
        - iap
        - cvs
        - telecom
        - pxpayplus
  securitySchemes:
    Bearer:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: |
        要存取需要授權的 api ，在 header 'Authorization' 必須有授權的 JWT token

        下面的格式必須在 'Authorization' header :

            Bearer xxxxxx.yyyyyyy.zzzzzz
    BasicAuth:
      type: http
      scheme: basic
      description: |-
        採用標準 Basic Auth 的方式

        將 app_id 和 app_secret 以冒號連結 `{{app_id}}:{{app_secret}}`
        把 1 的結果做 base64 encode
        加上前綴字 Basic （注意：後面空格是必要的）
        放在 header 的 Authorization
  parameters:
    titleID:
      name: titleID
      in: path
      required: true
      schema:
        type: string
      description: ID of title
    browseTabKey:
      name: browseTabKey
      in: path
      required: true
      schema:
        type: string
        example: featured
      description: browse tab key
    sort:
      name: sort
      in: query
      required: false
      schema:
        type: number
        enum:
          - 0
          - 1
          - 2
      description: '`0`: 最新; `1`: 熱門, `2`: 好評'
    collectionTarget:
      name: collectionTarget
      in: path
      required: true
      schema:
        type: string
      description: |
        the support value is following `{collectionType}:{collectionName}` pattern.
        Which collectionType includes:
          - country
          - theme
          - genre
          - tag
          - figure
          - rating
          - tag
          - content_provider
          - content_agent
        e.g. `country:Taiwan`, `content_provider:NHK`, `figure:木村拓哉`
    HeaderDeviceID:
      name: X-Device-ID
      in: header
      required: true
      schema:
        type: string
      description: device id
    orderNumber:
      name: orderNumber
      in: path
      required: true
      schema:
        type: string
      description: order number
    deviceType:
      name: deviceType
      in: path
      schema:
        type: string
        enum:
          - a
          - w
        example: a
      description: 'The device type. `a` for _app_, `w` for _web_'
      required: true
    page:
      name: page
      in: query
      required: false
      schema:
        type: integer
        default: 0
        minimum: 0
      description: start from _0_
    HeaderPlatform:
      name: X-KKTV-Platform
      in: header
      required: true
      schema:
        type: string
        enum:
          - tvOS
          - iPadOS
          - iOS
          - MOD
          - Android TV
          - Android
          - Web
      description: device platform
  responses: {}
security:
  - Bearer: []
paths:
  '/v4/seo/titles/{titleID}':
    get:
      summary: v4/ Get SEO meta for title detail page
      tags:
        - SEO
      operationId: get-v4-seo-titles-titleID
      description: ''
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    $ref: ./schemas/seo-title-meta.yaml
              examples:
                jsonObject:
                  $ref: ./responses/examples/get-seo-titles-title-id-200.yaml#/Example200
    parameters:
      - $ref: '#/components/parameters/titleID'
  '/v4/a/featured_pages/{browseTabKey}':
    get:
      summary: v4/app/ Get featured page content
      tags:
        - PAGE
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    $ref: ./schemas/page-content.yaml
      operationId: app-get-v4-pages
      description: |-
        <p style="color:red">**designed for mobile APP**</p>

        - 取得頁面內容
        - 可以先透過 [Get Browse list API](#get-/v4/browses)取得有哪些 browse tab 可以使用
    parameters:
      - $ref: '#/components/parameters/browseTabKey'
  '/v4/w/featured_pages/{browseTabKey}':
    parameters:
      - $ref: '#/components/parameters/browseTabKey'
    get:
      summary: v4/web/ Get featured page content
      tags:
        - PAGE
      operationId: web-get-v4-pages
      description: |
        <p style="color:red">**designed for WEB client**</p>

        - 取得頁面內容
        - 可以先透過 [Get Browse list API](#get-/v4/browses)取得有哪些 browse tab 可以使用
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    $ref: ./schemas/web/page-content.yaml
  /v4/a/browses:
    get:
      summary: v4/app/ Get available list of browse
      tags:
        - PAGE
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    properties:
                      items:
                        type: array
                        items:
                          $ref: ./schemas/browse-tab.yaml
      operationId: app-get-v4-browses
      description: |
        <p style="color:red">**designed for APP client**</p>

        用來取得現在有幾個 Browse 頁 可供user瀏覽, 如精選, 戲劇, 動畫, 娛樂...
    parameters: []
  /v4/w/browses:
    get:
      summary: v4/web/ Get available list of browse
      tags:
        - PAGE
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    properties:
                      items:
                        type: array
                        items:
                          $ref: ./schemas/browse-tab.yaml
      operationId: web-get-v4-browses
      description: |
        <p style="color:red">**designed for WEB client**</p>

        用來取得現在有幾個 Browse 頁 可供user瀏覽, 如精選, 戲劇, 動畫, 娛樂...

        WEB 可以用使 response 中的 `items[].type` 與 `items[].name` 來輔助組合 url.

        如 type="genre", name="戲劇" 則 url 可以組出 `/genre/戲劇`
    parameters: []
  /v4/cold-start/content-preferences:
    get:
      summary: v4/ Get all cold start preference choices
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    properties:
                      option_groups:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: string
                              description: in which setup steps should be showed to users
                              example: genre
                            instruction:
                              type: string
                              example: 選擇1~3個你喜歡的類別
                              description: The instruction showed to the user
                            options:
                              type: array
                              items:
                                type: object
                                properties:
                                  display_name:
                                    type: string
                                    example: 台劇
                                  key:
                                    type: string
                                    example: 'genre:戲劇+country:Taiwan'
                            description:
                              type: string
                              example: 為你推薦更多豐富的內容
                            max_selections:
                              type: string
                              example: '3'
                              minLength: 0
                          required:
                            - id
                            - options
                            - max_selections
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
      operationId: get-v4-cold-start-preferences
      description: To list all chocies for process the cold start settings
      tags:
        - COLD_START
    parameters: []
  /v4/users/me/content-preferences:
    parameters: []
    post:
      summary: v4/ Update the user's preference to title content
      operationId: post-v4-users-me-content-preferences
      responses:
        '200':
          description: OK
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                option_group:
                  type: array
                  items:
                    type: object
                    properties:
                      id:
                        type: string
                        description: in which setup steps should be showed to users
                        example: genre
                      option_keys:
                        type: array
                        description: The item keys
                        example:
                          - 'genre:戲劇+country:Taiwan'
                          - 'genre:電影'
                        items:
                          type: string
        description: ''
      description: |-
        `WIP`

        Use this API to tell what preference of content, such as category, theme, country of titles that the user prefer to
      tags:
        - COLD_START
  '/v4/a/title-lists/{shareID}':
    parameters:
      - schema:
          type: string
        name: shareID
        in: path
        required: true
        description: the share ID of title list
    get:
      summary: v4/app/ Get TitleList
      tags:
        - TITLE_LIST
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    properties:
                      title_list:
                        $ref: ./schemas/titlelist.yaml
                      fallback:
                        type: object
                        description: only present when error is not null
                        properties:
                          message:
                            type: string
                            example: 喔噢...此片單已經下架
                          redirect_url:
                            type: string
                            example: 'https://www.kktv.me/titleList/ranking'
      operationId: get-v4-a-title-list-shareID
      description: return the titlelist meta and the titles included
  /v4/w/title-lists/airing-anime:
    parameters: []
    get:
      summary: v4/web/ Get Airing Anime TitleList
      tags:
        - TITLE_LIST
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    properties:
                      title_list:
                        $ref: ./schemas/titlelist-airing-anime.yaml
                      fallback:
                        type: object
                        description: only present when error is not null
                        properties:
                          message:
                            type: string
                            example: 沒有跟播中的片單
                          redirect_url:
                            type: string
                            example: 'https://www.kktv.me/titleList/ranking'
      operationId: get-v4-a-title-list-airing-anime
      description: return the titlelist meta and the titles included
  /v4/a/title-lists/youwilllove:
    parameters: []
    get:
      summary: v4/app/ Get Coldstart recommendation titles
      tags:
        - TITLE_LIST
        - COLD_START
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    properties:
                      title_list:
                        $ref: ./schemas/titlelist.yaml
                      fallback:
                        type: object
                        description: only present when error is not null
                        properties:
                          message:
                            type: string
                            example: 喔噢...此片單已經下架
                          redirect_url:
                            type: string
                            example: 'https://www.kktv.me/titleList/ranking'
      operationId: get-v4-a-title-list-youwilllove
      description: Return the recommendation titles according to user's cold-start preference
      parameters:
        - schema:
            type: boolean
          in: query
          name: refresh
          description: to tell if need to change the recommendation result
  '/v3/title_list/{shareID}':
    parameters:
      - schema:
          type: string
        name: shareID
        in: path
        required: true
        description: the share ID of title list
    get:
      summary: v3/ Get TitleList
      tags:
        - TITLE_LIST
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    $ref: '#/components/schemas/V3ErrStatus'
                  data:
                    type: object
                    properties:
                      id:
                        type: string
                      background_image_url:
                        type: string
                      copyright:
                        type: string
                      description:
                        type: string
                      redirect_url:
                        type: string
                      fallback_msg:
                        type: string
                      share_link:
                        type: string
                      name:
                        type: string
                      og_image:
                        type: string
                      items:
                        type: array
                        items:
                          $ref: ./schemas/v3/listed-title.yaml
      operationId: get-v3-a-title-list-shareID
      description: return the titlelist meta and the titles included
  /v3/title_list/youwilllove:
    parameters: []
    get:
      summary: v3/ Get Coldstart recommendation titles
      tags:
        - TITLE_LIST
        - COLD_START
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    $ref: '#/components/schemas/V3ErrStatus'
                  data:
                    type: object
                    properties:
                      id:
                        type: string
                      background_image_url:
                        type: string
                      copyright:
                        type: string
                      description:
                        type: string
                      redirect_url:
                        type: string
                      fallback_msg:
                        type: string
                      share_link:
                        type: string
                      name:
                        type: string
                      og_image:
                        type: string
                      items:
                        type: array
                        items:
                          $ref: ./schemas/v3/listed-title.yaml
      operationId: get-v3-a-title-list-youwilllove
      description: Return the recommendation titles according to user's cold-start preference
  '/v4/{deviceType}/users/me/favorite-titles':
    parameters:
      - $ref: '#/components/parameters/deviceType'
    get:
      summary: v4/ Get current user's favorite titles
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    properties:
                      pagination:
                        $ref: ./schemas/pagination.yaml
                      items:
                        type: array
                        items:
                          allOf:
                            - $ref: ./schemas/listed-title-meta.yaml
                            - $ref: ./schemas/title-basic-meta.yaml
      operationId: get-v4-a-users-me-favorite
      description: ''
      parameters:
        - schema:
            type: string
            enum:
              - license_expired
          in: query
          name: filter_by
          description: '`license_expired`: 授權到期'
      tags:
        - FAVOURITE
  '/v4/{deviceType}/users/me/favorite-explorer':
    parameters:
      - $ref: '#/components/parameters/deviceType'
    get:
      summary: v4/ user 的 favorite playlist
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    $ref: ./schemas/favorite-explorer-resp.yaml
      operationId: get-v4-deviceType-users-me-favorite
      description: ''
      parameters:
        - schema:
            type: string
            enum:
              - license_expired
          in: query
          name: filter_by
          description: '`license_expired`: 授權到期'
      tags:
        - FAVOURITE
  /v4/users/me/favorite-titles:
    delete:
      summary: v4/ delete current user's favorite titles
      operationId: delete-v4-users-me-favorite
      tags:
        - FAVOURITE
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                title_ids:
                  type: array
                  items:
                    type: string
      responses:
        '204':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    nullable: true
              examples:
                example-1:
                  value:
                    error: null
                    data: null
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    nullable: true
              examples:
                example-400.3:
                  value:
                    error:
                      code: '400.3'
                      message: not found title_ids in body
    post:
      summary: v4/ add current user's favorite titles
      operationId: post-v4-users-me-favorite
      tags:
        - FAVOURITE
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                title_ids:
                  type: array
                  items:
                    type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    nullable: true
              examples:
                example-1:
                  value:
                    error: null
                    data: null
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    properties:
                      limit:
                        type: number
                        example: 500
              examples:
                example-400.4:
                  value:
                    error:
                      code: '400.4'
                      message: favorite title over limit
    patch:
      summary: v4/ update current user's favorite titles
      operationId: patch-v4-users-me-favorite
      tags:
        - FAVOURITE
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                title_ids:
                  type: array
                  items:
                    type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    nullable: true
              examples:
                example-1:
                  value:
                    error: null
                    data: null
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    nullable: true
              examples:
                example-400.1:
                  value:
                    error:
                      code: '400.1'
                      message: invalid parameters
  /v4/users/me/favorite-titles/ids:
    parameters: []
    get:
      summary: v4/ Get current user's favorite title ids
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    properties:
                      title_ids:
                        type: array
                        items:
                          type: string
      tags:
        - FAVOURITE
  '/v4/{deviceType}/users/me/watch-history':
    parameters:
      - $ref: '#/components/parameters/deviceType'
    get:
      summary: v4/ Get current user's watch history
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    properties:
                      items:
                        type: array
                        items:
                          type: object
                          properties:
                            title:
                              allOf:
                                - $ref: ./schemas/title-basic-meta.yaml
                                - type: object
                                  properties:
                                    latest_update_info:
                                      type: string
                                      example: 更新至第21集
                            user_authority:
                              $ref: ./schemas/authority.yaml
                            last_played_episode:
                              $ref: ./schemas/last-played-episode.yaml
                            display:
                              type: object
                              properties:
                                title:
                                  type: string
                                  example: IT狗
                                description_short:
                                  type: string
                                  example: 更新至第21集
                                description:
                                  type: string
                                  example: 第2集 / 更新至第21集
                                play_hint:
                                  type: string
                                  example: 第2集
                                played_percentage:
                                  type: number
                                  example: 0.375
      operationId: get-v4-users-me-watch-history
      description: ''
      parameters: []
      tags:
        - WATCH_HISTORY
  /v4/a/collections/expire_soon:
    parameters: []
    get:
      summary: v4/app/ Get Expire soon collections
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    properties:
                      items:
                        type: array
                        items:
                          allOf:
                            - $ref: ./schemas/listed-title-meta.yaml
                            - $ref: ./schemas/title-basic-meta.yaml
                      display_name:
                        type: string
                      collection_id:
                        type: string
                      collection_name:
                        type: string
                      collection_type:
                        type: string
                      pagination:
                        $ref: ./schemas/pagination.yaml
      operationId: get-v4-a-collections-expire-soon
      description: ''
      parameters: []
      tags:
        - COLLECTION
  '/v4/a/collections/{collectionTarget}':
    parameters:
      - $ref: '#/components/parameters/collectionTarget'
    get:
      summary: v4/app/ Get Collections
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    properties:
                      items:
                        type: array
                        items:
                          allOf:
                            - $ref: ./schemas/listed-title-meta.yaml
                            - $ref: ./schemas/title-basic-meta.yaml
                      display_name:
                        type: string
                      collection_id:
                        type: string
                      collection_name:
                        type: string
                      collection_type:
                        type: string
                      pagination:
                        $ref: ./schemas/pagination.yaml
      operationId: get-v4-a-collections-target
      description: ''
      parameters:
        - schema:
            type: integer
            minimum: 0
            default: 0
          in: query
          name: page
          description: starts from `0`
        - schema:
            type: integer
            default: 20
            minimum: 1
            maximum: 20
          in: query
          name: page_size
        - $ref: '#/components/parameters/sort'
        - schema:
            type: string
          in: query
          name: country
        - schema:
            type: string
          in: query
          name: theme
        - schema:
            type: string
            enum:
              - '2'
              - '5'
              - '6'
          in: query
          name: rating
          description: '年齡分級。 `2`: 2歲以下; `5`: 3-5歲; `6`: 6歲以上'
        - schema:
            type: string
          in: query
          name: figure
        - schema:
            type: string
          in: query
          name: tag
        - schema:
            type: string
          in: query
          name: content_provider
        - schema:
            type: string
          in: query
          name: content_agent
      tags:
        - COLLECTION
  '/v4/a/search/{keyword}':
    parameters:
      - schema:
          type: string
        name: keyword
        in: path
        required: true
    get:
      summary: v4/app Search by keyword
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    properties:
                      titles:
                        type: array
                        items:
                          allOf:
                            - $ref: ./schemas/listed-title-meta.yaml
                            - $ref: ./schemas/title-basic-meta.yaml
                      figures:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: string
                              example: 358c0f915f6aa3318c2b12495aadd2ffaf694e2573aa9
                            name:
                              type: string
                              example: Julia Wu
      operationId: get-v4-a-search-keyword
      description: ''
      parameters:
        - schema:
            type: boolean
            minimum: 0
            default: 0
          in: query
          name: save
          description: need to save the keyword for history
      tags:
        - TITLE
  '/v3/collections/{collectionTarget}':
    parameters:
      - $ref: '#/components/parameters/collectionTarget'
    get:
      summary: v3/ Get Collections
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                x-examples:
                  example-1:
                    status:
                      type: OK
                      subtype: null
                      message: null
                    data:
                      title: 戲劇
                      collection_id: 15ef7751af1e03ee2803af6c57200c06792ea4b8f1ca5ddda7933a52121ccd72
                      collection_name: 戲劇
                      collection_type: genre
                      total: 1
                      count: 1
                      page: 1
                      page_size: 3
                      filter:
                        - title: 地區
                          items:
                            - title: 台灣
                              collection_type: country
                              collection_name: Taiwan
                        - title: 類型
                          items:
                            - title: 浪漫愛情
                              collection_type: theme
                              collection_name: 浪漫愛情
                            - title: 懸疑推理
                              collection_type: theme
                              collection_name: 懸疑推理
                            - title: 奇幻冒險
                              collection_type: theme
                              collection_name: 奇幻冒險
                      items:
                        - id: '02000192'
                          title: 想見你
                          title_type: miniseries
                          status: license_valid
                          is_ending: true
                          is_containing_avod: true
                          is_validated: true
                          child_lock: false
                          release_year: 2019
                          user_rating: 4.939133107900386
                          user_rating_count: 3253
                          cover: 'https://images.kktv.com.tw/covers/48/48589798c7af98ff4965c2f00d02dc9139e8afc9.xs.jpg'
                          stills:
                            - 'https://images.kktv.com.tw/stills/81/818ba57fb0393b123a076de1516ee2fc562e0bd2.xs.jpg'
                            - 'https://images.kktv.com.tw/stills/a1/a17286372eb37a702f80cb2e001b60abcea0f390.xs.jpg'
                            - 'https://images.kktv.com.tw/stills/bc/bc3439ac95508f4fa39455ef5375e6a1f5f5e0c9.xs.jpg'
                            - 'https://images.kktv.com.tw/stills/b1/b18919fe03bd38b2bc9aab01facfc75d12a8318a.xs.jpg'
                            - 'https://images.kktv.com.tw/stills/60/60390c8017130528b99d681e3e29fba3f679bccf.xs.jpg'
                          total_episode_counts:
                            '0200019201': 13
                          total_series_count: 1
                          latest_update_info: 共13集
                          content_labels_for_freetrial_user: []
                          summary: 2019年的現在，因為走不出王詮勝離開的思念，黃雨萱試圖用一款可以找到世界上另一個自己的APP，找到了另一個與王詮勝相似的男人，不料在照片中，卻看到了另一個長得與自己非常相似的陳韻如…1998年的過去，陳韻如從昏迷中醒來，在她昏迷的這幾天，她做了一個好長好長的夢，夢裡的她，名字叫做黃雨萱…
                          themes:
                            - id: 05cdf10875d20a7e196f93c67a820e869556bca71f422e5b0c8c54d1f73ac9bc
                              collection_type: theme
                              collection_name: 浪漫愛情
                            - id: 2850e9b2e9e483cecaa97ef986e5d5e4994849918177448f2ef627b9f1b145e6
                              collection_type: theme
                              collection_name: 奇幻冒險
                            - id: 3c23b264003d334ffa5d7b92f829147560a3846b2262f2e9090073f9831d59c2
                              collection_type: theme
                              collection_name: 懸疑推理
                            - id: e2597a830642a83afb514c9faba90d4225721c6e1df01730cea49b966b5e46d5
                              collection_type: theme
                              collection_name: 免費
                          casts:
                            - id: 42b6efe1d1220de7f3c4e0ccfcf94b6e3296de1558c9164224272690bfdf4f50
                              collection_type: figure
                              collection_name: 柯佳嬿
                            - id: 7ef9387589c336f4516448e736a732c089725e550c4f1911fe5bf8298df12dac
                              collection_type: figure
                              collection_name: 許光漢
                            - id: 9b24b5ee798e3c791c1bb3913d4c1b229ce2974514aee32e71159a50b6f55a23
                              collection_type: figure
                              collection_name: 施柏宇
                            - id: 375aaa28c5a571cb924fe0f63925a7f659264f4e8a3018f93dcfcd886e62504d
                              collection_type: figure
                              collection_name: 顏毓麟
                properties:
                  status:
                    $ref: '#/components/schemas/V3ErrStatus'
                  data:
                    type: object
                    properties:
                      title:
                        type: string
                      collection_id:
                        type: string
                      collection_name:
                        type: string
                      collection_type:
                        type: string
                      total:
                        type: integer
                      count:
                        type: integer
                      page:
                        type: integer
                      page_size:
                        type: integer
                      filter:
                        type: array
                        items:
                          type: object
                          properties:
                            title:
                              type: string
                            items:
                              type: array
                              items:
                                type: object
                                properties:
                                  title:
                                    type: string
                                  collection_type:
                                    type: string
                                  collection_name:
                                    type: string
                      items:
                        type: array
                        items:
                          $ref: ./schemas/v3/listed-title.yaml
      operationId: get-v3-collections-target
      description: |
        回傳 collections 資料 在 URL 上面請盡量用真實的 collectionType 及 collectionName

        中間用半形冒號隔開像是 /v3/collections/country:韓國
        `country` 是 collectionType, `韓國` 是 collectionName ，

        api 有向後相容，也可以直接用 sha256 的編碼來查詢，其他 url query string 用法，請參照下面說明，資料格式，和其他列表 api 格式一致

        請參考下面範例資料結構

        目前支援查詢的分類有:
        - country ，例如: /collections/country:Taiwan
        - content_provider ，例如: /collections/content_provider:KBS
        - content_agent ，例如: /collections/content_agent:龍華
        - genre ，例如: /collections/genre:浪漫愛情
        - theme ，例如: /collections/theme:勵志
        - figure ，例如: /collections/figure:黑木華 (這裡面包含了， cast, director, writer, producer) 可以對應 title api 看 collection_type 及 collection_name
      parameters:
        - schema:
            type: integer
            minimum: 1
            default: 1
          in: query
          name: page
          description: |
            **!NOTICE!** starts from `1`
        - schema:
            type: integer
            default: 20
            minimum: 1
            maximum: 20
          in: query
          name: page_size
        - $ref: '#/components/parameters/sort'
        - schema:
            type: string
          in: query
          name: country
        - schema:
            type: string
          in: query
          name: theme
        - schema:
            type: string
            enum:
              - '2'
              - '5'
              - '6'
          in: query
          name: rating
          description: '年齡分級。 `2`: 2歲以下; `5`: 3-5歲; `6`: 6歲以上'
        - schema:
            type: string
          in: query
          name: figure
        - schema:
            type: string
          in: query
          name: tag
        - schema:
            type: string
          in: query
          name: content_provider
        - schema:
            type: string
          in: query
          name: content_agent
      tags:
        - COLLECTION
  /v4/users/account/verify:
    parameters: []
    post:
      summary: v4/ Verify user's account
      description: Verify user's account before update
      operationId: post-v4-users-account-verify
      tags:
        - USER
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                account:
                  type: string
                  description: e164 formatted phone number OR email address
              required:
                - account
            examples:
              example-phone:
                value:
                  account: '+************'
              example-email:
                value:
                  account: <EMAIL>
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    nullable: true
              examples:
                example-1:
                  value:
                    error: null
                    data: null
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    nullable: true
                    description: nullable
              examples:
                example-400.2:
                  value:
                    error:
                      code: '400.2'
                      message: Invalid account format
                    data: null
                example-400.3:
                  value:
                    error:
                      code: '400.3'
                      message: The account already exists
                    data: null
                example-400.12:
                  value:
                    error:
                      code: '400.12'
                      message: User's account is unchangeable
                    data: null
        '404':
          description: User not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    nullable: true
                    description: nullable
              examples:
                example-404.1:
                  value:
                    error:
                      code: '404.1'
                      message: User not found
                    data: null
  /v4/users/me/account/set:
    parameters: []
    put:
      summary: v4/ User set account (88kk1d)
      operationId: put-v4-users-me-account-set
      tags:
        - USER
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                account:
                  type: string
                  description: e164 formatted phone number OR email address
                action_token:
                  type: string
                  description: |
                    - 傳 POST /v4/users/otp/verify 回傳的 action_token
              required:
                - account
                - action_token
            examples:
              example-phone:
                value:
                  account: '+************'
                  action_token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.ReQGcwuNyCkrM2rihw05onOjsyr6aWxIrA6IKy74vNU
              example-email:
                value:
                  account: <EMAIL>
                  action_token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.ReQGcwuNyCkrM2rihw05onOjsyr6aWxIrA6IKy74vNU
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    nullable: true
              examples:
                example-1:
                  value:
                    error: null
                    data: null
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    nullable: true
                    description: nullable
              examples:
                example-400.4:
                  value:
                    error:
                      code: '400.4'
                      message: The account is unverified
                    data: null
                example-400.3:
                  value:
                    error:
                      code: '400.3'
                      message: The account already exists
                    data: null
                example-400.12:
                  value:
                    error:
                      code: '400.12'
                      message: User's account is unchangeable
                    data: null
        '404':
          description: User not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    nullable: true
                    description: nullable
              examples:
                example-404.1:
                  value:
                    error:
                      code: '404.1'
                      message: user not found
                    data: null
  /v4/users/me/account:
    parameters: []
    put:
      summary: v4/ User update account
      description: 變更帳號
      operationId: put-v4-users-me-account
      tags:
        - USER
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                account:
                  type: string
                  description: e164 formatted phone number OR email address
                action_token:
                  type: string
                  description: |
                    - 傳 POST /v4/users/me/password/verify 回傳的 action_token
              required:
                - account
                - action_token
            examples:
              example-phone:
                value:
                  account: '+************'
                  action_token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.ReQGcwuNyCkrM2rihw05onOjsyr6aWxIrA6IKy74vNU
              example-email:
                value:
                  account: <EMAIL>
                  action_token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.ReQGcwuNyCkrM2rihw05onOjsyr6aWxIrA6IKy74vNU
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    nullable: true
              examples:
                example-1:
                  value:
                    error: null
                    data: null
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    nullable: true
                    description: nullable
              examples:
                example-400.4:
                  value:
                    error:
                      code: '400.4'
                      message: The account is unverified
                    data: null
                example-400.3:
                  value:
                    error:
                      code: '400.3'
                      message: The account already exists
                    data: null
                example-400.12:
                  value:
                    error:
                      code: '400.12'
                      message: User's account is unchangeable
                    data: null
        '404':
          description: User not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    nullable: true
                    description: nullable
              examples:
                example-404.1:
                  value:
                    error:
                      code: '404.1'
                      message: user not found
                    data: null
  /v4/users/me/password/set:
    parameters: []
    put:
      summary: v4/ User set password (88kk1d)
      operationId: put-v4-users-me-password-set
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
              examples:
                example-1:
                  value:
                    error: null
                    data: {}
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
              examples:
                example-400.7:
                  value:
                    error:
                      code: '400.7'
                      message: Invalid password format
                    data: {}
      tags:
        - USER
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                password:
                  type: string
                repeat_password:
                  type: string
                action_token:
                  type: string
                  description: |
                    - 傳 POST /v4/users/otp/verify 回傳的 action_token
              required:
                - password
                - repeat_password
                - action_token
            examples:
              example:
                value:
                  password: 1234qwer
                  repeat_password: 1234qwer
                  action_token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.ReQGcwuNyCkrM2rihw05onOjsyr6aWxIrA6IKy74vNU
  /v4/users/me/password:
    parameters: []
    put:
      summary: v4/ User update password
      description: 變更密碼
      operationId: put-v4-users-me-password
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
              examples:
                example-1:
                  value:
                    error: null
                    data: {}
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
              examples:
                example-400.7:
                  value:
                    error:
                      code: '400.7'
                      message: Invalid password format
                    data: {}
      tags:
        - USER
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                password:
                  type: string
                repeat_password:
                  type: string
                action_token:
                  type: string
                  description: |
                    - 傳 POST /v4/users/me/password/verify 回傳的 action_token
              required:
                - password
                - repeat_password
                - action_token
            examples:
              example:
                value:
                  password: 1234qwer
                  repeat_password: 1234qwer
                  action_token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.ReQGcwuNyCkrM2rihw05onOjsyr6aWxIrA6IKy74vNU
  /v3/users/me:
    parameters: []
    get:
      summary: v3/ Get my user info
      operationId: get-v3-users-me
      description: |
        Get user's info by current login

        ---

        讀取目前登入 user 個人資訊，在 data -> paymentInfo user 下一期的訂單資訊 ，如果已經綁定 kkbox id ，屬性 kkboxsub 會有 kkboxid unique value
      tags:
        - USER
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                x-examples:
                  example-1:
                    status:
                      type: OK
                      subtype: null
                      message: null
                    data:
                      id: 8ac862a948eadd086cae69a666604742c893c85ae0efd216d333bb05a461eac9
                      email: <EMAIL>
                      phone: null
                      avatarUrl: 'https://scontent.xx.fbcdn.net/v/t1.0-1/c8.0.50.50/p50x50/31052_393758794115_6170893_n.jpg?oh=d829e537375b261d64cd1a867cf3efee&oe=58C128F4'
                      name: Test User
                      birthday: null
                      gender: null
                      role: premium
                      type: classmate
                      createdAt: '2016-11-30T18:35:12.558104+08:00'
                      createdBy: ''
                      expiredAt: 1575734400
                      lastSignedIn: 1626320388
                      autoRenew: false
                      hasPaid: true
                      paymentInfo:
                        nextOrderDate: 1530444525
                        nextOrderPrice: 150
                        type: mod
                        previousOrderName: mod.duration1month149.225
                      from:
                        bandott: false
                        facebook: true
                        facebook_info:
                          id: '10154789149134116'
                        kkbox: false
                        mod: false
                      was_prime: true
                properties:
                  status:
                    $ref: '#/components/schemas/V3ErrStatus'
                  data:
                    type: object
                    properties:
                      id:
                        type: string
                      email:
                        type: string
                      phone:
                        type: string
                      avatarUrl:
                        type: string
                      name:
                        type: string
                      birthday:
                        type: string
                      gender:
                        type: string
                      role:
                        type: string
                      type:
                        type: string
                      createdAt:
                        type: string
                      createdBy:
                        type: string
                      expiredAt:
                        type: integer
                      lastSignedIn:
                        type: integer
                      autoRenew:
                        type: boolean
                      hasPaid:
                        type: boolean
                      paymentInfo:
                        type: object
                        properties:
                          nextOrderDate:
                            type: integer
                          nextOrderPrice:
                            type: integer
                          type:
                            type: string
                          previousOrderName:
                            type: string
                          preorderProductItemName:
                            type: string
                            description: 預購方案名稱
                      from:
                        type: object
                        properties:
                          bandott:
                            type: boolean
                          facebook:
                            type: boolean
                          facebook_info:
                            type: object
                            properties:
                              id:
                                type: string
                          kkbox:
                            type: boolean
                          mod:
                            type: boolean
                      verify_alert:
                        type: object
                        nullable: true
                        properties:
                          title:
                            type: string
                            example: 提醒您
                          desc:
                            type: string
                            example: 為提供更好的服務，請馬上設定帳號登入資訊
                          btn_text:
                            type: string
                            example: 立即設定
                          btn_action:
                            type: string
                            example: 'https://www.kktv.me/88kk1d/password'
                            description: the redirect url
                      action_authority:
                        type: object
                        properties:
                          account_changeable:
                            type: boolean
                            description: 是否能更改帳號
                            example: false
                          password_changeable:
                            type: boolean
                            description: 是否能更改密碼
                            example: true
                          survey_completed:
                            type: object
                            nullable: true
                            description: 問卷完成狀況
                            properties:
                              signup:
                                type: boolean
                                description: 是否已完成註冊問卷
                                example: true
                      was_prime:
                        type: boolean
                        description: 是否曾經為 prime 會員
  /v3/users/me/orders:
    get:
      summary: v3/ User get orders
      operationId: get-v3-users-me-orders
      description: Return user orders for client to display payment history
      security:
        - Bearer: []
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      type:
                        type: string
                      subtype:
                        type: string
                      message:
                        type: string
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        order_id:
                          type: string
                          description: order number
                          example: KT0020221128868855 or 20200908915844 (billing)
                        realized_at:
                          type: integer
                          description: order realized datetime in unix timestamp
                          example: 1683273923
                        product_name:
                          type: string
                          description: name of the product in this order
                          example: KKTV VIP 雙人月繳方案
                        price:
                          type: integer
                          description: total price of this order
                          example: 249
                        order_date:
                          type: integer
                          description: order date in unix timestamp
                          example: 1683302400
                        start_date:
                          type: integer
                          description: start date of the order in unix timestamp
                          example: 1683302400
                        end_date:
                          type: integer
                          description: end date of the order in unix timestamp
                          example: 1685980800
                        status:
                          type: string
                          description: order status
                          example: ok
                        payment_info:
                          type: object
                          description: payment detail
                          properties:
                            type:
                              type: string
                              description: payment type of this order
                              example: credit_card
                            credit_card_6no:
                              type: string
                              description: credit card 6no
                            credit_card_4no:
                              type: string
                              description: credit card 4no
                        invoice:
                          type: object
                          description: ezpay responsed invoice data
                          properties:
                            merchant_id:
                              type: string
                              description: merchant id
                            merchant_order_no:
                              type: string
                              description: merchant order number
                            invoice_number:
                              type: string
                              description: invoice number
                            random_number:
                              type: string
                              description: invoice random number
                            total_amount:
                              type: integer
                              description: invoice total amount
                              example: 249
                            created_at:
                              type: integer
                              description: invoice created datetime in unix timestamp
                              example: 1683273926
                            _search_data:
                              type: string
                              description: POST data for ezpay search invoice api
                            _search_url:
                              type: string
                              description: API endpoint of ezpay search invoice api
      tags:
        - USER
  '/v4/a/titles/{titleID}':
    get:
      summary: v4/app/ Get title detail
      tags:
        - TITLE
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    required:
                      - title
                    properties:
                      title:
                        $ref: ./schemas/title-detail.yaml
                      user_actions:
                        type: object
                        description: only appear when user logged in
                        properties:
                          is_favorite:
                            type: boolean
                            description: true if the title is in user's favorite list
                          rating:
                            type: integer
                            description: user's rating of the title. Value is _null_ if such title never got any rating.
                            example: 3
                      continued_episode:
                        $ref: ./schemas/continued-episode.yaml
                      display:
                        type: object
                        properties:
                          play_hint:
                            type: string
                            example: 接續播放 第2季/ 第1集
                          played_percentage:
                            type: number
                            example: 0.02
                            minimum: 0
                            maximum: 1
                      user_authority:
                        allOf:
                          - type: object
                            properties:
                              allow_casting:
                                type: boolean
                              display_ads:
                                type: boolean
                              functions:
                                type: array
                                items:
                                  enum:
                                    - basic
                                    - advanced
                          - $ref: ./schemas/authority.yaml
      operationId: get-v4-a-titles-titleid
      description: Get the title detail by given title id
      parameters:
        - schema:
            type: number
            enum:
              - 0
              - 1
            example: 1
            default: 0
          in: query
          name: extra
          description: |
            To include the extra videos. `0`: not to include; `1`: to include
    parameters:
      - $ref: '#/components/parameters/titleID'
  '/v4/a/titles/{titleID}/related-titles':
    get:
      summary: v4/app/ Get related titles
      tags:
        - TITLE
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    properties:
                      items:
                        type: array
                        items:
                          allOf:
                            - $ref: ./schemas/listed-title-meta.yaml
                            - $ref: ./schemas/title-basic-meta.yaml
      operationId: get-v4-a-titles-titleid-related-titles
      description: Get other titles that related to the given title id
    parameters:
      - name: titleID
        in: path
        required: true
        schema:
          type: string
        description: ID of title
  '/v4/{deviceType}/episodes':
    get:
      summary: '[WIP] v4/app/ List episodes'
      tags:
        - TITLE
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    properties:
                      items:
                        type: array
                        items:
                          $ref: ./schemas/title-detail-episode-detail.yaml
                      pagination:
                        allOf:
                          - $ref: ./schemas/pagination.yaml
                          - type: object
                            properties:
                              has_next:
                                type: boolean
                                readOnly: true
                              has_prev:
                                type: boolean
      operationId: get-v4-a-episodes
      description: Get episodes by specified condition
      parameters:
        - schema:
            type: string
          in: query
          name: series_id
          description: id of series whom the episodes belong to
          required: true
        - schema:
            type: integer
            enum:
              - 0
              - 1
            default: 0
          in: query
          name: sort
          description: '`0`: asc, `1`: desc'
        - $ref: '#/components/parameters/page'
        - schema:
            type: integer
          in: query
          name: page_size
    parameters:
      - $ref: '#/components/parameters/deviceType'
  '/v4/{deviceType}/episodes/{episode_id}':
    get:
      summary: '[WIP] v4/app/ Get episode by id'
      tags:
        - TITLE
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    properties:
                      episode:
                        $ref: ./schemas/title-detail-episode-detail.yaml
                      series_id:
                        type: string
                      title_id:
                        type: string
                      navigator:
                        type: object
                        properties:
                          prev_id:
                            type: string
                          next_id:
                            type: string
                          sort:
                            type: integer
                            enum:
                              - 0
                              - 1
                          position:
                            type: integer
                            description: the record position of current episode in such series
                          total:
                            type: integer
                            description: 'total amount of episode in such '
                    required:
                      - episode
                      - series_id
                      - title_id
      operationId: get-v4-a-episodes-episode-id
      description: Get episode by given id
      parameters:
        - schema:
            type: string
            enum:
              - '0'
              - '1'
          in: query
          name: sort
          description: '`0`: by id asc; `1`: by id desc'
    parameters:
      - name: deviceType
        in: path
        schema:
          type: string
          enum:
            - a
            - w
          example: a
        description: 'The device type. `a` for _app_, `w` for _web_'
        required: true
      - schema:
          type: string
        name: episode_id
        in: path
        required: true
        description: the id of episode
  /v3/payment/billing/orders:
    parameters: []
    post:
      summary: v3/ Create billing credit card order for billing products
      operationId: post-v3-create-billing-orders
      description: Create billing credit card order for billing products
      security:
        - Bearer: []
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    $ref: '#/components/schemas/V3ErrStatus'
                  data:
                    $ref: ./schemas/billing-create-order-response.yaml
              examples: {}
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                product_identifier:
                  type: string
                  description: identifier of billing product
                  example: kktv.vip.cc.30d.sub
                quantity:
                  type: integer
                  description: product quantity
                  example: 1
                  minimum: 1
                email:
                  type: string
                  description: user's email
                return_url:
                  type: string
                  description: 'URL for client to return when payment flow completed, required when the requested product is valuable (price > 0)'
                cancel_url:
                  type: string
                  description: 'URL for client to return when payment flow canceled, required when the requested product is valuable (price > 0)'
                buyer_name:
                  type: string
                  description: 'user’s name on the invoice, required when invoice_type is "paper"'
                phone:
                  type: string
                  description: user’s phone
                referral:
                  type: string
                  description: referral code
                invoice_type:
                  type: string
                  description: 'invoice type, required when the requested product is valuable (price > 0)'
                  enum:
                    - carrier
                    - paper
                    - donate
                carrier_type:
                  type: string
                  description: 'carrier type, required when invoice_type is "carrier"'
                  enum:
                    - barcode
                    - citizen_certificate
                    - ezpay_member
                carrier_number:
                  type: string
                  description: 'carrier number, required when invoice_type is "barcode" or "citizen_certificate"'
                billing_address:
                  type: string
                  description: 'billing address, required when invoice_type is "paper"'
                donate_code:
                  type: string
                  description: 'donate code, required when invoice_type is "donate"'
                device_type:
                  type: string
                  enum:
                    - desktop
                    - mobile
                  example: desktop
              required:
                - product_identifier
                - quantity
                - email
            examples:
              example-1:
                value:
                  product_identifier: kktv.vip.cc.30d.sub
                  quantity: 1
                  email: string
                  return_url: string
                  cancel_url: string
                  buyer_name: string
                  phone: string
                  referral: string
                  invoice_type: carrier
                  carrier_type: barcode
                  carrier_number: string
                  billing_address: string
                  donate_code: string
                  device_type: desktop
      tags:
        - PAYMENT
  /v3/payment/billing/orders/grace-period:
    parameters: []
    post:
      summary: v3/ Create order for billing products in grace period
      operationId: post-v3-create-billing-orders-grace-period
      description: Create order for billing products in grace period
      security:
        - Bearer: []
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    $ref: '#/components/schemas/V3ErrStatus'
                  data:
                    $ref: ./schemas/billing-create-order-response.yaml
              examples: {}
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                order_id:
                  type: string
                  description: identifier of the order which has failed debit
                  example: '202408160429'
                return_url:
                  type: string
                  description: 'URL for client to return when payment flow completed, required when the requested product is valuable (price > 0)'
                cancel_url:
                  type: string
                  description: 'URL for client to return when payment flow canceled, required when the requested product is valuable (price > 0)'
                device_type:
                  type: string
                  enum:
                    - desktop
                    - mobile
                  example: desktop
              required:
                - order_id
            examples: {}
      tags:
        - PAYMENT
  '/v3/payment/orders/{orderNumber}':
    parameters:
      - $ref: '#/components/parameters/orderNumber'
    get:
      summary: v3/ Get order detail by order number
      operationId: get-v3-get-order-detail
      description: Return order detail for client to display after finish purchasing
      security:
        - Bearer: []
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      type:
                        type: string
                      subtype:
                        type: string
                      message:
                        type: string
                  data:
                    type: object
                    properties:
                      number:
                        type: string
                        description: order number
                        example: KT0020221128868855 or 20200908915844 (billing)
                      price:
                        type: integer
                        description: order price
                        example: 100
                      order_date:
                        type: integer
                        description: order date in unix timestamp
                      payment_status:
                        description: pending | paid | failed
                        type: string
                      product:
                        type: object
                        description: the product purchased in this order
                        properties:
                          name:
                            type: string
                            description: product name for client to display to customer
                            example: 學生方案
                          interval_amount:
                            type: string
                            description: product interval amount for client to display to customer
                      user:
                        type: object
                        description: the user who purchase the product
                        properties:
                          is_in_trial_period:
                            type: boolean
                            description: whether the user in still in trial period
      tags:
        - PAYMENT
  /v3/users/me/unsubscribe:
    parameters: []
    put:
      summary: v3/ Cancel user subscription
      description: Cancel user subscription
      operationId: put-v3-cancel-subscription
      security:
        - Bearer: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                user_id:
                  type: string
                  description: user's id
                reason:
                  type: string
                  description: reason of cancel subscription
              required:
                - user_id
                - reason
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      type:
                        type: string
                      subtype:
                        type: string
                      message:
                        type: string
                  data:
                    type: object
                    properties:
                      expired_at:
                        type: integer
                        description: last date of user's subscription in unix timestamp
                        example: 1687363200
      tags:
        - PAYMENT
  /v4/billing/credit_card:
    put:
      summary: v4/ update credit card in NEWEBPAY
      tags:
        - PAYMENT
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    properties:
                      order_number:
                        type: string
                        example: '20200908915844'
                      payment_required:
                        type: boolean
                        example: true
                      newebpay:
                        type: object
                        properties:
                          MerchantID:
                            type: string
                            example: MS3400874413
                          TradeInfo:
                            type: string
                          TradeSha:
                            type: string
                          Version:
                            type: string
                            example: '1.5'
      operationId: put-v4-billing-creditcard
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  description: user's email
                return_url:
                  type: string
                  description: 'URL for client to return when payment flow completed, required when the requested product is valuable (price > 0)'
                cancel_url:
                  type: string
                  description: 'URL for client to return when payment flow canceled, required when the requested product is valuable (price > 0)'
              required:
                - return_url
                - cancel_url
                - email
  /v4/users/otp:
    post:
      summary: v4/ request a temproary one time password by user's phone or email
      tags:
        - USER
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    properties:
                      retry_interval:
                        type: integer
                        example: 120
                        description: use second as unit
              examples:
                example-1:
                  value:
                    error:
                      code: string
                      message: string
                    data:
                      retry_interval: 120
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: object
                    properties:
                      code:
                        type: string
                      message:
                        type: string
              examples:
                example-1:
                  value:
                    error:
                      code: '400.1'
                      message: Invalid parameter
                    data: null
      operationId: post-v4-users-otp
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                account:
                  type: string
                  description: user's valid email or phone
              required:
                - account
            examples:
              example-1:
                value:
                  account: string
      description: ''
      security:
        - Bearer: []
  /v4/users/otp/verify:
    post:
      summary: v4/ verify one time password is correct or not
      tags:
        - USER
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    nullable: true
                    properties:
                      action_token:
                        type: string
                        description: action token represents to get authorization to use such account before the expire date
                        example: YNBi8R5ySYeNU9aPXqnJstPC7RetNei438hK5BWv3EgP77dNr5bQkl3gKA
                      expired_at:
                        type: integer
                        description: expire date of the action token. In unix time
                        example: **********
                    required:
                      - action_token
                      - expired_at
              examples:
                example-1:
                  value:
                    error:
                      code: string
                      message: string
                    data: null
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: object
                    properties:
                      code:
                        type: string
                      message:
                        type: string
              examples:
                example-1:
                  value:
                    error:
                      code: '400.1'
                      message: Invalid parameter
                    data: null
      operationId: post-v4-users-otp-verify
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                account:
                  type: string
                  description: user's valid email or phone
                otp:
                  type: string
                  description: user received from thire phone or email
              required:
                - account
                - otp
            examples:
              example-1:
                value:
                  account: string
                  otp: string
      description: ''
      security:
        - Bearer: []
  /v4/users/sign-up:
    post:
      summary: v4/ sign-up
      tags:
        - USER
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data: {}
              examples:
                example-1:
                  value:
                    error:
                      code: ''
                      message: ''
                    data: null
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    nullable: true
                    type: object
              examples:
                example-1:
                  value:
                    error:
                      code: '400.1'
                      message: Invalid parameter
                    data: null
      operationId: post-v4-signup
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                action_token:
                  type: string
                  description: 'It''s an encryption string that contains the user''s account, password, and expired_at.'
                account:
                  type: string
                password:
                  type: string
                repeat_password:
                  type: string
              required:
                - action_token
                - account
                - password
                - repeat_password
            examples:
              example-1:
                value:
                  action_token: YNBi8R5ySYeNU9aPXqnJstPC7RetNei438hK5BWv3EgP77dNr5bQkl3gKA
                  account: <EMAIL>
                  password: thisisjohndoe
                  repeat_password: thisisjohndoe
      description: 'When a user want to use kktv, but doesn''t have an account.'
      security:
        - Bearer: []
  /v4/users/login:
    parameters: []
    post:
      summary: v4/ Login
      description: login via KKTV's account and password
      operationId: post-v4-users-login
      tags:
        - USER
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                account:
                  type: string
                  description: e164 formatted phone number OR email address
                password:
                  type: string
            examples:
              example-phone:
                value:
                  account: '+************'
                  password: 123qwe
              example-email:
                value:
                  account: <EMAIL>
                  password: 123qwe
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    properties:
                      token:
                        type: string
                        description: bearer token
                        example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************.RMONXiROa2MsUqoyfozxD_RoZManwZVuBn7mkDODmoI
                      expired_at:
                        type: integer
                        example: **********
                        description: expired time of bearer token (timestamp)
                      refresh_token:
                        type: string
                        example: f690e40c64a33f47610fb5f1306778f07ee807567901aaea361a77f00eeb77bb
                        description: refresh token
                      refresh_token_expired_at:
                        type: integer
                        example: 1675134689
                        description: expired time of refresh token (timestamp)
              examples:
                example-1:
                  value:
                    error: null
                    data:
                      token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************.RMONXiROa2MsUqoyfozxD_RoZManwZVuBn7mkDODmoI
                      expired_at: **********
                      refresh_token: f690e40c64a33f47610fb5f1306778f07ee807567901aaea361a77f00eeb77bb
                      refresh_token_expired_at: 1675134689
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    nullable: true
                    description: nullable
              examples:
                example-400.1:
                  value:
                    error:
                      code: '400.1'
                      message: Invalid parameter
                    data: null
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    nullable: true
                    description: nullable
              examples:
                example-401.1:
                  value:
                    error:
                      code: '401.1'
                      message: Login failed
                    data: null
  /v4/users/password/reset:
    parameters: []
    post:
      summary: v4/ Verify if user's account exists
      description: Verify if user's account exists before password reset
      operationId: post-v4-users-password-reset
      tags:
        - USER
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                account:
                  type: string
                  description: e164 formatted phone number OR email address
              required:
                - account
            examples:
              example-phone:
                value:
                  account: '+************'
              example-email:
                value:
                  account: <EMAIL>
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    nullable: true
              examples:
                example-1:
                  value:
                    error: null
                    data: null
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    nullable: true
                    description: nullable
              examples:
                example-400.1:
                  value:
                    error:
                      code: '400.1'
                      message: Invalid parameter
                    data: null
                example-400.2:
                  value:
                    error:
                      code: '400.2'
                      message: Invalid account format
                    data: null
                example-400.10:
                  value:
                    error:
                      code: '400.10'
                      message: There are duplicate accounts
                    data: null
        '404':
          description: Not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    nullable: true
                    description: nullable
              examples:
                example-404.1:
                  value:
                    error:
                      code: '404.1'
                      message: User not found
                    data: null
  /v4/users/password:
    parameters: []
    put:
      summary: v4/ Reset specific user's passwrod after he or she pass the OTP verified
      description: 'After users have passed OTP, they can reset their password.'
      operationId: put-v4-users-password-reset
      tags:
        - USER
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                action_token:
                  description: you could get action token from verify otp api
                  type: string
                account:
                  type: string
                  description: e164 formatted phone number OR email address
                password:
                  type: string
                repeat_password:
                  type: string
                  description: should be the same with password field
              required:
                - action_token
                - account
                - password
                - repeat_password
            examples:
              example-1:
                value:
                  action_token: YNBi8R5ySYeNU9aPXqnJstPC7RetNei438hK5BWv3EgP77dNr5bQkl3gKA
                  account: <EMAIL>
                  password: 1234qwer
                  repeat_password: 1234qwer
        description: ''
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    nullable: true
              examples:
                example-1:
                  value:
                    error: null
                    data: null
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    nullable: true
                    description: nullable
              examples:
                example-400.1:
                  value:
                    error:
                      code: '400.1'
                      message: Invalid parameter
                    data: null
                example-400.2:
                  value:
                    error:
                      code: '400.2'
                      message: Invalid account format
                    data: null
        '404':
          description: Not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    nullable: true
                    description: nullable
              examples:
                example-404.1:
                  value:
                    error:
                      code: '404.1'
                      message: User not found
                    data: null
  /v4/users/me/password/verify:
    parameters: []
    post:
      summary: v4/ Verify user's password
      description: Verify if request password is the same as login user's password
      operationId: post-v4-users-me-password-verify
      tags:
        - USER
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                password:
                  type: string
              required:
                - password
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    properties:
                      action_token:
                        type: string
                        description: action token represents to get authorization to update account or password
              examples:
                example-1:
                  value:
                    error: null
                    data:
                      action_token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.ReQGcwuNyCkrM2rihw05onOjsyr6aWxIrA6IKy74vNU
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    nullable: true
              examples:
                example-400.1:
                  value:
                    error:
                      code: '400.1'
                      message: Invalid parameter
                    data: null
                example-400.7:
                  value:
                    error:
                      code: '400.7'
                      message: Invalid password format
                    data: null
                example-400.11:
                  value:
                    error:
                      code: '400.11'
                      message: Wrong password
                    data: null
  /v4/users/me/survey/signup:
    post:
      summary: v4/ User submit signup survey
      description: User submit signup survey
      operationId: post-v4-users-me-survey-signup
      tags:
        - USER
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                preferences:
                  type: object
                  properties:
                    kktv_source:
                      type: array
                      items:
                        type: string
                    registration_reason:
                      type: array
                      items:
                        type: string
                    frequent_video_platform:
                      type: array
                      items:
                        type: string
                    paid_video_platform:
                      type: array
                      items:
                        type: string
                    preferred_payment_method:
                      type: array
                      items:
                        type: string
                    preferred_payment_cycle:
                      type: array
                      items:
                        type: string
                    influencing_payment_factor:
                      type: array
                      items:
                        type: string
                    drama_source:
                      type: array
                      items:
                        type: string
                    animation_source:
                      type: array
                      items:
                        type: string
                    viewing_frequency:
                      type: string
                    influencing_ratings_factor:
                      type: array
                      items:
                        type: string
                  required:
                    - kktv_source
                    - registration_reason
                    - frequent_video_platform
                    - paid_video_platform
                    - preferred_payment_method
                    - preferred_payment_cycle
                    - influencing_payment_factor
                    - drama_source
                    - animation_source
                    - viewing_frequency
                    - influencing_ratings_factor
                user_info:
                  type: object
                  properties:
                    gender:
                      type: string
                    birthday:
                      type: string
                      format: date
                    job:
                      type: string
                    nickname:
                      type: string
                    phone:
                      type: string
                      description: e164 formatted phone
                    email:
                      type: string
                  required:
                    - gender
                    - birthday
                    - job
                    - nickname
              required:
                - preferences
                - user_info
            examples:
              example:
                value:
                  preferences:
                    kktv_source:
                      - 網頁主動搜尋-搜尋某齣劇/電影名稱
                      - 廣告-Instagram 廣告
                    registration_reason:
                      - 因為平台有豐富的特定國家/種類作品-日劇
                      - 因為平台有豐富的特定國家/種類作品-動漫
                      - 字幕翻譯品質佳
                      - 其他-爽
                    frequent_video_platform:
                      - KKTV
                      - Disney+
                      - YouTube
                    paid_video_platform:
                      - KKTV
                      - Disney+
                      - Netflix 網飛
                      - YouTube
                    preferred_payment_method:
                      - 共享(家庭) 方案-與家人、朋友分享
                      - 個人訂閱
                    preferred_payment_cycle:
                      - 以「月」為單位
                    influencing_payment_factor:
                      - 平台作品豐富
                      - 平台提供較多獨家作品
                      - 想看特定作品
                    drama_source:
                      - 其他-對戲劇沒興趣
                    animation_source:
                      - 透過親友得知-親友推薦、或聊天時提到
                      - 論壇得知-PTT
                    viewing_frequency: 每天觀看
                    influencing_ratings_factor:
                      - 影片畫質差
                  user_info:
                    gender: male
                    birthday: '1990-01-01'
                    job: 學生
                    nickname: 野獸前輩
                    phone: '+************'
                    email: <EMAIL>
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    nullable: true
              examples:
                example-1:
                  value:
                    error: null
                    data: null
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    nullable: true
              examples:
                example-400.1:
                  value:
                    error:
                      code: '400.13'
                      message: User has already submitted the survey
                    data: null
  /v4/auth/redirect:
    parameters: []
    get:
      summary: /v4/ get redirect uri with auth code
      operationId: get-v4-auth-redirect
      responses:
        '200':
          description: OK
          headers: {}
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    properties:
                      redirect_uri:
                        type: string
                        example: 'https://kktvtestapp/auth/code?state=21c89599-9c22-4d90-ac53-f6723dd4bfff&code=xxxxxx'
                    required:
                      - redirect_uri
      parameters:
        - schema:
            type: string
            example: EEC871AF3F079F49996A
          in: query
          name: client_id
          required: true
          description: the client app id
        - schema:
            type: string
            example: 21c89599-9c22-4d90-ac53-f6723dd4bfff
          in: query
          name: state
          required: true
          description: identifier of request
        - schema:
            type: string
            example: 'https://kktvtestapp/auth/code'
          in: query
          name: redirect_uri
          required: true
          description: the callback url of client app
      description: ''
      tags:
        - AUTH
  /v4/oauth/auth-code:
    parameters: []
    post:
      summary: '[WIP] /v4/ create oauth code'
      operationId: post-v4-oauth-auth-code
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  auth_code:
                    type: string
                  expires_in:
                    type: integer
                    example: 180
                    description: seconds to be expired
      description: to create an one-time-use authorization token with ttl
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                app_id:
                  type: string
                redirect_uri:
                  type: string
                scope:
                  type: string
                  description: the permission scope. use space to concat multi action
                  example: email phone
              required:
                - app_id
                - redirect_uri
      tags:
        - AUTH
  /v4/oauth/access-token:
    parameters: []
    post:
      summary: '[WIP] /v4/ Exchange to Access Token'
      operationId: post-v4-oauth-access-token
      description: |
        Validates the auth code and returns an access token.
        This is the third step in the OAuth flow, after the user has authorized the app.
        Use app_id and app_secret to do the basic auth in request header.
      tags:
        - AUTH
      parameters:
        - schema:
            type: string
            example: abc123def456
          in: query
          name: auth_code
          required: true
          description: The authorization code received from the auth endpoint
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    properties:
                      access_token:
                        type: string
                        example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
                      expires_in:
                        type: integer
                        example: 2592000
                    required:
                      - access_token
                      - expires_in
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: object
                    properties:
                      code:
                        type: string
                        example: '400.3'
                      message:
                        type: string
                        example: invalid authorization code
                  data:
                    type: object
                    nullable: true
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: object
                    properties:
                      code:
                        type: string
                        example: '500.0'
                      message:
                        type: string
                        example: unknown error
                  data:
                    type: object
                    nullable: true
      security:
        - BasicAuth: []
  /v4/text-contents:
    get:
      summary: v4/ Retrieve text contents
      description: Retrieve text contents
      operationId: get-v4-text-contents
      tags:
        - TEXT_CONTENT
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    nullable: true
                    properties:
                      signup_survey:
                        $ref: ./schemas/text-content-signup_survey.yaml
              examples:
                example-premium-or-expired:
                  $ref: ./schemas/text-content-example-premium-or-expired.yaml
                example-free-trial:
                  $ref: ./schemas/text-content-example-free-trail.yaml
                example-guest:
                  value:
                    error: null
                    data:
                      signup_survey: null
  '/v3/titles/{titleID}/episodes/{episodeID}/mezzanine/manifests':
    parameters:
      - name: titleID
        in: path
        required: true
        schema:
          type: string
        description: ID of title
      - schema:
          type: string
        name: episodeID
        in: path
        required: true
    get:
      summary: v3/ Retrieve mezzanine manifests for playback
      parameters:
        - in: query
          name: quality
          required: false
          description: |
            'Quality of the video playback. for the downloadable devices，only `medium` `high` are available
          schema:
            type: string
            enum:
              - playzone
              - low
              - medium
              - high
            example: medium
        - in: query
          name: device
          required: false
          description: Device used for playback
          schema:
            type: string
            enum:
              - android
              - ios
              - web
              - chromecast
              - airplay
        - in: query
          name: subtitles
          required: false
          description: 'Whether to include subtitles in the video playback: 0 represents for without subtitle'
          schema:
            type: integer
            minimum: 0
            maximum: 1
            enum:
              - 0
              - 1
        - in: query
          name: purpose
          required: false
          description: |
            Purpose of the playback. `download` only available for device `ios` or `android`
          schema:
            type: string
            enum:
              - playback
              - download
        - in: query
          name: medium
          required: false
          description: 'Medium of playback, e.g. SVOD'
          schema:
            type: string
            enum:
              - SVOD
              - AVOD
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    $ref: '#/components/schemas/V3ErrStatus'
                  data:
                    type: object
                    properties:
                      episodes:
                        type: array
                        items:
                          type: object
                          properties:
                            episode_id:
                              type: string
                              example: '452010026'
                            default_subtitle:
                              type: string
                              example: zh-Hant
                            subtitle_url:
                              type: object
                              additionalProperties:
                                type: string
                              example:
                                zh-Hant: 'https://kktv.cdn.net/fake/00000452010026_sub_sub/zh-Hant.vtt'
                            thumbnail_url:
                              type: string
                              example: 'https://theater.kktv.com.tw/fake/00000452010026_10bb3ee92eaa3/thumbnail.vtt'
                            thumbnail_small_url:
                              type: string
                              example: 'https://theater.kktv.com.tw/fake/00000452010026_b3ee92eaa3/thumbnailsmall.vtt'
                            dash:
                              type: object
                              properties:
                                url:
                                  type: string
                                  example: 'https://theater-kktv.cdn.fake.net/fake/000_dash.playback_p4.mpd'
                                size:
                                  type: number
                                  example: 12676649509.824299
                            hls:
                              type: object
                              properties:
                                url:
                                  type: string
                                  example: 'https://theater-kktv.cdn.fake.net/fake/00000452010/xxxx_hls.playback_p4.m3u8'
                                size:
                                  type: number
                                  example: 12676649509.824299
                            supported_quality:
                              type: array
                              items:
                                type: string
                      license_url:
                        type: object
                        properties:
                          fairplay:
                            type: string
                          playready:
                            type: string
                          widevine:
                            type: string
                      license_headers:
                        type: array
                        items:
                          type: object
                          properties:
                            key:
                              type: string
                              example: X-Kk-Tenant-Id
                            value:
                              type: string
                              example: '123456789'
              examples:
                example-1:
                  value:
                    status:
                      type: string
                      subtype: string
                      info: string
                    data:
                      episodes:
                        - episode_id: '452010026'
                          default_subtitle: zh-Hant
                          subtitle_url:
                            zh-Hant: 'https://kktv.cdn.net/fake/00000452010026_sub_sub/zh-Hant.vtt'
                          thumbnail_url: 'https://theater.kktv.com.tw/fake/00000452010026_10bb3ee92eaa3/thumbnail.vtt'
                          thumbnail_small_url: 'https://theater.kktv.com.tw/fake/00000452010026_b3ee92eaa3/thumbnailsmall.vtt'
                          dash:
                            url: 'https://theater-kktv.cdn.fake.net/fake/000_dash.playback_p4.mpd'
                            size: 12676649509.824299
                          hls:
                            url: 'https://theater-kktv.cdn.fake.net/fake/00000452010/xxxx_hls.playback_p4.m3u8'
                            size: 12676649509.824299
                          supported_quality:
                            - 240p
                            - 360p
                            - 480p
                      license_url:
                        fairplay: string
                        playready: string
                        widevine: string
                      license_headers:
                        - key: X-Kk-Tenant-Id
                          value: '123456789'
      operationId: v3-get-title-titleid-episode-episode-id-manifest
      description: 'you can obtain the manifest files for video streaming such as hls/dash url, subtitle files, thumbnail url, and the DRM license request URL.'
      tags:
        - PLAYBACK
  '/v4/trials/episodes/{episodeID}/manifests':
    parameters:
      - schema:
          type: string
          example: '01000749010001'
        name: episodeID
        in: path
        required: true
        description: a 14-digits string for episode id
    get:
      summary: '[WIP] v4/ Trial get episode manifests'
      parameters:
        - in: query
          name: quality
          required: false
          description: Quality of the video playback. for the downloadable devices，only `medium` `high` are available
          schema:
            type: string
            enum:
              - medium
              - high
            example: medium
            default: medium
        - in: query
          name: subtitles
          required: false
          description: 'Whether to include subtitles in the video playback: 0 represents for without subtitle'
          schema:
            type: integer
            minimum: 0
            maximum: 1
            enum:
              - 0
              - 1
            default: 0
        - $ref: '#/components/parameters/HeaderPlatform'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    properties:
                      episode:
                        type: object
                        properties:
                          episode_id:
                            type: string
                            example: '452010026'
                          default_subtitle:
                            type: string
                            example: zh-Hant
                          subtitle_url:
                            type: object
                            additionalProperties:
                              type: string
                            example:
                              zh-Hant: 'https://kktv.cdn.net/fake/00000452010026_sub_sub/zh-Hant.vtt'
                          thumbnail_url:
                            type: string
                            example: 'https://theater.kktv.com.tw/fake/00000452010026_10bb3ee92eaa3/thumbnail.vtt'
                          dash:
                            type: object
                            properties:
                              url:
                                type: string
                                example: 'https://theater-kktv.cdn.fake.net/fake/000_dash.playback_p4.mpd'
                              size:
                                type: number
                                example: 12676649509.824299
                          hls:
                            type: object
                            properties:
                              url:
                                type: string
                                example: 'https://theater-kktv.cdn.fake.net/fake/00000452010/xxxx_hls.playback_p4.m3u8'
                              size:
                                type: number
                                example: 12676649509.824299
                      license_url:
                        type: object
                        properties:
                          fairplay:
                            type: string
                            example: 'https://drm.platform.blendvision.com/api/v3/drm/license'
                          fairplay_cert:
                            type: string
                            example: 'https://drm.platform.blendvision.com/api/v3/drm/license/fairplay_cert'
                          playready:
                            type: string
                            example: 'https://drm.platform.blendvision.com/api/v3/drm/license'
                          widevine:
                            type: string
                            example: 'https://drm.platform.blendvision.com/api/v3/drm/license'
                      license_headers:
                        type: array
                        description: should pass all the key value pairs into the request header of DRM license
                        items:
                          type: object
                          properties:
                            key:
                              type: string
                              example: X-Kk-Tenant-Id
                            value:
                              type: string
                              example: '123456789'
              examples: {}
      operationId: v4-get-trials-episodes-episode-id-manifests
      description: |
        you can obtain the manifest files for video streaming such as hls/dash url, subtitle files, thumbnail url, and the DRM license request URL.


        ### manifest profile mapping table for VIP users
        | quality | device platform | profile |
        | -- |  -- | -- |
        | high | tvOS,iPadOS,iOS,MOD,Android TV,Android,Web | p5 |
        | medium | tvOS,iPadOS,iOS,MOD,Android TV,Android | p3 |
        | | Web | p4 |

        ### manifest profile mapping table for non VIP users
        | quality | device platform | profile |
        | -- |  -- | -- |
        | medium | tvOS,iPadOS,iOS,MOD,Android TV,Android | p3 |
        | | Web | p4 |
      tags:
        - TRIAL
  /v4/users/me/memberships:
    get:
      summary: v4/ Get my membership info
      tags:
        - USER
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    properties:
                      roles:
                        $ref: ./schemas/membership-role.yaml
                      account_status:
                        type: object
                        properties:
                          is_member:
                            type: boolean
                            description: indicate if the user is login as a member
                          is_freemium:
                            type: boolean
                            description: expired users or guest.
                          is_free_trial:
                            type: boolean
                          is_having_subscription:
                            type: boolean
                          need_check_payment:
                            type: boolean
                      authorities:
                        type: array
                        items:
                          enum:
                            - 'freemium:play'
                            - 'free_trial:play'
                            - 'premium:play'
                            - 'setting-streaming:update'
                            - 'extra:high-quality:play'
                            - 'prime:headline-link:display'
                            - 'prime:event:display'
                            - 'prime:banner:display'
                          type: string
                    required:
                      - roles
                      - account_status
      operationId: get-v4-users-me-memberships
    parameters: []
  /v4/users/me:
    get:
      summary: '[WIP] v4/ Get my user info'
      tags:
        - USER
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    properties:
                      email:
                        type: string
                      phone:
                        type: string
                      id:
                        type: string
                    required:
                      - id
      operationId: get-v4-users-me
    parameters: []
  /v4/users/me/service:
    get:
      summary: v4/ Get my service
      tags:
        - USER
        - PAYMENT
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    properties:
                      orders:
                        type: array
                        description: //TODO
                        items:
                          type: object
                      grace_period:
                        type: object
                        properties:
                          start:
                            $ref: '#/components/schemas/DateTime'
                          end:
                            $ref: '#/components/schemas/DateTime'
                          cta_link:
                            type: string
                            format: uri
                            example: 'https://www.kktv.me/account'
                          order_id:
                            type: string
                        required:
                          - start
                          - end
                          - cta_link
                      payment:
                        type: object
                        properties:
                          status:
                            type: string
                            enum:
                              - pending
                              - authorized
                              - paid
                              - failed
                              - refunded
                          type:
                            $ref: '#/components/schemas/PaymenType'
                          last_payment_at:
                            type: string
                            format: date-time
                            description: //TODO
                          next_payment_at:
                            type: string
                            format: date-time
                            description: //TODO
                        required:
                          - status
                          - type
      operationId: get-v4-users-my-info
    parameters: []
  /v4/product_packages:
    get:
      summary: v4/ Get product and package information
      tags:
        - PRODUCT
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    required:
                      - categories
                      - product_packages
                    properties:
                      categories:
                        type: array
                        items:
                          type: object
                          properties:
                            name:
                              type: string
                              example: 單人獨享
                            general:
                              type: boolean
                              description: if the category fixed of not
                            sort:
                              type: integer
                              description: the order of package category
                      product_packages:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: string
                              example: package id
                            price:
                              type: integer
                              description: the price of package
                            duration:
                              type: string
                              example: 年
                            title:
                              type: string
                              example: 動漫PASS年繳
                            description:
                              type: string
                              example: 付費權益僅限觀看『動漫分類』中的所有作品
                            button_text:
                              type: string
                              example: 立即購買
                            highlight:
                              type: string
                              example: 首賣限時優惠
                            label:
                              type: string
                              example: 新登場！動漫迷專屬
                            promotion:
                              type: string
                            products:
                              type: array
                              items:
                                type: object
                                properties:
                                  source:
                                    type: string
                                    description: billing | origin
                                  name:
                                    type: string
                                    example: kktv.animepass.cc.annual.launchpromo.688
                                  payment_type:
                                    type: string
                                    description: credit_card | iap | iab
                                  auto_renew:
                                    type: boolean
                                    description: auto renew or not
                                  as_subscribe:
                                    type: boolean
                                    description: as subscribe or not
                                  bundle:
                                    type: object
                            sort:
                              type: integer
                              description: the sort of package
                            pay_duration:
                              type: string
                              example: 月繳
                            category:
                              type: array
                              items:
                                type: string
                                example: 單人獨享
                            original_price:
                              type: string
                              example: '688'
                            layout:
                              type: object
                              properties:
                                title:
                                  type: string
                                  example: 動漫PASS 年繳$688
                                subtitle:
                                  type: string
                                  example: 動漫迷專屬的輕量訂閱方案
                                description:
                                  type: string
                                  example: 解鎖動漫分類內容\n無廣告高畫質追新番\n優質VIP體驗每天不到2元
                                package_description:
                                  type: string
                                  example: 首賣限時優惠期間：2024/07/01～2024/08....
                                text_color:
                                  type: string
                                  example: '#0f465a'
                                campaign_image:
                                  type: string
                                  example: 'https://images.kktv.com.tw/product/campaign_175_1719190956.png'
                                background_image:
                                  type: string
                                  example: 'https://images.kktv.com.tw/product/bkimage_175_1718968066.jpeg'
                                background_color:
                                  type: string
                                  example: '#0f465a'
    parameters:
      - schema:
          type: string
          example: web | campaign | ios | android
        in: query
        name: platform
        required: true
        description: the platform of package belonging
  /v3/playback_tokens:
    post:
      summary: v3/ Request a playback token
      operationId: post-v3-playback_token
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                x-examples:
                  Example 1:
                    status:
                      type: OK
                      subtype: null
                      message: null
                    data:
                      user_id: 32909d04-4f74-485f-8b9c-20ebc2e4e576
                      device_id: ''
                      medium: SVOD
                      title_id: '01000254'
                      episode_id: '01000254010001'
                      key_id: 6fb6737fa7a024a68c313e5482863d0a
                      playback_token: 399a0d7f022d46a0a4e4c430d93fa1094c3b10e05a6a40c0b40b1f974d90cefd
                      created_at: 1725260478
                      expires_at: 1725346878
                properties:
                  status:
                    $ref: '#/components/schemas/V3ErrStatus'
                  data:
                    type: object
                    properties:
                      user_id:
                        type: string
                      device_id:
                        type: string
                      medium:
                        type: string
                      title_id:
                        type: string
                      episode_id:
                        type: string
                      key_id:
                        type: string
                      playback_token:
                        type: string
                      created_at:
                        type: integer
                      expires_at:
                        type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                title_id:
                  type: string
                  example: '01000254'
                episode_id:
                  type: string
                  example: '01000254010001'
                medium:
                  type: string
                purpose:
                  type: string
                  enum:
                    - playback
                    - download
                  default: playback
                  nullable: true
        description: ''
      parameters:
        - $ref: '#/components/parameters/HeaderDeviceID'
      tags:
        - PLAYBACK
  /v4/trials/playback/tokens:
    post:
      summary: '[WIP] v4/ Trial create playback token'
      operationId: post-v4-trials-playback-token
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                x-examples:
                  Example 1:
                    status:
                      type: OK
                      subtype: null
                      message: null
                    data:
                      user_id: 32909d04-4f74-485f-8b9c-20ebc2e4e576
                      device_id: ''
                      medium: SVOD
                      title_id: '01000254'
                      episode_id: '01000254010001'
                      key_id: 6fb6737fa7a024a68c313e5482863d0a
                      playback_token: 399a0d7f022d46a0a4e4c430d93fa1094c3b10e05a6a40c0b40b1f974d90cefd
                      created_at: 1725260478
                      expires_at: 1725346878
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    properties:
                      user_id:
                        type: string
                      device_id:
                        type: string
                      playback_token:
                        type: string
                      created_at:
                        $ref: '#/components/schemas/DateTime'
                      expires_at:
                        $ref: '#/components/schemas/DateTime'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                episode_id:
                  type: string
                  example: '01000254010001'
              required:
                - episode_id
        description: ''
      parameters:
        - in: header
          name: X-Device-ID
          schema:
            type: string
            example: ltv_12345-678901-0000
          description: 'the device id, should be with prefix `ltv_`'
      tags:
        - TRIAL
      description: |-
        acquire a playback token that is needed for license request.
        ```bash
        curl 'https://drm.xxx.com/api/drm/license' \
          -H 'accept: */*' \
          -H 'origin: https://test-web.kktv.com.tw' \
          //...
          -H 'x-custom-data: token_type=playback&token_value={playback_token}&device_id={device_id}&client_platform={platform}' \
          -H 'x-kk-tenant-id: {tenant_id}' \
          --data-raw $'\u0008\u0004'
        ```
    parameters: []
  '/vendors/linetv/users/{userID}/service-status':
    parameters:
      - schema:
          type: string
        name: userID
        in: path
        required: true
    get:
      summary: '[WIP] LineTV: Get user''s service status'
      operationId: get-vendors-linetv-users-userID-service-status
      responses:
        '200':
          description: ok
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    properties:
                      membership_role:
                        $ref: ./schemas/member-role.yaml
                      expired_at:
                        $ref: '#/components/schemas/DateTime'
                      next_payment_at:
                        $ref: '#/components/schemas/DateTime'
                      is_in_subscription:
                        type: boolean
      parameters: []
      security:
        - BasicAuth: []
      tags:
        - VENDOR-LINETV
        - USER
  '/vendors/linetv/episodes/{episodeID}/manifest':
    parameters:
      - schema:
          type: string
        name: episodeID
        in: path
        required: true
        description: the episode id
    post:
      summary: '[WIP] LineTV: to acquire the playback manifest and license token to play an episode'
      parameters:
        - schema:
            type: string
          in: header
          name: X-Device-ID
          description: 'the device id, should be with prefix `ltv_`'
          required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    $ref: '#/components/schemas/ErrInfo'
                  data:
                    type: object
                    properties:
                      streaming_assets:
                        type: object
                        properties:
                          default_subtitle:
                            type: string
                            example: zh-Hant
                          subtitle_url:
                            type: object
                            additionalProperties:
                              type: string
                            example:
                              zh-Hant: 'https://kktv.cdn.net/fake/00000452010026_sub_sub/zh-Hant.vtt'
                          thumbnail_url:
                            type: string
                            example: 'https://theater.kktv.com.tw/fake/00000452010026_10bb3ee92eaa3/thumbnail.vtt'
                          dash:
                            type: object
                            properties:
                              url:
                                type: string
                                example: 'https://theater-kktv.cdn.fake.net/fake/000_dash.playback_p4.mpd'
                              size:
                                type: number
                                example: 12676649509.824299
                          hls:
                            type: object
                            properties:
                              url:
                                type: string
                                example: 'https://theater-kktv.cdn.fake.net/fake/00000452010/xxxx_hls.playback_p4.m3u8'
                              size:
                                type: number
                                example: 12676649509.824299
                          supported_quality:
                            type: array
                            items:
                              type: string
                              enum:
                                - 1080p
                                - 720p
                                - 480p
                                - 360p
                                - 240p
                      license_url:
                        type: object
                        required:
                          - fairplay
                          - fairplay_cert
                          - playready
                          - widevine
                        properties:
                          fairplay:
                            type: string
                            example: 'https://test-license.kktv.com.tw'
                          fairplay_cert:
                            type: string
                            example: 'https://test-license.kktv.com.tw/fairplay_cert'
                          playready:
                            type: string
                            example: 'https://drm.platform.blendvision.com/api/v3/drm/license'
                          widevine:
                            type: string
                            example: 'https://drm.platform.blendvision.com/api/v3/drm/license'
                      playback_token:
                        type: object
                        required:
                          - token
                          - created_at
                          - expires_at
                        properties:
                          token:
                            type: string
                          created_at:
                            $ref: '#/components/schemas/DateTime'
                          expires_at:
                            $ref: '#/components/schemas/DateTime'
                      license_headers:
                        type: array
                        description: should pass all the key value pairs into the request header of DRM license
                        items:
                          type: object
                          properties:
                            key:
                              type: string
                              example: X-Kk-Tenant-Id
                            value:
                              type: string
                              example: '123456789'
                            for:
                              type: array
                              description: only apply the item into http headers when client's DRM solution matches any value within `for`
                              items:
                                type: string
                                enum:
                                  - fairplay
                                  - widevine
                                  - playready
                              example:
                                - widevine
                                - playready
                    required:
                      - streaming_assets
                      - license_url
                      - playback_token
      operationId: post-vendors-linetv-episodes-episodeID-manifest
      security:
        - BasicAuth: []
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                user_id:
                  type: string
                  description: 'must start with `ltv_`'
                quality:
                  type: string
                  enum:
                    - p5
                    - p4
                    - p3
                    - p2
                    - p1
                subtitles:
                  type: boolean
                  description: 'Whether to include subtitles in the video playback: 0 represents for without subtitle'
                purpose:
                  type: string
                  enum:
                    - playback
                    - download
                  default: playback
                is_avod:
                  type: boolean
                  description: 'whether this playback is for AVOD or not'
                  default: false
              required:
                - user_id
                - quality
      tags:
        - VENDOR-LINETV
        - PLAYBACK
