title: SEOTitleMeta
type: object
properties:
  id:
    type: string
    example: '09000259'
  title_type:
    type: string
    example: miniseries
    enum:
      - miniseries
      - series
      - film
  title:
    type: string
    example: 測試用絕對達令
  cover:
    type: string
    example: 'https://images.kktv.com.tw/covers/d9/d92243f54afe77a56cd1cd6a5f6944b752cdef5b.xs.jpg'
  series:
    type: array
    items:
      type: object
      properties:
        id:
          type: string
          example: '0200005701'
        title:
          type: string
          example: 第1季
        episodes:
          type: array
          items:
            type: object
            properties:
              id:
                type: string
                example: '09000652010001'
              title:
                type: string
                example: 第1集
              series_title:
                type: string
                example: 證人
              still:
                type: string
                example: 'https://image.kktv.com.tw/stills/07/07250ad4e00f46f19998023ff3b0906afe2e31fb.xs.jpg'
              duration:
                type: number
                example: 7759.381333
              publish_date:
                type: integer
                example: **********
                description: timestamp of the publish
  summary:
    type: string
  country:
    type: string
  directors:
    type: array
    items:
      type: string
      example: 鄭正華
  casts:
    type: array
    items:
      type: string
      example:
        - 呂珍九
        - 方珉雅
  content_providers:
    type: array
    items:
      type: string
      example: jtbc
  ost:
    type: object
    properties:
      artist_name:
        type: string
        example: Aimer
      image:
        type: string
        example: 'https://i.kfs.io/album/global/145608529,2v1/fit/500x500.jpg'
      title:
        type: string
        example: 片頭曲：残響散歌
      url:
        type: string
        example: 'https://www.kkbox.com/tw/tc/album/95sK.7bsEAKAh0F8fSxH009H-index.html?album=www.kkbox.com%2Ftw%2Ftc%2Falbum%2F95sK.7bsEAKAh0F8fSxH009H-index.html'
  user_rating:
    type: number
    example: 3.1415
    minimum: 0
  user_rating_count:
    type: integer
    example: 999
    minimum: 0
  total_episode_counts:
    type: object
    example:
      '**********': 40
  publish_date:
    type: integer
    example: **********
  wiki_zh:
    type: string
  title_extra:
    $ref: ./seo-title-extra.yaml
  child_lock:
    type: boolean
  copyright:
    type: string
  release_year:
    type: integer
  genres:
    type: array
    items:
      type: string
  writers:
    type: array
    items:
      type: string
  is_containing_avod:
    type: boolean
  title_aliases:
    type: array
    items:
      type: string
  themes:
    type: array
    items:
      type: string
  tags:
    type: array
    items:
      type: string
