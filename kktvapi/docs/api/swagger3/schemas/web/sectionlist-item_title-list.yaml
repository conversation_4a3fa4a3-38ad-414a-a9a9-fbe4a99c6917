title: WebSectionlistItemTitleList
allOf:
  - $ref: ../sectionlist-item.yaml
  - type: object
    properties:
      title_list_id:
        type: string
        example: '12345'
      style:
        type: string
        example: titlelist
      display_name:
        type: string
        example: 全劇新上架
      items:
        type: array
        items:
          allOf:
            - $ref: ../title-basic-meta.yaml
            - $ref: ../listed-title-meta.yaml
            - $ref: ./listed-title-meta.yaml
    required:
      - title_list_id
      - style
      - display_name
      - items
