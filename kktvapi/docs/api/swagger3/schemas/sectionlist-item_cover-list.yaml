title: SectionlistItemCoverList
allOf:
  - properties:
      style:
        type: string
        example: cover_list
      display_name:
        type: string
        example: 動畫新番表
      items:
        type: array
        items:
          type: object
          description: CoverItem
          properties:
            description:
              type: string
              example: 共10部
            deeplink:
              type: string
              example: 'https://www.kktv.me/titleList/fakeurl'
            image:
              type: string
            id:
              type: string
      deeplink:
        type: string
description: ''
type: object
