title: LastPlayedEpisode
type: object
properties:
  id:
    type: string
  name:
    type: string
  is_completed:
    type: boolean
    description: the episode is completely watched by the user or not
  duration:
    type: number
    description: 'the whole vidoe duration, in second'
    minimum: 0
    example: 4127.29
  played_offset:
    type: number
    description: the offset second that user was watching at last time
    minimum: 0
    example: 93
  still:
    type: string
  is_avod:
    type: boolean
  deeplink:
    type: string
    example: 'https://www.kktv.me/play/01060429?offset=300'
  user_authority:
    $ref: ./authority.yaml
