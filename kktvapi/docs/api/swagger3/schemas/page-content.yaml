title: PageContent
type: object
x-tags:
  - PAGE
properties:
  content_sections:
    type: array
    items:
      oneOf:
        - $ref: ./sectionlist-item_headline.yaml
        - $ref: ./sectionlist-item_announcement.yaml
        - $ref: ./sectionlist-item_filter.yaml
        - $ref: ./sectionlist-item_highlight.yaml
        - $ref: ./sectionlist-item_watch-history.yaml
        - $ref: ./sectionlist-item_ranking.yaml
        - $ref: ./sectionlist-item_title-list.yaml
        - $ref: ./sectionlist-item_cover-list.yaml
        - $ref: ./sectionlist-item_banner.yaml
        - $ref: ./sectionlist-item_coldstart-title-list.yaml
required:
  - content_sections
