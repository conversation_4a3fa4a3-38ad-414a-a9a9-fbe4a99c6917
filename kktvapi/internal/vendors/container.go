package vendors

import (
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/vendors/feversocial"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/vendors/linetv"
)

type Handlers struct {
	FeverSocial *feversocial.Handler
	LineTV      *linetv.Handler
}

func NewHandlers() *Handlers {
	return &Handlers{
		FeverSocial: feversocial.NewFeverSocialHandler(), // 發燒網
<<<<<<< HEAD
		LineTV:      linetv.NewHandler(),                 // LineTV
=======
		LineTV:      linetv.NewHandler(),                 // LINE tv
>>>>>>> feature/KKTV-15426-add-service-status-api-for-linetv
	}
}
