// Code generated by MockGen. DO NOT EDIT.
// Source: service.go

// Package redeem is a generated GoMock package.
package redeem

import (
	reflect "reflect"

	billing "github.com/KKTV/kktv-api-v3/pkg/billing"
	dbredeem "github.com/KKTV/kktv-api-v3/pkg/model/dbredeem"
	dbuser "github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	gomock "github.com/golang/mock/gomock"
)

// MockService is a mock of Service interface.
type MockService struct {
	ctrl     *gomock.Controller
	recorder *MockServiceMockRecorder
}

// MockServiceMockRecorder is the mock recorder for MockService.
type MockServiceMockRecorder struct {
	mock *MockService
}

// NewMockService creates a new mock instance.
func NewMockService(ctrl *gomock.Controller) *MockService {
	mock := &MockService{ctrl: ctrl}
	mock.recorder = &MockServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockService) EXPECT() *MockServiceMockRecorder {
	return m.recorder
}

// CreateOrder mocks base method.
func (m *MockService) CreateOrder(user *dbuser.User, group *dbredeem.CouponGroup, code *dbredeem.CouponCode) (*dbuser.Order, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOrder", user, group, code)
	ret0, _ := ret[0].(*dbuser.Order)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateOrder indicates an expected call of CreateOrder.
func (mr *MockServiceMockRecorder) CreateOrder(user, group, code interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOrder", reflect.TypeOf((*MockService)(nil).CreateOrder), user, group, code)
}

// GetCodeRedemptionCountByUser mocks base method.
func (m *MockService) GetCodeRedemptionCountByUser(code, uid string) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCodeRedemptionCountByUser", code, uid)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCodeRedemptionCountByUser indicates an expected call of GetCodeRedemptionCountByUser.
func (mr *MockServiceMockRecorder) GetCodeRedemptionCountByUser(code, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCodeRedemptionCountByUser", reflect.TypeOf((*MockService)(nil).GetCodeRedemptionCountByUser), code, uid)
}

// GetCodeUsedCountByGroupAndUser mocks base method.
func (m *MockService) GetCodeUsedCountByGroupAndUser(code, uid string) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCodeUsedCountByGroupAndUser", code, uid)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCodeUsedCountByGroupAndUser indicates an expected call of GetCodeUsedCountByGroupAndUser.
func (mr *MockServiceMockRecorder) GetCodeUsedCountByGroupAndUser(code, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCodeUsedCountByGroupAndUser", reflect.TypeOf((*MockService)(nil).GetCodeUsedCountByGroupAndUser), code, uid)
}

// GetCouponCode mocks base method.
func (m *MockService) GetCouponCode(code string) (*dbredeem.CouponCode, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCouponCode", code)
	ret0, _ := ret[0].(*dbredeem.CouponCode)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCouponCode indicates an expected call of GetCouponCode.
func (mr *MockServiceMockRecorder) GetCouponCode(code interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCouponCode", reflect.TypeOf((*MockService)(nil).GetCouponCode), code)
}

// GetCouponGroup mocks base method.
func (m *MockService) GetCouponGroup(code string) (*dbredeem.CouponGroup, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCouponGroup", code)
	ret0, _ := ret[0].(*dbredeem.CouponGroup)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCouponGroup indicates an expected call of GetCouponGroup.
func (mr *MockServiceMockRecorder) GetCouponGroup(code interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCouponGroup", reflect.TypeOf((*MockService)(nil).GetCouponGroup), code)
}

// GetPaymentInfoByUserID mocks base method.
func (m *MockService) GetPaymentInfoByUserID(uid string) (*dbuser.PaymentInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPaymentInfoByUserID", uid)
	ret0, _ := ret[0].(*dbuser.PaymentInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPaymentInfoByUserID indicates an expected call of GetPaymentInfoByUserID.
func (mr *MockServiceMockRecorder) GetPaymentInfoByUserID(uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPaymentInfoByUserID", reflect.TypeOf((*MockService)(nil).GetPaymentInfoByUserID), uid)
}

// GetPaymentInfoFromBilling mocks base method.
func (m *MockService) GetPaymentInfoFromBilling(userID string) (*billing.CustomerContractData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPaymentInfoFromBilling", userID)
	ret0, _ := ret[0].(*billing.CustomerContractData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPaymentInfoFromBilling indicates an expected call of GetPaymentInfoFromBilling.
func (mr *MockServiceMockRecorder) GetPaymentInfoFromBilling(userID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPaymentInfoFromBilling", reflect.TypeOf((*MockService)(nil).GetPaymentInfoFromBilling), userID)
}

// GetUser mocks base method.
func (m *MockService) GetUser(uid string) (*dbuser.User, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUser", uid)
	ret0, _ := ret[0].(*dbuser.User)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUser indicates an expected call of GetUser.
func (mr *MockServiceMockRecorder) GetUser(uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUser", reflect.TypeOf((*MockService)(nil).GetUser), uid)
}

// IsUserUseTheSameCampaignRedeem mocks base method.
func (m *MockService) IsUserUseTheSameCampaignRedeem(campaingGroup, uid string) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsUserUseTheSameCampaignRedeem", campaingGroup, uid)
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsUserUseTheSameCampaignRedeem indicates an expected call of IsUserUseTheSameCampaignRedeem.
func (mr *MockServiceMockRecorder) IsUserUseTheSameCampaignRedeem(campaingGroup, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsUserUseTheSameCampaignRedeem", reflect.TypeOf((*MockService)(nil).IsUserUseTheSameCampaignRedeem), campaingGroup, uid)
}

// MarkCodeAsRedeemed mocks base method.
func (m *MockService) MarkCodeAsRedeemed(group *dbredeem.CouponGroup, code *dbredeem.CouponCode, user *dbuser.User) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MarkCodeAsRedeemed", group, code, user)
	ret0, _ := ret[0].(error)
	return ret0
}

// MarkCodeAsRedeemed indicates an expected call of MarkCodeAsRedeemed.
func (mr *MockServiceMockRecorder) MarkCodeAsRedeemed(group, code, user interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MarkCodeAsRedeemed", reflect.TypeOf((*MockService)(nil).MarkCodeAsRedeemed), group, code, user)
}

// RealizeOrder mocks base method.
func (m *MockService) RealizeOrder(order *dbuser.Order, user *dbuser.User) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RealizeOrder", order, user)
	ret0, _ := ret[0].(error)
	return ret0
}

// RealizeOrder indicates an expected call of RealizeOrder.
func (mr *MockServiceMockRecorder) RealizeOrder(order, user interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RealizeOrder", reflect.TypeOf((*MockService)(nil).RealizeOrder), order, user)
}

// RedeemBillingEdenredCode mocks base method.
func (m *MockService) RedeemBillingEdenredCode(redeemCode, userID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RedeemBillingEdenredCode", redeemCode, userID)
	ret0, _ := ret[0].(error)
	return ret0
}

// RedeemBillingEdenredCode indicates an expected call of RedeemBillingEdenredCode.
func (mr *MockServiceMockRecorder) RedeemBillingEdenredCode(redeemCode, userID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RedeemBillingEdenredCode", reflect.TypeOf((*MockService)(nil).RedeemBillingEdenredCode), redeemCode, userID)
}

// SendFacebookRedeemCompleteConversionEvent mocks base method.
func (m *MockService) SendFacebookRedeemCompleteConversionEvent(user *dbuser.User, order *dbuser.Order, group *dbredeem.CouponGroup, code *dbredeem.CouponCode) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendFacebookRedeemCompleteConversionEvent", user, order, group, code)
}

// SendFacebookRedeemCompleteConversionEvent indicates an expected call of SendFacebookRedeemCompleteConversionEvent.
func (mr *MockServiceMockRecorder) SendFacebookRedeemCompleteConversionEvent(user, order, group, code interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendFacebookRedeemCompleteConversionEvent", reflect.TypeOf((*MockService)(nil).SendFacebookRedeemCompleteConversionEvent), user, order, group, code)
}

// UpsertPaymentInfoFromBilling mocks base method.
func (m *MockService) UpsertPaymentInfoFromBilling(contract billing.Contract, userID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertPaymentInfoFromBilling", contract, userID)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpsertPaymentInfoFromBilling indicates an expected call of UpsertPaymentInfoFromBilling.
func (mr *MockServiceMockRecorder) UpsertPaymentInfoFromBilling(contract, userID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertPaymentInfoFromBilling", reflect.TypeOf((*MockService)(nil).UpsertPaymentInfoFromBilling), contract, userID)
}

// ValidateRedeemCode mocks base method.
func (m *MockService) ValidateRedeemCode(user *dbuser.User, group *dbredeem.CouponGroup, code *dbredeem.CouponCode) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateRedeemCode", user, group, code)
	ret0, _ := ret[0].(error)
	return ret0
}

// ValidateRedeemCode indicates an expected call of ValidateRedeemCode.
func (mr *MockServiceMockRecorder) ValidateRedeemCode(user, group, code interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateRedeemCode", reflect.TypeOf((*MockService)(nil).ValidateRedeemCode), user, group, code)
}

// ValidateRedeemEdenredCode mocks base method.
func (m *MockService) ValidateRedeemEdenredCode(user *dbuser.User) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateRedeemEdenredCode", user)
	ret0, _ := ret[0].(error)
	return ret0
}

// ValidateRedeemEdenredCode indicates an expected call of ValidateRedeemEdenredCode.
func (mr *MockServiceMockRecorder) ValidateRedeemEdenredCode(user interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateRedeemEdenredCode", reflect.TypeOf((*MockService)(nil).ValidateRedeemEdenredCode), user)
}
