package redeem

import (
	"errors"
	"testing"
	"time"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/redeem"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/user"
	"github.com/KKTV/kktv-api-v3/pkg/billing"
	"github.com/KKTV/kktv-api-v3/pkg/database"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbredeem"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
	"gopkg.in/guregu/null.v3"
)

type RealizeOrderTestSuite struct {
	suite.Suite
	mockCtrl          *gomock.Controller
	service           *service
	userRepo          *user.MockUserRepository
	orderRepo         *user.MockOrderRepository
	paymentInfoRepo   *user.MockPaymentInfoRepository
	couponCodeRepo    *redeem.MockCouponCodeRepository
	couponGroupRepo   *redeem.MockCouponGroupRepository
	redemptionLogRepo *redeem.MockRedemptionLogRepository
	billingclient     *billing.MockClient
	mockTxBeginner    *database.MockTxBeginner
	mockTx            *database.MockTx
}

func (suite *RealizeOrderTestSuite) SetupTest() {
	suite.mockCtrl = gomock.NewController(suite.T())
	suite.userRepo = user.NewMockUserRepository(suite.mockCtrl)
	suite.orderRepo = user.NewMockOrderRepository(suite.mockCtrl)
	suite.paymentInfoRepo = user.NewMockPaymentInfoRepository(suite.mockCtrl)
	suite.mockTxBeginner = database.NewMockTxBeginner(suite.mockCtrl)
	suite.couponCodeRepo = redeem.NewMockCouponCodeRepository(suite.mockCtrl)
	suite.couponGroupRepo = redeem.NewMockCouponGroupRepository(suite.mockCtrl)
	suite.redemptionLogRepo = redeem.NewMockRedemptionLogRepository(suite.mockCtrl)

	suite.billingclient = billing.NewMockClient(suite.mockCtrl)

	suite.mockTx = database.NewMockTx(suite.mockCtrl)
	suite.service = &service{
		txBeginner:        suite.mockTxBeginner,
		userRepo:          suite.userRepo,
		orderRepo:         suite.orderRepo,
		paymentInfoRepo:   suite.paymentInfoRepo,
		billingClient:     suite.billingclient,
		couponCodeRepo:    suite.couponCodeRepo,
		couponGroupRepo:   suite.couponGroupRepo,
		redemptionLogRepo: suite.redemptionLogRepo,
	}

}

func (suite *RealizeOrderTestSuite) TearDownTest() {
	suite.mockCtrl.Finish()
}

func (suite *RealizeOrderTestSuite) TestRealizeOrder() {
	order := &dbuser.Order{
		EndDate: time.Now().AddDate(0, 0, 1),
		Status:  null.NewString("", false),
	}
	mockUser := &dbuser.User{
		ID:        "12345678",
		Role:      string(dbuser.RolePremium),
		ExpiredAt: null.NewTime(time.Now().AddDate(0, 0, -1), true),
	}

	gomock.InOrder(
		suite.mockTxBeginner.EXPECT().Begin().Return(suite.mockTx, nil),
		suite.orderRepo.EXPECT().WithTx(suite.mockTx).Return(suite.orderRepo),
		suite.orderRepo.EXPECT().Update(order).Return(nil),
		suite.userRepo.EXPECT().WithTx(suite.mockTx).Return(suite.userRepo),
		suite.userRepo.EXPECT().UpdateByFields(mockUser.ID, gomock.Any()).Return(true, nil),
		suite.paymentInfoRepo.EXPECT().WithTx(suite.mockTx).Return(suite.paymentInfoRepo),
		suite.paymentInfoRepo.EXPECT().Upsert(gomock.Any()).Return(nil),
		suite.mockTx.EXPECT().Commit().Return(nil),
	)

	err := suite.service.RealizeOrder(order, mockUser)
	suite.NoError(err)
	suite.Equal("ok", order.Status.String)
	suite.WithinDuration(time.Now().UTC().Truncate(time.Second), order.RealizedAt.Time, time.Second)
	suite.Equal(string(dbuser.RolePremium), mockUser.Role)

}

func (s *RealizeOrderTestSuite) TestRealizeOrderBeginTxFail() {
	s.mockTxBeginner.EXPECT().Begin().Return(nil, errors.New("failed to begin transaction"))

	err := s.service.RealizeOrder(&dbuser.Order{}, &dbuser.User{})
	s.Error(err)
	s.EqualError(err, "failed to begin transaction")
}

func (s *RealizeOrderTestSuite) TestRealizeOrderUpdateUserFail() {
	s.mockTxBeginner.EXPECT().Begin().Return(s.mockTx, nil)
	s.orderRepo.EXPECT().WithTx(gomock.Any()).Return(s.orderRepo)
	s.orderRepo.EXPECT().Update(gomock.Any()).Return(nil)
	s.userRepo.EXPECT().WithTx(gomock.Any()).Return(s.userRepo)
	s.userRepo.EXPECT().UpdateByFields(gomock.Any(), gomock.Any()).Return(false, errors.New("failed to update user"))
	s.mockTx.EXPECT().Rollback().Return(nil)

	err := s.service.RealizeOrder(&dbuser.Order{}, &dbuser.User{})
	s.Error(err)
}

func (s *RealizeOrderTestSuite) TestValidateRedeemCode() {

	user := dbuser.User{
		ID:        "123456",
		AutoRenew: false,
	}
	user.Membership.AddRole(dbuser.RoleModel{
		Role: dbuser.MemberRoleExpired,
	})

	redeemGroup := dbredeem.CouponGroup{
		ID:        1,
		ExpiresAt: time.Now().AddDate(0, 1, 0),
	}
	code := dbredeem.CouponCode{
		Code: "1234567",
	}

	s.couponGroupRepo.EXPECT().CountByCampaignGroupAndUser(gomock.Eq(redeemGroup.ID), gomock.Eq(user.ID)).Return(int64(1), nil).AnyTimes()
	s.redemptionLogRepo.EXPECT().CountByCodeAndUser(code.Code, user.ID).Return(int64(1), nil).AnyTimes()
	s.couponCodeRepo.EXPECT().CountByGroupAndUser(code.Code, user.ID).Return(int64(1), nil).AnyTimes()
	r := billing.Response{}
	r.Status.Code = "200"
	s.billingclient.EXPECT().GetPaymentDecision(gomock.Any()).Return(r, nil).AnyTimes()

	err := s.service.ValidateRedeemCode(&user, &redeemGroup, &code)
	s.NoError(err)
}

func TestSuite(t *testing.T) {
	suite.Run(t, new(RealizeOrderTestSuite))
}
