package manifest

import "github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"

type manifestsResp struct {
	Episodes       []Mezzanine       `json:"episodes"`
	LicenseURL     map[string]string `json:"license_url"`
	LicenseHeaders []headerItem      `json:"license_headers"`
}

type headerItem struct {
	Key   string   `json:"key"`
	Value string   `json:"value"`
	For   []string `json:"for,omitempty"`
}

type trialMezzanine struct {
	EpisodeID       string               `json:"episode_id"`
	DefaultSubtitle string               `json:"default_subtitle"`
	SubtitleURL     map[string]string    `json:"subtitle_url"`
	ThumbnailURL    string               `json:"thumbnail_url,omitempty"`
	Dash            dbmeta.MezzanineFile `json:"dash"`
	Hls             dbmeta.MezzanineFile `json:"hls"`
}

type trialManifestsResp struct {
	Episode        trialMezzanine    `json:"episode"`
	LicenseURL     map[string]string `json:"license_url"`
	LicenseHeaders []headerItem      `json:"license_headers"`
}
