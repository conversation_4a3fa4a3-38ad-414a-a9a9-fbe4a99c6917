// Code generated by MockGen. DO NOT EDIT.
// Source: legacy.go

// Package manifest is a generated GoMock package.
package manifest

import (
	reflect "reflect"

	dbmeta "github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	gomock "github.com/golang/mock/gomock"
)

// MockLegacyHelper is a mock of LegacyHelper interface.
type MockLegacyHelper struct {
	ctrl     *gomock.Controller
	recorder *MockLegacyHelperMockRecorder
}

// MockLegacyHelperMockRecorder is the mock recorder for MockLegacyHelper.
type MockLegacyHelperMockRecorder struct {
	mock *MockLegacyHelper
}

// NewMockLegacyHelper creates a new mock instance.
func NewMockLegacyHelper(ctrl *gomock.Controller) *MockLegacyHelper {
	mock := &MockLegacyHelper{ctrl: ctrl}
	mock.recorder = &MockLegacyHelperMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLegacyHelper) EXPECT() *MockLegacyHelperMockRecorder {
	return m.recorder
}

// GetEpisodeByID mocks base method.
func (m *MockLegacyHelper) GetEpisodeByID(epID string) (*dbmeta.EpisodeMeta, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEpisodeByID", epID)
	ret0, _ := ret[0].(*dbmeta.EpisodeMeta)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEpisodeByID indicates an expected call of GetEpisodeByID.
func (mr *MockLegacyHelperMockRecorder) GetEpisodeByID(epID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEpisodeByID", reflect.TypeOf((*MockLegacyHelper)(nil).GetEpisodeByID), epID)
}

// GetManifests mocks base method.
func (m *MockLegacyHelper) GetManifests(epIDs []string, device, quality, purpose, platform string, withSubtitle bool) ([]Mezzanine, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetManifests", epIDs, device, quality, purpose, platform, withSubtitle)
	ret0, _ := ret[0].([]Mezzanine)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetManifests indicates an expected call of GetManifests.
func (mr *MockLegacyHelperMockRecorder) GetManifests(epIDs, device, quality, purpose, platform, withSubtitle interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetManifests", reflect.TypeOf((*MockLegacyHelper)(nil).GetManifests), epIDs, device, quality, purpose, platform, withSubtitle)
}

// GetManifestsByTargetFile mocks base method.
func (m *MockLegacyHelper) GetManifestsByTargetFile(epIDs []string, targetFile, purpose string, withSubtitle bool) ([]Mezzanine, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetManifestsByTargetFile", epIDs, targetFile, purpose, withSubtitle)
	ret0, _ := ret[0].([]Mezzanine)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetManifestsByTargetFile indicates an expected call of GetManifestsByTargetFile.
func (mr *MockLegacyHelperMockRecorder) GetManifestsByTargetFile(epIDs, targetFile, purpose, withSubtitle interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetManifestsByTargetFile", reflect.TypeOf((*MockLegacyHelper)(nil).GetManifestsByTargetFile), epIDs, targetFile, purpose, withSubtitle)
}
