package manifest

import (
	"crypto/md5"
	"encoding/base64"
	"fmt"
	"net/url"
	"strings"
	"time"
)

// withSignature add token and expire query parameter
// refer to manual handbook in the hinet CDN Portal (https://portal.cdn.hinet.net) > 技術支援 > Token保護使用手冊
func withSignature(cdnPath, secret string, now time.Time) (string, error) {
	parsedURL, err := url.Parse(cdnPath)
	if err != nil {
		return "", err
	}
	expireTime := now.Add(24 * time.Hour)
	expireTimestamp := fmt.Sprintf("%d", expireTime.Unix())

	// generate token
	path := parsedURL.RequestURI()
	md5Bytes := md5.Sum([]byte(secret + expireTimestamp + path)) // refer to
	token := base64.StdEncoding.EncodeToString(md5Bytes[:])
	token = strings.Replace(token, "+", "-", -1)
	token = strings.Replace(token, "/", "_", -1)
	token = strings.Replace(token, "=", "", -1)

	// with query parameter
	q := parsedURL.Query()
	q.Set("t", token)
	q.Set("e", expireTimestamp)
	parsedURL.RawQuery = q.Encode()
	return parsedURL.String(), nil
}
