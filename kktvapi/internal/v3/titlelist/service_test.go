package titlelist

import (
	"testing"

	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/deeplink"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/meta"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/v4/coldstart"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/v4/presenter"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/collection"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

type ServiceSuite struct {
	suite.Suite
	ctrl                 *gomock.Controller
	mockRepo             *meta.MockTitlelistRepository
	mockTitleRepo        *meta.MockTitleRepository
	mockColdStartService *coldstart.MockService
	mockCache            *cache.MockCacher

	service Service

	assert *require.Assertions
}

func TestServiceSuite(t *testing.T) {
	suite.Run(t, new(ServiceSuite))
}

func (suite *ServiceSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
	suite.mockRepo = meta.NewMockTitlelistRepository(suite.ctrl)
	suite.mockTitleRepo = meta.NewMockTitleRepository(suite.ctrl)
	suite.mockCache = cache.NewMockCacher(suite.ctrl)
	suite.mockColdStartService = coldstart.NewMockService(suite.ctrl)
	suite.service = &service{
		repo:             suite.mockRepo,
		titleRepo:        suite.mockTitleRepo,
		coldStartService: suite.mockColdStartService,
		metaCache:        suite.mockCache,
	}
	suite.assert = suite.Require()
}

func (suite *ServiceSuite) TearDownTest() {
	defer suite.ctrl.Finish()
}

func (suite *ServiceSuite) TestGetPageByShareID() {
	var (
		shareID     = "josiesmile"
		dbTitleList = suite.aTitleList()
	)

	testcases := []struct {
		name    string
		shareID string
		given   func()
		then    func(page *PageInfo, fallback *presenter.Fallback)
	}{
		{
			name:    "found 5 titleList and return page info",
			shareID: shareID,
			given: func() {
				titleIDs := []string{"042901", "042902", "042903", "042904", "042905"}
				tl := *dbTitleList
				tl.Meta.TitleID = titleIDs
				tl.Meta.ShareId = shareID
				suite.mockRepo.EXPECT().GetByShareID(shareID).Return(&tl, nil)
			},
			then: func(page *PageInfo, fallback *presenter.Fallback) {
				suite.assert.Nil(fallback)

				suite.assert.Equal(shareID, page.ID)
				suite.assert.Equal(dbTitleList.Meta.TitleID, page.TitleIDs)
				suite.assert.Equal(dbTitleList.Caption, page.Name)
				suite.assert.Equal(dbTitleList.Meta.OGImage, page.OGImgURL)
				suite.assert.Equal(dbTitleList.Meta.BackgroundImageUrl, page.BgImgURL)
				suite.assert.Equal(dbTitleList.Meta.Copyright, page.Copyright)
				suite.assert.Equal(dbTitleList.Meta.Description, page.Description)
				suite.assert.Equal(deeplink.TitleListPageShareLink(dbTitleList.Meta.ShareId, dbTitleList.Caption), page.ShareLink)
			},
		},
		{
			name:    "found 3 titleList and return fallback",
			shareID: shareID,
			given: func() {
				titleIDs := []string{"042901", "042902", "042903"}
				tl := *dbTitleList
				tl.Meta.TitleID = titleIDs
				suite.mockRepo.EXPECT().GetByShareID(shareID).Return(&tl, nil)
				suite.mockGetDefaultFallbackTitlelistShareID()
			},
			then: suite.assertGotFallback("https://www.kktv.me/titleList/ranking", "喔噢...此片單已經下架"),
		},
	}
	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			defer suite.ctrl.Finish()

			tc.given()
			data, err := suite.service.GetPageByShareID(tc.shareID)
			tc.then(data, err)
		})
	}

}

func (suite *ServiceSuite) TestGetExpireSoonPage() {
	var (
		titleIDs = []string{"04290106", "01060429", "20220712"}
	)

	testcases := []struct {
		name    string
		shareID string
		given   func()
		then    func(page *PageInfo, fallback *presenter.Fallback)
	}{
		{
			name:    "invoke titleRepo.ListExpireSoon when filter is empty",
			shareID: "expiredsoon",
			given: func() {
				suite.mockTitleRepo.EXPECT().FindTitleIDsForExpireSoon(meta.TitleCondition{}).Return(titleIDs, nil)
			},
			then: func(page *PageInfo, fallback *presenter.Fallback) {
				suite.assert.Nil(fallback)

				suite.assert.Equal("expiredsoon", page.ID)
				suite.assert.Equal(titleIDs, page.TitleIDs)
				suite.assert.Equal("莫待過期無劇追", page.Name)
				link := deeplink.TitleListPageWithParam("expiredsoon", &deeplink.TitleListPageParam{
					Caption:     "莫待過期無劇追",
					IsShareLink: true,
				})
				suite.assert.Equal(link, page.ShareLink)
			},
		},
		{
			name:    "invoke titleRepo.ListExpireSoon when genre is 戲劇",
			shareID: "expiredsoon-genre:戲劇",
			given: func() {
				suite.mockTitleRepo.EXPECT().FindTitleIDsForExpireSoon(meta.TitleCondition{Genre: "戲劇"}).Return(titleIDs, nil)
			},
			then: func(page *PageInfo, fallback *presenter.Fallback) {
				suite.assert.Nil(fallback)

				suite.assert.Equal("expiredsoon-genre:戲劇", page.ID)
				suite.assert.Equal(titleIDs, page.TitleIDs)
				suite.assert.Equal("莫待過期無劇追", page.Name)
				link := deeplink.TitleListPageWithParam("expiredsoon-genre:戲劇", &deeplink.TitleListPageParam{
					Caption:     "莫待過期無劇追",
					IsShareLink: true,
				})
				suite.assert.Equal(link, page.ShareLink)
			},
		},
		{
			name:    "invoke titleRepo.ListExpireSoon with genre when query is content_agent=Medialink",
			shareID: "expiredsoon-content_agent:Medialink",
			given: func() {
				suite.mockTitleRepo.EXPECT().FindTitleIDsForExpireSoon(meta.TitleCondition{ContentAgent: "Medialink"}).Return([]string{}, nil)
				suite.mockGetDefaultFallbackTitlelistShareID()
			},
			then: suite.assertGotFallback("https://www.kktv.me/titleList/ranking", "近日沒有即將下架的內容"),
		},
		{
			name:    "invalid shareID",
			shareID: "expiredsoon-content_agent-采昌",
			given: func() {
				suite.mockGetDefaultFallbackTitlelistShareID()
			},
			then: suite.assertGotFallback("https://www.kktv.me/titleList/ranking", "喔噢...此片單已經下架"),
		},
	}
	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			defer suite.ctrl.Finish()

			tc.given()
			data, err := suite.service.GetExpireSoonPage(tc.shareID)
			tc.then(data, err)
		})
	}

}

func (suite *ServiceSuite) TestGetAiringAnimePage() {
	var (
		titleIDs = []string{"04290106", "01060429", "20220712"}
	)

	testcases := []struct {
		name  string
		given func()
		then  func(page *PageInfo, fallback *presenter.Fallback)
	}{
		{
			name: "invoke titleRepo.FindAiringTitleIDs and GetByShareID return nil",
			given: func() {
				suite.mockTitleRepo.EXPECT().FindAiringTitleIDs(meta.TitleCondition{Genre: collection.GenreNameAnime}).Return(titleIDs, nil)
				suite.mockRepo.EXPECT().GetByShareID("anime-airing").Return(nil, nil)
			},
			then: func(page *PageInfo, fallback *presenter.Fallback) {
				suite.assert.Nil(fallback)

				suite.assert.Equal("anime-airing", page.ID)
				suite.assert.Equal(titleIDs, page.TitleIDs)
				suite.assert.Equal("新番跟播時間表", page.Name)
				link := deeplink.TimelinePageWithParam("anime-airing", &deeplink.TitleListPageParam{
					Caption:     "新番跟播時間表",
					IsShareLink: true,
				})
				suite.assert.Equal(link, page.ShareLink)
			},
		},
		{
			name: "get airing titleIDs and GetByShareID return airing-anime setting",
			given: func() {
				suite.mockTitleRepo.EXPECT().FindAiringTitleIDs(meta.TitleCondition{Genre: collection.GenreNameAnime}).Return(titleIDs, nil)
				suite.mockRepo.EXPECT().GetByShareID("anime-airing").Return(&dbmeta.TitleList{
					Caption: "特別狠的跟播表",
					Meta: &dbmeta.TitleListMeta{
						ShareId:            "anime-airing",
						OGImage:            "http://fakeimg.com/josie-og.jpg",
						BackgroundImageUrl: "http://fakeimg.com/josie.jpg",
						Description:        "跟播表特別的敘述",
						OGDescription:      "跟播表OG的敘述",
					},
				}, nil)
			},
			then: func(page *PageInfo, fallback *presenter.Fallback) {
				suite.assert.Nil(fallback)

				suite.assert.Equal("anime-airing", page.ID)
				suite.assert.Equal(titleIDs, page.TitleIDs)
				suite.assert.Equal("特別狠的跟播表", page.Name)
				link := deeplink.TimelinePageWithParam("anime-airing", &deeplink.TitleListPageParam{
					Caption:     "新番跟播時間表",
					IsShareLink: true,
				})
				suite.assert.Equal(link, page.ShareLink)
			},
		},
		{
			name: "get airing titleIDs and GetByShareID return airing-anime setting with empty caption",
			given: func() {
				suite.mockTitleRepo.EXPECT().FindAiringTitleIDs(meta.TitleCondition{Genre: collection.GenreNameAnime}).Return(titleIDs, nil)
				suite.mockRepo.EXPECT().GetByShareID("anime-airing").Return(&dbmeta.TitleList{
					Caption: "",
					Meta: &dbmeta.TitleListMeta{
						ShareId:            "anime-airing",
						OGImage:            "http://fakeimg.com/josie-og.jpg",
						BackgroundImageUrl: "http://fakeimg.com/josie.jpg",
						Description:        "跟播表特別的敘述",
						OGDescription:      "跟播表OG的敘述",
					},
				}, nil)
			},
			then: func(page *PageInfo, fallback *presenter.Fallback) {
				suite.assert.Nil(fallback)

				suite.assert.Equal("anime-airing", page.ID)
				suite.assert.Equal(titleIDs, page.TitleIDs)
				suite.assert.Equal("新番跟播時間表", page.Name)
				link := deeplink.TimelinePageWithParam("anime-airing", &deeplink.TitleListPageParam{
					Caption:     "新番跟播時間表",
					IsShareLink: true,
				})
				suite.assert.Equal(link, page.ShareLink)
			},
		},
	}
	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			defer suite.ctrl.Finish()
			tc.given()
			data, err := suite.service.GetAiringAnime()
			tc.then(data, err)
		})
	}

}

func (suite *ServiceSuite) assertGotPageInfo(dbTitleList *dbmeta.TitleList) func(page *PageInfo, fallback *presenter.Fallback) {
	return func(page *PageInfo, fallback *presenter.Fallback) {
		suite.assert.Nil(fallback)

		suite.assert.Equal("animemonday", page.ID)
		suite.assert.Equal(dbTitleList.Meta.TitleID, page.TitleIDs)
		suite.assert.Equal(dbTitleList.Caption, page.Name)
		suite.assert.Equal(dbTitleList.Meta.OGImage, page.OGImgURL)
		suite.assert.Equal(dbTitleList.Meta.BackgroundImageUrl, page.BgImgURL)
		suite.assert.Equal(dbTitleList.Meta.Copyright, page.Copyright)
		suite.assert.Equal(dbTitleList.Meta.Description, page.Description)
		suite.assert.Equal(deeplink.TitleListPageShareLink(dbTitleList.Meta.ShareId, dbTitleList.Caption), page.ShareLink)
	}
}

func (suite *ServiceSuite) assertGotFallback(redirectURL, msg string) func(page *PageInfo, fallback *presenter.Fallback) {
	return func(page *PageInfo, fallback *presenter.Fallback) {
		suite.assert.Nil(page)
		suite.assert.EqualValues(redirectURL, fallback.RedirectURL)
		suite.assert.EqualValues(msg, fallback.Message)
	}
}

func (suite *ServiceSuite) mockGetDefaultFallbackTitlelistShareID() string {
	url := "https://www.kktv.me/titleList/ranking"
	suite.mockRepo.EXPECT().GetDefaultFallbackURL().Return(url, nil)
	return url
}

func (suite *ServiceSuite) aTitleList() *dbmeta.TitleList {
	return &dbmeta.TitleList{
		Caption: "Josie cutie",
		ID:      429,
		Meta: &dbmeta.TitleListMeta{
			ShareId:            "josiecute",
			Description:        "josie is cute",
			BackgroundImageUrl: "http://fakeimg.com/josie.jpg",
			Copyright:          "kktv",
			OGImage:            "http://fakeimg.com/josie-og.jpg",
		},
	}
}
