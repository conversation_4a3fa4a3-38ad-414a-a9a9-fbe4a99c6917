// Code generated by MockGen. DO NOT EDIT.
// Source: service.go

// Package paymentinfo is a generated GoMock package.
package paymentinfo

import (
	reflect "reflect"

	dbuser "github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	gomock "github.com/golang/mock/gomock"
)

// MockService is a mock of Service interface.
type MockService struct {
	ctrl     *gomock.Controller
	recorder *MockServiceMockRecorder
}

// MockServiceMockRecorder is the mock recorder for MockService.
type MockServiceMockRecorder struct {
	mock *MockService
}

// NewMockService creates a new mock instance.
func NewMockService(ctrl *gomock.Controller) *MockService {
	mock := &MockService{ctrl: ctrl}
	mock.recorder = &MockServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockService) EXPECT() *MockServiceMockRecorder {
	return m.recorder
}

// ClearMODSubscriberData mocks base method.
func (m *MockService) ClearMODSubscriberData(userID string) (*dbuser.PaymentInfo, *dbuser.PaymentInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClearMODSubscriberData", userID)
	ret0, _ := ret[0].(*dbuser.PaymentInfo)
	ret1, _ := ret[1].(*dbuser.PaymentInfo)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ClearMODSubscriberData indicates an expected call of ClearMODSubscriberData.
func (mr *MockServiceMockRecorder) ClearMODSubscriberData(userID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearMODSubscriberData", reflect.TypeOf((*MockService)(nil).ClearMODSubscriberData), userID)
}
