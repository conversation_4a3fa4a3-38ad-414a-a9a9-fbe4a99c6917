// Code generated by MockGen. DO NOT EDIT.
// Source: service.go

// Package product is a generated GoMock package.
package product

import (
	reflect "reflect"

	dbuser "github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	gomock "github.com/golang/mock/gomock"
)

// MockService is a mock of Service interface.
type MockService struct {
	ctrl     *gomock.Controller
	recorder *MockServiceMockRecorder
}

// MockServiceMockRecorder is the mock recorder for MockService.
type MockServiceMockRecorder struct {
	mock *MockService
}

// NewMockService creates a new mock instance.
func NewMockService(ctrl *gomock.Controller) *MockService {
	mock := &MockService{ctrl: ctrl}
	mock.recorder = &MockServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockService) EXPECT() *MockServiceMockRecorder {
	return m.recorder
}

// GetMODProductByOrderItem mocks base method.
func (m *MockService) GetMODProductByOrderItem(itemID, itemType string) (*dbuser.Product, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMODProductByOrderItem", itemID, itemType)
	ret0, _ := ret[0].(*dbuser.Product)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMODProductByOrderItem indicates an expected call of GetMODProductByOrderItem.
func (mr *MockServiceMockRecorder) GetMODProductByOrderItem(itemID, itemType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMODProductByOrderItem", reflect.TypeOf((*MockService)(nil).GetMODProductByOrderItem), itemID, itemType)
}
