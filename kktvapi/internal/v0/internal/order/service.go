//go:generate mockgen -source service.go -destination service_mock.go -package order
package order

import (
	"context"
	"fmt"

	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/product"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/v0/internal/errs"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/auditing"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/serials"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/wrapper"
	"github.com/KKTV/kktv-api-v3/pkg/amplitudelib"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/clock"
	"github.com/KKTV/kktv-api-v3/pkg/database"
	"github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
)

type Service interface {
	// Subscribe return errs.ErrNotFound if user not found
	// or return errs.ErrBadRequest if the request is invalid
	Subscribe(ctx context.Context, req *SubscribeReq) (*dbuser.Order, error)
}

type service struct {
	clock              clock.Clock
	userService        wrapper.UserService
	orderService       wrapper.OrderService
	paymentInfoService wrapper.PaymentInfoService
	auditLogRepo       auditing.LogRepository

	productRepo product.Repository

	amplitudeClient amplitudelib.Client
	serialGen       serials.Generator
	dbConn          database.TxBeginner
}

func NewService() Service {
	dbPool := container.DBPoolUser()
	userDBWriter := dbPool.Master().Unsafe()
	userDBReader := dbPool.Slave().Unsafe()
	redisPool := container.CachePoolUser()
	userCacheWriter := cache.New(redisPool.Master())
	userCacheReader := cache.New(redisPool.Slave())

	return &service{
		clock:              clock.New(),
		userService:        wrapper.NewUserService(userDBWriter),
		paymentInfoService: wrapper.NewPaymentInfoService(userDBWriter),
		orderService:       wrapper.NewOrderService(userDBReader, userDBWriter),
		productRepo:        product.NewRepository(),
		serialGen:          serials.NewGenerator(userCacheWriter, userCacheReader),
		amplitudeClient:    amplitudelib.NewClient(config.Env),
		auditLogRepo:       container.AuditingRepo(),
		dbConn:             &database.Conn{DB: userDBWriter},
	}
}

func (s *service) Subscribe(ctx context.Context, req *SubscribeReq) (*dbuser.Order, error) {
	// get user
	user, err := s.userService.GetByID(req.UserID)
	if err != nil {
		return nil, err
	} else if user == nil {
		return nil, fmt.Errorf("%w: user %s", errs.ErrNotFound, req.UserID)
	}
	// find product if exists
	pdt, err := s.productRepo.GetByName(req.ProductName)
	if err != nil {
		return nil, err
	} else if pdt == nil {
		return nil, fmt.Errorf("%w: product %s", errs.ErrNotFound, req.ProductName)
	}

	// check if user's order is autoRenew
	if pdt.AutoRenew && user.AutoRenew && !req.IsForce {
		return nil, fmt.Errorf("%w: user already subscribed: %s", errs.ErrBadRequest, req.UserID)
	}

	factory := subscriberFactory{
		srv:     s,
		user:    user,
		product: pdt,
		req:     req,
	}
	subr := factory.NewSubscriber()
	if subr == nil {
		return nil, fmt.Errorf("%w: not supported product subscription: %s", errs.ErrBadRequest, req.ProductName)
	}

	orderDetail, err := subr.fulfillOrder()
	if err != nil {
		return nil, err
	}

	go func() {
		if err := subr.sendTrackingEvent(orderDetail, pdt, user); err != nil {
			log.Warn("order service: subscribe: send amplitude event failed").
				Str("user_id", user.ID).Str("product_name", req.ProductName).Err(err).Send()
		}
	}()
	return orderDetail.order, nil
}
