package auth

import (
	"context"
	mwauditing "github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/middleware/auditing"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/auditing"
)

func auditLogUpdate(ctx context.Context, old, new any, targetType, targetID, note string) error {
	if old != nil && new != nil {
		if logDiffs, err := auditing.GetDiffFields(old, new); err != nil {
			return err
		} else {
			mwauditing.Log(ctx, func(builder *mwauditing.Auditing) {
				builder.TargetUpdated(targetType, targetID).
					DetailDiff(logDiffs...).
					Note(note)
			}, mwauditing.KeepLog())
		}
	}
	return nil
}
