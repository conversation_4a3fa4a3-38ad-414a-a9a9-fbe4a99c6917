// Code generated by MockGen. DO NOT EDIT.
// Source: coupon_code_repository.go

// Package redeem is a generated GoMock package.
package redeem

import (
	reflect "reflect"

	dbredeem "github.com/KKTV/kktv-api-v3/pkg/model/dbredeem"
	gomock "github.com/golang/mock/gomock"
)

// MockCouponCodeRepository is a mock of CouponCodeRepository interface.
type MockCouponCodeRepository struct {
	ctrl     *gomock.Controller
	recorder *MockCouponCodeRepositoryMockRecorder
}

// MockCouponCodeRepositoryMockRecorder is the mock recorder for MockCouponCodeRepository.
type MockCouponCodeRepositoryMockRecorder struct {
	mock *MockCouponCodeRepository
}

// NewMockCouponCodeRepository creates a new mock instance.
func NewMockCouponCodeRepository(ctrl *gomock.Controller) *MockCouponCodeRepository {
	mock := &MockCouponCodeRepository{ctrl: ctrl}
	mock.recorder = &MockCouponCodeRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCouponCodeRepository) EXPECT() *MockCouponCodeRepositoryMockRecorder {
	return m.recorder
}

// CountByGroupAndUser mocks base method.
func (m *MockCouponCodeRepository) CountByGroupAndUser(groupID, uid string) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountByGroupAndUser", groupID, uid)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountByGroupAndUser indicates an expected call of CountByGroupAndUser.
func (mr *MockCouponCodeRepositoryMockRecorder) CountByGroupAndUser(groupID, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountByGroupAndUser", reflect.TypeOf((*MockCouponCodeRepository)(nil).CountByGroupAndUser), groupID, uid)
}

// GetByCode mocks base method.
func (m *MockCouponCodeRepository) GetByCode(code string) (*dbredeem.CouponCode, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByCode", code)
	ret0, _ := ret[0].(*dbredeem.CouponCode)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByCode indicates an expected call of GetByCode.
func (mr *MockCouponCodeRepositoryMockRecorder) GetByCode(code interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByCode", reflect.TypeOf((*MockCouponCodeRepository)(nil).GetByCode), code)
}

// Update mocks base method.
func (m *MockCouponCodeRepository) Update(code *dbredeem.CouponCode) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", code)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockCouponCodeRepositoryMockRecorder) Update(code interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockCouponCodeRepository)(nil).Update), code)
}
