package platform

type DeviceType string

const (
	DeviceTypeWeb       DeviceType = "web"
	DeviceTypeMobileApp DeviceType = "app"
	DeviceTypeTV        DeviceType = "tv"
	DeviceTypeMOD       DeviceType = "mod"
)

func (dt DeviceType) String() string {
	return string(dt)
}

func (dt DeviceType) ShortName() string {
	switch dt {
	case DeviceTypeWeb:
		return "w"
	case DeviceTypeMobileApp:
		return "a"
	case DeviceTypeTV:
		return "tv"
	default:
		return ""
	}
}
