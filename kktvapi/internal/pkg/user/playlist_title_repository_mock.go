// Code generated by MockGen. DO NOT EDIT.
// Source: playlist_title_repository.go

// Package user is a generated GoMock package.
package user

import (
	reflect "reflect"

	dbuser "github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	gomock "github.com/golang/mock/gomock"
)

// MockPlaylistTitleRepository is a mock of PlaylistTitleRepository interface.
type MockPlaylistTitleRepository struct {
	ctrl     *gomock.Controller
	recorder *MockPlaylistTitleRepositoryMockRecorder
}

// MockPlaylistTitleRepositoryMockRecorder is the mock recorder for MockPlaylistTitleRepository.
type MockPlaylistTitleRepositoryMockRecorder struct {
	mock *MockPlaylistTitleRepository
}

// NewMockPlaylistTitleRepository creates a new mock instance.
func NewMockPlaylistTitleRepository(ctrl *gomock.Controller) *MockPlaylistTitleRepository {
	mock := &MockPlaylistTitleRepository{ctrl: ctrl}
	mock.recorder = &MockPlaylistTitleRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPlaylistTitleRepository) EXPECT() *MockPlaylistTitleRepositoryMockRecorder {
	return m.recorder
}

// AddTitlesToPlaylist mocks base method.
func (m *MockPlaylistTitleRepository) AddTitlesToPlaylist(playlistID string, titleIDs []string) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddTitlesToPlaylist", playlistID, titleIDs)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddTitlesToPlaylist indicates an expected call of AddTitlesToPlaylist.
func (mr *MockPlaylistTitleRepositoryMockRecorder) AddTitlesToPlaylist(playlistID, titleIDs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddTitlesToPlaylist", reflect.TypeOf((*MockPlaylistTitleRepository)(nil).AddTitlesToPlaylist), playlistID, titleIDs)
}

// CountTitlesInPlaylist mocks base method.
func (m *MockPlaylistTitleRepository) CountTitlesInPlaylist(playlistID string) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountTitlesInPlaylist", playlistID)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountTitlesInPlaylist indicates an expected call of CountTitlesInPlaylist.
func (mr *MockPlaylistTitleRepositoryMockRecorder) CountTitlesInPlaylist(playlistID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountTitlesInPlaylist", reflect.TypeOf((*MockPlaylistTitleRepository)(nil).CountTitlesInPlaylist), playlistID)
}

// GetPlaylistTitleIDsByPlaylistID mocks base method.
func (m *MockPlaylistTitleRepository) GetPlaylistTitleIDsByPlaylistID(playlistID string) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPlaylistTitleIDsByPlaylistID", playlistID)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPlaylistTitleIDsByPlaylistID indicates an expected call of GetPlaylistTitleIDsByPlaylistID.
func (mr *MockPlaylistTitleRepositoryMockRecorder) GetPlaylistTitleIDsByPlaylistID(playlistID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPlaylistTitleIDsByPlaylistID", reflect.TypeOf((*MockPlaylistTitleRepository)(nil).GetPlaylistTitleIDsByPlaylistID), playlistID)
}

// GetPlaylistTitlesByUserIDAndName mocks base method.
func (m *MockPlaylistTitleRepository) GetPlaylistTitlesByUserIDAndName(userID, playlistName string) ([]*dbuser.PlaylistTitle, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPlaylistTitlesByUserIDAndName", userID, playlistName)
	ret0, _ := ret[0].([]*dbuser.PlaylistTitle)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPlaylistTitlesByUserIDAndName indicates an expected call of GetPlaylistTitlesByUserIDAndName.
func (mr *MockPlaylistTitleRepositoryMockRecorder) GetPlaylistTitlesByUserIDAndName(userID, playlistName interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPlaylistTitlesByUserIDAndName", reflect.TypeOf((*MockPlaylistTitleRepository)(nil).GetPlaylistTitlesByUserIDAndName), userID, playlistName)
}

// IsTitleInPlaylist mocks base method.
func (m *MockPlaylistTitleRepository) IsTitleInPlaylist(playlistID, titleID string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsTitleInPlaylist", playlistID, titleID)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsTitleInPlaylist indicates an expected call of IsTitleInPlaylist.
func (mr *MockPlaylistTitleRepositoryMockRecorder) IsTitleInPlaylist(playlistID, titleID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsTitleInPlaylist", reflect.TypeOf((*MockPlaylistTitleRepository)(nil).IsTitleInPlaylist), playlistID, titleID)
}

// RemoveTitlesFromPlaylist mocks base method.
func (m *MockPlaylistTitleRepository) RemoveTitlesFromPlaylist(playlistID string, titleIDs []string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveTitlesFromPlaylist", playlistID, titleIDs)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveTitlesFromPlaylist indicates an expected call of RemoveTitlesFromPlaylist.
func (mr *MockPlaylistTitleRepositoryMockRecorder) RemoveTitlesFromPlaylist(playlistID, titleIDs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveTitlesFromPlaylist", reflect.TypeOf((*MockPlaylistTitleRepository)(nil).RemoveTitlesFromPlaylist), playlistID, titleIDs)
}
