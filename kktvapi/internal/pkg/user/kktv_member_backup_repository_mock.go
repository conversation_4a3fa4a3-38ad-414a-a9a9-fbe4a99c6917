// Code generated by MockGen. DO NOT EDIT.
// Source: kktv_member_backup_repository.go

// Package user is a generated GoMock package.
package user

import (
	reflect "reflect"

	dbuser "github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	gomock "github.com/golang/mock/gomock"
)

// MockBackupRepository is a mock of BackupRepository interface.
type MockBackupRepository struct {
	ctrl     *gomock.Controller
	recorder *MockBackupRepositoryMockRecorder
}

// MockBackupRepositoryMockRecorder is the mock recorder for MockBackupRepository.
type MockBackupRepositoryMockRecorder struct {
	mock *MockBackupRepository
}

// NewMockBackupRepository creates a new mock instance.
func NewMockBackupRepository(ctrl *gomock.Controller) *MockBackupRepository {
	mock := &MockBackupRepository{ctrl: ctrl}
	mock.recorder = &MockBackupRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockBackupRepository) EXPECT() *MockBackupRepositoryMockRecorder {
	return m.recorder
}

// GetUserFromKKIDBackup mocks base method.
func (m *MockBackupRepository) GetUserFromKKIDBackup(account string) (*dbuser.KkidBackupUser, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserFromKKIDBackup", account)
	ret0, _ := ret[0].(*dbuser.KkidBackupUser)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserFromKKIDBackup indicates an expected call of GetUserFromKKIDBackup.
func (mr *MockBackupRepositoryMockRecorder) GetUserFromKKIDBackup(account interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserFromKKIDBackup", reflect.TypeOf((*MockBackupRepository)(nil).GetUserFromKKIDBackup), account)
}
