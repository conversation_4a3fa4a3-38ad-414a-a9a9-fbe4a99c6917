// Code generated by MockGen. DO NOT EDIT.
// Source: package_layout_repository.go

// Package user is a generated GoMock package.
package user

import (
	reflect "reflect"

	dbuser "github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	gomock "github.com/golang/mock/gomock"
)

// MockPackageLayoutRepository is a mock of PackageLayoutRepository interface.
type MockPackageLayoutRepository struct {
	ctrl     *gomock.Controller
	recorder *MockPackageLayoutRepositoryMockRecorder
}

// MockPackageLayoutRepositoryMockRecorder is the mock recorder for MockPackageLayoutRepository.
type MockPackageLayoutRepositoryMockRecorder struct {
	mock *MockPackageLayoutRepository
}

// NewMockPackageLayoutRepository creates a new mock instance.
func NewMockPackageLayoutRepository(ctrl *gomock.Controller) *MockPackageLayoutRepository {
	mock := &MockPackageLayoutRepository{ctrl: ctrl}
	mock.recorder = &MockPackageLayoutRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPackageLayoutRepository) EXPECT() *MockPackageLayoutRepositoryMockRecorder {
	return m.recorder
}

// GetByPackageID mocks base method.
func (m *MockPackageLayoutRepository) GetByPackageID(pkgID int64) (*dbuser.PackageLayout, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByPackageID", pkgID)
	ret0, _ := ret[0].(*dbuser.PackageLayout)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByPackageID indicates an expected call of GetByPackageID.
func (mr *MockPackageLayoutRepositoryMockRecorder) GetByPackageID(pkgID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByPackageID", reflect.TypeOf((*MockPackageLayoutRepository)(nil).GetByPackageID), pkgID)
}

// GetByPackageIDs mocks base method.
func (m *MockPackageLayoutRepository) GetByPackageIDs(pkgIDs []int) (*dbuser.PackageLayouts, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByPackageIDs", pkgIDs)
	ret0, _ := ret[0].(*dbuser.PackageLayouts)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByPackageIDs indicates an expected call of GetByPackageIDs.
func (mr *MockPackageLayoutRepositoryMockRecorder) GetByPackageIDs(pkgIDs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByPackageIDs", reflect.TypeOf((*MockPackageLayoutRepository)(nil).GetByPackageIDs), pkgIDs)
}

// UpdateByFields mocks base method.
func (m *MockPackageLayoutRepository) UpdateByFields(pkgID int64, fields map[dbuser.PackageLayoutField]interface{}) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateByFields", pkgID, fields)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateByFields indicates an expected call of UpdateByFields.
func (mr *MockPackageLayoutRepositoryMockRecorder) UpdateByFields(pkgID, fields interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateByFields", reflect.TypeOf((*MockPackageLayoutRepository)(nil).UpdateByFields), pkgID, fields)
}
