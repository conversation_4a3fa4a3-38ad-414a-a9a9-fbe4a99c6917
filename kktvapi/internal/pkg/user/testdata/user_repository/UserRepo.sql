INSERT INTO public.users (id, email, phone, avatar_url, name, birthday, gender, expired_at, master, created_at,
                          updated_at, "role", media_source, auto_renew, payment_info, "type", created_by,
                          cold_start_selected_titles, kkid_bound_at, origin_provider, kkid, revoked_at,
                          unsubscribed_edm_at, "password", email_verified_at, phone_verified_at, membership)
VALUES ('kktv-api-unittest-0001', '<EMAIL>', NULL, NULL, NULL, NULL, NULL,
        '2016-11-21 00:00:00.000', NULL, '2016-11-16 23:20:56.172', '2016-11-21 00:08:55.276', 'expired', '{"kkbox": {"sub": "testSubJosiE", "kkid": "testKkiDJoSIE", "status": "", "territory": "TW", "identifier": "<EMAIL>"}, "family": ["a167686a-ae58-43f2-93a9-67f0e580efe1", "0420e345-df37-4c23-ad22-184d85c9fb72"], "appsflyer": {"idfa": "00000000-0000-0000-0000-000000000000", "bundleId": "com.kktv.ios.kktv"}, "childlock": "0313"}', false,
        NULL, 'general'::users_type, '', NULL, NULL, 'facebook'::users_origin_provider, NULL, NULL, NULL, NULL, NULL,
        NULL, '[{"role": "expired"}]');
INSERT INTO public.users (id, email, phone, avatar_url, name, birthday, gender, expired_at, master, created_at,
                          updated_at, "role", media_source, auto_renew, payment_info, "type", created_by,
                          cold_start_selected_titles, kkid_bound_at, origin_provider, kkid, revoked_at,
                          unsubscribed_edm_at, "password", email_verified_at, phone_verified_at)
VALUES ('kktv-api-unittest-0002', '<EMAIL>', NULL, NULL, NULL, NULL, NULL,
        '2016-11-21 00:00:00.000', NULL, '2016-11-16 23:20:56.172', '2016-11-21 00:08:55.276', 'expired', NULL, false,
        NULL, 'general'::users_type, '', NULL, NULL, 'facebook'::users_origin_provider, NULL, '2020-11-16 23:20:56.172', NULL, NULL, NULL,
        NULL);
INSERT INTO public.users (id, email, phone, avatar_url, name, birthday, gender, expired_at, master, created_at,
                          updated_at, "role", media_source, auto_renew, payment_info, "type", created_by,
                          cold_start_selected_titles, kkid_bound_at, origin_provider, kkid, revoked_at,
                          unsubscribed_edm_at, "password", email_verified_at, phone_verified_at)
VALUES ('kktv-api-unittest-0003', NULL, NULL, NULL, NULL, NULL, NULL,
        '2016-11-21 00:00:00.000', NULL, '2016-11-16 23:20:56.172', '2016-11-21 00:08:55.276', 'expired', NULL, false,
        NULL, 'general'::users_type, '', NULL, NULL, 'facebook'::users_origin_provider, NULL, NULL, NULL, NULL, NULL,
        NULL);
INSERT INTO public.users (id, email, phone, avatar_url, name, birthday, gender, expired_at, master, created_at,
                          updated_at, "role", media_source, auto_renew, payment_info, "type", created_by,
                          cold_start_selected_titles, kkid_bound_at, origin_provider, kkid, revoked_at,
                          unsubscribed_edm_at, "password", email_verified_at, phone_verified_at)
VALUES ('kktv-api-unittest-0004', NULL, '**********', NULL, NULL, NULL, NULL,
        '2016-11-21 00:00:00.000', NULL, '2016-11-16 23:20:56.172', '2016-11-21 00:08:55.276', 'expired', NULL, false,
        NULL, 'general'::users_type, '', NULL, NULL, 'facebook'::users_origin_provider, NULL, NULL, NULL, NULL, NULL,
        NULL);
INSERT INTO public.users (id, email, phone, avatar_url, name, birthday, gender, expired_at, master, created_at,
                          updated_at, "role", media_source, auto_renew, payment_info, "type", created_by,
                          cold_start_selected_titles, kkid_bound_at, origin_provider, kkid, revoked_at,
                          unsubscribed_edm_at, "password", email_verified_at, phone_verified_at)
VALUES ('kktv-api-unittest-0005', NULL, '886956432154', NULL, NULL, NULL, NULL,
        '2016-11-21 00:00:00.000', NULL, '2016-11-16 23:20:56.172', '2016-11-21 00:08:55.276', 'expired', NULL, false,
        NULL, 'general'::users_type, '', NULL, NULL, 'facebook'::users_origin_provider, NULL, NULL, NULL, NULL, NULL,
        NULL);
INSERT INTO public.users (id, email, phone, avatar_url, name, birthday, gender, expired_at, master, created_at,
                          updated_at, "role", media_source, auto_renew, payment_info, "type", created_by,
                          cold_start_selected_titles, kkid_bound_at, origin_provider, kkid, revoked_at,
                          unsubscribed_edm_at, "password", email_verified_at, phone_verified_at)
VALUES ('kktv-api-unittest-0006', NULL, '+886955667788', NULL, NULL, NULL, NULL,
        '2016-11-21 00:00:00.000', NULL, '2016-11-16 23:20:56.172', '2016-11-21 00:08:55.276', 'expired', NULL, false,
        NULL, 'general'::users_type, '', NULL, NULL, 'facebook'::users_origin_provider, NULL, NULL, NULL, NULL, NULL,
        NULL);
INSERT INTO public.users (id, email, phone, avatar_url, name, birthday, gender, expired_at, master, created_at,
                          updated_at, "role", media_source, auto_renew, payment_info, "type", created_by,
                          cold_start_selected_titles, kkid_bound_at, origin_provider, kkid, revoked_at,
                          unsubscribed_edm_at, "password", email_verified_at, phone_verified_at)
VALUES ('kktv-api-unittest-0007', NULL, '+886977777777', NULL, NULL, NULL, NULL,
        '2016-11-21 00:00:00.000', NULL, '2016-11-16 23:20:56.172', '2016-11-21 00:08:55.276', 'expired', NULL, false,
        NULL, 'general'::users_type, '', NULL, NULL, 'facebook'::users_origin_provider, NULL, '2020-11-16 23:20:56.172', NULL, NULL, NULL,
        NULL);
INSERT INTO public.users (id, email, phone, avatar_url, name, birthday, gender, expired_at, master, created_at,
                          updated_at, "role", media_source, auto_renew, payment_info, "type", created_by,
                          cold_start_selected_titles, kkid_bound_at, origin_provider, kkid, revoked_at,
                          unsubscribed_edm_at, "password", email_verified_at, phone_verified_at)
VALUES ('kktv-api-unittest-0008', NULL, '+886900000000', NULL, NULL, NULL, NULL,
        '2016-11-21 00:00:00.000', NULL, '2016-11-16 23:20:56.172', '2022-11-21 00:08:55.276', 'expired', NULL, false,
        NULL, 'general'::users_type, '', NULL, NULL, 'facebook'::users_origin_provider, NULL, NULL, NULL, NULL, NULL,
        '2022-12-16 00:00:00.000');
INSERT INTO public.users (id, email, phone, avatar_url, name, birthday, gender, expired_at, master, created_at,
                          updated_at, "role", media_source, auto_renew, payment_info, "type", created_by,
                          cold_start_selected_titles, kkid_bound_at, origin_provider, kkid, revoked_at,
                          unsubscribed_edm_at, "password", email_verified_at, phone_verified_at)
VALUES ('kktv-api-unittest-0009', NULL, '+886900000000', NULL, NULL, NULL, NULL,
        '2016-11-21 00:00:00.000', NULL, '2016-11-16 23:20:56.172', '2022-11-21 00:08:55.276', 'expired', NULL, false,
        NULL, 'general'::users_type, '', NULL, NULL, 'facebook'::users_origin_provider, NULL, NULL, NULL, NULL, NULL,
        NULL);
INSERT INTO public.users (id, email, phone, avatar_url, name, birthday, gender, expired_at, master, created_at,
                          updated_at, "role", media_source, auto_renew, payment_info, "type", created_by,
                          cold_start_selected_titles, kkid_bound_at, origin_provider, kkid, revoked_at,
                          unsubscribed_edm_at, "password", email_verified_at, phone_verified_at)
VALUES ('kktv-api-unittest-0010', '<EMAIL>', NULL, NULL, NULL, NULL, NULL,
        '2016-11-21 00:00:00.000', NULL, '2016-11-16 23:20:56.172', '2022-11-21 00:08:55.276', 'expired', NULL, false,
        NULL, 'general'::users_type, '', NULL, NULL, 'facebook'::users_origin_provider, NULL, NULL, NULL, NULL, '2022-12-16 00:00:00.000',
        '2022-12-16 00:00:00.000');
INSERT INTO public.users (id, email, phone, avatar_url, name, birthday, gender, expired_at, master, created_at,
                          updated_at, "role", media_source, auto_renew, payment_info, "type", created_by,
                          cold_start_selected_titles, kkid_bound_at, origin_provider, kkid, revoked_at,
                          unsubscribed_edm_at, "password", email_verified_at, phone_verified_at)
VALUES ('kktv-api-unittest-0011', '<EMAIL>', NULL, NULL, NULL, NULL, NULL,
        '2016-11-21 00:00:00.000', NULL, '2016-11-16 23:20:56.172', '2022-11-21 00:08:55.276', 'expired', NULL, false,
        NULL, 'general'::users_type, '', NULL, NULL, 'facebook'::users_origin_provider, NULL, NULL, NULL, NULL, NULL,
        NULL);
