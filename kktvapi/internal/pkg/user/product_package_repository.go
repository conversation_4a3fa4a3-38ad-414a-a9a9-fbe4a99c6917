//go:generate mockgen -source product_package_repository.go -destination product_package_repository_mock.go -package user
package user

import (
	"sync"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/pkg/database"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
)

var (
	productPackageRepo     ProductPackageRepository
	onceProductPackageRepo sync.Once
)

type ProductPackageRepository interface {
	ListByPlatform(platform string) (*dbuser.ProductPackages, error)
	ListBillingPkgsByPlatform(platform string) (*dbuser.ProductPackages, error)
}

type productPackageRepository struct {
	dbReader database.DB
	dbWriter database.DB
}

func NewProductPackageRepository() ProductPackageRepository {
	onceProductPackageRepo.Do(func() {
		productPackageRepo = &productPackageRepository{
			dbReader: container.DBPoolUser().Slave().Unsafe(),
			dbWriter: container.DBPoolUser().Master().Unsafe(),
		}
	})
	return productPackageRepo
}

func (r *productPackageRepository) ListByPlatform(platform string) (*dbuser.ProductPackages, error) {
	pkgs := new(dbuser.ProductPackages)
	if err := r.dbReader.Select(pkgs,
		`SELECT pkg.id, pkg.platform, pkg.price::money::numeric::float8,
				pkg.duration, pkg.title, pkg.description,
				pkg.button_text, pkg.label, pkg.created_at, pkg.updated_at,
				pd.id as "product.id",
				pd.name as "product.name",
				pd.country as "product.country",
				pd.price::money::numeric::integer as "product.price",
				EXTRACT(EPOCH from pd.duration) as "product.duration",
				pd.created_at as "product.created_at",
				pd.updated_at as "product.updated_at",
				pd.deleted_at as "product.deleted_at",
				pd.payment_type as "product.payment_type",
				pd.currency as "product.currency",
				pd.price_no_tax::money::numeric::integer as "product.price_no_tax",
				pd.tax_rate as "product.tax_rate",
				pd.item_name as "product.item_name",
				pd.item_unit as "product.item_unit",
				pd.auto_renew as "product.auto_renew",
				pd.active as "product.active",
				EXTRACT(EPOCH from pd.free_duration) as "product.free_duration",
				pd.as_subscribe as "product.as_subscribe",
				pd.fee as "product.fee", pd.payment_type_code as "product.payment_type_code",
				pd.purchase_upper_limit_count as "product.purchase_upper_limit_count",
				pd.external_product_id as "product.external_product_id",
				EXTRACT(EPOCH from pd.discount_duration) as "product.discount_duration",
				pd.discount_price::money::numeric::integer as "product.discount_price",
				pd.bundle as "product.bundle", pd.sort as "product.sort",
				pd.category as "pd.category", pd.fee_rate as "product.fee_rate",
				pd.discount_price_no_tax::money::numeric::integer as "product.discount_price_no_tax",
				pd.discount_fee as "product.discount_fee",
				pkg.active, pkg.highlight, pkg.auto_renew,
				pkg.sort, pkg.promotion,
				pkg.info, pkg.targets,
				pkg.category as category, pkg.pay_duration,
				pkg.billing_product_ids
		FROM product_packages pkg JOIN products pd ON pkg.product_ids::jsonb @> (pd.id::text)::jsonb
		WHERE pkg.platform = $1
			AND pkg.active = TRUE
		ORDER BY
			pkg.sort,
			pkg.id,
			pd.id;`,
		platform); err != nil {
		return nil, err
	}
	return pkgs, nil
}

func (r *productPackageRepository) ListBillingPkgsByPlatform(platform string) (*dbuser.ProductPackages, error) {
	pkgs := new(dbuser.ProductPackages)
	if err := r.dbReader.Select(pkgs,
		`SELECT pkg.id, pkg.platform, pkg.price::money::numeric::float8,
				pkg.duration, pkg.title, pkg.description,
				pkg.button_text, pkg.label, pkg.created_at, pkg.updated_at,
				pkg.active, pkg.highlight, pkg.auto_renew,
				pkg.sort, pkg.promotion,
				pkg.info, pkg.targets,
				pkg.category as category, pkg.pay_duration,
				pkg.billing_product_ids
		FROM product_packages pkg
		WHERE pkg.platform = $1
			AND pkg.active = TRUE
			AND jsonb_array_length((pkg.billing_product_ids)::jsonb) > 0
			AND jsonb_array_length((pkg.product_ids)::jsonb) = 0
		ORDER BY
			pkg.sort,
			pkg.id;`,
		platform); err != nil {
		return nil, err
	}
	return pkgs, nil
}
