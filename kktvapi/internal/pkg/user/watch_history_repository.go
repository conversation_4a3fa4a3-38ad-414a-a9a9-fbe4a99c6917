//go:generate mockgen -source watch_history_repository.go -destination watch_history_repository_mock.go -package user
package user

import (
	"errors"
	"sync"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/key"
)

type WatchHistoryRepository interface {
	// GetByUserID returns the title ids in watch history of the user.
	GetByUserID(userID string) ([]string, error)
	PurgeAfter(userID string, index int) error
}

var (
	watchHistoryRepo     WatchHistoryRepository
	onceWatchHistoryRepo sync.Once
)

type watchHistoryRepository struct {
	cacheReader cache.Cacher
	cacheWriter cache.Cacher
}

func NewWatchHistoryRepository() WatchHistoryRepository {
	onceWatchHistoryRepo.Do(func() {
		pool := container.CachePoolUser()
		watchHistoryRepo = &watchHistoryRepository{
			cacheReader: cache.New(pool.Slave()),
			cacheWriter: cache.New(pool.Master()),
		}
	})
	return watchHistoryRepo
}

func (r *watchHistoryRepository) GetByUserID(userID string) ([]string, error) {
	data, err := r.cacheReader.ZRevRange(key.UserWatchHistory(userID), 0, -1)
	if errors.Is(err, cache.ErrCacheMiss) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}
	var titleIDs []string
	for _, titleID := range data {
		titleIDs = append(titleIDs, string(titleID))
	}
	return titleIDs, nil
}

func (r *watchHistoryRepository) PurgeAfter(userID string, index int) error {
	return r.cacheWriter.ZRemRangeByRank(key.UserWatchHistory(userID), 0, -index)
}
