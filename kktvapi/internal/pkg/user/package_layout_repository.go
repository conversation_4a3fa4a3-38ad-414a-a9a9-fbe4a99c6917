//go:generate mockgen -source package_layout_repository.go -destination package_layout_repository_mock.go -package user
package user

import (
	"database/sql"
	"errors"
	"fmt"
	"sync"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/pkg/clock"
	"github.com/KKTV/kktv-api-v3/pkg/database"
	"github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/lib/pq"
	"gopkg.in/guregu/null.v3"
)

var (
	packageLayoutRepo     PackageLayoutRepository
	oncePackageLayoutRepo sync.Once
)

type PackageLayoutRepository interface {
	Create(layout *dbuser.PackageLayout) (err error)
	GetByPackageIDs(pkgIDs []int) (*dbuser.PackageLayouts, error)
	GetByPackageID(pkgID int64) (*dbuser.PackageLayout, error)
	UpdateByFields(pkgID int64, fields map[dbuser.PackageLayoutField]interface{}) (bool, error)
}

type packageLayoutRepository struct {
	dbReader database.DB
	dbWriter database.DB
	clock    clock.Clock
}

func NewPackageLayoutRepository() PackageLayoutRepository {
	oncePackageLayoutRepo.Do(func() {
		packageLayoutRepo = &packageLayoutRepository{
			dbReader: container.DBPoolUser().Slave().Unsafe(),
			dbWriter: container.DBPoolUser().Master().Unsafe(),
			clock:    clock.New(),
		}
	})
	return packageLayoutRepo
}

func NewPackageLayoutRepositoryWith(dbReader, dbWriter database.DB) PackageLayoutRepository {
	return &packageLayoutRepository{
		dbReader: dbReader,
		dbWriter: dbWriter,
		clock:    clock.New(),
	}
}

func (r *packageLayoutRepository) GetByPackageIDs(pkgIDs []int) (*dbuser.PackageLayouts, error) {
	var layouts = new(dbuser.PackageLayouts)
	if err := r.dbReader.Select(layouts,
		`SELECT *
		FROM package_layout
		WHERE product_packages_id = ANY($1);`,
		pq.Array(pkgIDs)); errors.Is(err, sql.ErrNoRows) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}

	return layouts, nil
}

func (r *packageLayoutRepository) Create(layout *dbuser.PackageLayout) (err error) {
	now := r.clock.Now()
	layout.CreatedAt = null.TimeFrom(now)
	layout.UpdatedAt = null.TimeFrom(now)
	dbFields := database.GetDBFields(layout, "id")

	insertSQL := `INSERT INTO package_layout (` + dbFields.String() + `) VALUES (` + dbFields.NamedString() + `);`

	log.Debug("packageLayoutRepository: create").Str("sql", insertSQL).Send()
	_, err = r.dbWriter.NamedExec(insertSQL, layout)
	if err != nil {
		return err
	}
	return nil
}

func (r *packageLayoutRepository) GetByPackageID(pkgID int64) (*dbuser.PackageLayout, error) {
	var layout = new(dbuser.PackageLayout)
	if err := r.dbReader.Get(layout,
		`SELECT *
		FROM package_layout
		WHERE product_packages_id = $1;`, pkgID); errors.Is(err, sql.ErrNoRows) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}
	return layout, nil
}

func (r *packageLayoutRepository) UpdateByFields(pkgID int64, vals map[dbuser.PackageLayoutField]interface{}) (bool, error) {
	if len(vals) == 0 {
		return false, nil
	}
	namedArgs := map[string]interface{}{}
	q := `UPDATE package_layout SET updated_at = NOW()`
	for k, v := range vals {
		q += fmt.Sprintf(`, %s = :%s`, k, k)
		namedArgs[k.String()] = v
	}
	q += ` WHERE product_packages_id = :product_packages_id`
	namedArgs["product_packages_id"] = pkgID

	result, err := r.dbWriter.NamedExec(q, namedArgs)
	if err != nil {
		return false, err
	}
	affected, _ := result.RowsAffected()
	return affected > 0, nil
}
