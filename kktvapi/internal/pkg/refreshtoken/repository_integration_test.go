package refreshtoken

import (
	"os"
	"testing"
	"time"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/dbtest"
	"github.com/KKTV/kktv-api-v3/pkg/clock"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/KKTV/kktv-api-v3/pkg/rand"
	"github.com/golang/mock/gomock"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"gopkg.in/guregu/null.v3"
)

type RefreshTokenRepositoryTestSuite struct {
	dbtest.Suite
	r    *require.Assertions
	ctrl *gomock.Controller

	mockClock  *clock.MockClock
	mockRandom *rand.MockRand
	repo       Repository
}

func TestRefreshTokenRepositoryIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(RefreshTokenRepositoryTestSuite))
}

func (suite *RefreshTokenRepositoryTestSuite) SetupTest() {
	suite.Suite.SetupTest()
	tx := suite.GetTransaction()

	suite.ctrl = gomock.NewController(suite.T())
	suite.mockClock = clock.NewMockClock(suite.ctrl)
	suite.mockRandom = rand.NewMockRand(suite.ctrl)

	suite.repo = &repository{
		dbReader: tx,
		dbWriter: tx,
		clock:    suite.mockClock,
		random:   suite.mockRandom,
	}

	suite.r = suite.Require()
}

func (suite *RefreshTokenRepositoryTestSuite) TearDownTest() {
	suite.Suite.TearDownTest()
}

func (suite *RefreshTokenRepositoryTestSuite) SetupSuite() {
	suite.Suite.SetupSuite(os.Getenv("TEST_DB_USER_DSN"))
}

func init() {
	_ = os.Setenv("TEST_DB_USER_DSN", "postgres://foo:bar@localhost:5432/kktv_users?sslmode=disable")
}

func (suite *RefreshTokenRepositoryTestSuite) TestWithTx() {
	suite.Run("get refresh token repo with tx", func() {
		suite.r.Implements((*Repository)(nil), suite.repo.WithTx(&sqlx.Tx{}))
	})
}

func (suite *RefreshTokenRepositoryTestSuite) TestCreate() {
	now := time.Now()
	refreshToken := &dbuser.RefreshToken{
		TokenID:      "a9e58c2a70ff0f7abbbf0bfb1f8c54402603a988a256aca0515f0626f746f287",
		RefreshToken: "3c931b26b7a9afb18c0e7aab97dee713d57d0c16c15b163a4794a36df7ab0c35",
		ExpiredAt:    null.TimeFrom(now),
	}

	testcases := []struct {
		name         string
		refreshToken *dbuser.RefreshToken
		then         func(created *dbuser.RefreshToken, err error)
	}{
		{
			name:         "create refresh token successfully",
			refreshToken: refreshToken,
			then: func(created *dbuser.RefreshToken, err error) {
				suite.r.NoError(err)
				suite.NotNil(created)
				suite.r.Equal(refreshToken.TokenID, created.TokenID)
				suite.r.Equal(refreshToken.RefreshToken, created.RefreshToken)
			},
		},
	}

	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			suite.mockClock.EXPECT().Now().Return(now)
			tc.then(suite.repo.Create(refreshToken))
		})
	}

}
