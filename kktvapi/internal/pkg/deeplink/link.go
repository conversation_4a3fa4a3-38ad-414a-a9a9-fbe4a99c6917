package deeplink

import (
	"fmt"
	"net/url"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/platform"
)

var (
	clientHost = "https://www.kktv.me"
	apiHost    = "https://api.kktv.me"
)

func Init(env string) {
	switch env {
	case "test", "dev", "stage":
		clientHost = "https://test-web.kktv.com.tw"
		apiHost = "https://test-api.kktv.me"
	default:
		// default using PRODUCTION: "https://www.kktv.me"
		clientHost = "https://www.kktv.me"
		apiHost = "https://api.kktv.me"
	}
}

func TitlePage(titleID string) string {
	return withClientHost("/titles/%s", titleID)
}

type TitleListPageParam struct {
	Caption     string
	IsShareLink bool
	Queries     map[string]string
}

func TitleListPageWithParam(titleListID string, param *TitleListPageParam) string {
	return ShareLinkParam(titleListID, param, TitleListPage)
}

func TimelinePageWithParam(titleListID string, param *TitleListPageParam) string {
	return ShareLinkParam(titleListID, param, TimeLinePage)
}

func ShareLinkParam(titleListID string, param *TitleListPageParam, path func(string) string) string {
	base, _ := url.Parse(path(titleListID))
	urlValues := url.Values{}
	if param != nil {
		if param.IsShareLink {
			urlValues.Add("utm_source", "official")
			urlValues.Add("utm_medium", "share")
			urlValues.Add("utm_campaign", param.Caption)
		}
		if len(param.Queries) > 0 {
			for k, v := range param.Queries {
				urlValues.Add(k, v)
			}
		}
	}
	if len(urlValues) > 0 {
		base.RawQuery = urlValues.Encode()
	}
	return base.String()
}

func TitleListPage(titleListID string) string {
	return withClientHost("/titleList/%s", titleListID)
}
func TimeLinePage(titleListID string) string {
	return withClientHost("/timeline/%s", titleListID)
}

func HighlightPage(highlightID string) string {
	return withClientHost("/picks/%s", highlightID)
}

func CollectionPage(collectionType, collectionName string, queries map[string]string) string {
	base, _ := url.Parse(withClientHost("/browse/"))
	base = base.JoinPath(collectionType).JoinPath(url.PathEscape(collectionName))
	if len(queries) > 0 {
		params := url.Values{}
		for k, v := range queries {
			params.Add(k, v)
		}
		base.RawQuery = params.Encode()
	}
	return base.String()
}

func FeaturedPage(collectionKey string) string {
	base, _ := url.Parse(withClientHost("/featured"))
	params := url.Values{}
	params.Add("browse", collectionKey)
	base.RawQuery = params.Encode()
	return base.String()
}

func WatchHistory() string {
	return withClientHost("/account/watch-history")
}

func WatchHistoryAPI() string {
	return withAPIHost("/v3/users/me/watch_history")
}

func V4WatchHistoryAPI(deviceType platform.DeviceType) string {
	return withAPIHost("/v4/%s/users/me/watch-history", deviceType.ShortName())
}

func V4TitleListAPI(shareID string, deviceType platform.DeviceType) string {
	return withAPIHost("/v4/%s/title-lists/%s", deviceType.ShortName(), shareID)
}

func ColdStartPage() string {
	return withClientHost("/cold_start")
}

func ContinuePlay(episodeID string, offset int64) string {
	path := withClientHost("/play/%s", episodeID)
	if offset > 0 {
		return fmt.Sprintf("%s?offset=%d", path, offset)
	}
	return path
}

func TitleListPageShareLink(shareID, listName string) string {
	utm := url.Values{
		"utm_source":   {"official"},
		"utm_medium":   {"share"},
		"utm_campaign": {listName},
	}
	return fmt.Sprintf("%s?%s", TitleListPage(shareID), utm.Encode())
}

func TitleListHighlightShareLink(shareID, listName string) string {
	utm := url.Values{
		"utm_source":   {"official"},
		"utm_medium":   {"share"},
		"utm_campaign": {listName},
	}
	return fmt.Sprintf("%s?%s", HighlightPage(shareID), utm.Encode())
}

func SignupSurveyPage() string {
	return withClientHost("/survey-preference")
}

func PrimePage() string {
	return "https://hi.kktv.to/53hz43"
}

func MyAccountPage() string {
	return withClientHost("/account")
}

func withClientHost(path string, params ...interface{}) string {
	baseUrl, _ := url.Parse(clientHost)
	newPath := fmt.Sprintf(path, params...)
	baseUrl = baseUrl.JoinPath(newPath)
	return baseUrl.String()
}

func withAPIHost(path string, params ...interface{}) string {
	return fmt.Sprintf(apiHost+path, params...)
}
