package container

import (
	"fmt"
	"strings"

	"github.com/KKTV/kktv-api-v3/kkapp/datastore"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/auditing"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/order"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/permission"
	"github.com/KKTV/kktv-api-v3/pkg/billing"
)

var (
	dbPoolMeta    *datastore.DBPool
	cachePoolMeta *datastore.RedisPool

	dbPoolUser    *datastore.DBPool
	cachePoolUser *datastore.RedisPool

	dbPoolRedeem *datastore.DBPool

	auditRepo         auditing.LogRepository
	billingClient     billing.Client
	permissionService permission.Service
	orderService      order.Service
)

func RegisterMetaDB(dsn string) {
	dbPoolMeta = newDB(dsn)
}

func RegisterUserDB(dsn string) {
	dbPoolUser = newDB(dsn)
}

// RegisterUserDBConn alternative to RegisterUserDB, provide another way to register an existed db connection
func RegisterUserDBConn(db *datastore.DBPool) {
	dbPoolUser = db
}

func RegisterRedeemDB(dsn string) {
	dbPoolRedeem = newDB(dsn)
}

func RegisterMetaCache(dsn string) {
	cachePoolMeta = newRedis(dsn)
}

func DBPoolMeta() *datastore.DBPool {
	return dbPoolMeta
}

func CachePoolMeta() *datastore.RedisPool {
	return cachePoolMeta
}

func RegisterUserCache(dsn string) {
	cachePoolUser = newRedis(dsn)
}

func CachePoolUser() *datastore.RedisPool {
	return cachePoolUser
}

func DBPoolUser() *datastore.DBPool {
	return dbPoolUser
}

func DBPoolRedeem() *datastore.DBPool {
	return dbPoolRedeem
}

func RegisterAuditingRepo(repo auditing.LogRepository) {
	auditRepo = repo
}

func AuditingRepo() auditing.LogRepository {
	return auditRepo
}

func RegisterBillingClient(client billing.Client) {
	billingClient = client
}

func BillingClient() billing.Client {
	return billingClient
}

func RegisterPermissionService(ps permission.Service) {
	permissionService = ps
}

func PermissionService() permission.Service {
	return permissionService
}

func RegisterOrderService(os order.Service) {
	orderService = os
}

func OrderService() order.Service {
	return orderService
}

func newDB(dsn string) (p *datastore.DBPool) {
	dbs := parseDBDSN(dsn)
	p = datastore.NewDBPool(dbs)
	return p
}

func parseDBDSN(uri string) []string {
	var dsns []string
	for _, dbURI := range strings.Split(uri, ",") {
		dsns = append(dsns, fmt.Sprintf("%s?sslmode=disable", dbURI))
	}
	return dsns
}

func newRedis(uri string) *datastore.RedisPool {
	return datastore.NewRedisPool(strings.Split(uri, ","))
}
