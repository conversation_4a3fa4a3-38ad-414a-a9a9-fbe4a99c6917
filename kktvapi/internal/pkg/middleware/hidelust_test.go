package middleware

import (
	"context"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/KKTV/kktv-api-v3/kkapp/kkmiddleware"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/httpreq"
	"github.com/KKTV/kktv-api-v3/pkg/model/cachemeta"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
)

func TestHideLustContent(t *testing.T) {

	var (
		mockHiddenByReviewVersion = func(mockCacheReader *cache.MockCacher) {
			mockCacheReader.EXPECT().HGet(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Do(func(_ string, key string, value interface{}) {
				conf := cachemeta.ContentControlConfig{}
				conf.HideLustContent.HiddenBy = "review_version"
				*value.(*cachemeta.ContentControlConfig) = conf
			})
		}
		mockHiddenByPlatforms = func(mockCacheReader *cache.MockCacher) {
			mockCacheReader.EXPECT().HGet(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Do(func(_ string, key string, value interface{}) {
				conf := cachemeta.ContentControlConfig{}
				conf.HideLustContent.HiddenBy = "platforms"
				conf.HideLustContent.Platforms = []string{"ios", "tvOS", "iPadOS"} // intent to make different uppercase to make sure it's case-insensitive
				*value.(*cachemeta.ContentControlConfig) = conf
			})
		}
	)

	tests := []struct {
		name           string
		platform       string
		version        string
		givenMock      func(mockCacheReader *cache.MockCacher)
		serviceStatus  *model.ServiceStatus
		expectedResult bool
	}{
		{
			name:           "When version info is missing, should set to false",
			platform:       "",
			version:        "",
			givenMock:      func(mockCacheReader *cache.MockCacher) {},
			expectedResult: false,
		},
		{
			name:      "When not in review version, should set to false",
			platform:  "iOS",
			version:   "1.0.0",
			givenMock: mockHiddenByReviewVersion,
			serviceStatus: &model.ServiceStatus{
				AppVersion: map[string]model.MobileApp{
					"ios": {
						ReviewVersion: "v2.0.0",
					},
				},
			},
			expectedResult: false,
		},
		{
			name:      "When in review version, should set to true",
			platform:  "iPadOS",
			version:   "v2.0.0",
			givenMock: mockHiddenByReviewVersion,
			serviceStatus: &model.ServiceStatus{
				AppVersion: map[string]model.MobileApp{
					"ios": {
						ReviewVersion: "2.0.0",
					},
				},
			},
			expectedResult: true,
		},
		{
			name:           "When in review version, but service status is missing, should set to false",
			platform:       "iPadOS",
			version:        "v2.0.0",
			givenMock:      mockHiddenByReviewVersion,
			serviceStatus:  nil,
			expectedResult: false,
		},
		{
			name:           "When hidden by platforms(ios), Android device should get false",
			platform:       "Android",
			version:        "3.0.0",
			givenMock:      mockHiddenByPlatforms,
			expectedResult: false,
		},
		{
			name:           "When hidden by platforms(ios), ios device should get true",
			platform:       "iOS",
			version:        "3.0.0",
			givenMock:      mockHiddenByPlatforms,
			expectedResult: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := httptest.NewRequest(http.MethodGet, "/test", nil)
			if tt.platform != "" {
				req.Header.Set(httpreq.HeaderPlatform, tt.platform)
			}
			if tt.version != "" {
				req.Header.Set(httpreq.HeaderAppVersion, tt.version)
			}
			ctx := req.Context()
			if tt.serviceStatus != nil {
				ctx = context.WithValue(ctx, kkmiddleware.ServiceStatusContextKey, tt.serviceStatus)
			}
			req = req.WithContext(ctx)
			rr := httptest.NewRecorder()

			testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				hideLustContent, ok := r.Context().Value(KeyHideLustContent).(bool)
				assert.True(t, ok, "Should be able to get hide_lust_content value from context")
				assert.Equal(t, tt.expectedResult, hideLustContent, "hide_lust_content value should match expected result")
				w.WriteHeader(http.StatusOK)
			})

			ctrl := gomock.NewController(t)
			mockCacheReader := cache.NewMockCacher(ctrl)
			tt.givenMock(mockCacheReader)

			hideLust := func(next http.Handler) http.Handler {
				return NewHideLustContentMiddleware(mockCacheReader).Handle(next)
			}
			handler := hideLust(testHandler)
			handler.ServeHTTP(rr, req)

			assert.Equal(t, http.StatusOK, rr.Code, "Response status code should be 200 OK")
		})
	}
}
