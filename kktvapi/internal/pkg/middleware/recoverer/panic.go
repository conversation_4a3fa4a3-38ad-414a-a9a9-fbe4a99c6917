package recoverer

import (
	"fmt"
	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/rest"
	"github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/render"
	"github.com/getsentry/sentry-go"
	"net/http"
	"os"
	"runtime/debug"
)

// Recover is the middleware to trace the panic stack and send to sentry
func Recover(next http.Handler) http.Handler {

	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer func() {
			if rvr := recover(); rvr != nil {

				stack := debug.Stack()
				sentry.ConfigureScope(func(scope *sentry.Scope) {
					scope.SetLevel(sentry.LevelFatal)
				})
				sentry.CaptureMessage(string(stack))

				if config.Env == "prod" {
					log.Error("panic").Bytes("stack", stack).Send()
				} else {
					fmt.Fprintf(os.Stderr, "Panic: %+v\n", rvr)
					debug.PrintStack()
				}
				render.JSONInternalServerErr(w, rest.Error("internal_server_error", "500.999"))
			}
		}()

		next.ServeHTTP(w, r)
	})
}
