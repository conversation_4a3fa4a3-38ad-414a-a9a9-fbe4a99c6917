package auth

import (
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

func TestActionToken(t *testing.T) {
	assert := require.New(t)
	now := time.Now()
	exp := now.Add(5 * time.Minute)
	src := ActionTokenSource{
		UserID:  "test-user-id",
		Account: "<EMAIL>",
	}

	var (
		err                error
		expiredTokenString string
		invalidSourceToken string
	)

	at := NewActionToken("tested")
	expiredTokenString, err = at.Generate(src, now, now.Add(-5*time.Minute))
	assert.NoError(err)
	invalidSourceToken, err = at.Generate(ActionTokenSource{}, now, exp)
	assert.NoError(err)

	t.Run("Generate then compare", func(t *testing.T) {
		var validTokenString string
		validTokenString, err = at.Generate(src, now, exp)

		assert.NoError(err)
		assert.NotEmpty(validTokenString)

		t.Run("Compare", func(t *testing.T) {
			verifyFunc := func(claims *ActionTokenClaims) error {
				if claims.Account != src.Account || claims.UserID != src.UserID {
					return ErrTokenInvalid
				}
				return nil
			}

			t.Run("valid", func(t *testing.T) {
				err = at.Compare(validTokenString, verifyFunc)
				assert.NoError(err)
			})

			t.Run("invalid: expired", func(t *testing.T) {
				err = at.Compare(expiredTokenString, verifyFunc)
				assert.Error(err)
				assert.ErrorIs(err, ErrTokenExpired)
			})

			t.Run("invalid: not same source", func(t *testing.T) {
				err = at.Compare(invalidSourceToken, verifyFunc)
				assert.Error(err)
				assert.ErrorIs(err, ErrTokenInvalid)
			})
		})
	})

}
