package auth

import (
	"time"

	"github.com/KKTV/kktv-api-v3/pkg/auth"
	"gopkg.in/dgrijalva/jwt-go.v3"
)

type ActionToken interface {
	Generate(src ActionTokenSource, now time.Time, exp time.Time) (string, error)
	Compare(tokenString string, compareFunc func(claims *ActionTokenClaims) error) error
}

type actionToken struct {
	jwtAuth auth.JWTAuth
}

type ActionTokenSource struct {
	UserID       string `json:"user_id"`
	Account      string `json:"account"`
	Verification string `json:"verification"`
}

type ActionTokenClaims struct {
	ActionTokenSource
	jwt.StandardClaims
}

func NewActionToken(signedStr string) ActionToken {
	return &actionToken{
		jwtAuth: auth.NewJWTAuth(jwt.SigningMethodHS256, []byte(signedStr)),
	}
}

func (a *actionToken) Generate(src ActionTokenSource, now time.Time, exp time.Time) (string, error) {
	claims := ActionTokenClaims{
		src,
		jwt.StandardClaims{
			Audience:  AudienceKKTV,
			ExpiresAt: exp.Unix(),
			IssuedAt:  now.Unix(),
			Issuer:    IssuerKKTV,
		},
	}

	return a.jwtAuth.GenerateToken(claims)
}

func (a *actionToken) Compare(tokenString string, verifyFunc func(claims *ActionTokenClaims) error) error {
	token, err := a.jwtAuth.ParseToken(tokenString, &ActionTokenClaims{})
	if err != nil {
		if ve, ok := err.(*jwt.ValidationError); ok && ve.Errors&jwt.ValidationErrorExpired != 0 {
			return ErrTokenExpired
		}
		return err
	}

	claims, ok := token.Claims.(*ActionTokenClaims)
	if !ok {
		return ErrTokenInvalid
	}
	if err = verifyFunc(claims); err != nil {
		return err
	}

	return nil
}
