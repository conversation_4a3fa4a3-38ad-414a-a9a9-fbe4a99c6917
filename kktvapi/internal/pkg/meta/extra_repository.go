//go:generate mockgen -source extra_repository.go -destination extra_repository_mock.go -package meta
package meta

import (
	"database/sql"
	"errors"
	"sync"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/pkg/database"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbmeta"
)

type ExtraRepository interface {
	GetByID(id string) (*dbmeta.Extra, error)
}

var (
	extraRepository ExtraRepository
	onceExtraRepo   sync.Once
)

type extraRepo struct {
	metaDBReader database.DB
}

func NewExtraRepository() ExtraRepository {
	onceExtraRepo.Do(func() {
		extraRepository = &extraRepo{
			metaDBReader: container.DBPoolMeta().Slave().Unsafe(),
		}
	})
	return extraRepository
}

func (e *extraRepo) GetByID(id string) (*dbmeta.Extra, error) {
	record := new(dbmeta.Extra)
	if err := e.metaDBReader.Get(record, `SELECT * FROM meta_extra WHERE id = $1`, id); errors.Is(err, sql.ErrNoRows) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}
	return record, nil
}
