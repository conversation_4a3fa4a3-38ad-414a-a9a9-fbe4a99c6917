// Code generated by MockGen. DO NOT EDIT.
// Source: kktvapi/internal/pkg/meta/text_content_repository.go

// Package meta is a generated GoMock package.
package meta

import (
	reflect "reflect"

	cachemeta "github.com/KKTV/kktv-api-v3/pkg/model/cachemeta"
	gomock "github.com/golang/mock/gomock"
)

// MockTextContentRepository is a mock of TextContentRepository interface.
type MockTextContentRepository struct {
	ctrl     *gomock.Controller
	recorder *MockTextContentRepositoryMockRecorder
}

// MockTextContentRepositoryMockRecorder is the mock recorder for MockTextContentRepository.
type MockTextContentRepositoryMockRecorder struct {
	mock *MockTextContentRepository
}

// NewMockTextContentRepository creates a new mock instance.
func NewMockTextContentRepository(ctrl *gomock.Controller) *MockTextContentRepository {
	mock := &MockTextContentRepository{ctrl: ctrl}
	mock.recorder = &MockTextContentRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTextContentRepository) EXPECT() *MockTextContentRepositoryMockRecorder {
	return m.recorder
}

// Get mocks base method.
func (m *MockTextContentRepository) Get() (*cachemeta.TextContent, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get")
	ret0, _ := ret[0].(*cachemeta.TextContent)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockTextContentRepositoryMockRecorder) Get() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockTextContentRepository)(nil).Get))
}
