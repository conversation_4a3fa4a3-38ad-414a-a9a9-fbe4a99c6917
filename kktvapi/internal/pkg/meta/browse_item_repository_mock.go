// Code generated by MockGen. DO NOT EDIT.
// Source: browse_item_repository.go

// Package meta is a generated GoMock package.
package meta

import (
	reflect "reflect"

	cachemeta "github.com/KKTV/kktv-api-v3/pkg/model/cachemeta"
	gomock "github.com/golang/mock/gomock"
)

// MockBrowseItemRepository is a mock of BrowseItemRepository interface.
type MockBrowseItemRepository struct {
	ctrl     *gomock.Controller
	recorder *MockBrowseItemRepositoryMockRecorder
}

// MockBrowseItemRepositoryMockRecorder is the mock recorder for MockBrowseItemRepository.
type MockBrowseItemRepositoryMockRecorder struct {
	mock *MockBrowseItemRepository
}

// NewMockBrowseItemRepository creates a new mock instance.
func NewMockBrowseItemRepository(ctrl *gomock.Controller) *MockBrowseItemRepository {
	mock := &MockBrowseItemRepository{ctrl: ctrl}
	mock.recorder = &MockBrowseItemRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockBrowseItemRepository) EXPECT() *MockBrowseItemRepositoryMockRecorder {
	return m.recorder
}

// FindByCollection mocks base method.
func (m *MockBrowseItemRepository) FindByCollection(key, name string) (*cachemeta.BrowseItem, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindByCollection", key, name)
	ret0, _ := ret[0].(*cachemeta.BrowseItem)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByCollection indicates an expected call of FindByCollection.
func (mr *MockBrowseItemRepositoryMockRecorder) FindByCollection(key, name interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByCollection", reflect.TypeOf((*MockBrowseItemRepository)(nil).FindByCollection), key, name)
}

// ListByPlatform mocks base method.
func (m *MockBrowseItemRepository) ListByPlatform(platform cachemeta.BrowsePlatform) ([]*cachemeta.BrowseItem, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByPlatform", platform)
	ret0, _ := ret[0].([]*cachemeta.BrowseItem)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByPlatform indicates an expected call of ListByPlatform.
func (mr *MockBrowseItemRepositoryMockRecorder) ListByPlatform(platform interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByPlatform", reflect.TypeOf((*MockBrowseItemRepository)(nil).ListByPlatform), platform)
}
