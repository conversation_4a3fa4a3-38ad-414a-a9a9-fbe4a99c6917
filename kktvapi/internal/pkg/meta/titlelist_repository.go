//go:generate mockgen -source titlelist_repository.go -destination titlelist_repository_mock.go -package meta
package meta

import (
	"database/sql"
	"errors"
	"sync"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/deeplink"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/database"
	"github.com/KKTV/kktv-api-v3/pkg/key"
	metamodel "github.com/KKTV/kktv-api-v3/pkg/model/dbmeta"
	"github.com/lib/pq"
)

var (
	titlelistRepo     TitlelistRepository
	onceTitlelistRepo sync.Once
)

type TitlelistRepository interface {
	ListHeadlines(collectionKey string, displayTime time.Time) ([]*dbmeta.TitleList, error)
	ListHighlight(displayTime time.Time) ([]*dbmeta.TitleList, error)
	GetPinned(collectionKey string, pinType metamodel.TitlelistPinType, displayTime time.Time) (*dbmeta.TitleList, error)
	ListChoices(collectionKey string, displayTime time.Time) ([]*dbmeta.TitleList, error)
	GetRanking(collectionKey string, displayTime time.Time) (*dbmeta.TitleList, error)
	GetByShareID(shareID string) (*dbmeta.TitleList, error)
	ListOnlyTitlesList(displayTime time.Time) ([]*dbmeta.TitleList, error)
	GetDefaultFallbackURL() (string, error)
}

type titleListRepository struct {
	dbReader        database.DB
	metaCacheReader cache.Cacher
}

// NewTitlelistRepositoryWith creates a new instance of TitlelistRepository with given dependencies
func NewTitlelistRepositoryWith(dbReader database.DB, cacheReader cache.Cacher) TitlelistRepository {
	return &titleListRepository{
		dbReader:        dbReader,
		metaCacheReader: cacheReader,
	}
}

func NewTitleListRepository() TitlelistRepository {
	onceTitlelistRepo.Do(func() {
		titlelistRepo = &titleListRepository{
			dbReader:        container.DBPoolMeta().Slave(),
			metaCacheReader: cache.New(container.CachePoolMeta().Slave()),
		}
	})
	return titlelistRepo
}

func (r *titleListRepository) ListHighlight(displayTime time.Time) ([]*dbmeta.TitleList, error) {
	var records []*dbmeta.TitleList
	if err := r.dbReader.Select(&records,
		`SELECT * FROM meta_titlelist
		WHERE
			list_type = 'highlight'
			AND visible_since <= $1 AND visible_until >= $1
			AND enabled = true
		ORDER BY "order" ASC`,
		displayTime); err != nil {
		return nil, err
	}
	//FIXME should just use sql.Scan
	for _, r := range records {
		r.Parse()
	}
	return records, nil
}

func (r *titleListRepository) ListHeadlines(collectionKey string, displayTime time.Time) ([]*dbmeta.TitleList, error) {
	var records []*dbmeta.TitleList
	if err := r.dbReader.Select(&records,
		`SELECT * FROM meta_titlelist
		WHERE
			list_type = ANY(ARRAY['title', 'link']) AND meta->'collections' ? $1
			AND visible_since <= $2 AND visible_until >= $2
			AND enabled = true
		ORDER BY "order" ASC`,
		collectionKey, displayTime); err != nil {
		return nil, err
	}
	//FIXME should just use sql.Scan
	for _, r := range records {
		r.Parse()
	}
	return records, nil
}

func (r *titleListRepository) GetPinned(collectionKey string, pinType metamodel.TitlelistPinType, displayTime time.Time) (*dbmeta.TitleList, error) {
	var record dbmeta.TitleList
	if err := r.dbReader.Get(&record,
		`SELECT * FROM meta_titlelist
		WHERE
			list_type = 'choice'
			AND meta->'collections' ? $1 AND meta->>'pinned' = $2
			AND visible_since <= $3 AND visible_until >= $3
			AND enabled = true
		ORDER BY "order" ASC
		LIMIT 1`,
		collectionKey, pinType, displayTime); errors.Is(err, sql.ErrNoRows) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}
	//FIXME should just use sql.Scan
	record.Parse()
	return &record, nil
}

func (r *titleListRepository) ListChoices(collectionKey string, displayTime time.Time) ([]*dbmeta.TitleList, error) {
	var records []*dbmeta.TitleList
	if err := r.dbReader.Select(&records,
		`SELECT * FROM meta_titlelist
		WHERE
			meta->'airing' is null
			AND list_type = 'choice' AND meta->'collections' ? $1
			AND visible_since <= $2 AND visible_until >= $2
			AND (
				meta->>'pinned' is null
				OR meta->>'pinned' = 'guaranteed_visible'
			)
			AND enabled = true
		ORDER BY "order" ASC`,
		collectionKey, displayTime); err != nil {
		return nil, err
	}
	//FIXME should just use sql.Scan
	for _, r := range records {
		r.Parse()
	}
	return records, nil
}

func (r *titleListRepository) GetRanking(collectionKey string, displayTime time.Time) (*dbmeta.TitleList, error) {
	var record dbmeta.TitleList
	if err := r.dbReader.Get(&record,
		`SELECT * FROM meta_titlelist
		WHERE
			list_type = 'ranking'
			AND meta->'collections' ? $1
			AND visible_since <= $2 AND visible_until >= $2
			AND enabled = true
		ORDER BY "order" ASC
		LIMIT 1`,
		collectionKey, displayTime); errors.Is(err, sql.ErrNoRows) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}
	//FIXME should just use sql.Scan
	record.Parse()
	return &record, nil
}

func (r *titleListRepository) GetByShareID(shareID string) (*dbmeta.TitleList, error) {
	var record dbmeta.TitleList
	if err := r.dbReader.Get(&record,
		`SELECT * FROM meta_titlelist
		WHERE meta->>'share_id' = $1 AND enabled = true
		LIMIT 1`,
		shareID); errors.Is(err, sql.ErrNoRows) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}
	//FIXME should just use sql.Scan
	record.Parse()
	return &record, nil
}

// ListOnlyTitlesList returns only title lists but not including Headline
func (r *titleListRepository) ListOnlyTitlesList(displayTime time.Time) ([]*dbmeta.TitleList, error) {
	onlyTitleListTypes := pq.StringArray{
		metamodel.TitlelistTypeHighlight.String(),
		metamodel.TitlelistTypeChoice.String(),
		metamodel.TitlelistTypeRanking.String(),
		metamodel.TitlelistTypeAiring.String(),
	}
	var records []*dbmeta.TitleList
	if err := r.dbReader.Select(&records,
		`SELECT * FROM meta_titlelist 
		WHERE 
			list_type = ANY($1)
			AND visible_since <= $2 AND visible_until >= $2
			AND enabled = true
			AND jsonb_array_length(meta->'title_id') >= 4
			AND meta->>'share_id' is not null
		ORDER BY "order" ASC`,
		onlyTitleListTypes, displayTime); err != nil {
		return nil, err
	}

	for _, r := range records {
		r.Parse()
	}
	return records, nil
}

func (r *titleListRepository) GetDefaultFallbackURL() (string, error) {
	var shareID string
	err := r.metaCacheReader.Get(key.GetFallbackTitlelistPage(), &shareID)
	if shareID != "" {
		return deeplink.TitleListPage(shareID), nil
	}
	return "", err
}
