//go:generate mockgen -source episode_repository.go -destination episode_repository_mock.go -package meta
package meta

import (
	"database/sql"
	"errors"
	"sync"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/pkg/database"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbmeta"
)

type EpisodeRepository interface {
	GetByID(id string) (*dbmeta.Episode, error)
}

var (
	episodeRepository EpisodeRepository
	onceEpisodeRepo   sync.Once
)

type episodeRepo struct {
	metaDBReader database.DB
}

func NewEpisodeRepository() EpisodeRepository {
	onceEpisodeRepo.Do(func() {
		episodeRepository = &episodeRepo{
			metaDBReader: container.DBPoolMeta().Slave().Unsafe(),
		}
	})
	return episodeRepository
}

// GetByID returns the episode by given episode id.
func (r *episodeRepo) GetByID(id string) (*dbmeta.Episode, error) {
	var record dbmeta.Episode
	if err := r.metaDBReader.Get(&record, `SELECT * FROM meta_episode WHERE id = $1`, id); errors.Is(err, sql.ErrNoRows) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}
	return &record, nil
}
