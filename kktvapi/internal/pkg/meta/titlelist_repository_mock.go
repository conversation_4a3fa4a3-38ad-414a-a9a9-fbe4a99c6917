// Code generated by MockGen. DO NOT EDIT.
// Source: titlelist_repository.go

// Package meta is a generated GoMock package.
package meta

import (
	reflect "reflect"
	time "time"

	dbmeta "github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	dbmeta0 "github.com/KKTV/kktv-api-v3/pkg/model/dbmeta"
	gomock "github.com/golang/mock/gomock"
)

// MockTitlelistRepository is a mock of TitlelistRepository interface.
type MockTitlelistRepository struct {
	ctrl     *gomock.Controller
	recorder *MockTitlelistRepositoryMockRecorder
}

// MockTitlelistRepositoryMockRecorder is the mock recorder for MockTitlelistRepository.
type MockTitlelistRepositoryMockRecorder struct {
	mock *MockTitlelistRepository
}

// NewMockTitlelistRepository creates a new mock instance.
func NewMockTitlelistRepository(ctrl *gomock.Controller) *MockTitlelistRepository {
	mock := &MockTitlelistRepository{ctrl: ctrl}
	mock.recorder = &MockTitlelistRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTitlelistRepository) EXPECT() *MockTitlelistRepositoryMockRecorder {
	return m.recorder
}

// GetByShareID mocks base method.
func (m *MockTitlelistRepository) GetByShareID(shareID string) (*dbmeta.TitleList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByShareID", shareID)
	ret0, _ := ret[0].(*dbmeta.TitleList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByShareID indicates an expected call of GetByShareID.
func (mr *MockTitlelistRepositoryMockRecorder) GetByShareID(shareID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByShareID", reflect.TypeOf((*MockTitlelistRepository)(nil).GetByShareID), shareID)
}

// GetDefaultFallbackURL mocks base method.
func (m *MockTitlelistRepository) GetDefaultFallbackURL() (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDefaultFallbackURL")
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDefaultFallbackURL indicates an expected call of GetDefaultFallbackURL.
func (mr *MockTitlelistRepositoryMockRecorder) GetDefaultFallbackURL() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDefaultFallbackURL", reflect.TypeOf((*MockTitlelistRepository)(nil).GetDefaultFallbackURL))
}

// GetPinned mocks base method.
func (m *MockTitlelistRepository) GetPinned(collectionKey string, pinType dbmeta0.TitlelistPinType, displayTime time.Time) (*dbmeta.TitleList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPinned", collectionKey, pinType, displayTime)
	ret0, _ := ret[0].(*dbmeta.TitleList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPinned indicates an expected call of GetPinned.
func (mr *MockTitlelistRepositoryMockRecorder) GetPinned(collectionKey, pinType, displayTime interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPinned", reflect.TypeOf((*MockTitlelistRepository)(nil).GetPinned), collectionKey, pinType, displayTime)
}

// GetRanking mocks base method.
func (m *MockTitlelistRepository) GetRanking(collectionKey string, displayTime time.Time) (*dbmeta.TitleList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRanking", collectionKey, displayTime)
	ret0, _ := ret[0].(*dbmeta.TitleList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRanking indicates an expected call of GetRanking.
func (mr *MockTitlelistRepositoryMockRecorder) GetRanking(collectionKey, displayTime interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRanking", reflect.TypeOf((*MockTitlelistRepository)(nil).GetRanking), collectionKey, displayTime)
}

// ListChoices mocks base method.
func (m *MockTitlelistRepository) ListChoices(collectionKey string, displayTime time.Time) ([]*dbmeta.TitleList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListChoices", collectionKey, displayTime)
	ret0, _ := ret[0].([]*dbmeta.TitleList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListChoices indicates an expected call of ListChoices.
func (mr *MockTitlelistRepositoryMockRecorder) ListChoices(collectionKey, displayTime interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListChoices", reflect.TypeOf((*MockTitlelistRepository)(nil).ListChoices), collectionKey, displayTime)
}

// ListHeadlines mocks base method.
func (m *MockTitlelistRepository) ListHeadlines(collectionKey string, displayTime time.Time) ([]*dbmeta.TitleList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListHeadlines", collectionKey, displayTime)
	ret0, _ := ret[0].([]*dbmeta.TitleList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListHeadlines indicates an expected call of ListHeadlines.
func (mr *MockTitlelistRepositoryMockRecorder) ListHeadlines(collectionKey, displayTime interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListHeadlines", reflect.TypeOf((*MockTitlelistRepository)(nil).ListHeadlines), collectionKey, displayTime)
}

// ListHighlight mocks base method.
func (m *MockTitlelistRepository) ListHighlight(displayTime time.Time) ([]*dbmeta.TitleList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListHighlight", displayTime)
	ret0, _ := ret[0].([]*dbmeta.TitleList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListHighlight indicates an expected call of ListHighlight.
func (mr *MockTitlelistRepositoryMockRecorder) ListHighlight(displayTime interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListHighlight", reflect.TypeOf((*MockTitlelistRepository)(nil).ListHighlight), displayTime)
}

// ListOnlyTitlesList mocks base method.
func (m *MockTitlelistRepository) ListOnlyTitlesList(displayTime time.Time) ([]*dbmeta.TitleList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListOnlyTitlesList", displayTime)
	ret0, _ := ret[0].([]*dbmeta.TitleList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListOnlyTitlesList indicates an expected call of ListOnlyTitlesList.
func (mr *MockTitlelistRepositoryMockRecorder) ListOnlyTitlesList(displayTime interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListOnlyTitlesList", reflect.TypeOf((*MockTitlelistRepository)(nil).ListOnlyTitlesList), displayTime)
}
