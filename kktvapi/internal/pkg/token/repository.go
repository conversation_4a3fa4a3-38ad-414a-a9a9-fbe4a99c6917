//go:generate mockgen -source repository.go -destination repository_mock.go -package token
package token

import (
	"crypto/sha256"
	"database/sql"
	"errors"
	"fmt"
	"sync"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/pkg/clock"
	"github.com/KKTV/kktv-api-v3/pkg/database"
	"github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/KKTV/kktv-api-v3/pkg/rand"
	"gopkg.in/guregu/null.v3"
)

type Repository interface {
	WithTx(tx database.Tx) Repository
	Create(token *dbuser.Token) (*dbuser.Token, error)
	GetByTokenHashAndUserID(tokenHash, userID string) (*dbuser.Token, error)
}

type repository struct {
	dbReader database.DB
	dbWriter database.DB
	clock    clock.Clock
	random   rand.Rand
}

var (
	tokenRepo     Repository
	onceTokenRepo sync.Once
)

func NewRepository() Repository {
	onceTokenRepo.Do(func() {
		tokenRepo = &repository{
			dbReader: container.DBPoolUser().Slave().Unsafe(),
			dbWriter: container.DBPoolUser().Master().Unsafe(),
			clock:    clock.New(),
			random:   rand.New(),
		}
	})
	return tokenRepo
}

func NewRepositoryWithTx(db database.DB) Repository {
	tokenRepo = &repository{
		dbWriter: db,
		dbReader: db,
		clock:    clock.New(),
		random:   rand.New(),
	}
	return tokenRepo
}

func (r *repository) WithTx(tx database.Tx) Repository {
	return &repository{
		dbReader: tx,
		dbWriter: tx,
		clock:    r.clock,
		random:   r.random,
	}
}

func (r *repository) Create(token *dbuser.Token) (*dbuser.Token, error) {
	now := r.clock.Now()
	idBytes := sha256.Sum256([]byte(r.random.UUID()))

	token.ID = fmt.Sprintf("%x", idBytes)
	token.CreatedAt = null.TimeFrom(now)
	token.UpdatedAt = null.TimeFrom(now)

	dbFields := database.GetDBFields(token)
	sql := `INSERT INTO tokens (` + dbFields.String() + `) VALUES (` + dbFields.NamedString() + `);`
	log.Debug("token repository: create").Str("sql", sql)

	if _, err := r.dbWriter.NamedExec(sql, token); err != nil {
		return nil, err
	}

	return token, nil
}

func (r *repository) GetByTokenHashAndUserID(tokenHash, userID string) (*dbuser.Token, error) {
	token := new(dbuser.Token)
	if err := r.dbReader.Get(token,
		`SELECT
			id, user_id, token, token_hash, source_ip, user_agent,
			expired_at,
			created_at, updated_at
		FROM tokens
		WHERE token_hash = $1 AND user_id = $2
			AND expired_at >= NOW() + interval '1 days'`,
		tokenHash, userID); errors.Is(err, sql.ErrNoRows) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}

	return token, nil
}
