//go:generate mockgen -source repository.go -destination repository_mock.go -package productpackage
package productpackage

import (
	"database/sql"
	"errors"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/pkg/database"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/lib/pq"
	"sync"
)

var (
	productPackageRepo     Repository
	onceProductPackageRepo sync.Once
)

type Repository interface {
	GetByID(id int64) (*dbuser.ProductPackage, error)
	GetByIDs(ids []int) ([]*dbuser.ProductPackage, error)
	GetByBillingID(billingID string) (*dbuser.ProductPackage, error)
	GetByProductID(productID int64) (*dbuser.ProductPackage, error)
}

type repository struct {
	dbReader database.DB
	dbWriter database.DB
}

func NewRepository() Repository {
	onceProductPackageRepo.Do(func() {
		productPackageRepo = &repository{
			dbReader: container.DBPoolUser().Slave().Unsafe(),
			dbWriter: container.DBPoolUser().Master().Unsafe(),
		}
	})
	return productPackageRepo
}

func NewRepositoryWith(dbReader, dbWriter database.DB) Repository {
	return &repository{
		dbReader: dbReader,
		dbWriter: dbWriter,
	}
}

func (r *repository) GetByID(id int64) (*dbuser.ProductPackage, error) {
	productPackage := new(dbuser.ProductPackage)
	if err := r.dbReader.Get(productPackage,
		`SELECT * FROM product_packages WHERE id = $1`,
		id); errors.Is(err, sql.ErrNoRows) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}

	return productPackage, nil
}

func (r *repository) GetByIDs(ids []int) ([]*dbuser.ProductPackage, error) {
	var productPackages []*dbuser.ProductPackage
	if err := r.dbReader.Select(&productPackages,
		`SELECT * FROM product_packages WHERE id = ANY($1)`,
		pq.Array(ids)); err != nil {
		return nil, err
	}
	return productPackages, nil
}

func (r *repository) GetByBillingID(billingID string) (*dbuser.ProductPackage, error) {
	productPackage := new(dbuser.ProductPackage)
	if err := r.dbReader.Get(productPackage,
		`SELECT * FROM product_packages WHERE billing_product_ids ? $1`,
		billingID); errors.Is(err, sql.ErrNoRows) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}
	return productPackage, nil
}

func (r *repository) GetByProductID(productID int64) (*dbuser.ProductPackage, error) {
	productPackage := new(dbuser.ProductPackage)
	if err := r.dbReader.Get(productPackage,
		`SELECT * FROM product_packages WHERE product_ids ? $1`,
		productID); errors.Is(err, sql.ErrNoRows) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}
	return productPackage, nil
}
