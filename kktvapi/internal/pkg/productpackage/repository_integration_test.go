package productpackage

import (
	_ "embed"
	"os"
	"testing"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/dbtest"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

type RepositoryTestSuite struct {
	dbtest.Suite

	r    *require.Assertions
	ctrl *gomock.Controller
	repo Repository
}

func init() {
	_ = os.Setenv("TEST_DB_USER_DSN", "postgres://foo:bar@localhost:5432/kktv_users?sslmode=disable")
}

func TestRepositoryIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(RepositoryTestSuite))
}

func (suite *RepositoryTestSuite) SetupSuite() {
	suite.Suite.SetupSuite(os.Getenv("TEST_DB_USER_DSN"))
}

func (suite *RepositoryTestSuite) SetupTest() {
	suite.Suite.SetupTest()

	tx := suite.GetTransaction()
	suite.r = suite.Require()
	suite.ctrl = gomock.NewController(suite.T())
	suite.repo = &repository{
		dbReader: tx,
		dbWriter: tx,
	}
}

func (suite *RepositoryTestSuite) TearDownTest() {
	suite.Suite.TearDownTest()
}

func (suite *RepositoryTestSuite) TestGetByBillingID() {
	suite.givenProductPackages()

	testcases := []struct {
		name      string
		billingID string
		then      func(productPackage *dbuser.ProductPackage, err error)
	}{
		{
			name:      "should return nil when billing id not found",
			billingID: "test-not-found-billing-id",
			then: func(productPackage *dbuser.ProductPackage, err error) {
				suite.r.NoError(err)
				suite.r.Nil(productPackage)
			},
		},
		{
			name:      "should return product package when billing id found",
			billingID: "kktv.vip.cc.1y.sub.777.prime-only",
			then: func(productPackage *dbuser.ProductPackage, err error) {
				suite.r.NoError(err)
				suite.r.NotNil(productPackage)
				suite.r.Equal("kktv.vip.cc.1y.sub.777.prime-only", (*productPackage.BillingProductIds)[0])
			},
		},
	}

	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			tc.then(suite.repo.GetByBillingID(tc.billingID))
		})
	}
}

func (suite *RepositoryTestSuite) TestGetByIDs() {
	suite.givenProductPackages()

	testcases := []struct {
		name string
		ids  []int
		then func(productPackages []*dbuser.ProductPackage, err error)
	}{
		{
			name: "should return nil when ids not found",
			ids:  []int{999999999},
			then: func(productPackages []*dbuser.ProductPackage, err error) {
				suite.r.NoError(err)
				suite.r.Len(productPackages, 0)
			},
		},
		{
			name: "should return product packages when ids found",
			ids:  []int{999990, 999991, 999992},
			then: func(productPackages []*dbuser.ProductPackage, err error) {
				suite.r.NoError(err)
				suite.r.Len(productPackages, 3)
				suite.r.Equal(999990, productPackages[0].ID)
				suite.r.Equal(999991, productPackages[1].ID)
				suite.r.Equal(999992, productPackages[2].ID)
			},
		},
	}

	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			tc.then(suite.repo.GetByIDs(tc.ids))
		})
	}
}

//go:embed testdata/repository/Repo.sql
var dataForProductPackageRepo string

func (suite *RepositoryTestSuite) givenProductPackages() {
	if _, err := suite.GetTransaction().Exec(dataForProductPackageRepo); err != nil {
		suite.T().Fatal("failed to insert test data for product package:", err)
	}
}
