package validation

import (
	"testing"
)

func TestPasswordValidator_Validate(t *testing.T) {

	tests := []struct {
		name    string
		input   interface{}
		wantErr bool
	}{
		{
			name: "valid password",
			input: struct {
				Password string `validate:"password"`
			}{
				Password: "s@cr&e[]",
			},
			wantErr: false,
		},
		{
			name: "has left space, SH<PERSON><PERSON><PERSON> got err",
			input: struct {
				Password string `validate:"password"`
			}{
				Password: "  s@cr&e[]",
			},
			wantErr: true,
		},
		{
			name: "not enough length, SHOULD got err",
			input: struct {
				Password string `validate:"password,min=10"`
			}{
				Password: "s@cr&e[]",
			},
			wantErr: true,
		},
		{
			name: "space inside, SHOULD be ok",
			input: struct {
				Password string `validate:"password,min=8"`
			}{
				Password: "s@cr& e]",
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Validate(tt.input); tt.wantErr != (got != nil) {
				t.<PERSON>rrorf("Validate() = %v, want %v", got, tt.wantErr)
			}
		})
	}
}
