package validation

import (
	"regexp"
	"strings"

	"github.com/go-playground/validator/v10"
)

type PasswordValidator struct {
	matcher *regexp.Regexp
}

func NewPasswordValidator() PasswordValidator {
	return PasswordValidator{
		matcher: regexp.MustCompile("^[a-zA-Z0-9-!$%^&*@#()_+|~=`{}\\[\\]:\";'<>?,.\\/\\ ]+$"),
	}
}

func (v PasswordValidator) TagName() string {
	return "password"
}

func (v PasswordValidator) Validate(fl validator.FieldLevel) bool {
	password, _ := fl.Field().Interface().(string)
	if trim := strings.TrimRight(strings.TrimLeft(password, " "), " "); trim != password {
		return false
	}
	return v.matcher.MatchString(password)
}
