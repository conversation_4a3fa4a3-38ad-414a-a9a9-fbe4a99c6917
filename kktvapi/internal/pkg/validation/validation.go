package validation

import (
	plog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/go-playground/validator/v10"
)

var validate *validator.Validate

func init() {
	validate = validator.New()

	passwordValidator := NewPasswordValidator()
	err := validate.RegisterValidation(passwordValidator.TagName(), passwordValidator.Validate)
	if err != nil {
		plog.Error("validation: failed to register password validator").Err(err).Send()
		return
	}
}

func Validate(s interface{}) error {
	return validate.Struct(s)
}
