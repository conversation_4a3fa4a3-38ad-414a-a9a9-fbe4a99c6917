// Code generated by MockGen. DO NOT EDIT.
// Source: authorization_repository.go

// Package oauth is a generated GoMock package.
package oauth

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockAuthorizationRepository is a mock of AuthorizationRepository interface.
type MockAuthorizationRepository struct {
	ctrl     *gomock.Controller
	recorder *MockAuthorizationRepositoryMockRecorder
}

// MockAuthorizationRepositoryMockRecorder is the mock recorder for MockAuthorizationRepository.
type MockAuthorizationRepositoryMockRecorder struct {
	mock *MockAuthorizationRepository
}

// NewMockAuthorizationRepository creates a new mock instance.
func NewMockAuthorizationRepository(ctrl *gomock.Controller) *MockAuthorizationRepository {
	mock := &MockAuthorizationRepository{ctrl: ctrl}
	mock.recorder = &MockAuthorizationRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAuthorizationRepository) EXPECT() *MockAuthorizationRepositoryMockRecorder {
	return m.recorder
}

// CreateAuthorization mocks base method.
func (m *MockAuthorizationRepository) CreateAuthorization(appID, userID, scope, redirectURI string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAuthorization", appID, userID, scope, redirectURI)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateAuthorization indicates an expected call of CreateAuthorization.
func (mr *MockAuthorizationRepositoryMockRecorder) CreateAuthorization(appID, userID, scope, redirectURI interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAuthorization", reflect.TypeOf((*MockAuthorizationRepository)(nil).CreateAuthorization), appID, userID, scope, redirectURI)
}
