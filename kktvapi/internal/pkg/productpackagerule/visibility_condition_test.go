package productpackagerule

import (
	"testing"

	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
)

func TestBuildRulesFromPackage(t *testing.T) {
	tests := []struct {
		name                 string
		pkg                  *dbuser.ProductPackage
		userTargetIdentities []dbuser.PackageTargetIdentity
		wantRuleCount        int
		wantFirstRule        func(rule AndRule) bool
		wantSecondRule       func(rule AndRule) bool
		wantSatisfied        []bool
		userLatestPackageID  int64
	}{
		{
			name: "基本情境：一個include一個exclude",
			pkg: &dbuser.ProductPackage{
				ID: 1,
				Targets: &dbuser.PackageTargets{
					{
						Condition: dbuser.PackageTargetCondition{
							Identities: []string{"vip", "expired.purchased"},
							LatestPackages: &dbuser.PackageTargetLatestPackages{
								Operator: "include",
								IDs:      []int64{1, 2},
							},
						},
					},
					{
						Condition: dbuser.PackageTargetCondition{
							Identities: []string{"prime"},
							LatestPackages: &dbuser.PackageTargetLatestPackages{
								Operator: "exclude",
								IDs:      []int64{3},
							},
						},
					},
				},
			},
			userTargetIdentities: []dbuser.PackageTargetIdentity{dbuser.PackageTargetIdentityVip},
			wantRuleCount:        2,
			wantFirstRule: func(rule AndRule) bool {
				if len(rule.Rules) < 2 {
					return false
				}
				_, ok1 := rule.Rules[0].(*IdentityRule)
				_, ok2 := rule.Rules[1].(*IncludeRule[int64])
				return ok1 && ok2
			},
			wantSecondRule: func(rule AndRule) bool {
				if len(rule.Rules) < 2 {
					return false
				}
				_, ok1 := rule.Rules[0].(*IdentityRule)
				_, ok2 := rule.Rules[1].(*ExcludeRule[int64])
				return ok1 && ok2
			},
			wantSatisfied:       []bool{true, true},
			userLatestPackageID: 1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			rulesWithTargets := BuildRulesFromPackage(tt.pkg, tt.userTargetIdentities, tt.userLatestPackageID)
			if len(rulesWithTargets[0].Rule.Rules) != tt.wantRuleCount {
				t.Fatalf("預期產生 %d 個 rule，實際為 %d", tt.wantRuleCount, len(rulesWithTargets[0].Rule.Rules))
			}
			if !tt.wantFirstRule(*rulesWithTargets[0].Rule) {
				t.Errorf("第一個 rule 型別或內容不符預期")
			}
			if !tt.wantSecondRule(*rulesWithTargets[1].Rule) {
				t.Errorf("第二個 rule 型別或內容不符預期")
			}

			for i, want := range tt.wantSatisfied {
				got := rulesWithTargets[0].Rule.IsSatisfied()
				if got != want {
					t.Errorf("第 %d 個 rule 的結果不符預期，預期為 %t，實際為 %t", i+1, want, got)
				}
			}
		})
	}
}
