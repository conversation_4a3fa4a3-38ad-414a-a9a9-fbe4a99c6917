package productpackagerule

type ExcludeRule[T comparable] struct {
	ExcludedValues []T
	CurrentValue   T
}

func (er *ExcludeRule[T]) IsSatisfied() bool {
	if len(er.ExcludedValues) == 0 {
		return true
	}

	for _, excludedValue := range er.ExcludedValues {
		if excludedValue == er.CurrentValue {
			return false
		}
	}

	return true
}

func NewExcludeRule[T comparable](excludedValues []T, currentValue T) *ExcludeRule[T] {
	return &ExcludeRule[T]{
		ExcludedValues: excludedValues,
		CurrentValue:   currentValue,
	}
}
