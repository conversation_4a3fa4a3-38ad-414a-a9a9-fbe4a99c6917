//go:generate mockgen -source repository.go -destination repository_mock.go -package event
package event

import (
	"errors"
	"sync"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/key"
)

var (
	repo     Repository
	onceRepo sync.Once
)

type Repository interface {
	ListAvailable(timing time.Time) ([]model.EventItem, error)
}

type repository struct {
	cacheReader cache.Cacher
}

func NewRepository() Repository {
	onceRepo.Do(func() {
		repo = &repository{
			cacheReader: cache.New(container.CachePoolMeta().Slave()),
		}
	})
	return repo
}

type eventObj struct {
	Events []model.EventItem `json:"events"`
}

func (t *repository) ListAvailable(timing time.Time) ([]model.EventItem, error) {
	var obj eventObj
	err := t.cacheReader.Get(key.GetSystemEvents(), &obj)
	if errors.Is(err, cache.ErrCacheMiss) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}

	ets := make([]model.EventItem, 0)
	for _, e := range obj.Events {
		targetTime := timing.Unix()
		if !(targetTime >= e.StartTime && targetTime <= e.EndTime) {
			continue
		}
		ets = append(ets, e)
	}
	return ets, nil
}
