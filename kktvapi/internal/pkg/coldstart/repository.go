//go:generate mockgen -source repository.go -destination repository_mock.go -package coldstart
package coldstart

import (
	"errors"
	"fmt"
	"sync"
	"time"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/kktverror"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/key"
	"github.com/KKTV/kktv-api-v3/pkg/model/cacheuser"
)

var (
	repo     Repository
	onceRepo sync.Once
)

const (
	ttlTitlePool = 24 * time.Hour          // 1 day
	ttlUserPref  = 1 * 30 * 24 * time.Hour // 1 month
)

type Repository interface {
	ListPreferenceOptions() ([]cacheuser.ColdStartOptionGroup, error)
	GetUserPreference(userID, deviceID string) (*cacheuser.ColdStartUserPreference, error)
	GetOptionsRelations() (*cacheuser.ColdStartOptRelation, error)
	GetHottestTitleIDsByOption(optKey string) ([]string, error)
	UpdateUserPreference(userID string, deviceID string, pref *cacheuser.ColdStartUserPreference) error
	GetRecommendTitlePool(poolKey string) (*cacheuser.ColdStartOptionsTitlePool, error)
	UpdateRecommendTitlePool(poolKey string, pool *cacheuser.ColdStartOptionsTitlePool) error
}

type repository struct {
	userCacheReader cache.Cacher
	userCacheWriter cache.Cacher
	metaCacheReader cache.Cacher
}

func NewRepository() Repository {
	onceRepo.Do(func() {
		userRedisPool := container.CachePoolUser()
		repo = &repository{
			userCacheReader: cache.New(userRedisPool.Slave()),
			userCacheWriter: cache.New(userRedisPool.Master()),
			metaCacheReader: cache.New(container.CachePoolMeta().Slave()),
		}
	})
	return repo
}

func (c *repository) ListPreferenceOptions() ([]cacheuser.ColdStartOptionGroup, error) {
	var categories []cacheuser.ColdStartOptionGroup
	err := c.metaCacheReader.HGet(key.GetColdStartConfig(), "categories", &categories)
	if err != nil {
		return nil, err
	}
	return categories, nil
}

func (c *repository) GetUserPreference(userID, deviceID string) (*cacheuser.ColdStartUserPreference, error) {
	var cp cacheuser.ColdStartUserPreference
	if err := c.userCacheReader.Get(key.GetColdStartPreferenceOfUser(userID), &cp); err == nil {
		return &cp, nil
	} else if !errors.Is(err, cache.ErrCacheMiss) {
		return nil, err
	}
	if err := c.userCacheReader.Get(key.GetColdStartPreferenceOfDevice(deviceID), &cp); err == nil {
		return &cp, nil
	} else if !errors.Is(err, cache.ErrCacheMiss) {
		return nil, err
	}
	return nil, nil
}

func (c *repository) GetOptionsRelations() (*cacheuser.ColdStartOptRelation, error) {
	var lv0 cacheuser.ColdStartOptRelationLv0
	err := c.metaCacheReader.HGet(key.GetColdStartConfig(), "lv0_relation", &lv0)
	if err != nil {
		return nil, err
	}

	var lv1 cacheuser.ColdStartOptRelationLv1
	err = c.metaCacheReader.HGet(key.GetColdStartConfig(), "lv1_relation", &lv1)
	if err != nil {
		return nil, err
	}
	return &cacheuser.ColdStartOptRelation{
		Lv0: lv0,
		Lv1: lv1,
	}, nil
}

func (c *repository) GetHottestTitleIDsByOption(optKey string) ([]string, error) {
	var titleIDs []string
	err := c.metaCacheReader.HGet(key.ColdStartOptionTitlesMap(), optKey, &titleIDs)
	if errors.Is(err, cache.ErrCacheMiss) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}
	return titleIDs, nil
}

func (c *repository) UpdateUserPreference(userID string, deviceID string, pref *cacheuser.ColdStartUserPreference) error {
	var cKey string
	if userID != "" {
		cKey = key.GetColdStartPreferenceOfUser(userID)
	} else if deviceID != "" {
		cKey = key.GetColdStartPreferenceOfDevice(deviceID)
	} else {
		return fmt.Errorf("%w: need userID or deviceID", kktverror.ErrInvalidParameter)
	}
	if err := c.userCacheWriter.Set(cKey, pref, ttlUserPref); err != nil {
		return err
	}
	return nil
}

func (c *repository) GetRecommendTitlePool(optionsKey string) (*cacheuser.ColdStartOptionsTitlePool, error) {
	cKey := key.ColdStartTitlePool(optionsKey)
	var pool cacheuser.ColdStartOptionsTitlePool
	if err := c.userCacheReader.Get(cKey, &pool); errors.Is(err, cache.ErrCacheMiss) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}
	return &pool, nil
}

func (c *repository) UpdateRecommendTitlePool(optionsKey string, pool *cacheuser.ColdStartOptionsTitlePool) error {
	if pool == nil {
		return fmt.Errorf("%w: pool is nil", kktverror.ErrInvalidParameter)
	}
	cKey := key.ColdStartTitlePool(optionsKey)
	return c.userCacheWriter.Set(cKey, pool, ttlTitlePool)
}
