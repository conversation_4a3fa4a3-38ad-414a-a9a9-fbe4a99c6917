// Code generated by MockGen. DO NOT EDIT.
// Source: repository.go

// Package coldstart is a generated GoMock package.
package coldstart

import (
	reflect "reflect"

	cacheuser "github.com/KKTV/kktv-api-v3/pkg/model/cacheuser"
	gomock "github.com/golang/mock/gomock"
)

// MockRepository is a mock of Repository interface.
type MockRepository struct {
	ctrl     *gomock.Controller
	recorder *MockRepositoryMockRecorder
}

// MockRepositoryMockRecorder is the mock recorder for MockRepository.
type MockRepositoryMockRecorder struct {
	mock *MockRepository
}

// NewMockRepository creates a new mock instance.
func NewMockRepository(ctrl *gomock.Controller) *MockRepository {
	mock := &MockRepository{ctrl: ctrl}
	mock.recorder = &MockRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRepository) EXPECT() *MockRepositoryMockRecorder {
	return m.recorder
}

// GetHottestTitleIDsByOption mocks base method.
func (m *MockRepository) GetHottestTitleIDsByOption(optKey string) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHottestTitleIDsByOption", optKey)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHottestTitleIDsByOption indicates an expected call of GetHottestTitleIDsByOption.
func (mr *MockRepositoryMockRecorder) GetHottestTitleIDsByOption(optKey interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHottestTitleIDsByOption", reflect.TypeOf((*MockRepository)(nil).GetHottestTitleIDsByOption), optKey)
}

// GetOptionsRelations mocks base method.
func (m *MockRepository) GetOptionsRelations() (*cacheuser.ColdStartOptRelation, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOptionsRelations")
	ret0, _ := ret[0].(*cacheuser.ColdStartOptRelation)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOptionsRelations indicates an expected call of GetOptionsRelations.
func (mr *MockRepositoryMockRecorder) GetOptionsRelations() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOptionsRelations", reflect.TypeOf((*MockRepository)(nil).GetOptionsRelations))
}

// GetRecommendTitlePool mocks base method.
func (m *MockRepository) GetRecommendTitlePool(poolKey string) (*cacheuser.ColdStartOptionsTitlePool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRecommendTitlePool", poolKey)
	ret0, _ := ret[0].(*cacheuser.ColdStartOptionsTitlePool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRecommendTitlePool indicates an expected call of GetRecommendTitlePool.
func (mr *MockRepositoryMockRecorder) GetRecommendTitlePool(poolKey interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRecommendTitlePool", reflect.TypeOf((*MockRepository)(nil).GetRecommendTitlePool), poolKey)
}

// GetUserPreference mocks base method.
func (m *MockRepository) GetUserPreference(userID, deviceID string) (*cacheuser.ColdStartUserPreference, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserPreference", userID, deviceID)
	ret0, _ := ret[0].(*cacheuser.ColdStartUserPreference)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserPreference indicates an expected call of GetUserPreference.
func (mr *MockRepositoryMockRecorder) GetUserPreference(userID, deviceID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPreference", reflect.TypeOf((*MockRepository)(nil).GetUserPreference), userID, deviceID)
}

// ListPreferenceOptions mocks base method.
func (m *MockRepository) ListPreferenceOptions() ([]cacheuser.ColdStartOptionGroup, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListPreferenceOptions")
	ret0, _ := ret[0].([]cacheuser.ColdStartOptionGroup)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListPreferenceOptions indicates an expected call of ListPreferenceOptions.
func (mr *MockRepositoryMockRecorder) ListPreferenceOptions() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListPreferenceOptions", reflect.TypeOf((*MockRepository)(nil).ListPreferenceOptions))
}

// UpdateRecommendTitlePool mocks base method.
func (m *MockRepository) UpdateRecommendTitlePool(poolKey string, pool *cacheuser.ColdStartOptionsTitlePool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateRecommendTitlePool", poolKey, pool)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateRecommendTitlePool indicates an expected call of UpdateRecommendTitlePool.
func (mr *MockRepositoryMockRecorder) UpdateRecommendTitlePool(poolKey, pool interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRecommendTitlePool", reflect.TypeOf((*MockRepository)(nil).UpdateRecommendTitlePool), poolKey, pool)
}

// UpdateUserPreference mocks base method.
func (m *MockRepository) UpdateUserPreference(userID, deviceID string, pref *cacheuser.ColdStartUserPreference) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateUserPreference", userID, deviceID, pref)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateUserPreference indicates an expected call of UpdateUserPreference.
func (mr *MockRepositoryMockRecorder) UpdateUserPreference(userID, deviceID, pref interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateUserPreference", reflect.TypeOf((*MockRepository)(nil).UpdateUserPreference), userID, deviceID, pref)
}
