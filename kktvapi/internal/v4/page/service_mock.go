// Code generated by MockGen. DO NOT EDIT.
// Source: service.go

// Package page is a generated GoMock package.
package page

import (
	reflect "reflect"

	cachemeta "github.com/KKTV/kktv-api-v3/pkg/model/cachemeta"
	gomock "github.com/golang/mock/gomock"
)

// MockService is a mock of Service interface.
type MockService struct {
	ctrl     *gomock.Controller
	recorder *MockServiceMockRecorder
}

// MockServiceMockRecorder is the mock recorder for MockService.
type MockServiceMockRecorder struct {
	mock *MockService
}

// NewMockService creates a new mock instance.
func NewMockService(ctrl *gomock.Controller) *MockService {
	mock := &MockService{ctrl: ctrl}
	mock.recorder = &MockServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockService) EXPECT() *MockServiceMockRecorder {
	return m.recorder
}

// FindBrowseByKeyAndPlatform mocks base method.
func (m *MockService) FindBrowseByKeyAndPlatform(key string, pf cachemeta.BrowsePlatform) (*cachemeta.BrowseItem, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindBrowseByKeyAndPlatform", key, pf)
	ret0, _ := ret[0].(*cachemeta.BrowseItem)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindBrowseByKeyAndPlatform indicates an expected call of FindBrowseByKeyAndPlatform.
func (mr *MockServiceMockRecorder) FindBrowseByKeyAndPlatform(key, pf interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindBrowseByKeyAndPlatform", reflect.TypeOf((*MockService)(nil).FindBrowseByKeyAndPlatform), key, pf)
}

// GetSections mocks base method.
func (m *MockService) GetSections(conf BrowseConfig) ([]interface{}, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSections", conf)
	ret0, _ := ret[0].([]interface{})
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSections indicates an expected call of GetSections.
func (mr *MockServiceMockRecorder) GetSections(conf interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSections", reflect.TypeOf((*MockService)(nil).GetSections), conf)
}

// ListBrowsesByPlatform mocks base method.
func (m *MockService) ListBrowsesByPlatform(pf cachemeta.BrowsePlatform) ([]*cachemeta.BrowseItem, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListBrowsesByPlatform", pf)
	ret0, _ := ret[0].([]*cachemeta.BrowseItem)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListBrowsesByPlatform indicates an expected call of ListBrowsesByPlatform.
func (mr *MockServiceMockRecorder) ListBrowsesByPlatform(pf interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListBrowsesByPlatform", reflect.TypeOf((*MockService)(nil).ListBrowsesByPlatform), pf)
}
