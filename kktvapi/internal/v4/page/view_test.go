package page

import (
	"testing"

	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/platform"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/v4/presenter"
	metamodel "github.com/KKTV/kktv-api-v3/pkg/model/dbmeta"
	"github.com/stretchr/testify/assert"
	"gopkg.in/guregu/null.v3"
)

var (
	exampleTitleDetail = &model.TitleDetail{
		ID:                             "id1",
		Title:                          "title1",
		TitleType:                      metamodel.TitleTypeLive.String(),
		Status:                         "license_valid",
		IsEnding:                       false,
		ChildLock:                      true,
		UserRating:                     4.29,
		Cover:                          "https://fakeimg.com/cover.jpg",
		Stills:                         []string{"https://fakeimg.com/2.jpg"},
		Review:                         map[string]string{"content": "劇你所知567"},
		LatestUpdateInfo:               "共2季",
		ContentLabelsWithFullAccess:    []string{presenter.LabelTypeDualSubtitle.String()},
		ContentLabelsWithoutFullAccess: []string{presenter.LabelTypeDualSubtitle.String(), presenter.LabelTypeVIP.String()},
		Summary:                        "Josie looks beautiful",
		Series:                         nil,
		LiveInfo:                       &dbmeta.LiveInfo{IsStreaming: true},
	}
	exampleBigTitle = &BigTitle{
		ListedTitle: presenter.ListedTitle{
			TitleBasicInfo: presenter.TitleBasicInfo{
				ID:        "id1",
				Name:      "title1",
				TitleType: metamodel.TitleTypeLive.String(),
				Status:    "license_valid",
				IsEnding:  false,
				ChildLock: true,
			},
			ListedTitleMeta: presenter.ListedTitleMeta{
				LatestUpdateInfo: "共2季",
				Labels: []presenter.Label{
					{BGColor: "#6449C2", FontColor: "#FFFFFF", Key: "dual_subtitle", DisplayName: "雙字幕"},
				},
				Cover:      "https://fakeimg.com/cover.jpg",
				Stills:     []string{"https://fakeimg.com/2.jpg"},
				UserRating: null.FloatFrom(4.29),
				DeepLink:   "https://test-web.kktv.com.tw/titles/id1",
				LiveInfo:   &presenter.LiveInfo{IsStreaming: true},
				Genres:     []presenter.CollectionTag{},
				Tags:       []presenter.CollectionTag{},
			},
		},
		Review:  "劇你所知567",
		Summary: "Josie looks beautiful",
	}
)

func TestBigTitle_ConvertFromTitleDetail(t *testing.T) {

	testcases := []struct {
		name        string
		titleDetail *model.TitleDetail
		fullAccess  bool
		deviceType  platform.DeviceType
		expect      *BigTitle
	}{
		{
			name:       "WHEN role is VIP, device is APP, Title is Live",
			fullAccess: true, deviceType: platform.DeviceTypeMobileApp,
			titleDetail: func() *model.TitleDetail {
				td := *exampleTitleDetail
				return &td
			}(),
			expect: func() *BigTitle {
				bt := *exampleBigTitle
				return &bt
			}(),
		},
	}
	for _, tc := range testcases {
		t.Run(tc.name, func(t *testing.T) {
			actual := &BigTitle{}
			actual.ConvertFromTitleDetail(tc.titleDetail, tc.fullAccess, tc.deviceType)
			assert.Equal(t, tc.expect, actual)
		})
	}
}
