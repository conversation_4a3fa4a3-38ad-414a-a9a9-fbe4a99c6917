package page

import (
	"errors"
	"fmt"
	"net/http"

	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/collection"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/feature"
	"github.com/KKTV/kktv-api-v3/pkg/model/cachemeta"

	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/deeplink"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/middleware"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/platform"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/request"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/rest"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/v4/presenter"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/kktverror"
	"github.com/KKTV/kktv-api-v3/pkg/amplitudelib"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/clock"
	"github.com/KKTV/kktv-api-v3/pkg/httpreq"
	"github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	modelmw "github.com/KKTV/kktv-api-v3/pkg/model/middleware"
	"github.com/KKTV/kktv-api-v3/pkg/render"
	"github.com/go-zoo/bone"
)

type Handler struct {
	clock           clock.Clock
	service         Service
	featureSrv      feature.Service
	amplitudeClient amplitudelib.Client

	userCacheWriter cache.Cacher
	userCacheReader cache.Cacher
}

func NewHandler() *Handler {
	userCachePool := container.CachePoolUser()
	return &Handler{
		clock:           clock.New(),
		userCacheReader: cache.New(userCachePool.Slave()),
		userCacheWriter: cache.New(userCachePool.Master()),
		service:         NewService(),
		featureSrv:      feature.NewService(),
		amplitudeClient: amplitudelib.NewClient(config.Env),
	}
}

func (h *Handler) ListBrowses(w http.ResponseWriter, r *http.Request) {
	resp := rest.Ok()

	browses, err := h.service.ListBrowsesByPlatform(cachemeta.BrowsePlatformWebAndApp)
	if err != nil {
		resp.Err = ErrRespUnknown
		render.JSON(w, http.StatusInternalServerError, resp)
		return
	}

	canBrowse := h.canBrowseFunc(r)
	browseTabs := make([]presenter.BrowseTab, 0)
	for _, b := range browses {
		if !canBrowse(b) {
			continue
		}

		browseTabs = append(browseTabs, presenter.BrowseTab{
			DisplayName:    b.Title,
			Key:            getCollectionKey(b.CollectionType, b.CollectionName),
			CollectionName: b.CollectionName,
			CollectionType: b.CollectionType,
			Deeplink:       deeplink.FeaturedPage(fmt.Sprintf("%s:%s", b.CollectionType, b.CollectionName)),
			EntryType:      b.GetEntryType().String(),
		})
	}
	resp.Data = map[string]interface{}{
		"items": browseTabs,
	}
	render.JSON(w, http.StatusOK, resp)
}

func (h *Handler) canBrowseFunc(r *http.Request) func(b *cachemeta.BrowseItem) bool {
	isHideLustContent, _ := r.Context().Value(middleware.KeyHideLustContent).(bool)
	supportBrowseEntryProtect, err := h.featureSrv.HasFlag(feature.FlagSupportBrowseEntryProtect, r)
	if err != nil {
		log.Warn("v4 page handler: fail to get feature flag").Str("flag", feature.FlagSupportBrowseEntryProtect.String()).
			Err(err).Send()
	}
	return func(b *cachemeta.BrowseItem) bool {
		if isHideLustContent && b.GetEntryType() == cachemeta.BrowseEntryTypeProtected {
			return false
		}

		if b.GetEntryType() == cachemeta.BrowseEntryTypeProtected && !supportBrowseEntryProtect {
			return false
		}
		return true
	}
}

func (h *Handler) getWatchHistoryAPIUri(r *http.Request, deviceType platform.DeviceType) string {
	provideV4WatchHistoryAPI, err := h.featureSrv.HasFlag(feature.FlagProvideV4WatchHistoryAPI, r)

	if err != nil {
		log.Warn("v4 page handler: fail to get feature flag").Str("flag", feature.FlagProvideV4WatchHistoryAPI.String()).
			Err(err).Send()
		return deeplink.WatchHistoryAPI()
	}

	if !provideV4WatchHistoryAPI {
		return deeplink.WatchHistoryAPI()
	}

	return deeplink.V4WatchHistoryAPI(deviceType)
}

func (h *Handler) GetFeaturedContent(w http.ResponseWriter, r *http.Request) {
	deviceType := request.GetDeviceType(r)

	resp := rest.Ok()
	browseKey := bone.GetValue(r, "browseKey")
	browse, err := h.service.FindBrowseByKeyAndPlatform(browseKey, cachemeta.BrowsePlatformWebAndApp)
	if err != nil {
		if errors.Is(err, kktverror.ErrResourceNotFound) {
			resp.Err = ErrRespBrowseKey
			render.JSONNotFound(w, resp)
			return
		}
		resp.Err = ErrRespUnknown
		log.Error("GET featured content: fail to find browse by key").Str("browse_key", browseKey).Err(err).Send()
		render.JSONInternalServerErr(w, resp)
		return
	}

	canBrowse := h.canBrowseFunc(r)
	if !canBrowse(browse) {
		resp.Err = ErrRespBrowseKey
		render.JSONNotFound(w, resp)
		return
	}

	template := h.getPageTemplate(browse, deviceType)
	if len(template) == 0 {
		resp.Err = ErrRespNotSupportedBrowse
		render.JSONNotFound(w, resp)
		return
	}

	UserID, DeviceID := getIdentity(r)

	bc := BrowseConfig{
		request:         r,
		CollectionType:  browse.CollectionType,
		CollectionName:  browse.CollectionName,
		Template:        template,
		DeviceType:      deviceType,
		UserID:          UserID,
		DeviceID:        DeviceID,
		Platform:        httpreq.GetPlatform(r),
		WatchHistoryAPI: h.getWatchHistoryAPIUri(r, deviceType),
	}

	// if it cannot get login user from middleware, it should be guest
	bc.Membership = dbuser.NonMember
	if access, ok := r.Context().Value(middleware.KeyAccessUser).(modelmw.AccessUser); ok {
		bc.Membership = access.Memberships
	}

	if user, ok := r.Context().Value(middleware.KeyUser).(model.JwtUser); ok {
		bc.HasUserBoughtPrime = user.HasBoughtPrime
	}

	sections, err := h.service.GetSections(bc)
	if err != nil {
		resp.Err = ErrRespUnknown
		log.Error("GET featured content: fail to get sections").Str("browse_key", browseKey).Err(err).Send()
		render.JSONInternalServerErr(w, resp)
		return
	}

	resp.Data = Response{
		ContentSections: sections,
		EntryType:       browse.GetEntryType().String(),
	}
	render.JSON(w, http.StatusOK, resp)
}

func getIdentity(r *http.Request) (userID, deviceID string) {
	if access, ok := r.Context().Value(middleware.KeyAccessUser).(modelmw.AccessUser); ok {
		userID = access.UserID
	}
	deviceID = r.Header.Get(httpreq.HeaderDeviceID)

	return
}

func (h *Handler) getPageTemplate(browse *cachemeta.BrowseItem, deviceType platform.DeviceType) []SectionType {
	var template []SectionType
	//TODO add template for WEB
	switch deviceType {
	case platform.DeviceTypeMobileApp:
		if browse.CollectionType == collection.TypePlan {
			return fixedTemplatePlanPageApp
		}
		if browse.CollectionType == collection.TypeGenre {
			if browse.CollectionName == collection.GenreNameFeatured {
				template = fixedTemplateFeaturedPageApp
			} else if browse.CollectionName == collection.GenreNameAnime {
				template = fixedTemplateGenreAnimePageApp
			} else {
				template = fixedTemplateGenrePageApp
			}
		} else if browse.CollectionType == collection.TypeContentAgent || browse.CollectionType == collection.TypeContentProvider {
			template = fixedTemplateContentAgentApp
		}
	case platform.DeviceTypeWeb:
		if browse.CollectionType == collection.TypePlan {
			return fixedTemplatePlanPageWeb
		}
		if browse.CollectionType == collection.TypeGenre {
			if browse.CollectionName == collection.GenreNameFeatured {
				template = fixedTemplateFeaturedPageWeb
			} else if browse.CollectionName == collection.GenreNameAnime {
				template = fixedTemplateGenreAnimePageWeb
			} else {
				template = fixedTemplateGenrePageWeb
			}
		} else if browse.CollectionType == collection.TypeContentAgent || browse.CollectionType == collection.TypeContentProvider {
			template = fixedTemplateContentAgentWeb
		}
	}
	return template
}
