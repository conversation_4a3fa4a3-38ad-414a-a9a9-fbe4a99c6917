package page

import (
	"fmt"
	"sort"
	"strings"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/deeplink"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/platform"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/v4/presenter"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/kktverror"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/permission"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbmeta/legacy"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/duke-git/lancet/v2/slice"

	"github.com/KKTV/kktv-api-v3/pkg/log"
)

func getCollectionKey(collectionType, collectionName string) string {
	return fmt.Sprintf("%s:%s", collectionType, collectionName)
}

func profilingLog(page, action, subAction string, start time.Time) {
	pEnd := time.Now()
	log.Info("profiling: maker_common").
		Str("page", page).
		Str("action", action).
		Str("sub_action", subAction).Int64("millisec", pEnd.Sub(start).Milliseconds()).Send()
}

func getExpireSoonTitleListShareID(collectionType, collectionName string) string {
	return fmt.Sprintf("%s-%s:%s", expireSoonTitlelistShareID, collectionType, collectionName)
}

type TitleType interface {
	*BigTitle | *presenter.ListedTitle | *presenter.AiringTitle
	ConvertFromModel(td *legacymeta.TitleDetail, userCanFullAccess bool, deviceType platform.DeviceType)
}

var (
	constructorListedTitle = func() *presenter.ListedTitle {
		return &presenter.ListedTitle{}
	}
	constructorBigTitle = func() *BigTitle {
		return &BigTitle{}
	}
	constructorAiringTitle = func() *presenter.AiringTitle {
		return &presenter.AiringTitle{}
	}
)

func genTitlelistSection[T TitleType](permissionSrv permission.Service, bc *BrowseConfig, tds []*legacymeta.TitleDetail, tl *dbmeta.TitleList,
	constructor func() T) (SectionTitleList[T], error) {
	sec, err := genTitlesSection(permissionSrv, bc, tds, constructor)
	if err != nil {
		return sec, err
	}
	sec.TitleListID = tl.Meta.ShareId
	sec.DisplayName = tl.Caption
	sec.DeepLink = deeplink.TitleListPage(tl.Meta.ShareId)
	return sec, nil
}

func genTitlesSection[T TitleType](srv permission.Service, bc *BrowseConfig, tds []*legacymeta.TitleDetail, constructor func() T) (SectionTitleList[T], error) {
	titles := make([]T, len(tds))
	for i, td := range tds {
		var t = constructor()
		isFullAccess := false
		if err := srv.Grant(permission.RequestFullAccessTitleDetail(td, bc.Membership)); kktverror.IsInternalErr(err) {
			return SectionTitleList[T]{}, fmt.Errorf("permissionService: %w", err)
		} else if err == nil {
			isFullAccess = true
		}
		t.ConvertFromModel(td, isFullAccess, bc.DeviceType)
		titles[i] = t
	}
	return SectionTitleList[T]{
		Section: Section{Style: StyleNormalTitleList}, // default use normal style
		Items:   titles,
	}, nil
}

func covertTitlelistToBigViewSection(permissionSrv permission.Service, bc *BrowseConfig, tds []*legacymeta.TitleDetail, tl *dbmeta.TitleList) (any, error) {
	if bc.DeviceType == platform.DeviceTypeWeb {
		sec, err := genTitlelistSection[*BigTitle](permissionSrv, bc, tds, tl, constructorBigTitle)
		if err != nil {
			return sec, err
		}
		sec.Style = StyleBigTitleList
		return sec, nil
	} else {
		sec, err := genTitlelistSection[*presenter.ListedTitle](permissionSrv, bc, tds, tl, constructorListedTitle)
		if err != nil {
			return sec, err
		}
		sec.Style = StyleNormalTitleList
		return sec, nil
	}
}

func convertMembershipToRoleString(membership dbuser.Membership) string {
	if !membership.IsMember() {
		return dbuser.RoleGuest.String()
	}
	sort.Sort(membership)
	roles := slice.FlatMap(membership, func(_ int, m dbuser.RoleModel) []string {
		s := m.Role.String()
		return []string{s}
	})
	result := strings.Join(roles, "+")

	return result
}
