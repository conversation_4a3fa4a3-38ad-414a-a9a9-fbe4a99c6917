package search

import (
	"net/http"
	"sync"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/middleware"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/request"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/rest"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/v4/presenter"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/kktverror"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/permission"
	"github.com/KKTV/kktv-api-v3/pkg/httpreq"
	"github.com/KKTV/kktv-api-v3/pkg/log"
	legacymeta "github.com/KKTV/kktv-api-v3/pkg/model/dbmeta/legacy"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	modelmw "github.com/KKTV/kktv-api-v3/pkg/model/middleware"
	"github.com/KKTV/kktv-api-v3/pkg/render"
	"github.com/go-zoo/bone"
)

type Handler struct {
	service           Service
	permissionService permission.Service
}

func NewHandler() *Handler {
	return &Handler{
		service:           NewService(),
		permissionService: container.PermissionService(),
	}
}

type Result struct {
	Titles  []presenter.ListedTitle `json:"titles"`
	Figures []Figure                `json:"figures"`
}

type searchParams struct {
	Save bool `json:"save"`
}

func (h *Handler) KeywordSearch(w http.ResponseWriter, r *http.Request) {
	deviceType := request.GetDeviceType(r)
	resp := rest.Ok()

	target := bone.GetValue(r, "keyword")
	if target == "" {
		render.JSON(w, http.StatusOK, resp)
		return
	}

	membership := dbuser.NonMember
	access, ok := r.Context().Value(middleware.KeyAccessUser).(modelmw.AccessUser)
	if ok {
		membership = access.Memberships
	}

	var wg sync.WaitGroup

	var tempFigures []Figure
	var tempTitles []presenter.ListedTitle

	hideLustContent, _ := r.Context().Value(middleware.KeyHideLustContent).(bool)

	wg.Add(1)
	go func() {
		defer wg.Done()
		figures, err := h.service.SearchFigure(target, hideLustContent)
		if err != nil {
			log.Warn("v4SearchHandler: KeywordSearch: search for figure fail").Err(err).Str("keyword", target).Send()
			figures = make([]Figure, 0)
		}
		tempFigures = figures
	}()

	wg.Add(1)
	go func() {
		defer wg.Done()
		titles, err := h.service.SearchTitle(target, hideLustContent)
		if err != nil {
			log.Warn("v4SearchHandler: KeywordSearch: search for title fail").Err(err).Str("keyword", target).Send()
			titles = make([]legacymeta.TitleDetail, 0)
		}
		tempTitles = make([]presenter.ListedTitle, len(titles))
		for i, td := range titles {
			req := permission.RequestFullAccessTitleDetail(&td, membership)
			err := h.permissionService.Grant(req)
			if kktverror.IsInternalErr(err) {
				log.Error("v4SearchHandler: KeywordSearch: grant permission fail").Err(err).Interface("req", req).Send()
				render.JSONInternalServerErr(w, rest.Error(ErrUnknown.Message, ErrUnknown.Code))
				return
			}
			fullAccess := err == nil
			t := presenter.ListedTitle{}
			t.ConvertFromModel(&td, fullAccess, deviceType)
			tempTitles[i] = t
		}
	}()
	wg.Wait()

	params := searchParams{
		Save: true, // default to save history
	}
	_ = httpreq.Scan(&params, r.URL.Query())
	if params.Save && access.IsMember() {
		go func() {
			userID := access.UserID
			if err := h.service.SaveHistory(userID, target); err != nil {
				log.Error("v4SearchHandler: KeywordSearch: fail to push history").Err(err).
					Str("keyword", target).Str("user_id", userID).Send()
			}
		}()
	}

	resp.Data = Result{
		Titles:  tempTitles,
		Figures: tempFigures,
	}
	render.JSONOk(w, resp)
}
