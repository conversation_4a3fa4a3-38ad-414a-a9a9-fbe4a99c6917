package search

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"sync"
	"testing"

	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/middleware"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/rest"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/v4/presenter"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/kktverror"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/permission"
	"github.com/KKTV/kktv-api-v3/pkg/httpreq"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbmeta/legacy"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	modelmw "github.com/KKTV/kktv-api-v3/pkg/model/middleware"
	"github.com/go-zoo/bone"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
)

type HandlerTestSuite struct {
	suite.Suite
	ctrl    *gomock.Controller
	handler Handler
	app     *bone.Mux

	mockSrv               *MockService
	mockPermissionService *permission.MockService
}

func TestHandlerTestSuite(t *testing.T) {
	suite.Run(t, new(HandlerTestSuite))
}

func (suite *HandlerTestSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
	suite.mockSrv = NewMockService(suite.ctrl)
	suite.mockPermissionService = permission.NewMockService(suite.ctrl)
	suite.handler = Handler{
		service:           suite.mockSrv,
		permissionService: suite.mockPermissionService,
	}
	suite.app = bone.New()
	suite.app.GetFunc("/v4/:deviceType/search/:keyword", suite.handler.KeywordSearch)
}

func (suite *HandlerTestSuite) TearDownTest() {
	defer suite.ctrl.Finish()
}

type searchResp struct {
	Error *rest.Err
	Data  Result
}

func (suite *HandlerTestSuite) TestKeywordSearch() {
	var (
		keyword = "測試"
		td      = legacymeta.TitleDetail{
			LegacyTitleDetail: &model.TitleDetail{
				ID:                             "04290106",
				Title:                          "Josie 測試",
				TitleType:                      "series",
				Status:                         "license_valid",
				Summary:                        "測試 summary",
				ContentLabelsWithoutFullAccess: []string{"vip"},
				ContentLabelsWithFullAccess:    make([]string, 0),
			},
		}
	)
	testcases := []struct {
		name       string
		param      string
		hideLust   bool
		access     modelmw.AccessUser
		given      func() *sync.WaitGroup
		thenAssert func(code int, body string)
	}{
		{
			name:     "return search result AND not to push history WHEN guest query is not empty",
			access:   modelmw.AccessUser{Memberships: dbuser.NonMember},
			hideLust: true,
			given: func() *sync.WaitGroup {
				suite.mockServiceSearch(keyword, true, td)
				suite.mockNotGrantFullAccess(td, dbuser.NonMember)
				return nil
			},
			thenAssert: func(code int, body string) {
				suite.Equal(http.StatusOK, code)
				firstTitle := suite.assertSearchResponse(body)
				suite.Equal("VIP", firstTitle.Labels[0].DisplayName)
			},
		},
		{
			name:     "not to save history WHEN member but argument save is false",
			param:    "?save=false",
			hideLust: true,
			access:   modelmw.AccessUser{Memberships: dbuser.MembershipPremiumOnly},
			given: func() *sync.WaitGroup {
				suite.mockServiceSearch(keyword, true, td)
				suite.mockNotGrantFullAccess(td, dbuser.MembershipPremiumOnly)
				return nil
			},
			thenAssert: func(code int, body string) {
				suite.Equal(http.StatusOK, code)
				suite.assertSearchResponse(body)
			},
		},
		{
			name:     "return search result AND push history WHEN premium query is not empty",
			hideLust: false,
			access: modelmw.AccessUser{
				UserID:      "josie",
				Memberships: dbuser.MembershipPremiumOnly,
			},
			given: func() *sync.WaitGroup {
				suite.mockServiceSearch(keyword, false, td)
				suite.mockGrantFullAccess(td, dbuser.MembershipPremiumOnly)

				var wg sync.WaitGroup
				wg.Add(1)
				suite.mockSrv.EXPECT().SaveHistory("josie", keyword).Times(1).
					DoAndReturn(func(userID, keyword string) error {
						wg.Done()
						return nil
					})
				return &wg
			},
			thenAssert: func(code int, body string) {
				suite.Equal(http.StatusOK, code)
				firstTitle := suite.assertSearchResponse(body)
				suite.Empty(firstTitle.Labels)
			},
		},
	}
	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			wait := tc.given()

			req := httptest.NewRequest(http.MethodGet, "/v4/a/search/"+keyword+tc.param, nil)
			req.Header.Set(httpreq.HeaderPlatform, "Web")
			req = req.WithContext(context.WithValue(req.Context(), middleware.KeyAccessUser, tc.access))
			req = req.WithContext(context.WithValue(req.Context(), middleware.KeyHideLustContent, tc.hideLust))
			rr := httptest.NewRecorder()
			suite.app.ServeHTTP(rr, req)

			if wait != nil {
				wait.Wait()
			}
			tc.thenAssert(rr.Code, rr.Body.String())
		})
	}
}

func (suite *HandlerTestSuite) mockServiceSearch(keyword string, hideLust bool, returnedTitle legacymeta.TitleDetail) {
	suite.mockSrv.EXPECT().SearchFigure(keyword, hideLust).Return([]Figure{
		{ID: "figure-0001", Name: "Julia wu"},
	}, nil).Times(1)
	suite.mockSrv.EXPECT().SearchTitle(keyword, hideLust).Return([]legacymeta.TitleDetail{returnedTitle}, nil).Times(1)
}

func (suite *HandlerTestSuite) assertSearchResponse(body string) presenter.ListedTitle {
	resp := searchResp{}
	suite.NoError(json.Unmarshal([]byte(body), &resp))
	suite.Nil(resp.Error)
	suite.Equal(1, len(resp.Data.Figures))
	suite.Equal(1, len(resp.Data.Titles))
	firstTitle := resp.Data.Titles[0]
	suite.Equal("https://www.kktv.me/titles/04290106", firstTitle.DeepLink)
	suite.Equal("Josie 測試", firstTitle.Name)
	suite.Equal("04290106", firstTitle.ID)
	return firstTitle
}

func (suite *HandlerTestSuite) mockGrantFullAccess(td legacymeta.TitleDetail, ms dbuser.Membership) *gomock.Call {
	return suite.mockPermissionService.EXPECT().Grant(
		permission.RequestFullAccessTitleDetail(&td, ms),
	).Return(nil).Times(1)
}
func (suite *HandlerTestSuite) mockNotGrantFullAccess(td legacymeta.TitleDetail, ms dbuser.Membership) *gomock.Call {
	return suite.mockPermissionService.EXPECT().Grant(
		permission.RequestFullAccessTitleDetail(&td, ms),
	).Return(kktverror.ErrResourceAccessDenied)
}
