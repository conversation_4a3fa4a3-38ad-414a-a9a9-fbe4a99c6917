package favorite

import (
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/meta"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/middleware"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/rest"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/user"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/v4/presenter"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/v4/watchhistory"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/kktverror"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/permission"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/datatype"
	"github.com/KKTV/kktv-api-v3/pkg/key"
	"github.com/KKTV/kktv-api-v3/pkg/model/authority"
	legacymeta "github.com/KKTV/kktv-api-v3/pkg/model/dbmeta/legacy"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	modelmw "github.com/KKTV/kktv-api-v3/pkg/model/middleware"
	"github.com/go-zoo/bone"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
)

type respStruct struct {
	Error *rest.Err `json:"error"`
}

type HandlerTestSuite struct {
	suite.Suite
	ctrl    *gomock.Controller
	handler Handler
	app     *bone.Mux

	mockService           *MockService
	mockUserPlaylistRepo  *user.MockUserPlaylistRepository
	mockPlaylistTitleRepo *user.MockPlaylistTitleRepository
	mockTitleRepo         *meta.MockTitleRepository
	mockPermissionSrv     *permission.MockService
	mockWatchHistorySrv   *watchhistory.MockService
	mockMetaCacheReader   *cache.MockCacher
}

func TestHandlerTestSuite(t *testing.T) {
	suite.Run(t, new(HandlerTestSuite))
}

func (suite *HandlerTestSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
	suite.mockService = NewMockService(suite.ctrl)
	suite.mockUserPlaylistRepo = user.NewMockUserPlaylistRepository(suite.ctrl)
	suite.mockPlaylistTitleRepo = user.NewMockPlaylistTitleRepository(suite.ctrl)
	suite.mockTitleRepo = meta.NewMockTitleRepository(suite.ctrl)
	suite.mockPermissionSrv = permission.NewMockService(suite.ctrl)
	suite.mockWatchHistorySrv = watchhistory.NewMockService(suite.ctrl)
	suite.mockMetaCacheReader = cache.NewMockCacher(suite.ctrl)
	suite.handler = Handler{
		service:           suite.mockService,
		userPlaylistRepo:  suite.mockUserPlaylistRepo,
		playlistTitleRepo: suite.mockPlaylistTitleRepo,
		titleRepo:         suite.mockTitleRepo,
		permissionSrv:     suite.mockPermissionSrv,
		watchHistorySrv:   suite.mockWatchHistorySrv,
		metaCacheReader:   suite.mockMetaCacheReader,
	}
	suite.app = bone.New()
	suite.app.GetFunc("/v4/#deviceType^[aw]$/users/me/favorite-titles", suite.handler.GetMyFavoriteTitles)
	suite.app.GetFunc("/v4/#deviceType^[aw]$/users/me/favorite-explorer", suite.handler.ExploreMyFavorite)
	suite.app.GetFunc("/v4/users/me/favorite-titles/ids", suite.handler.GetMyFavoriteTitleIDs)
	suite.app.DeleteFunc("/v4/users/me/favorite-titles", suite.handler.DeleteMyFavoriteTitles)
	suite.app.PostFunc("/v4/users/me/favorite-titles", suite.handler.AddMyFavoriteTitles)
	suite.app.PatchFunc("/v4/users/me/favorite-titles", suite.handler.UpdateMyFavoriteTitles)
}

func (suite *HandlerTestSuite) TearDownTest() {
	defer suite.ctrl.Finish()
}

func (suite *HandlerTestSuite) TestGetMyFavoriteTitles() {
	var (
		userID          = "test-userid"
		setRequestLogin = func(req *http.Request, membership dbuser.Membership) *http.Request {
			ctx := context.WithValue(req.Context(), middleware.KeyAccessUser, modelmw.AccessUser{
				Memberships: membership,
				UserID:      userID,
			},
			)
			return req.WithContext(ctx)
		}
	)
	testcases := []struct {
		name        string
		queryString string
		userID      string
		given       func()
		thenAssert  func(code int, body []byte)
		modifyReq   func(req *http.Request) *http.Request
	}{
		{
			name:   "return all favorite titles WHEN no filter and paging given",
			userID: userID,
			modifyReq: func(req *http.Request) *http.Request {
				req = setRequestLogin(req, dbuser.MembershipPremiumOnly)
				return req
			},
			given: func() {
				titleIDs := []string{"04290106", "01060429"}
				suite.mockUserPlaylistRepo.EXPECT().GetFavoritePlaylist(userID).Return(
					&dbuser.UserPlaylist{ID: "test-playlist-id"}, nil).Times(1)
				suite.mockPlaylistTitleRepo.EXPECT().GetPlaylistTitleIDsByPlaylistID("test-playlist-id").Return(
					titleIDs, nil).Times(1)
				tds := []*legacymeta.TitleDetail{
					{&model.TitleDetail{ID: "04290106", IsEnding: true}},
					{&model.TitleDetail{ID: "01060429", IsEnding: false}},
				}
				suite.mockTitleRepoListedTitleDetails(titleIDs, tds)
				for _, td := range tds {
					suite.mockPermissionSrv.EXPECT().Grant(permission.RequestFullAccessTitleDetail(td, dbuser.MembershipPremiumOnly)).Return(nil)
				}

			},
			thenAssert: func(code int, body []byte) {
				suite.Equal(http.StatusOK, code)

				resp := map[string]interface{}{}
				suite.Require().NoError(json.Unmarshal(body, &resp))

				respData := resp["data"].(map[string]interface{})
				titles := (respData["items"]).([]interface{})
				suite.Len(titles, 2)
				suite.ElementsMatch([]string{"04290106", "01060429"}, (respData["all_title_ids"]).([]any))
				respPagination := (respData["pagination"]).(map[string]interface{})
				suite.EqualValues(1, respPagination["page"])
				suite.EqualValues(20, respPagination["page_size"])
				suite.EqualValues(2, respPagination["total"])
			},
		},
		{
			name:        "only return not is_ending WHEN filter `airing` is given",
			queryString: "?filter_by=airing",
			userID:      userID,
			modifyReq: func(req *http.Request) *http.Request {
				req = setRequestLogin(req, dbuser.MembershipPremiumOnly)
				return req
			},
			given: func() {
				titleIDs := []string{"04290106", "01060429"}
				suite.mockUserPlaylistRepo.EXPECT().GetFavoritePlaylist(userID).Return(
					&dbuser.UserPlaylist{ID: "test-playlist-id"}, nil).Times(1)
				suite.mockPlaylistTitleRepo.EXPECT().GetPlaylistTitleIDsByPlaylistID("test-playlist-id").Return(
					titleIDs, nil).Times(1)
				tds := []*legacymeta.TitleDetail{
					{&model.TitleDetail{ID: "04290106", IsEnding: true}},
					{&model.TitleDetail{ID: "01060429", IsEnding: false}},
				}
				suite.mockTitleRepoListedTitleDetails(titleIDs, tds)
				suite.mockPermissionSrv.EXPECT().Grant(permission.RequestFullAccessTitleDetail(tds[1], dbuser.MembershipPremiumOnly)).Return(nil)
			},
			thenAssert: func(code int, body []byte) {
				suite.Equal(http.StatusOK, code)

				resp := map[string]interface{}{}
				suite.Require().NoError(json.Unmarshal(body, &resp))

				respData := resp["data"].(map[string]interface{})
				suite.ElementsMatch([]string{"04290106", "01060429"}, (respData["all_title_ids"]).([]any))
				titles := (respData["items"]).([]interface{})
				suite.Len(titles, 1)
				suite.Equal("01060429", titles[0].(map[string]interface{})["id"])
				respPagination := (respData["pagination"]).(map[string]interface{})
				suite.EqualValues(1, respPagination["total"])
			},
		},
		{
			name:        "only return expire_soon WHEN filter `expire_soon` is given",
			queryString: "?filter_by=expire_soon",
			userID:      userID,
			modifyReq: func(req *http.Request) *http.Request {
				req = setRequestLogin(req, dbuser.MembershipPremiumOnly)
				return req
			},
			given: func() {
				titleIDs := []string{"04290106", "01060429"}
				suite.mockUserPlaylistRepo.EXPECT().GetFavoritePlaylist(userID).Return(
					&dbuser.UserPlaylist{ID: "test-playlist-id"}, nil).Times(1)
				suite.mockPlaylistTitleRepo.EXPECT().GetPlaylistTitleIDsByPlaylistID("test-playlist-id").Return(
					titleIDs, nil).Times(1)
				tds := []*legacymeta.TitleDetail{
					{&model.TitleDetail{ID: "04290106"}},
					{&model.TitleDetail{ID: "01060429", ContentLabels: []string{"expire_soon"}}},
				}
				suite.mockTitleRepoListedTitleDetails(titleIDs, tds)
				suite.mockPermissionSrv.EXPECT().Grant(permission.RequestFullAccessTitleDetail(tds[1], dbuser.MembershipPremiumOnly)).Return(nil)
			},
			thenAssert: func(code int, body []byte) {
				suite.Equal(http.StatusOK, code)

				resp := map[string]interface{}{}
				suite.Require().NoError(json.Unmarshal(body, &resp))

				respData := resp["data"].(map[string]interface{})
				titles := (respData["items"]).([]interface{})
				suite.Len(titles, 1)
				suite.Equal("01060429", titles[0].(map[string]interface{})["id"])
			},
		},
		{
			name:   "return all favorite WHEN no filter and its label contain without full access",
			userID: userID,
			modifyReq: func(req *http.Request) *http.Request {
				req = setRequestLogin(req, dbuser.MembershipExpired)
				return req
			},
			given: func() {
				titleIDs := []string{"04290106", "01060429"}
				suite.mockUserPlaylistRepo.EXPECT().GetFavoritePlaylist(userID).Return(
					&dbuser.UserPlaylist{ID: "test-playlist-id"}, nil).Times(1)
				suite.mockPlaylistTitleRepo.EXPECT().GetPlaylistTitleIDsByPlaylistID("test-playlist-id").Return(
					titleIDs, nil).Times(1)
				tds := []*legacymeta.TitleDetail{
					{&model.TitleDetail{ID: "04290106"}},
					{&model.TitleDetail{ID: "01060429", ContentLabelsWithoutFullAccess: []string{
						presenter.LabelTypeExpireSoon.String(),
						presenter.LabelTypeVIP.String(),
					}}},
				}
				suite.mockTitleRepoListedTitleDetails(titleIDs, tds)
				for _, td := range tds {
					suite.mockPermissionSrv.EXPECT().Grant(permission.RequestFullAccessTitleDetail(td, dbuser.MembershipExpired)).Return(kktverror.ErrResourceAccessDenied)
				}
			},
			thenAssert: func(code int, body []byte) {
				suite.Equal(http.StatusOK, code)

				resp := map[string]interface{}{}
				suite.Require().NoError(json.Unmarshal(body, &resp))

				respData := resp["data"].(map[string]interface{})
				titles := (respData["items"]).([]interface{})
				suite.Len(titles, 2)

				respPagination := (respData["pagination"]).(map[string]interface{})
				suite.EqualValues(1, respPagination["page"])
				suite.EqualValues(20, respPagination["page_size"])
				suite.EqualValues(2, respPagination["total"])

				title := titles[1].(map[string]interface{})
				labels := title["labels"].([]interface{})
				suite.Len(labels, 2)
				expireSoonLabel := labels[0].(map[string]interface{})
				suite.EqualValues("expire_soon", expireSoonLabel["key"])
				vipLabel := labels[1].(map[string]interface{})
				suite.EqualValues("vip", vipLabel["key"])
			},
		},
	}
	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			tc.given()

			req := httptest.NewRequest(http.MethodGet, "/v4/a/users/me/favorite-titles"+tc.queryString, nil)
			req = tc.modifyReq(req)
			rr := httptest.NewRecorder()
			suite.app.ServeHTTP(rr, req)

			s := rr.Body.String()
			println(s)
			tc.thenAssert(rr.Code, rr.Body.Bytes())
		})
	}
}

func (suite *HandlerTestSuite) TestExploreMyFavorite() {
	var (
		userID          = "test-userid"
		setRequestLogin = func(req *http.Request, membership dbuser.Membership) *http.Request {
			ctx := context.WithValue(req.Context(), middleware.KeyAccessUser, modelmw.AccessUser{
				Memberships: membership,
				UserID:      userID,
			},
			)
			return req.WithContext(ctx)
		}
		createdAt, _ = time.Parse("2006-01-02T15:04:05Z", "2024-05-01T00:00:00Z")
	)
	testcases := []struct {
		name        string
		queryString string
		userID      string
		given       func()
		thenAssert  func(code int, body []byte)
		modifyReq   func(req *http.Request) *http.Request
	}{
		{
			name:        "return zero favorite WHEN no filter",
			userID:      userID,
			queryString: "",
			modifyReq: func(req *http.Request) *http.Request {
				req = setRequestLogin(req, dbuser.MembershipPremiumOnly)
				return req
			},
			given: func() {
				suite.mockPlaylistTitleRepo.EXPECT().GetPlaylistTitlesByUserIDAndName(userID, "favorite").Return(
					[]*dbuser.PlaylistTitle{}, nil).Times(1)
			},
			thenAssert: func(code int, body []byte) {
				suite.Equal(http.StatusOK, code)

				resp := map[string]interface{}{}
				suite.Require().NoError(json.Unmarshal(body, &resp))

				respData := resp["data"].(map[string]interface{})
				suite.EqualValues(0, respData["total"])
				suite.EqualValues(0, len(respData["genres"].([]any)))
				suite.EqualValues(0, len(respData["items"].([]any)))
			},
		},
		{
			name:        "return all favorite explorer without filter",
			userID:      userID,
			queryString: "",
			modifyReq: func(req *http.Request) *http.Request {
				req = setRequestLogin(req, dbuser.MembershipPremiumOnly)
				return req
			},
			given: func() {
				suite.mockPlaylistTitleRepo.EXPECT().GetPlaylistTitlesByUserIDAndName(userID, "favorite").Return(
					[]*dbuser.PlaylistTitle{
						{
							ID:         "test-playlist-title-id",
							PlaylistID: "test-playlist-id",
							TitleID:    "06000501",
							CreatedAt:  datatype.DateTime(createdAt),
						},
						{
							ID:         "test-playlist-title-id",
							PlaylistID: "test-playlist-id",
							TitleID:    "06000502",
							CreatedAt:  datatype.DateTime(createdAt),
						},
					}, nil).Times(1)
				tds := []*legacymeta.TitleDetail{
					{&model.TitleDetail{ID: "06000501", Status: "license_valid", Genres: []*dbmeta.CollectionItem{
						{CollectionType: "genre", CollectionName: "動漫"},
					}, AcceptedAuthorities: []authority.Authority{authority.FreemiumPlay}}},
					{&model.TitleDetail{ID: "06000502", Status: "license_expired"}},
				}
				suite.mockTitleRepo.EXPECT().ListBulkViewableTitleDetailByIDs([]string{"06000501", "06000502"}).Return(tds, nil)
				suite.mockMetaCacheReader.EXPECT().HGet(key.MetaGetServiceGeneralConfig(), key.MetaServiceGeneralConfigHashKeys.PlanLustTitleIDs, gomock.Any()).DoAndReturn(
					func(ck, hk, receiver interface{}) error {
						r := receiver.(*struct {
							TitleIDs []string `json:"title_ids"`
						})
						r.TitleIDs = []string{}
						return nil
					})
				suite.mockWatchHistorySrv.EXPECT().GetByUserID(userID).Return(nil, watchhistory.ErrNotFound)

				suite.mockPermissionSrv.EXPECT().Grant(permission.RequestFullAccessTitleDetail(tds[0], dbuser.MembershipPremiumOnly)).Return(nil)
			},
			thenAssert: func(code int, body []byte) {
				suite.Equal(http.StatusOK, code)

				resp := map[string]interface{}{}
				suite.Require().NoError(json.Unmarshal(body, &resp))

				respData := resp["data"].(map[string]interface{})
				suite.EqualValues(1, respData["total"])
				suite.EqualValues([]any{"動漫"}, respData["genres"])
			},
		},
		{
			name:        "return expired favorite explorer with filter",
			userID:      userID,
			queryString: "?filter_by=license_expired",
			modifyReq: func(req *http.Request) *http.Request {
				req = setRequestLogin(req, dbuser.MembershipPremiumOnly)
				return req
			},
			given: func() {
				suite.mockPlaylistTitleRepo.EXPECT().GetPlaylistTitlesByUserIDAndName(userID, "favorite").Return(
					[]*dbuser.PlaylistTitle{
						{
							ID:         "test-playlist-title-id",
							PlaylistID: "test-playlist-id",
							TitleID:    "06000501",
							CreatedAt:  datatype.DateTime(createdAt),
						},
						{
							ID:         "test-playlist-title-id",
							PlaylistID: "test-playlist-id",
							TitleID:    "06000502",
							CreatedAt:  datatype.DateTime(createdAt),
						},
					}, nil).Times(1)
				tds := []*legacymeta.TitleDetail{
					{&model.TitleDetail{ID: "06000501", Status: "", Genres: []*dbmeta.CollectionItem{
						{CollectionType: "genre", CollectionName: "動漫"},
					}, AcceptedAuthorities: []authority.Authority{authority.FreemiumPlay}}},
					{&model.TitleDetail{ID: "06000502", Status: "license_valid"}},
				}
				suite.mockTitleRepo.EXPECT().ListBulkViewableTitleDetailByIDs([]string{"06000501", "06000502"}).Return(tds, nil)
				suite.mockMetaCacheReader.EXPECT().HGet(key.MetaGetServiceGeneralConfig(), key.MetaServiceGeneralConfigHashKeys.PlanLustTitleIDs, gomock.Any()).DoAndReturn(
					func(ck, hk, receiver interface{}) error {
						r := receiver.(*struct {
							TitleIDs []string `json:"title_ids"`
						})
						r.TitleIDs = []string{}
						return nil
					})
				suite.mockWatchHistorySrv.EXPECT().GetByUserID(userID).Return(nil, watchhistory.ErrNotFound)

				suite.mockPermissionSrv.EXPECT().Grant(permission.RequestFullAccessTitleDetail(tds[0], dbuser.MembershipPremiumOnly)).Return(nil)
			},
			thenAssert: func(code int, body []byte) {
				suite.Equal(http.StatusOK, code)

				resp := map[string]interface{}{}
				suite.Require().NoError(json.Unmarshal(body, &resp))

				respData := resp["data"].(map[string]interface{})
				suite.EqualValues(1, respData["total"])
				suite.EqualValues([]any{"動漫"}, respData["genres"])
			},
		},
	}
	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			tc.given()

			req := httptest.NewRequest(http.MethodGet, "/v4/w/users/me/favorite-explorer"+tc.queryString, nil)
			req = tc.modifyReq(req)
			rr := httptest.NewRecorder()
			suite.app.ServeHTTP(rr, req)
			tc.thenAssert(rr.Code, rr.Body.Bytes())
		})
	}
}

func (suite *HandlerTestSuite) TestSortGenres() {
	testcases := []struct {
		name     string
		genres   map[string]bool
		expected []string
	}{
		{
			name:     "sort genres",
			genres:   map[string]bool{"動漫": true, "戲劇": true, "電影": true, "娛樂": true, "親子": true, "深夜": true},
			expected: []string{"戲劇", "動漫", "娛樂", "電影", "親子", "深夜"},
		},
		{
			name:     "sort genres with 娛樂 genre",
			genres:   map[string]bool{"動漫": true, "戲劇": true, "電影": true, "親子": true, "深夜": true},
			expected: []string{"戲劇", "動漫", "電影", "親子", "深夜"},
		},
		{
			name:     "sort genres only 動漫 genre",
			genres:   map[string]bool{"動漫": true},
			expected: []string{"動漫"},
		},
	}

	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			got := suite.handler.sortGenres(tc.genres)
			suite.Equal(tc.expected, got)
		})
	}
}

func (suite *HandlerTestSuite) TestGetMyFavoriteTitlesIds() {
	var (
		userID          = "test-userid"
		setRequestLogin = func(req *http.Request, membership dbuser.Membership) *http.Request {
			ctx := context.WithValue(req.Context(), middleware.KeyAccessUser, modelmw.AccessUser{
				Memberships: membership,
				UserID:      userID,
			},
			)
			return req.WithContext(ctx)
		}
		createdAt, _ = time.Parse("2006-01-02T15:04:05Z", "2024-05-01T00:00:00Z")
	)
	testcases := []struct {
		name        string
		queryString string
		userID      string
		given       func()
		thenAssert  func(code int, body []byte)
		modifyReq   func(req *http.Request) *http.Request
	}{
		{
			name:        "return nil data when favoritePlaylist is nil",
			userID:      userID,
			queryString: "",
			modifyReq: func(req *http.Request) *http.Request {
				req = setRequestLogin(req, dbuser.MembershipPremiumOnly)
				return req
			},
			given: func() {
				suite.mockUserPlaylistRepo.EXPECT().GetFavoritePlaylist(userID).Return(
					nil, nil).Times(1)
			},
			thenAssert: func(code int, body []byte) {
				suite.Equal(http.StatusOK, code)

				resp := map[string]interface{}{}
				suite.Require().NoError(json.Unmarshal(body, &resp))

				respData := resp["data"].(map[string]interface{})
				suite.Equal(nil, respData["title_ids"])
			},
		},
		{
			name:        "return empty title ids",
			userID:      userID,
			queryString: "",
			modifyReq: func(req *http.Request) *http.Request {
				req = setRequestLogin(req, dbuser.MembershipPremiumOnly)
				return req
			},
			given: func() {
				suite.mockUserPlaylistRepo.EXPECT().GetFavoritePlaylist(userID).Return(
					&dbuser.UserPlaylist{
						ID:        "test-playlist-title-id",
						UserID:    userID,
						Name:      "favorite",
						CreatedAt: createdAt,
					}, nil).Times(1)

				suite.mockPlaylistTitleRepo.EXPECT().GetPlaylistTitleIDsByPlaylistID("test-playlist-title-id").Return(nil, nil)
			},
			thenAssert: func(code int, body []byte) {
				suite.Equal(http.StatusOK, code)

				resp := map[string]interface{}{}
				suite.Require().NoError(json.Unmarshal(body, &resp))

				respData := resp["data"].(map[string]interface{})
				suite.Equal(nil, respData["title_ids"])
			},
		},
		{
			name:        "return all favorite",
			userID:      userID,
			queryString: "",
			modifyReq: func(req *http.Request) *http.Request {
				req = setRequestLogin(req, dbuser.MembershipPremiumOnly)
				return req
			},
			given: func() {
				suite.mockUserPlaylistRepo.EXPECT().GetFavoritePlaylist(userID).Return(
					&dbuser.UserPlaylist{
						ID:        "test-playlist-title-id",
						UserID:    userID,
						Name:      "favorite",
						CreatedAt: createdAt,
					}, nil).Times(1)
				mockTitleIDs := []string{"06000501", "06000502"}
				suite.mockPlaylistTitleRepo.EXPECT().GetPlaylistTitleIDsByPlaylistID("test-playlist-title-id").Return(mockTitleIDs, nil)
			},
			thenAssert: func(code int, body []byte) {
				suite.Equal(http.StatusOK, code)

				resp := map[string]interface{}{}
				suite.Require().NoError(json.Unmarshal(body, &resp))

				respData := resp["data"].(map[string]interface{})
				resptitleIDs := (respData["title_ids"]).([]interface{})
				suite.Equal(resptitleIDs[0].(string), "06000501")
				suite.Equal(resptitleIDs[1].(string), "06000502")
			},
		},
	}
	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			tc.given()

			req := httptest.NewRequest(http.MethodGet, "/v4/users/me/favorite-titles/ids", nil)
			req = tc.modifyReq(req)
			rr := httptest.NewRecorder()
			suite.app.ServeHTTP(rr, req)
			tc.thenAssert(rr.Code, rr.Body.Bytes())
		})
	}
}

func (suite *HandlerTestSuite) mockTitleRepoListedTitleDetails(titleIDs []string, tds []*legacymeta.TitleDetail) *gomock.Call {
	return suite.mockTitleRepo.EXPECT().ListViewableTitleDetailWithoutSeries(titleIDs, false).Return(tds, nil).Times(1)
}

func (suite *HandlerTestSuite) TestDeleteMyFavoriteTitles() {
	var (
		userID          = "test-userid"
		setRequestLogin = func(req *http.Request, membership dbuser.Membership) *http.Request {
			ctx := context.WithValue(req.Context(), middleware.KeyAccessUser, modelmw.AccessUser{
				Memberships: membership,
				UserID:      userID,
			},
			)
			return req.WithContext(ctx)
		}
	)
	testcases := []struct {
		name       string
		userID     string
		body       string
		given      func()
		thenAssert func(code int, body []byte)
		modifyReq  func(req *http.Request) *http.Request
	}{
		{
			name:   "successful deletion of favorite titles",
			userID: userID,
			body:   `{"title_ids": ["title1", "title2"]}`,
			modifyReq: func(req *http.Request) *http.Request {
				req = setRequestLogin(req, dbuser.MembershipPremiumOnly)
				return req
			},
			given: func() {
				suite.mockService.EXPECT().RemoveTitlesFromFavoritePlaylist(userID, []string{"title1", "title2"}).Return(nil).Times(1)
			},
			thenAssert: func(code int, body []byte) {
				suite.Equal(http.StatusNoContent, code)
			},
		},
		{
			name:   "invalid request body",
			userID: userID,
			body:   `invalid json`,
			modifyReq: func(req *http.Request) *http.Request {
				req = setRequestLogin(req, dbuser.MembershipPremiumOnly)
				return req
			},
			given: func() {},
			thenAssert: func(code int, body []byte) {
				suite.Equal(http.StatusBadRequest, code)

				var resp respStruct
				suite.Require().NoError(json.Unmarshal(body, &resp))
				suite.Equal(resp.Error.Code, ErrInvalidBody.Code)
				suite.Equal(resp.Error.Message, ErrInvalidBody.Message)
			},
		},
		{
			name:   "missing title_ids in request body",
			userID: userID,
			body:   `{}`,
			modifyReq: func(req *http.Request) *http.Request {
				req = setRequestLogin(req, dbuser.MembershipPremiumOnly)
				return req
			},
			given: func() {},
			thenAssert: func(code int, body []byte) {
				suite.Equal(http.StatusBadRequest, code)

				var resp respStruct
				suite.Require().NoError(json.Unmarshal(body, &resp))
				suite.Equal(resp.Error.Code, ErrNotFoundBodyTitleIDs.Code)
				suite.Equal(resp.Error.Message, ErrNotFoundBodyTitleIDs.Message)
			},
		},
		{
			name:   "error during deletion",
			userID: userID,
			body:   `{"title_ids": ["title1", "title2"]}`,
			modifyReq: func(req *http.Request) *http.Request {
				req = setRequestLogin(req, dbuser.MembershipPremiumOnly)
				return req
			},
			given: func() {
				suite.mockService.EXPECT().RemoveTitlesFromFavoritePlaylist(userID, []string{"title1", "title2"}).Return(errors.New("db error")).Times(1)
			},
			thenAssert: func(code int, body []byte) {
				suite.Equal(http.StatusInternalServerError, code)

				var resp respStruct
				suite.Require().NoError(json.Unmarshal(body, &resp))
				suite.Equal(resp.Error.Code, ErrUnknown.Code)
				suite.Equal(resp.Error.Message, ErrUnknown.Message)
			},
		},
	}

	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			tc.given()

			req := httptest.NewRequest(http.MethodDelete, "/v4/users/me/favorite-titles", strings.NewReader(tc.body))
			req.Header.Set("Content-Type", "application/json")
			req = tc.modifyReq(req)
			rr := httptest.NewRecorder()
			suite.app.ServeHTTP(rr, req)

			tc.thenAssert(rr.Code, rr.Body.Bytes())
		})
	}
}

func (suite *HandlerTestSuite) TestAddMyFavoriteTitles() {
	var (
		userID          = "test-userid"
		setRequestLogin = func(req *http.Request, membership dbuser.Membership) *http.Request {
			ctx := context.WithValue(req.Context(), middleware.KeyAccessUser, modelmw.AccessUser{
				Memberships: membership,
				UserID:      userID,
			},
			)
			return req.WithContext(ctx)
		}
	)
	testcases := []struct {
		name       string
		userID     string
		body       string
		given      func()
		thenAssert func(code int, body []byte)
		modifyReq  func(req *http.Request) *http.Request
	}{
		{
			name:   "successful addition of favorite titles",
			userID: userID,
			body:   `{"title_ids": ["06000001", "06000002"]}`,
			modifyReq: func(req *http.Request) *http.Request {
				req = setRequestLogin(req, dbuser.MembershipPremiumOnly)
				return req
			},
			given: func() {
				suite.mockService.EXPECT().AddFavorite(userID, []string{"06000001", "06000002"}).Return([]string{"06000001", "06000002"}, nil)
			},
			thenAssert: func(code int, body []byte) {
				suite.Equal(http.StatusOK, code)
			},
		},
		{
			name:   "error during addition",
			userID: userID,
			body:   `{"title_ids": ["06000001", "06000002"]}`,
			modifyReq: func(req *http.Request) *http.Request {
				req = setRequestLogin(req, dbuser.MembershipPremiumOnly)
				return req
			},
			given: func() {
				suite.mockService.EXPECT().AddFavorite(userID, []string{"06000001", "06000002"}).Return(nil, ErrMaxFavoriteCountReached)
			},
			thenAssert: func(code int, body []byte) {
				suite.Equal(http.StatusBadRequest, code)

				var resp rest.Resp
				suite.Require().NoError(json.Unmarshal(body, &resp))
				suite.Equal(resp.Err.Code, ErrFavoriteTitleOverLimit.Code)
				suite.Equal(resp.Err.Message, ErrFavoriteTitleOverLimit.Message)
				respData := resp.Data.(map[string]interface{})
				suite.EqualValues(MaxFavoriteCount, respData["limit"])
			},
		},
	}
	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			tc.given()

			req := httptest.NewRequest(http.MethodPost, "/v4/users/me/favorite-titles", strings.NewReader(tc.body))
			req.Header.Set("Content-Type", "application/json")
			req = tc.modifyReq(req)
			rr := httptest.NewRecorder()
			suite.app.ServeHTTP(rr, req)

			tc.thenAssert(rr.Code, rr.Body.Bytes())
		})
	}
}

func (suite *HandlerTestSuite) TestUpdateMyFavoriteTitles() {
	var (
		userID          = "test-userid"
		setRequestLogin = func(req *http.Request, membership dbuser.Membership) *http.Request {
			ctx := context.WithValue(req.Context(), middleware.KeyAccessUser, modelmw.AccessUser{
				Memberships: membership,
				UserID:      userID,
			},
			)
			return req.WithContext(ctx)
		}
	)
	testcases := []struct {
		name       string
		userID     string
		body       string
		given      func()
		thenAssert func(code int, body []byte)
		modifyReq  func(req *http.Request) *http.Request
	}{
		{
			name:   "successful update of favorite titles",
			userID: userID,
			body:   `{"title_ids": ["06000001", "06000002"]}`,
			modifyReq: func(req *http.Request) *http.Request {
				req = setRequestLogin(req, dbuser.MembershipPremiumOnly)
				return req
			},
			given: func() {
				suite.mockService.EXPECT().AddFavoriteAndRemoveLatest(userID, []string{"06000001", "06000002"}).Return(nil)
			},
			thenAssert: func(code int, body []byte) {
				suite.Equal(http.StatusOK, code)
			},
		},
	}

	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			tc.given()

			req := httptest.NewRequest(http.MethodPatch, "/v4/users/me/favorite-titles", strings.NewReader(tc.body))
			req.Header.Set("Content-Type", "application/json")
			req = tc.modifyReq(req)
			rr := httptest.NewRecorder()
			suite.app.ServeHTTP(rr, req)

			tc.thenAssert(rr.Code, rr.Body.Bytes())
		})
	}
}
