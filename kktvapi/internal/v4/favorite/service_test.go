package favorite

import (
	"fmt"
	"testing"
	"time"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/user"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
)

type ServiceSuite struct {
	suite.Suite
	ctrl                  *gomock.Controller
	mockUserPlaylistRepo  *user.MockUserPlaylistRepository
	mockPlaylistTitleRepo *user.MockPlaylistTitleRepository
	service               Service
}

func TestServiceSuite(t *testing.T) {
	suite.Run(t, new(ServiceSuite))
}

func (suite *ServiceSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
	suite.mockUserPlaylistRepo = user.NewMockUserPlaylistRepository(suite.ctrl)
	suite.mockPlaylistTitleRepo = user.NewMockPlaylistTitleRepository(suite.ctrl)

	suite.service = &service{
		userPlaylistRepo:  suite.mockUserPlaylistRepo,
		playlistTitleRepo: suite.mockPlaylistTitleRepo,
	}
}

func (suite *ServiceSuite) TearDownTest() {
	defer suite.ctrl.Finish()
}

func (suite *ServiceSuite) TestAddFavorite() {
	testcases := []struct {
		name     string
		userID   string
		titleIDs []string
		given    func()
		then     func(err error)
	}{
		{
			name:     "User add favorite successfully",
			userID:   "test-user-ID",
			titleIDs: []string{"01000465"},
			given: func() {
				suite.mockUserPlaylistRepo.EXPECT().GetFavoritePlaylist("test-user-ID").Return(&dbuser.UserPlaylist{
					ID:        "test-user-playlist-ID",
					UserID:    "test-user-ID",
					Name:      "favorite",
					CreatedAt: time.Now(),
				}, nil)

				suite.mockPlaylistTitleRepo.EXPECT().CountTitlesInPlaylist("test-user-playlist-ID").Return(10, nil)
				suite.mockPlaylistTitleRepo.EXPECT().RemoveTitlesFromPlaylist("test-user-playlist-ID", []string{"01000465"}).Return(nil)
				suite.mockPlaylistTitleRepo.EXPECT().AddTitlesToPlaylist("test-user-playlist-ID", []string{"01000465"}).Return([]string{"01000465"}, nil)
			},
			then: func(err error) {
				suite.NoError(err)
			},
		},
		{
			name:     "User add favorite but user has favorite over limit",
			userID:   "test-user-ID",
			titleIDs: []string{"01000465"},
			given: func() {
				suite.mockUserPlaylistRepo.EXPECT().GetFavoritePlaylist("test-user-ID").Return(&dbuser.UserPlaylist{
					ID:        "test-user-playlist-ID",
					UserID:    "test-user-ID",
					Name:      "favorite",
					CreatedAt: time.Now(),
				}, nil)

				suite.mockPlaylistTitleRepo.EXPECT().CountTitlesInPlaylist("test-user-playlist-ID").Return(MaxFavoriteCount, nil)
			},
			then: func(err error) {
				suite.Error(err, ErrMaxFavoriteCountReached)
			},
		},
		{
			name:     "User add favorite but user has no favorite playlist",
			userID:   "test-user-ID",
			titleIDs: []string{"01000465"},
			given: func() {
				suite.mockUserPlaylistRepo.EXPECT().GetFavoritePlaylist("test-user-ID").Return(nil, nil)
				suite.mockUserPlaylistRepo.EXPECT().CreatePlaylist("test-user-ID", "favorite").Return(&dbuser.UserPlaylist{
					ID:        "test-user-playlist-ID",
					UserID:    "test-user-ID",
					Name:      "favorite",
					CreatedAt: time.Now(),
				}, nil)

				suite.mockPlaylistTitleRepo.EXPECT().AddTitlesToPlaylist("test-user-playlist-ID", []string{"01000465"}).Return([]string{"01000465"}, nil)
			},
			then: func(err error) {
				suite.NoError(err)
			},
		},
	}
	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			defer suite.ctrl.Finish()

			tc.given()
			_, err := suite.service.AddFavorite(tc.userID, tc.titleIDs)
			tc.then(err)
		})
	}
}

func (suite *ServiceSuite) TestRemoveTitlesFromFavoritePlaylist() {
	testcases := []struct {
		name     string
		given    func()
		then     func(err error)
		userID   string
		titleIDs []string
	}{
		{
			name: "remove titles from favorite playlist",
			given: func() {
				suite.mockUserPlaylistRepo.EXPECT().GetPlaylistByUserIDAndName("test-userID", "favorite").Return(&dbuser.UserPlaylist{ID: "test-playlist-id"}, nil)
				suite.mockPlaylistTitleRepo.EXPECT().RemoveTitlesFromPlaylist("test-playlist-id", []string{"01000339", "06000501"}).Return(nil)
			},
			then: func(err error) {
				suite.NoError(err)
			},
			userID:   "test-userID",
			titleIDs: []string{"01000339", "06000501"},
		},
	}
	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			defer suite.ctrl.Finish()

			tc.given()
			err := suite.service.RemoveTitlesFromFavoritePlaylist(tc.userID, tc.titleIDs)
			tc.then(err)
		})
	}
}

func (suite *ServiceSuite) TestAddFavoriteAndRemoveLatest() {
	var playlistTitleIDs []string
	var thousandsPlaylistTitleIDs []string
	var playlistTitleIDsLessThanMaxCount []string
	for i := 1; i <= 500; i++ {
		playlistTitleIDs = append(playlistTitleIDs, fmt.Sprintf("06%06d", i))
	}
	for i := 1; i <= 1000; i++ {
		thousandsPlaylistTitleIDs = append(thousandsPlaylistTitleIDs, fmt.Sprintf("06%06d", i))
	}
	for i := 1; i <= 499; i++ {
		playlistTitleIDsLessThanMaxCount = append(playlistTitleIDsLessThanMaxCount, fmt.Sprintf("06%06d", i))
	}

	testcases := []struct {
		name     string
		given    func()
		then     func(err error)
		userID   string
		titleIDs []string
	}{
		{
			name: "Given 500 titles, and add 1 title, then remove 1 oldest title",
			given: func() {
				suite.mockUserPlaylistRepo.EXPECT().GetPlaylistByUserIDAndName("test-userID", "favorite").Return(&dbuser.UserPlaylist{ID: "test-playlist-id"}, nil)
				suite.mockPlaylistTitleRepo.EXPECT().CountTitlesInPlaylist("test-playlist-id").Return(500, nil)
				suite.mockPlaylistTitleRepo.EXPECT().GetPlaylistTitleIDsByPlaylistID("test-playlist-id").Return(playlistTitleIDs, nil)
				suite.mockPlaylistTitleRepo.EXPECT().RemoveTitlesFromPlaylist("test-playlist-id", []string{"06000500"}).Return(nil)
				suite.mockPlaylistTitleRepo.EXPECT().AddTitlesToPlaylist("test-playlist-id", []string{"01000465"}).Return([]string{"01000465"}, nil)
			},
			then: func(err error) {
				suite.NoError(err)
			},
			userID:   "test-userID",
			titleIDs: []string{"01000465"},
		},
		{
			name: "Given 1000 titles, and add 2 title, then remove 2 oldest title",
			given: func() {
				suite.mockUserPlaylistRepo.EXPECT().GetPlaylistByUserIDAndName("test-userID", "favorite").Return(&dbuser.UserPlaylist{ID: "test-playlist-id"}, nil)
				suite.mockPlaylistTitleRepo.EXPECT().CountTitlesInPlaylist("test-playlist-id").Return(1000, nil)
				suite.mockPlaylistTitleRepo.EXPECT().GetPlaylistTitleIDsByPlaylistID("test-playlist-id").Return(thousandsPlaylistTitleIDs, nil)
				suite.mockPlaylistTitleRepo.EXPECT().RemoveTitlesFromPlaylist("test-playlist-id", []string{"06000999", "06001000"}).Return(nil)
				suite.mockPlaylistTitleRepo.EXPECT().AddTitlesToPlaylist("test-playlist-id", []string{"01000465", "01000466"}).Return([]string{"01000465", "01000466"}, nil)
			},
			then: func(err error) {
				suite.NoError(err)
			},
			userID:   "test-userID",
			titleIDs: []string{"01000465", "01000466"},
		},
		{
			name: "Given 499 titles, and add 3 title, then remove 2 oldest title",
			given: func() {
				suite.mockUserPlaylistRepo.EXPECT().GetPlaylistByUserIDAndName("test-userID", "favorite").Return(&dbuser.UserPlaylist{ID: "test-playlist-id"}, nil)
				suite.mockPlaylistTitleRepo.EXPECT().CountTitlesInPlaylist("test-playlist-id").Return(499, nil)
				suite.mockPlaylistTitleRepo.EXPECT().GetPlaylistTitleIDsByPlaylistID("test-playlist-id").Return(playlistTitleIDsLessThanMaxCount, nil)
				suite.mockPlaylistTitleRepo.EXPECT().RemoveTitlesFromPlaylist("test-playlist-id", []string{"06000498", "06000499"}).Return(nil)
				suite.mockPlaylistTitleRepo.EXPECT().AddTitlesToPlaylist("test-playlist-id", []string{"01000465", "01000466", "01000467"}).Return([]string{"01000465", "01000466", "01000467"}, nil)
			},
			then: func(err error) {
				suite.NoError(err)
			},
			userID:   "test-userID",
			titleIDs: []string{"01000465", "01000466", "01000467"},
		},
	}
	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			defer suite.ctrl.Finish()

			tc.given()
			err := suite.service.AddFavoriteAndRemoveLatest(tc.userID, tc.titleIDs)
			tc.then(err)
		})
	}
}
