// Code generated by MockGen. DO NOT EDIT.
// Source: legacy.go

// Package productpackage is a generated GoMock package.
package productpackage

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MocklegacyHelper is a mock of legacyHelper interface.
type MocklegacyHelper struct {
	ctrl     *gomock.Controller
	recorder *MocklegacyHelperMockRecorder
}

// MocklegacyHelperMockRecorder is the mock recorder for MocklegacyHelper.
type MocklegacyHelperMockRecorder struct {
	mock *MocklegacyHelper
}

// NewMocklegacyHelper creates a new mock instance.
func NewMocklegacyHelper(ctrl *gomock.Controller) *MocklegacyHelper {
	mock := &MocklegacyHelper{ctrl: ctrl}
	mock.recorder = &MocklegacyHelperMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MocklegacyHelper) EXPECT() *MocklegacyHelperMockRecorder {
	return m.recorder
}

// getLegacyUserInfoWithWasPrime mocks base method.
func (m *MocklegacyHelper) getLegacyUserInfoWithWasPrime(userID string) (*legacyUserInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "getLegacyUserInfoWithWasPrime", userID)
	ret0, _ := ret[0].(*legacyUserInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// getLegacyUserInfoWithWasPrime indicates an expected call of getLegacyUserInfoWithWasPrime.
func (mr *MocklegacyHelperMockRecorder) getLegacyUserInfoWithWasPrime(userID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "getLegacyUserInfoWithWasPrime", reflect.TypeOf((*MocklegacyHelper)(nil).getLegacyUserInfoWithWasPrime), userID)
}
