// Code generated by MockGen. DO NOT EDIT.
// Source: kktvapi/internal/v4/productpackage/package_category_service.go

// Package productpackage is a generated GoMock package.
package productpackage

import (
	reflect "reflect"

	dbuser "github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	gomock "github.com/golang/mock/gomock"
)

// MockPackageCategoryService is a mock of PackageCategoryService interface.
type MockPackageCategoryService struct {
	ctrl     *gomock.Controller
	recorder *MockPackageCategoryServiceMockRecorder
}

// MockPackageCategoryServiceMockRecorder is the mock recorder for MockPackageCategoryService.
type MockPackageCategoryServiceMockRecorder struct {
	mock *MockPackageCategoryService
}

// NewMockPackageCategoryService creates a new mock instance.
func NewMockPackageCategoryService(ctrl *gomock.Controller) *MockPackageCategoryService {
	mock := &MockPackageCategoryService{ctrl: ctrl}
	mock.recorder = &MockPackageCategoryServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPackageCategoryService) EXPECT() *MockPackageCategoryServiceMockRecorder {
	return m.recorder
}

// GetByCategories mocks base method.
func (m *MockPackageCategoryService) GetByCategories(categories []string) (*dbuser.PackageCategories, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByCategories", categories)
	ret0, _ := ret[0].(*dbuser.PackageCategories)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByCategories indicates an expected call of GetByCategories.
func (mr *MockPackageCategoryServiceMockRecorder) GetByCategories(categories interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByCategories", reflect.TypeOf((*MockPackageCategoryService)(nil).GetByCategories), categories)
}
