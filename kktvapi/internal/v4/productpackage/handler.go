package productpackage

import (
	"net/http"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/middleware"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/user"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/validation"
	"github.com/KKTV/kktv-api-v3/pkg/httpreq"
	mwmodel "github.com/KKTV/kktv-api-v3/pkg/model/middleware"
	"github.com/duke-git/lancet/v2/slice"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/rest"
	"github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/KKTV/kktv-api-v3/pkg/render"
)

type Handler struct {
	service          Service
	billingOrderRepo user.PackageBillingOrderRepository
	categorySrv      PackageCategoryService
	legacyHelper     legacyHelper
}

func NewHandler() *Handler {
	return &Handler{
		service:          NewService(),
		categorySrv:      NewPackageCategoryService(),
		legacyHelper:     &legacyHelp{},
		billingOrderRepo: user.NewPackageBillingOrderRepository(),
	}
}

type listPackagesReq struct {
	Platform    string `schema:"platform" validate:"required,oneof=web ios android campaign"`
	PaymentType string `schema:"payment_type" validate:"omitempty"`
}

func (h *Handler) GetProductPackages(w http.ResponseWriter, r *http.Request) {
	var params listPackagesReq
	if err := httpreq.Scan(&params, r.URL.Query()); err != nil {
		render.JSON(w, http.StatusBadRequest, rest.Error(ErrRespInvalidParam.Message, ErrRespInvalidParam.Code))
		return
	} else if err := validation.Validate(params); err != nil {
		render.JSON(w, http.StatusBadRequest, rest.Error(ErrRespInvalidParam.Message, ErrRespInvalidParam.Code))
		return
	}

	platform := params.Platform
	paymentType := params.PaymentType

	var userID string
	access, ok := r.Context().Value(middleware.KeyAccessUser).(mwmodel.AccessUser)
	if ok && access.IsMember() {
		userID = access.UserID
	}

	var err error
	var pkgs, billingPkgs *dbuser.ProductPackages

	if pkgs, err = h.service.ListByPlatform(platform); err != nil {
		log.Warn("v4 product package: get product packages: service fails to list packages").Str("platform", platform).Err(err).Send()
	}
	if billingPkgs, err = h.service.ListBillingByPlatform(platform); err != nil {
		log.Warn("v4 product package: get product packages: service fails to list billing packages").
			Str("platform", platform).Err(err).Send()
	}
	pkgs = pkgs.Append(billingPkgs)
	filteredPkgs, err := h.service.FilterPackages(userID, pkgs)

	var packageIDs []int
	for _, filteredPkg := range filteredPkgs {
		packageIDs = append(packageIDs, filteredPkg.Package.ID)
	}

	pkgLayouts, err := h.service.GetLayoutByPackageIDs(packageIDs)
	if err != nil {
		log.Warn("v4ProductPackage: GetLayoutByPackageIDs: failed to list package layout").
			Str("platform", platform).Interface("pkgIDs", packageIDs).Err(err).Send()
	}

	layoutsMap := convertToMap(pkgLayouts)
	packageViews := h.toPackageViews(filteredPkgs, paymentType, layoutsMap)
	var categories []string
	for _, pkg := range packageViews {
		categories = append(categories, pkg.Category...)
	}
	categories = slice.Unique(categories)
	categoryViews := h.getCategoriesFromPackages(categories)

	resp := rest.Ok()
	resp.Data = ProductPackagesWithCategory{
		ProductPackages: packageViews,
		Categories:      categoryViews,
	}
	render.JSONOk(w, resp)
}

func convertToMap(layouts *dbuser.PackageLayouts) map[int]*dbuser.PackageLayout {
	layoutsMap := make(map[int]*dbuser.PackageLayout)

	if layouts == nil {
		return layoutsMap
	}

	for _, layout := range *layouts {
		layoutsMap[int(layout.ProductPackagesID.Int64)] = layout
	}
	return layoutsMap
}

func (h *Handler) toPackageViews(filteredPkgs []*FilteredPackage, paymentType string, layouts map[int]*dbuser.PackageLayout) []Package {
	billingProducts, err := h.service.ListBillingProducts()
	if err != nil {
		log.Warn("v4 product package: service fail to fetch billing products").Err(err).Send()
	}
	packageViews := make([]Package, 0)
	for _, fpkg := range filteredPkgs {
		pkg := fpkg.Package
		pkgView := Package{}
		pkgView.ConvertFromModel(fpkg, billingProducts, layouts[pkg.ID])
		if len(pkgView.Products) == 0 {
			continue
		}
		if paymentType != "" {
			matched := false
			for _, p := range pkgView.Products {
				if p.PaymentType == paymentType {
					matched = true
					break
				}
			}
			if !matched {
				continue
			}
		}
		packageViews = append(packageViews, pkgView)
	}
	return packageViews
}

func (h *Handler) getCategoriesFromPackages(categories []string) []Category {
	pkgCategories, err := h.categorySrv.GetByCategories(categories)
	if err != nil {
		log.Warn("v4ProductPackage: getCategoriesFromPackages: categoryService fails to get").Err(err).
			Strs("categories", categories).Send()
		return []Category{}
	}

	views := make([]Category, 0)
	for _, pkgCategory := range *pkgCategories {
		category := Category{
			Name:    pkgCategory.Category,
			General: pkgCategory.General,
			Sort:    pkgCategory.Sort,
		}
		views = append(views, category)
	}
	return views
}
