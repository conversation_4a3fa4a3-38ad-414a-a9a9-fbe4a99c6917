//go:generate mockgen -source legacy.go -destination legacy_mock.go -package watchhistory
package watchhistory

import legacymodel "github.com/KKTV/kktv-api-v3/kkapp/model"

type legacyHelper interface {
	NewUserTitleDetails(userID string, titleIDs []string) (WatchedTitles, error)
}

type legacyHelperImpl struct{}

func (h *legacyHelperImpl) NewUserTitleDetails(userID string, titleIDs []string) (WatchedTitles, error) {
	return legacymodel.NewUserTitleDetails(userID, titleIDs)
}
