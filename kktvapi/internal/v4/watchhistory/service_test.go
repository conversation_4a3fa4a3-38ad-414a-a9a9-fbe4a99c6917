package watchhistory

import (
	"fmt"
	"testing"

	legacymodel "github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/user"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbmeta"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
)

type ServiceTestSuite struct {
	suite.Suite
	ctrl                 *gomock.Controller
	mockLegacyHelper     *MocklegacyHelper
	mockWatchHistoryRepo *user.MockWatchHistoryRepository
	srv                  Service
}

func TestServiceTestSuite(t *testing.T) {
	suite.Run(t, new(ServiceTestSuite))
}

func (suite *ServiceTestSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
	suite.mockLegacyHelper = NewMocklegacyHelper(suite.ctrl)
	suite.mockWatchHistoryRepo = user.NewMockWatchHistoryRepository(suite.ctrl)
	suite.srv = &service{
		legacyHelper: suite.mockLegacyHelper,
		repo:         suite.mockWatchHistoryRepo,
	}
}

func (suite *ServiceTestSuite) TearDownTest() {
	defer suite.ctrl.Finish()
}

func (suite *ServiceTestSuite) TestGetByUserID() {
	var (
		userID = "test-userid"
	)
	testcases := []struct {
		name       string
		given      func() chan struct{}
		thenAssert func(titles WatchedTitles, err error)
	}{
		{
			name: "All of Live titles or without episode ID will be skipped",
			given: func() chan struct{} {
				suite.mockWatchHistoryRepo.EXPECT().GetByUserID(userID).Return([]string{"test-titleid"}, nil)
				suite.mockLegacyHelper.EXPECT().NewUserTitleDetails(userID, []string{"test-titleid"}).Return(WatchedTitles{
					{
						TitleDetail: &legacymodel.TitleDetail{
							ID:        "01060429",
							TitleType: dbmeta.TitleTypeSeries.String(),
						},
						LastPlayedEpisode: legacymodel.EpisodePlayed{ID: "01060429010001"},
					},
					{
						TitleDetail: &legacymodel.TitleDetail{
							ID:        "01060430",
							TitleType: dbmeta.TitleTypeLive.String(),
						},
						LastPlayedEpisode: legacymodel.EpisodePlayed{ID: "01060430010001"},
					},
					{
						TitleDetail: &legacymodel.TitleDetail{
							ID:        "01060431",
							TitleType: dbmeta.TitleTypeSeries.String(),
						},
					},
				}, nil)
				return nil
			},
			thenAssert: func(titles WatchedTitles, err error) {
				suite.Nil(err)
				suite.Equal(1, len(titles))
				suite.Equal("01060429", titles[0].TitleDetail.ID)
			},
		},
		{
			name: "titles have new arrive episode",
			given: func() chan struct{} {
				suite.mockWatchHistoryRepo.EXPECT().GetByUserID(userID).Return([]string{"test-titleid"}, nil)
				suite.mockLegacyHelper.EXPECT().NewUserTitleDetails(userID, []string{"test-titleid"}).Return(WatchedTitles{
					{
						TitleDetail: &legacymodel.TitleDetail{
							ID:            "01060429",
							TitleType:     dbmeta.TitleTypeSeries.String(),
							ContentLabels: []string{"new_arrival"},
						},
						LastPlayedEpisode: legacymodel.EpisodePlayed{ID: "01060429010001"},
					},
				}, nil)
				return nil
			},
			thenAssert: func(titles WatchedTitles, err error) {
				suite.Nil(err)
				suite.Equal(1, len(titles))
				suite.Equal("01060429", titles[0].TitleDetail.ID)
				suite.Equal([]string{"new_arrival"}, titles[0].ContentLabels)
			},
		},
		{
			name: "WHEN watched titles than 28 rows, only return latest 20 titles AND purge the earlier ones",
			given: func() chan struct{} {
				first20Records := []string{"test-titleid-1", "test-titleid-2", "test-titleid-3", "test-titleid-4", "test-titleid-5", "test-titleid-6", "test-titleid-7", "test-titleid-8", "test-titleid-9", "test-titleid-10", "test-titleid-11", "test-titleid-12", "test-titleid-13", "test-titleid-14", "test-titleid-15", "test-titleid-16", "test-titleid-17", "test-titleid-18", "test-titleid-19", "test-titleid-20"}
				suite.mockWatchHistoryRepo.EXPECT().GetByUserID(userID).Return(append(first20Records, "test-titleid-21", "test-titleid-22", "test-titleid-23", "test-titleid-24", "test-titleid-25", "test-titleid-26", "test-titleid-27", "test-titleid-28", "test-titleid-29", "test-titleid-30"), nil)
				suite.mockLegacyHelper.EXPECT().NewUserTitleDetails(userID, first20Records).Return(fakeWatchedTitles(20), nil)
				wait := make(chan struct{})
				suite.mockWatchHistoryRepo.EXPECT().PurgeAfter(userID, 21).DoAndReturn(func(userID string, keep int) error {
					suite.Equal(21, keep)
					close(wait)
					return nil
				})
				return wait
			},
			thenAssert: func(titles WatchedTitles, err error) {
				suite.Nil(err)
				suite.Equal(20, len(titles))
			},
		},
		{
			name: "got ErrNotFound WHEN no watch history found by repo",
			given: func() chan struct{} {
				suite.mockWatchHistoryRepo.EXPECT().GetByUserID(userID).Return(nil, nil)
				return nil
			},
			thenAssert: func(titles WatchedTitles, err error) {
				suite.Equal(ErrNotFound, err)
			},
		},
	}
	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			wait := tc.given()
			titles, err := suite.srv.GetByUserID(userID)
			if wait != nil {
				<-wait
			}
			tc.thenAssert(titles, err)
		})
	}
}

func fakeWatchedTitles(amount int) WatchedTitles {
	titles := make(WatchedTitles, 0, amount)
	for i := 0; i < amount; i++ {
		titles = append(titles, &legacymodel.UserTitleDetail{
			TitleDetail: &legacymodel.TitleDetail{
				ID:        fmt.Sprintf("test-titleid-%d", i),
				TitleType: dbmeta.TitleTypeSeries.String(),
			},
			LastPlayedEpisode: legacymodel.EpisodePlayed{ID: fmt.Sprintf("test-epid-%d", i)},
		})
	}
	return titles
}
