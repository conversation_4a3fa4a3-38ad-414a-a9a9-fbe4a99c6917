package watchhistory

import (
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/v4/presenter"
	"github.com/KKTV/kktv-api-v3/pkg/datatype"
)

type Episode struct {
	ID           string                `json:"id"`
	Name         string                `json:"name"`
	Duration     datatype.RoundedFloat `json:"duration"`
	Still        string                `json:"still"`
	PlayedOffset int64                 `json:"played_offset"`
	Deeplink     string                `json:"deeplink"`
	IsAVOD       bool                  `json:"is_avod"`
}

type Title struct {
	presenter.TitleBasicInfo
	LatestUpdateInfo string            `json:"latest_update_info"`
	Labels           []presenter.Label `json:"labels"`
}

type DisplayInfo struct {
	Title            string                `json:"title"`
	Desc             string                `json:"description"`
	DescShort        string                `json:"description_short"`
	PlayHint         string                `json:"play_hint"`
	PlayedPercentage datatype.RoundedFloat `json:"played_percentage"`
}

type HistoryItem struct {
	Title         *Title                    `json:"title"`
	Ep            *Episode                  `json:"last_played_episode"`
	Display       *DisplayInfo              `json:"display"`
	UserAuthority *presenter.VideoAuthority `json:"user_authority,omitempty"`
}
