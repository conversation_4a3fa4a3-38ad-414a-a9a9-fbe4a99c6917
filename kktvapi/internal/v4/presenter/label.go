package presenter

type Label struct {
	BGColor     string `json:"bg_color"`
	FontColor   string `json:"font_color"`
	Key         string `json:"key"`
	DisplayName string `json:"display_name"`
}

type LabelType string

func (l LabelType) String() string {
	return string(l)
}

const (
	LabelTypeComingSoon    LabelType = "coming_soon"
	LabelTypeStreamingSoon LabelType = "streaming_soon"
	LabelTypeExpireSoon    LabelType = "expire_soon"
	LabelTypeNewArrival    LabelType = "new_arrival"
	LabelTypeVIP           LabelType = "vip"
	LabelTypePartialEpFree LabelType = "partial_episodes_free"
	LabelTypeDualSubtitle  LabelType = "dual_subtitle"
)

const (
	fontColor = "#FFFFFF"
)

var labelConfigMap = map[LabelType]Label{
	LabelTypeComingSoon:    {BGColor: "#6A8A22", DisplayName: "即將上架", FontColor: fontColor, Key: LabelTypeComingSoon.String()},
	LabelTypeStreamingSoon: {BGColor: "#B94343", DisplayName: "即將直播", FontColor: fontColor, Key: LabelTypeStreamingSoon.String()},
	LabelTypeExpireSoon:    {BGColor: "#5F5F5F", DisplayName: "即將到期", FontColor: fontColor, Key: LabelTypeExpireSoon.String()},
	LabelTypeNewArrival:    {BGColor: "#B40F42", DisplayName: "NEW", FontColor: fontColor, Key: LabelTypeNewArrival.String()},
	LabelTypeVIP:           {BGColor: "#339899", DisplayName: "VIP", FontColor: fontColor, Key: LabelTypeVIP.String()},
	LabelTypePartialEpFree: {BGColor: "#4A88CC", DisplayName: "部分免費", FontColor: fontColor, Key: LabelTypePartialEpFree.String()},
	LabelTypeDualSubtitle:  {BGColor: "#6449C2", DisplayName: "雙字幕", FontColor: fontColor, Key: LabelTypeDualSubtitle.String()},
}

func GetLabel(lt LabelType) Label {
	return labelConfigMap[lt]
}
