package presenter

import (
	"testing"

	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/platform"
	"github.com/KKTV/kktv-api-v3/pkg/model/authority"
	metamodel "github.com/KKTV/kktv-api-v3/pkg/model/dbmeta"
	"github.com/stretchr/testify/assert"
)

var (
	exampleTitleDetail = &model.TitleDetail{
		ID:    "00000429",
		Title: "Josie smile", TitleType: metamodel.TitleTypeSeries.String(),
		Status:   "license_valid",
		IsEnding: true, ChildLock: true,
		ContentLabelsWithoutFullAccess: []string{"coming_soon", "vip"},
		ContentLabelsWithFullAccess:    []string{"coming_soon"},
		AcceptedAuthorities:            []authority.Authority{authority.PremiumPlay},
		LatestUpdateInfo:               "共3集",
	}
	exampleExpect = &ListedTitle{
		TitleBasicInfo: TitleBasicInfo{
			ID:        "00000429",
			Name:      "Josie smile",
			TitleType: metamodel.TitleTypeSeries.String(),
			Status:    "license_valid",
			IsEnding:  true,
			ChildLock: true,
		},
		ListedTitleMeta: ListedTitleMeta{
			LatestUpdateInfo: "共3集",
			DeepLink:         "https://www.kktv.me/titles/00000429",
			Tags:             make([]CollectionTag, 0),
			Genres:           make([]CollectionTag, 0),
		},
	}
)

func TestListedTitle_ConvertFromTitleDetail(t *testing.T) {
	testcases := []struct {
		name        string
		titleDetail *model.TitleDetail
		userCanPlay bool
		deviceType  platform.DeviceType
		expect      ListedTitle
	}{
		{
			name: "Title is series, role is guest, device is APP",
			titleDetail: func(d model.TitleDetail) *model.TitleDetail {
				return &d
			}(*exampleTitleDetail),
			userCanPlay: false,
			deviceType:  platform.DeviceTypeMobileApp,
			expect: func(lt ListedTitle) ListedTitle {
				lt.Labels = []Label{
					{
						BGColor: "#6A8A22", FontColor: "#FFFFFF",
						Key: "coming_soon", DisplayName: "即將上架",
					},
					{
						BGColor: "#339899", FontColor: "#FFFFFF",
						Key: "vip", DisplayName: "VIP",
					},
				}
				return lt
			}(*exampleExpect),
		},
		{
			name: "Title is series and user can play it, device is APP",
			titleDetail: func(d model.TitleDetail) *model.TitleDetail {
				return &d
			}(*exampleTitleDetail),
			userCanPlay: true,
			deviceType:  platform.DeviceTypeMobileApp,
			expect: func(lt ListedTitle) ListedTitle {
				lt.Labels = []Label{
					{
						BGColor: "#6A8A22", FontColor: "#FFFFFF",
						Key: "coming_soon", DisplayName: "即將上架",
					},
				}
				return lt
			}(*exampleExpect),
		},
		{
			name: "POC: Title is LIVE, and now is during license THEN has live_info.is_streaming = true",
			titleDetail: func(d model.TitleDetail) *model.TitleDetail {
				d.TitleType = metamodel.TitleTypeLive.String()
				d.IsEnding = false
				d.ContentLabelsWithoutFullAccess = []string{}
				return &d
			}(*exampleTitleDetail),
			userCanPlay: false,
			deviceType:  platform.DeviceTypeMobileApp,
			expect: func(lt ListedTitle) ListedTitle {
				lt.TitleType = metamodel.TitleTypeLive.String()
				lt.IsEnding = false
				lt.LiveInfo = &LiveInfo{IsStreaming: true}
				lt.Labels = []Label{}
				return lt
			}(*exampleExpect),
		},
		{
			name: "POC: Title is a coming soon LIVE, SHOULD has the '即將直播' label ",
			titleDetail: func(d model.TitleDetail) *model.TitleDetail {
				d.TitleType = metamodel.TitleTypeLive.String()
				d.Status = "coming_soon"
				d.IsEnding = false
				d.ContentLabelsWithoutFullAccess = []string{LabelTypeComingSoon.String()}
				return &d
			}(*exampleTitleDetail),
			userCanPlay: false,
			deviceType:  platform.DeviceTypeMobileApp,
			expect: func(lt ListedTitle) ListedTitle {
				lt.TitleType = metamodel.TitleTypeLive.String()
				lt.Status = "coming_soon"
				lt.IsEnding = false
				lt.LiveInfo = &LiveInfo{IsStreaming: false}
				lt.Labels = []Label{
					{
						BGColor:     "#B94343",
						FontColor:   "#FFFFFF",
						Key:         "streaming_soon",
						DisplayName: "即將直播",
					},
				}
				return lt
			}(*exampleExpect),
		},
	}
	for _, tc := range testcases {
		t.Run(tc.name, func(t *testing.T) {
			actual := ListedTitle{}
			actual.ConvertFromTitleDetail(tc.titleDetail, tc.userCanPlay, tc.deviceType)
			assert.Equal(t, tc.expect, actual)
		})
	}
}
