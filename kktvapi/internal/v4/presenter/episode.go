package presenter

import "github.com/KKTV/kktv-api-v3/pkg/datatype"

type EpisodeLastPlayed struct {
	PlayedPercentage datatype.RoundedFloat `json:"played_percentage"`
	Deeplink         string                `json:"deeplink"`
	PlayedOffset     int64                 `json:"played_offset"`
	IsCompleted      bool                  `json:"is_completed"`
}

type Episode struct {
	ID            string                `json:"id"`
	Name          string                `json:"name"`
	IsAvod        bool                  `json:"is_avod"`
	IsFreeTrial   bool                  `json:"is_free_trial"`
	Duration      datatype.RoundedFloat `json:"duration"`
	Still         string                `json:"still"`
	Subtitles     []string              `json:"subtitles,omitempty"`
	AllowOffline  *bool                 `json:"allow_offline,omitempty"`
	LicenseEnd    *int64                `json:"license_end,omitempty"`
	LicenseStart  *int64                `json:"license_start,omitempty"`
	LastPlayed    *EpisodeLastPlayed    `json:"last_played,omitempty"`
	UserAuthority *VideoAuthority       `json:"user_authority,omitempty"`
}
