// Code generated by MockGen. DO NOT EDIT.
// Source: service.go

// Package collection is a generated GoMock package.
package collection

import (
	reflect "reflect"

	platform "github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/platform"
	dbuser "github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	gomock "github.com/golang/mock/gomock"
)

// MockService is a mock of Service interface.
type MockService struct {
	ctrl     *gomock.Controller
	recorder *MockServiceMockRecorder
}

// MockServiceMockRecorder is the mock recorder for MockService.
type MockServiceMockRecorder struct {
	mock *MockService
}

// NewMockService creates a new mock instance.
func NewMockService(ctrl *gomock.Controller) *MockService {
	mock := &MockService{ctrl: ctrl}
	mock.recorder = &MockServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockService) EXPECT() *MockServiceMockRecorder {
	return m.recorder
}

// QueryCollection mocks base method.
func (m *MockService) QueryCollection(query CollectionQuery, membership dbuser.Membership, deviceType platform.DeviceType, isHideLustContent bool) (*Collection, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryCollection", query, membership, deviceType, isHideLustContent)
	ret0, _ := ret[0].(*Collection)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryCollection indicates an expected call of QueryCollection.
func (mr *MockServiceMockRecorder) QueryCollection(query, membership, deviceType, isHideLustContent interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryCollection", reflect.TypeOf((*MockService)(nil).QueryCollection), query, membership, deviceType, isHideLustContent)
}
