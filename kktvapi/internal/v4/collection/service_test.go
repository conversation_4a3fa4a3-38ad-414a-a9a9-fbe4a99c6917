package collection

import (
	"fmt"
	"net/url"
	"testing"
	"time"

	legacySearch "github.com/KKTV/kktv-api-v3/kkapp/kksearch"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/meta"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/platform"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/v4/presenter"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/kktverror"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/permission"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
)

type ServiceTestSuite struct {
	suite.Suite
	ctrl    *gomock.Controller
	service Service

	mockTitleRepo     *meta.MockTitleRepository
	mockLegacyHelper  *MocklegacyHelper
	mockPermissionSrv *permission.MockService
}

func TestServiceTestSuite(t *testing.T) {
	suite.Run(t, new(ServiceTestSuite))
}

func (suite *ServiceTestSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
	suite.mockLegacyHelper = NewMocklegacyHelper(suite.ctrl)
	suite.mockTitleRepo = meta.NewMockTitleRepository(suite.ctrl)
	suite.mockPermissionSrv = permission.NewMockService(suite.ctrl)
	suite.service = &service{
		titleRepo:     suite.mockTitleRepo,
		legacyHelper:  suite.mockLegacyHelper,
		permissionSrv: suite.mockPermissionSrv,
	}
}

func (suite *ServiceTestSuite) TearDownTest() {
	defer suite.ctrl.Finish()
}

func (suite *ServiceTestSuite) TestQueryCollection() {
	testcases := []struct {
		name       string
		given      func()
		query      CollectionQuery
		membership dbuser.Membership
		deviceType platform.DeviceType
		platform   string
		then       func(collection *Collection, err error)
	}{
		{
			name: "Given genre:娛樂, iOS, and premium, return full access label and without error",
			given: func() {
				legacyCollection := &legacyCollection{
					&legacySearch.Collection{
						Filter: getMockFilter(),
						Items:  getMockTitleDetail(),
					},
				}
				suite.mockLegacyHelper.EXPECT().QueryCollection("genre:娛樂", url.Values{}, false).Return(legacyCollection, nil)
				suite.mockGrantedFullAccess(legacyCollection.Items[0], dbuser.MembershipPremiumOnly)
			},
			query: CollectionQuery{
				Target:    "genre:娛樂",
				URLValues: url.Values{},
			},
			membership: dbuser.MembershipPremiumOnly,
			deviceType: platform.DeviceTypeMobileApp,
			platform:   "iOS",
			then: func(collection *Collection, err error) {
				suite.NoError(err)
				suite.Equal("韓國", collection.Filter[0].Options[0].DisplayName)
				suite.Equal("country", collection.Filter[0].Options[0].CollectionType)
				suite.Equal("Korea", collection.Filter[0].Options[0].CollectionName)
				suite.Equal("00000429", collection.Items[0].TitleBasicInfo.ID)
				suite.Equal("expire_soon", collection.Items[0].Labels[0].Key)
				suite.Len(collection.Items[0].Labels, 1)
			},
		},
		{
			name: "Given genre:娛樂, iOS, and guest, return label of non full access and without error",
			given: func() {
				legacyCollection := &legacyCollection{
					&legacySearch.Collection{
						Filter: getMockFilter(),
						Items:  getMockTitleDetail(),
					},
				}
				suite.mockLegacyHelper.EXPECT().QueryCollection("genre:娛樂", url.Values{}, false).Return(legacyCollection, nil)
				suite.mockGrantFullAccessFail(legacyCollection.Items[0], dbuser.NonMember)
			},
			query: CollectionQuery{
				Target:    "genre:娛樂",
				URLValues: url.Values{},
			},
			membership: dbuser.NonMember,
			deviceType: platform.DeviceTypeMobileApp,
			platform:   "iOS",
			then: func(collection *Collection, err error) {
				fmt.Printf("%+v\n", collection.Items[0].Labels)
				suite.NoError(err)
				suite.Equal("韓國", collection.Filter[0].Options[0].DisplayName)
				suite.Equal("country", collection.Filter[0].Options[0].CollectionType)
				suite.Equal("Korea", collection.Filter[0].Options[0].CollectionName)
				suite.Equal("00000429", collection.Items[0].TitleBasicInfo.ID)
				suite.Equal("expire_soon", collection.Items[0].Labels[0].Key)
				suite.Equal("vip", collection.Items[0].Labels[1].Key)

			},
		},
	}

	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			defer suite.ctrl.Finish()

			tc.given()
			collection, err := suite.service.QueryCollection(tc.query, tc.membership, tc.deviceType, false)
			tc.then(collection, err)
		})
	}
}

func (suite *ServiceTestSuite) mockGrantFullAccessFail(td *model.TitleDetail, ms dbuser.Membership) {
	suite.mockPermissionSrv.EXPECT().Grant(permission.RequestFullAccessTitleDetail(td, ms)).Return(kktverror.ErrResourceAccessDenied)
}

func (suite *ServiceTestSuite) mockGrantedFullAccess(td *model.TitleDetail, ms dbuser.Membership) {
	suite.mockPermissionSrv.EXPECT().Grant(
		permission.RequestFullAccessTitleDetail(td, ms),
	).Return(nil)
}

func getMockFilter() []legacySearch.CollectionFilter {
	return []legacySearch.CollectionFilter{
		legacySearch.CollectionFilter{
			Items: []*legacySearch.CollectAddition{
				&legacySearch.CollectAddition{
					Title:          "韓國",
					CollectionType: "country",
					CollectionName: "Korea",
				},
			},
		},
	}
}

func getMockTitleDetail() []*model.TitleDetail {
	return []*model.TitleDetail{
		&model.TitleDetail{
			ID:        "00000429",
			Title:     "Josie smiles",
			TitleType: "miniseries",
			Status:    "license_valid",
			Review:    map[string]string{"content": "劇我所知123"},
			Summary:   "summary123",
			Casts: []*dbmeta.CollectionItem{
				{
					ID:             "",
					Title:          "龍套A",
					CollectionType: "cast",
					CollectionName: "龍套A",
				},
			},
			IsEnding:                       true,
			IsContainingAvod:               true,
			IsValidated:                    true,
			ChildLock:                      true,
			ReleaseYear:                    2019,
			EndYear:                        0,
			UserRating:                     4.966,
			UserRatingCount:                0,
			Cover:                          "https://fakeimg.com/josie.jpg",
			Stills:                         []string{"https://fakeimg.com/1.jpg"},
			TotalSeriesCount:               0,
			LatestUpdateInfo:               "共20集",
			ContentLabels:                  []string{presenter.LabelTypeExpireSoon.String()},
			ContentLabelsForExpiredUser:    []string{presenter.LabelTypeExpireSoon.String(), presenter.LabelTypeVIP.String()},
			ContentLabelsForFreeTrialUser:  []string{presenter.LabelTypeExpireSoon.String(), presenter.LabelTypeVIP.String()},
			ContentLabelsWithFullAccess:    []string{presenter.LabelTypeExpireSoon.String()},
			ContentLabelsWithoutFullAccess: []string{presenter.LabelTypeExpireSoon.String(), presenter.LabelTypeVIP.String()},
			Series: []*dbmeta.Series{
				{
					Episodes: []*dbmeta.EpisodeMeta{
						{Pub: time.Date(2020, 4, 29, 1, 0, 0, 0, time.UTC).Unix()},
						{Pub: time.Date(2020, 4, 29, 2, 0, 0, 0, time.UTC).Unix()},
					},
				},
			},
		},
	}
}
