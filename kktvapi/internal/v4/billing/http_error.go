package billing

import "github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/rest"

var (
	ErrRespInvalidParameter = &rest.Err{Code: "400.1", Message: "invalid parameter"}
	ErrRespInvalidEmail     = &rest.Err{Code: "400.2", Message: "invalid email"}
	ErrRespUserNotFound     = &rest.Err{Code: "400.3", Message: "User not found"}
	ErrRespUserNotLogin     = &rest.Err{Code: "403.1", Message: "Forbidden to access because user is not login"}
	ErrRespUnknown          = &rest.Err{Code: "500.0", Message: "Unknown error"}
)
