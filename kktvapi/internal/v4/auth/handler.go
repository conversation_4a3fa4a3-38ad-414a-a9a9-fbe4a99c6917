package auth

import (
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/appauth"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/auth"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/middleware"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/oauth"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/rest"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/clock"
	"github.com/KKTV/kktv-api-v3/pkg/encrypt"
	"github.com/KKTV/kktv-api-v3/pkg/httpreq"
	"github.com/KKTV/kktv-api-v3/pkg/key"
	"github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/model/authority"
	"github.com/KKTV/kktv-api-v3/pkg/model/cacheuser"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	modelmw "github.com/KKTV/kktv-api-v3/pkg/model/middleware"
	"github.com/KKTV/kktv-api-v3/pkg/rand"
	"github.com/KKTV/kktv-api-v3/pkg/render"
	"github.com/KKTV/kktv-api-v3/pkg/validator"
	"github.com/duke-git/lancet/v2/slice"
)

type Handler struct {
	appAuthRepo            appauth.Repository
	oauthAuthorizationRepo oauth.AuthorizationRepository
	userCacheWriter        cache.Cacher
	userCacheReader        cache.Cacher
	codeGen                encrypt.CodeGen
	clock                  clock.Clock
	rand                   rand.Rand
}

func NewHandler() *Handler {
	return &Handler{
		appAuthRepo:            appauth.NewRepository(),
		oauthAuthorizationRepo: oauth.NewAuthorizationRepository(),
		userCacheWriter:        cache.New(container.CachePoolUser().Master()),
		userCacheReader:        cache.New(container.CachePoolUser().Slave()),
		codeGen:                encrypt.NewCodeGen(),
		clock:                  clock.New(),
		rand:                   rand.New(),
	}
}

const (
	ttlAuthUserCode = 5 * time.Minute
	ttlAccessToken  = 7 * 24 * time.Hour // 7 day
)
const (
	scopeUserInfoGet = "user-info:get"
)

var (
	scopeAuthorityMap = map[string][]authority.Authority{
		scopeUserInfoGet: {authority.UserInfoGet},
	}
)

func (h *Handler) SignRedirectURI(w http.ResponseWriter, r *http.Request) {
	req := signRedirectReq{}
	if values, err := httpreq.ParseQuery(r); err != nil {
		render.JSONBadRequest(w, rest.Error(ErrInvalidParameters.Message, ErrInvalidParameters.Code))
		return
	} else if err := httpreq.Scan(&req, values); err != nil {
		render.JSONBadRequest(w, rest.Error(ErrInvalidParameters.Message, ErrInvalidParameters.Code))
		return
	} else if err := validator.Validate(req); err != nil {
		render.JSONBadRequest(w, rest.Error(ErrInvalidParameters.Message, ErrInvalidParameters.Code))
		return
	}

	user, ok := r.Context().Value("user").(model.JwtUser)
	if !ok || user.IsGuest() {
		render.JSONUnauthorized(w, rest.Error(ErrUserNotLogin.Message, ErrUserNotLogin.Code))
		return
	}

	req.RedirectURI = normalizeURI(req.RedirectURI)

	app, err := h.appAuthRepo.GetActiveByAppID(req.AppID)
	if err != nil {
		log.Error("v4 auth handler: sign redirect uri: fail to get app by id").Str("app_id", req.AppID).Err(err).Send()
		render.JSONInternalServerErr(w, rest.Error(ErrUnknown.Message, ErrUnknown.Code))
		return
	} else if app == nil {
		render.JSONBadRequest(w, rest.Error(ErrInvalidApp.Message, ErrInvalidApp.Code))
		return
	}
	if !slice.Contain(app.RedirectURIs, req.RedirectURI) {
		render.JSONBadRequest(w, rest.Error(ErrURIMismatched.Message, ErrURIMismatched.Code))
		return
	}

	userCode, err := h.cacheUserHashCode(user.Sub)
	if err != nil {
		log.Error("v4 auth handler: sign redirect uri: fail to write to cache").Str("user_id", user.Sub).Err(err).Send()
		render.JSONInternalServerErr(w, rest.Error(ErrUnknown.Message, ErrUnknown.Code))
		return
	}

	uri, _ := url.Parse(req.RedirectURI)
	values := url.Values{}
	values.Add("state", req.State)
	values.Add("code", userCode)
	uri.RawQuery = values.Encode()

	resp := rest.Ok()
	resp.Data = map[string]interface{}{
		"redirect_uri": uri.String(),
	}
	render.JSONOk(w, resp)
}

func normalizeURI(uri string) string {
	// Parse the URI to extract the base path without query parameters
	parsedURI, err := url.Parse(uri)
	if err != nil {
		// If parsing fails, fall back to the original behavior
		return strings.TrimSuffix(strings.TrimSuffix(uri, "?"), "/")
	}

	// Return the scheme, host, and path without query parameters
	parsedURI.RawQuery = ""
	parsedURI.ForceQuery = false
	return strings.TrimSuffix(parsedURI.String(), "/")
}

func (h *Handler) cacheUserHashCode(userID string) (string, error) {
	hash := h.encodeUserID(userID)
	cKey := key.UserAuthCode(hash)
	if err := h.userCacheWriter.Set(cKey, userID, ttlAuthUserCode); err != nil {
		return "", err
	}
	return hash, nil
}

func (h *Handler) encodeUserID(userID string) string {
	t := strconv.FormatInt(h.clock.Now().Unix(), 10)
	return h.codeGen.StringsHashcode([]string{userID, t}, ":")
}

// CreateAuthCode creates a one-time-use authorization token with TTL
func (h *Handler) CreateAuthCode(w http.ResponseWriter, r *http.Request) {
	req := createAuthCodeReq{
		// TODO: by app to validate if the scope is required
		Scope: fmt.Sprintf("%s", scopeUserInfoGet), // default scope
	}
	if err := httpreq.PayloadBinding(&req, r); err != nil {
		render.JSONBadRequest(w, rest.Error(ErrInvalidParameters.Message, ErrInvalidParameters.Code))
		return
	}

	accessUser := r.Context().Value(middleware.KeyAccessUser).(modelmw.AccessUser)

	req.RedirectURI = normalizeURI(req.RedirectURI)

	app, err := h.appAuthRepo.GetActiveByAppID(req.AppID)
	if err != nil {
		log.Error("v4 auth handler: create auth code: fail to get app by id").Str("app_id", req.AppID).Err(err).Send()
		render.JSONInternalServerErr(w, rest.Error(ErrUnknown.Message, ErrUnknown.Code))
		return
	} else if app == nil {
		render.JSONBadRequest(w, rest.Error(ErrInvalidApp.Message, ErrInvalidApp.Code))
		return
	}
	if !slice.Contain(app.RedirectURIs, req.RedirectURI) {
		render.JSONBadRequest(w, rest.Error(ErrURIMismatched.Message, ErrURIMismatched.Code))
		return
	}

	authCode := h.rand.RandomString(32)

	cKey := key.OAuthAuthCode(authCode)
	authData := cacheuser.OAuthData{
		AppID:       req.AppID,
		UserID:      accessUser.UserID,
		RedirectURI: req.RedirectURI,
		Scope:       req.Scope,
	}

	if err := h.userCacheWriter.Set(cKey, authData, ttlAuthUserCode); err != nil {
		log.Error("v4AuthHandler: create auth code: fail to write to cache").Str("auth_code", authCode).Err(err).Send()
		render.JSONInternalServerErr(w, rest.Error(ErrUnknown.Message, ErrUnknown.Code))
		return
	}

	go func() {
		if err := h.oauthAuthorizationRepo.CreateAuthorization(req.AppID, accessUser.UserID, req.Scope, req.RedirectURI); err != nil {
			log.Error("v4AuthHandler: create auth code: fail to write authorization log").
				Str("app_id", req.AppID).
				Str("user_id", accessUser.UserID).
				Err(err).
				Send()
		}
	}()

	resp := createAuthCodeResp{
		AuthCode:  authCode,
		ExpiresIn: int(ttlAuthUserCode.Seconds()),
	}
	render.JSONOk(w, resp)
}

// ExchangeAccessToken validates the auth code and returns an access token
func (h *Handler) ExchangeAccessToken(w http.ResponseWriter, r *http.Request) {
	var req exchangeAccessTokenReq
	urlValues, err := httpreq.ParseQuery(r)
	if err != nil {
		render.JSONBadRequest(w, rest.Error(ErrInvalidParameters.Message, ErrInvalidParameters.Code))
		return
	}
	if err := httpreq.Scan(&req, urlValues); err != nil {
		render.JSONBadRequest(w, rest.Error(ErrInvalidParameters.Message, ErrInvalidParameters.Code))
		return
	}
	if err := validator.Validate(req); err != nil {
		render.JSONBadRequest(w, rest.Error(ErrInvalidParameters.Message, ErrInvalidParameters.Code))
		return
	}

	app, ok := r.Context().Value(middleware.KeyAuthedApp).(*dbuser.AuthedApp)
	if !ok {
		render.JSONUnauthorized(w, rest.Error(ErrInvalidApp.Message, ErrInvalidApp.Code))
		return
	} else if app.SignKey.IsZero() {
		log.Error("v4AuthHandler: exchange access token: app sign key is empty").Str("app_id", app.AppID).Send()
		render.JSONInternalServerErr(w, rest.Error(ErrInvalidApp.Message, ErrInvalidApp.Code))
		return
	}

	var authData cacheuser.OAuthData
	cKey := key.OAuthAuthCode(req.AuthCode)
	if err := h.userCacheReader.Get(cKey, &authData); errors.Is(err, cache.ErrCacheMiss) {
		render.JSONBadRequest(w, rest.Error(ErrInvalidAuthCode.Message, ErrInvalidAuthCode.Code))
		return
	} else if err != nil {
		log.Error("v4 auth handler: exchange access token: fail to get auth code from cache").Str("auth_code", req.AuthCode).Err(err).Send()
		render.JSONInternalServerErr(w, rest.Error(ErrUnknown.Message, ErrUnknown.Code))
		return
	}

	if authData.AppID != app.AppID {
		render.JSONBadRequest(w, rest.Error(ErrInvalidApp.Message, ErrInvalidApp.Code))
		return
	}

	// Delete the auth code from cache (one-time use)
	defer func() {
		go func() {
			if err := h.userCacheWriter.Del(cKey); err != nil {
				log.Warn("v4AuthHandler: exchange access token: fail to delete auth code from cache").Str("auth_code", req.AuthCode).Err(err).Send()
			}
		}()
	}()

	// Generate JWT token
	now := h.clock.Now()
	expiresAt := now.Add(ttlAccessToken)
	accessTokenIssuer := auth.NewAccessTokenIssuer(app.SignKey.String)
	authorities := h.getAuthoritiesFromScope(authData.Scope)
	accessToken, err := accessTokenIssuer.GenerateForApp(authData.UserID, authorities, app.Name, now, expiresAt)
	if err != nil {
		log.Error("v4 auth handler: exchange access token: fail to generate accessToken").Str("app_id", app.AppID).Interface("auth_data", authData).Err(err).Send()
		render.JSONInternalServerErr(w, rest.Error(ErrUnknown.Message, ErrUnknown.Code))
		return
	}
	resp := rest.Ok()
	resp.Data = accessTokenResp{
		AccessToken: accessToken,
		ExpiresIn:   int(ttlAccessToken.Seconds()),
	}
	render.JSONOk(w, resp)
}

func (h *Handler) getAuthoritiesFromScope(scope string) []authority.Authority {
	var authorities []authority.Authority
	for _, s := range strings.Split(scope, " ") {
		if auths, ok := scopeAuthorityMap[s]; ok {
			authorities = append(authorities, auths...)
		}
	}
	return authorities
}
