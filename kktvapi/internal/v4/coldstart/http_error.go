package coldstart

import "github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/rest"

var (
	ErrRespInvalidParameter = &rest.Err{Code: "400.1", Message: "invalid parameter"}
	ErrRespNoIdentity       = &rest.Err{Code: "401.1", Message: "no any identity is given"}
	ErrRespNotAllowed       = &rest.Err{Code: "403.1", Message: "Forbidden to access because the use has completed cold-start process before"}
	ErrRespUnknown          = &rest.Err{Code: "500.0", Message: "Unknown error"}
)
