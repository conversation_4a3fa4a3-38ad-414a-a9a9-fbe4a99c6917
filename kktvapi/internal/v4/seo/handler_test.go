package seo

import (
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/kktverror"
	"github.com/go-zoo/bone"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
	"gopkg.in/guregu/null.v3"
)

type HandlerTestSuite struct {
	suite.Suite
	ctrl           *gomock.Controller
	app            *bone.Mux
	mockSeoService *MockService
}

func TestHandlerTestSuite(t *testing.T) {
	suite.Run(t, new(HandlerTestSuite))
}
func (suite *HandlerTestSuite) SetupTest() {
	suite.app = bone.New()
	suite.ctrl = gomock.NewController(suite.T())
	suite.mockSeoService = NewMockService(suite.ctrl)
	handler := &Handler{
		service: suite.mockSeoService,
	}
	suite.app.GetFunc("/v4/seo/titles/:titleId", handler.GetTitleMeta)
}
func (suite *HandlerTestSuite) TearDownTest() {
	defer suite.ctrl.Finish()
}
func (suite *HandlerTestSuite) TestGetTitleMeta() {
	var (
		titleID = "0001"
		meta    = &titleMeta{
			ID:        titleID,
			Name:      "測試用絕對達令",
			TitleType: "miniseries",
			Cover:     "https://images.kktv.com.tw/covers/d9/d92243f54afe77a56cd1cd6a5f6944b752cdef5b.xs.jpg",
			Series: []series{
				{
					ID:   "0200005701",
					Name: "第1季",
					Episodes: []episode{
						{
							ID:          "09000652010001",
							Name:        "第1集",
							SeriesTitle: "證人",
							Still:       "https://image.kktv.com.tw/stills/07/07250ad4e00f46f19998023ff3b0906afe2e31fb.xs.jpg",
							Duration:    7759.381333,
							PublishDate: **********,
							IsAvod:      true,
							Mezzanines: mezzanines{
								HLS: &manifest{
									URI: "https://fake.hls.com/1.m3u8",
								},
							},
						},
					},
				},
			},
			Summary:          "然後一起發大財吧！！",
			Country:          "Korea",
			Directors:        []string{"鄭正華"},
			Casts:            []string{"呂珍九", "方珉雅"},
			ContentProviders: []string{"tvb"},
			Ost: &ost{
				ArtistName: "Aimer",
				Image:      "https://i.kfs.io/album/global/*********,2v1/fit/500x500.jpg",
				Title:      "片頭曲：残響散歌",
				URL:        "https://www.kkbox.com/tw/tc/album/95sK.7bsEAKAh0F8fSxH009H-index.html?album=www.kkbox.com%2Ftw%2Ftc%2Falbum%2F95sK.7bsEAKAh0F8fSxH009H-index.html",
			},
			UserRating:         3.1415,
			RatingUserCount:    999,
			TotalEpisodeCounts: map[string]int{"**********": 40},
			PublishDate:        **********,
			WikiZh:             null.StringFrom("https://zh.wikipedia.org/wiki/KKBOX2017").Ptr(),
			ChildLock:          true,
			Copyright:          "KKCompany",
			ReleaseYear:        2019,
			Genres:             []string{"浪漫愛情", "科幻想像"},
			Writers:            []string{"岡田惠和"},
			TitleExtra: titleExtra{
				Series: []extraSeries{
					{
						Episodes: []extraEpisode{
							{
								ID:    "00000315tr0005",
								Name:  "預告5",
								Still: "https://image.kktv.com.tw/stills/07/00000315tr0005.xs.jpg",
							},
						},
					},
				},
			},
			IsContainingAvod: true,
			TitleAliases:     []string{"미치겠다, 너땜에!"},
			Themes:           []string{"浪漫愛情", "雙字幕"},
			Tags:             []string{"罕見疾病", "小說改編"},
		}
	)

	testcases := []struct {
		name  string
		given func()
		then  func(code int, body string)
	}{
		{
			name: "got meta WHEN title exists",
			given: func() {
				suite.mockSeoService.EXPECT().GetTitleMeta(titleID).Return(meta, nil).Times(1)
			},
			then: func(code int, body string) {
				suite.Equal(http.StatusOK, code)
				suite.assertBodyIsEqual(meta, body)
			},
		},
		{
			name: "got 404 WHEN title not found",
			given: func() {
				suite.mockSeoService.EXPECT().GetTitleMeta(titleID).Return(nil, kktverror.ErrResourceNotFound).Times(1)
			},
			then: func(code int, body string) {
				suite.Equal(http.StatusNotFound, code)
				suite.JSONEq(`{"error":{"code":"404.1","message":"Title not found"},"data":null}`, body)
			},
		},
	}
	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			// GIVEN
			tc.given()
			// WHEN
			req := httptest.NewRequest(http.MethodGet, "/v4/seo/titles/"+titleID, nil)
			rr := httptest.NewRecorder()
			suite.app.ServeHTTP(rr, req)
			// THEN
			tc.then(rr.Code, rr.Body.String())
		})
	}
}

func (suite *HandlerTestSuite) assertBodyIsEqual(meta *titleMeta, body string) bool {
	return suite.JSONEq(fmt.Sprintf(`{
  "error": null,
  "data": {
    "id": "%s",
    "name": "%s", "title_type": "%s",
    "cover": "%s",
    "series": [{
      "id": "0200005701",
      "name": "第1季",
      "episodes": [
        {
          "id": "09000652010001",
          "name": "第1集",
          "series_title": "證人",
          "still": "https://image.kktv.com.tw/stills/07/07250ad4e00f46f19998023ff3b0906afe2e31fb.xs.jpg",
          "duration": 7759.3813,
          "publish_date": **********,
		  "is_avod": true,
		  "mezzanines": {
			"hls": {"uri": "https://fake.hls.com/1.m3u8"}, "dash": null
		  }
        }
      ]
    }],
		"stills": null,
    "summary": "%s",
    "country": "%s",
    "directors": ["鄭正華"],
    "casts": ["呂珍九","方珉雅"],
    "content_providers": ["tvb"],
	"child_lock": true,
	"copyright": "KKCompany",
	"release_year": 2019,
	"genres": ["浪漫愛情","科幻想像"],
	"writers": ["岡田惠和"],
	"title_extra": {
		"series": [
			{
				"episodes": [
					{
					  "id": "00000315tr0005",
					  "name": "預告5",
					  "still": "https://image.kktv.com.tw/stills/07/00000315tr0005.xs.jpg"
					}
			  	]
			}
		]
	},
	"is_containing_avod": true,
	"title_aliases": ["미치겠다, 너땜에!"],
	"themes": ["浪漫愛情","雙字幕"],
	"tags": ["罕見疾病", "小說改編"],
    "ost": {
      "artist_name": "Aimer",
      "image": "https://i.kfs.io/album/global/*********,2v1/fit/500x500.jpg",
      "title": "片頭曲：残響散歌",
      "url": "https://www.kkbox.com/tw/tc/album/95sK.7bsEAKAh0F8fSxH009H-index.html?album=www.kkbox.com%%2Ftw%%2Ftc%%2Falbum%%2F95sK.7bsEAKAh0F8fSxH009H-index.html"
    },
    "user_rating": %f,
    "rating_user_count": %d,
    "total_episode_counts": {"**********": 40},
	"wiki_zh": "https://zh.wikipedia.org/wiki/KKBOX2017",
	"publish_date": %d, "is_listed": %v, "status": "%s"
  }
}`, meta.ID, meta.Name, meta.TitleType, meta.Cover, meta.Summary, meta.Country, meta.UserRating, meta.RatingUserCount, meta.PublishDate, meta.IsListed, meta.Status), body)
}
