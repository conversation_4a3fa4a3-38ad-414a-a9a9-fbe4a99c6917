package seo

import (
	"testing"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/meta"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/kktverror"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
)

type ServiceSuite struct {
	suite.Suite
	ctrl          *gomock.Controller
	mockTitleRepo *meta.MockTitleRepository
	mockCache     *cache.MockCacher
	mockLegacy    *MocklegacyHelper

	service Service
}

func TestServiceSuite(t *testing.T) {
	suite.Run(t, new(ServiceSuite))
}

func (suite *ServiceSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
	suite.mockTitleRepo = meta.NewMockTitleRepository(suite.ctrl)
	suite.mockCache = cache.NewMockCacher(suite.ctrl)
	suite.mockLegacy = NewMocklegacyHelper(suite.ctrl)

	suite.service = &service{
		legacy:   suite.mockLegacy,
		metaRepo: suite.mockTitleRepo,
		cache:    suite.mockCache,
	}
}

func (suite *ServiceSuite) TearDownTest() {
	defer suite.ctrl.Finish()
}

func (suite *ServiceSuite) TestGetTitleMeta() {
	var (
		titleID   = "00000429"
		existed   = suite.anExistedMetaTitle(titleID)
		extraList = []*dbmeta.ExtraRow{
			{
				ID:       "0000106tr001",
				TitleID:  "0000106",
				SeriesID: "0000106tr",
				Meta: dbmeta.ExtraRowMeta{
					EpisodeName: "預告1", Still: "https://fake.image.com/1.png",
				},
			},
			{
				ID:       "0000106tr002",
				TitleID:  "0000106",
				SeriesID: "0000106tr",
				Meta: dbmeta.ExtraRowMeta{
					EpisodeName: "預告2", Still: "https://fake.image.com/2.png",
				},
			},
			{
				ID:       "0000106ot001",
				TitleID:  "0000106",
				SeriesID: "0000106ot",
				Meta: dbmeta.ExtraRowMeta{
					EpisodeName: "花絮1", Still: "https://fake.image.com/3.png",
				},
			},
		}
	)

	expected := suite.anExpectedTitleMeta(existed.meta, extraList)

	testcases := []struct {
		name  string
		given func()
		then  func(actual *titleMeta, err error)
	}{
		{
			name: "should return meta WHEN found title without country",
			given: func() {
				foundMeta := *(existed.meta)
				foundMeta.Country = nil
				found := legacyTitleMeta{meta: &foundMeta}

				suite.mockLegacy.EXPECT().GetTitleByID(gomock.Any()).Return(&found, nil).Times(1)
				suite.mockTitleRepo.EXPECT().ListExtraByTitleID(titleID).Return(extraList, nil).Times(1)
			},
			then: func(actual *titleMeta, err error) {
				suite.Nil(err)
				suite.assertTitleMetaEqual(func() titleMeta {
					e := *expected
					e.Country = ""
					return e
				}(), *actual)
			},
		},
		{
			name: "should return meta WHEN found title",
			given: func() {
				suite.mockLegacy.EXPECT().GetTitleByID(gomock.Any()).Return(existed, nil).Times(1)
				suite.mockTitleRepo.EXPECT().ListExtraByTitleID(titleID).Return(extraList, nil).Times(1)
			},
			then: func(actual *titleMeta, err error) {
				suite.Nil(err)
				suite.assertTitleMetaEqual(*expected, *actual)
			},
		},
		{
			name: "should return NotFoundError when repo cannot find title",
			given: func() {
				suite.mockThatCannotFindTitle(titleID)
			},
			then: func(actual *titleMeta, err error) {
				suite.Error(err, kktverror.ErrResourceNotFound)
			},
		},
		{
			name: "got TitleMeta but is not Available, return ErrResourceNotFound",
			given: func() {
				suite.mockThatCannotFindTitle(titleID)
				found := &dbmeta.TitleMeta{}
				found.Available = false
				suite.mockLegacy.EXPECT().GetTitleByID(gomock.Any()).Return(&legacyTitleMeta{
					meta: found,
				}, nil).Times(1)
			},
			then: func(actual *titleMeta, err error) {
				suite.Error(err, kktverror.ErrResourceNotFound)
			},
		},
	}
	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			defer suite.ctrl.Finish()

			tc.given()
			data, err := suite.service.GetTitleMeta(titleID)
			tc.then(data, err)
		})
	}

}

func (suite *ServiceSuite) assertTitleMetaEqual(expect titleMeta, actual titleMeta) {
	suite.Equal(expect.Name, actual.Name)
	suite.Equal(expect.ID, actual.ID)
	suite.ElementsMatch(expect.TitleExtra.Series, actual.TitleExtra.Series)
	suite.Equal(expect.Series, actual.Series)
	suite.Equal(expect.Writers, actual.Writers)
	suite.Equal(expect.ContentProviders, actual.ContentProviders)
	suite.Equal(expect.Genres, actual.Genres)
	suite.Equal(expect.PublishDate, actual.PublishDate)
	suite.Equal(expect.TitleAliases, actual.TitleAliases)
	suite.Equal(expect.ChildLock, actual.ChildLock)
	suite.Equal(expect.Tags, actual.Tags)
	suite.Equal(expect.Status, actual.Status)
	suite.Equal(expect.IsListed, actual.IsListed)
	suite.Equal(expect.Country, actual.Country)
}

func (suite *ServiceSuite) anExpectedTitleMeta(title *dbmeta.TitleMeta, extraList []*dbmeta.ExtraRow) *titleMeta {
	eps := make([]episode, 0)
	ser := title.Series[0]
	for _, e := range ser.Episodes {
		ep := episode{
			ID:          e.ID,
			Name:        e.EpisodeName,
			SeriesTitle: e.SeriesTitle,
			Still:       e.Still,
			Duration:    e.Duration,
			PublishDate: e.PublishTime,
			IsAvod:      e.IsAvod,
			Mezzanines: mezzanines{
				Dash: &manifest{URI: e.Mezzanines.Dash.URI},
				HLS:  &manifest{URI: e.Mezzanines.Hls.URI},
			},
		}
		eps = append(eps, ep)
	}
	wikiZh := title.WikiZh
	expected := &titleMeta{
		ID:        title.ID,
		Name:      title.Title,
		Status:    "license_valid",
		TitleType: title.TitleType,
		Cover:     title.Cover,
		Series: []series{
			{ID: ser.ID, Name: ser.Title,
				Episodes: []episode{eps[0], eps[1]}}, // eps[2] is not valid
		},
		Summary:          title.Summary,
		Country:          title.Country.CollectionName,
		Directors:        mappingToName(title.Directors),
		Casts:            mappingToName(title.Casts),
		ContentProviders: mappingToName(title.ContentProviders),
		Ost: &ost{
			ArtistName: title.Ost.ArtistName,
			Image:      title.Ost.Image,
			Title:      title.Ost.Title,
			URL:        title.Ost.URL,
		},
		UserRating:         title.UserRating,
		RatingUserCount:    title.UserRatingCount,
		TotalEpisodeCounts: title.TotalEpisodeCounts,
		PublishDate:        ser.Episodes[1].PublishTime,
		WikiZh:             &wikiZh,
		TitleExtra: titleExtra{
			Series: []extraSeries{
				{
					Episodes: []extraEpisode{
						{
							Name:  extraList[0].Meta.EpisodeName,
							ID:    extraList[0].ID,
							Still: extraList[0].Meta.Still,
						},
						{
							Name:  extraList[1].Meta.EpisodeName,
							ID:    extraList[1].ID,
							Still: extraList[1].Meta.Still,
						},
					},
				},
				{
					Episodes: []extraEpisode{
						{
							Name:  extraList[2].Meta.EpisodeName,
							ID:    extraList[2].ID,
							Still: extraList[2].Meta.Still,
						},
					},
				},
			},
		},
		ChildLock:        title.ChildLock,
		Copyright:        title.Copyright,
		Genres:           mappingToName(title.Genres),
		Writers:          mappingToName(title.Writers),
		Themes:           mappingToName(title.Themes),
		Tags:             mappingToName(title.Tags),
		ReleaseYear:      title.ReleaseYear,
		IsContainingAvod: title.IsContainingAvod,
		TitleAliases:     title.TitleAliases,
		IsListed:         true,
	}
	return expected
}

func (suite *ServiceSuite) anExistedMetaTitle(titleID string) *legacyTitleMeta {
	type mezz = struct {
		Dash *dbmeta.Dash `json:"dash,omitempty"`
		Hls  *dbmeta.Hls  `json:"hls,omitempty"`
	}

	now := time.Now()
	mezzanine := mezz{Dash: &dbmeta.Dash{
		URI: "https://fake.mezzanine.org/1.mpd",
	}, Hls: &dbmeta.Hls{
		URI: "https://fake.mezzanine.org/1.m3u8",
	}}
	series1Eps := []*dbmeta.EpisodeMeta{
		{
			SeriesTitle: "ep1", Still: "https://fakeimage.org/ep1.jpg", EpisodeName: "紅酒夜", Title: "紅酒夜", EndYear: 2019, StartYear: 2019, Duration: 1000, PlayZone: true,
			Mezzanines: mezzanine,
			Available:  true, HasSubtitles: true, SourceSubtitles: []string{"https://fake.subtitle.com/1.txt"},
			LicenseStart: now.AddDate(0, 0, -10).Unix(),
			LicenseEnd:   now.AddDate(0, 0, 20).Unix(),
			PublishTime:  now.AddDate(0, 0, -1).Unix(),
			Pub:          now.AddDate(0, 0, -1).Unix(), UnPub: now.AddDate(0, 0, 30).Unix(),
			IsAvod:      true,
			IsValidated: true,
		}, {
			SeriesTitle: "ep2", Still: "https://fakeimage.org/ep2.jpg", EpisodeName: "早餐", Title: "早餐", EndYear: 2019, StartYear: 2019, Duration: 1000,
			Mezzanines: mezzanine,
			Available:  true, HasSubtitles: true, SourceSubtitles: []string{"https://fake.subtitle.com/2.txt"},
			LicenseStart: now.AddDate(0, 0, -10).Unix(),
			LicenseEnd:   now.AddDate(0, 0, 20).Unix(),
			PublishTime:  now.AddDate(0, 0, -1).Unix(),
			Pub:          now.AddDate(0, 0, -1).Unix(), UnPub: now.AddDate(0, 0, 30).Unix(),
			IsAvod:      false,
			IsValidated: true,
		}, {
			PublishTime: 0, SeriesTitle: "ep3", Still: "https://fakeimage.org/ep3.jpg",
			Mezzanines: mezzanine,
		}}

	return &legacyTitleMeta{
		meta: &dbmeta.TitleMeta{
			IsValidated: true,
			Available:   true,
			Status:      "license_valid",
			ID:          titleID,
			Series: []*dbmeta.Series{
				{
					Episodes: series1Eps,
				},
			},
			Title:            "生日快樂",
			Summary:          "happy birthday to you",
			ReleaseInfo:      "每周二不播",
			Directors:        []*dbmeta.CollectionItem{{CollectionName: "黑澤明"}},
			Casts:            []*dbmeta.CollectionItem{{CollectionName: "simon"}},
			ContentProviders: []*dbmeta.CollectionItem{{CollectionName: "tvb"}},
			Country:          &dbmeta.CollectionItem{CollectionName: "jap"},
			WikiZh:           "https://fakewiki.org/hello.html",
			UserRatingCount:  1000,
			UserRating:       3.5,
			Stills:           []string{"https://fakeimg.org/0106.jpg", "https://fakeimg.org/0429.jpg"},
			Cover:            "https://fakeimg.org/josie.jpg",
			TitleType:        "miniseries",
			Ost: &dbmeta.Ost{
				ArtistName: "abc",
				Image:      "http://fakeimg.org/josie_smile.jpg",
				Title:      "josie smile",
				URL:        "https://kkbox.com/josie_smile",
			},
			Tags:      []*dbmeta.CollectionItem{{CollectionName: "戀愛"}},
			Copyright: "KKCompany",
			Themes:    []*dbmeta.CollectionItem{{CollectionName: "約會"}},
		},
	}
}

func (suite *ServiceSuite) mockThatCannotFindTitle(titleID string) {
	suite.mockLegacy.EXPECT().GetTitleByID(titleID).Return(nil, nil)
}
