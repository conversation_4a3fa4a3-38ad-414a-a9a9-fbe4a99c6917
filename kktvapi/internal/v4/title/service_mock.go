// Code generated by MockGen. DO NOT EDIT.
// Source: kktvapi/internal/v4/title/service.go

// Package title is a generated GoMock package.
package title

import (
	reflect "reflect"

	dbuser "github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	gomock "github.com/golang/mock/gomock"
)

// MockService is a mock of Service interface.
type MockService struct {
	ctrl     *gomock.Controller
	recorder *MockServiceMockRecorder
}

// MockServiceMockRecorder is the mock recorder for MockService.
type MockServiceMockRecorder struct {
	mock *MockService
}

// NewMockService creates a new mock instance.
func NewMockService(ctrl *gomock.Controller) *MockService {
	mock := &MockService{ctrl: ctrl}
	mock.recorder = &MockServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockService) EXPECT() *MockServiceMockRecorder {
	return m.recorder
}

// GetExtra mocks base method.
func (m *MockService) GetExtra(titleID string) (*Extra, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExtra", titleID)
	ret0, _ := ret[0].(*Extra)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExtra indicates an expected call of GetExtra.
func (mr *MockServiceMockRecorder) GetExtra(titleID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExtra", reflect.TypeOf((*MockService)(nil).GetExtra), titleID)
}

// GetPageByID mocks base method.
func (m *MockService) GetPageByID(titleID string) (*DetailPage, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPageByID", titleID)
	ret0, _ := ret[0].(*DetailPage)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPageByID indicates an expected call of GetPageByID.
func (mr *MockServiceMockRecorder) GetPageByID(titleID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPageByID", reflect.TypeOf((*MockService)(nil).GetPageByID), titleID)
}

// GetPageByIDForUser mocks base method.
func (m *MockService) GetPageByIDForUser(titleID, userID string, membership dbuser.Membership) (*DetailPage, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPageByIDForUser", titleID, userID, membership)
	ret0, _ := ret[0].(*DetailPage)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPageByIDForUser indicates an expected call of GetPageByIDForUser.
func (mr *MockServiceMockRecorder) GetPageByIDForUser(titleID, userID, membership interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPageByIDForUser", reflect.TypeOf((*MockService)(nil).GetPageByIDForUser), titleID, userID, membership)
}

// ListRelated mocks base method.
func (m *MockService) ListRelated(titleID string) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListRelated", titleID)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListRelated indicates an expected call of ListRelated.
func (mr *MockServiceMockRecorder) ListRelated(titleID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListRelated", reflect.TypeOf((*MockService)(nil).ListRelated), titleID)
}

// buildUserAuthority mocks base method.
func (m *MockService) buildUserAuthority(isMember, canPlay, fullAccess, hasAdvancedPlayerFunction bool) userAuthority {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "buildUserAuthority", isMember, canPlay, fullAccess, hasAdvancedPlayerFunction)
	ret0, _ := ret[0].(userAuthority)
	return ret0
}

// buildUserAuthority indicates an expected call of buildUserAuthority.
func (mr *MockServiceMockRecorder) buildUserAuthority(isMember, canPlay, fullAccess, hasAdvancedPlayerFunction interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "buildUserAuthority", reflect.TypeOf((*MockService)(nil).buildUserAuthority), isMember, canPlay, fullAccess, hasAdvancedPlayerFunction)
}
