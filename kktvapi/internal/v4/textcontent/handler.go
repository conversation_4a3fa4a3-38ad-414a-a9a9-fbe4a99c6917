package textcontent

import (
	"errors"
	"net/http"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/meta"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/middleware"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/rest"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/user"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/v4/presenter"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/kktverror"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/permission"
	plog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/model/cachemeta"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	modelmw "github.com/KKTV/kktv-api-v3/pkg/model/middleware"
	"github.com/KKTV/kktv-api-v3/pkg/render"
)

type Handler struct {
	repo              meta.TextContentRepository
	userRepo          user.UserRepository
	permissionService permission.Service
}

func NewHandler() *Handler {
	return &Handler{
		repo:              meta.NewTextContentRepository(),
		userRepo:          user.NewUserRepository(),
		permissionService: container.PermissionService(),
	}
}

func (h *Handler) Get(w http.ResponseWriter, r *http.Request) {
	// remember this api will controll many different text contents, not only signup survey
	var statusCode, resp = http.StatusOK, rest.Ok()
	defer func() {
		render.JSON(w, statusCode, resp)
	}()

	access, ok := r.Context().Value(middleware.KeyAccessUser).(modelmw.AccessUser)
	if !ok {
		statusCode, resp = http.StatusUnauthorized, rest.Error(ErrRespInvalidToken.Message, ErrRespInvalidToken.Code)
		return
	}

	var (
		err error
		tc  *cachemeta.TextContent
	)

	if tc, err = h.repo.Get(); err != nil {
		statusCode, resp = http.StatusInternalServerError, rest.Error(ErrRespUnknown.Message, ErrRespUnknown.Code)
		plog.Error("v4textcontentHandler: Get: failed to Get").
			Str("user_id", access.UserID).
			Err(err).
			Send()
		return
	}

	var user *dbuser.User
	tcGetter := NewGetter(tc)
	tcResp := new(presenter.TextContent)

	// We need to fetch the latest membership from the database instead of using the JWT token.
	// This is necessary because:
	// 1. The user's membership might have been upgraded.
	// 2. The user might be accessing from multiple devices with different token states.
	// Fetching from the database ensures we always have the most up-to-date membership information.
	if access.IsMember() {
		if user, err = h.userRepo.GetActiveByID(access.UserID); err != nil {
			statusCode, resp = http.StatusInternalServerError, rest.Error(ErrRespUnknown.Message, ErrRespUnknown.Code)
			plog.Error("v4textcontentHandler: Get: failed to GetActiveByID").Str("user_id", access.UserID).Err(err).Send()
			return
		} else if user == nil {
			statusCode, resp = http.StatusNotFound, rest.Error(ErrRespUserNotFound.Message, ErrRespUserNotFound.Code)
			return
		}
		// if permission not granted, return 200. else return tcResp
		permissionReq := permission.RequestSignupSurveyFormDisplay(user.Membership)
		if err = h.permissionService.Grant(permissionReq); err != nil {
			if errors.Is(err, kktverror.ErrResourceAccessDenied) {
				tcResp.SignupSurvey = nil
			} else {
				statusCode, resp = http.StatusInternalServerError, rest.Error(ErrRespUnknown.Message, ErrRespUnknown.Code)
				return
			}
		} else {
			signupSurvey := tcGetter.GetSignupSurvey(user.Membership)
			tcResp.SignupSurvey = signupSurvey
		}
		resp.Data = tcResp
	}
}
