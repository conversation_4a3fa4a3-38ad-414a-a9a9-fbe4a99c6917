package user

import (
	"context"
	"errors"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp/kkbilling"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/auth"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/onetimepassword"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/refreshtoken"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/token"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/user"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/v4/presenter"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/kktverror"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/permission"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/clock"
	"github.com/KKTV/kktv-api-v3/pkg/database"
	"github.com/KKTV/kktv-api-v3/pkg/datetimer"
	"github.com/KKTV/kktv-api-v3/pkg/encrypt"
	"github.com/KKTV/kktv-api-v3/pkg/key"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/duke-git/lancet/v2/datetime"
	"github.com/golang/mock/gomock"
	"github.com/mediocregopher/radix.v2/redis"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"gopkg.in/guregu/null.v3"
)

type ServiceTestSuite struct {
	suite.Suite
	ctrl   *gomock.Controller
	assert *require.Assertions

	srv                   UserService
	mockRepo              *user.MockUserRepository
	mockOTPRepo           *onetimepassword.MockOTPRepository
	mockBackupRepo        *user.MockBackupRepository
	mockTokenRepo         *token.MockRepository
	mockRefreshTokenRepo  *refreshtoken.MockRepository
	mockPaymentInfoRepo   *user.MockPaymentInfoRepository
	mockPrimeMemberRepo   *user.MockPrimeMemberRepository
	mockUserCacheWriter   *cache.MockCacher
	mockTxBeginner        *database.MockTxBeginner
	mockTx                *database.MockTx
	mockClock             *clock.MockClock
	mockLegacyHelper      *MocklegacyHelper
	mockAccessToken       *auth.MockAccessToken
	mockPermissionService *permission.MockService
}

func TestServiceTestSuite(t *testing.T) {
	suite.Run(t, new(ServiceTestSuite))
}

func (suite *ServiceTestSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
	suite.assert = suite.Require()

	suite.mockRepo = user.NewMockUserRepository(suite.ctrl)
	suite.mockOTPRepo = onetimepassword.NewMockOTPRepository(suite.ctrl)
	suite.mockBackupRepo = user.NewMockBackupRepository(suite.ctrl)
	suite.mockTokenRepo = token.NewMockRepository(suite.ctrl)
	suite.mockRefreshTokenRepo = refreshtoken.NewMockRepository(suite.ctrl)
	suite.mockPaymentInfoRepo = user.NewMockPaymentInfoRepository(suite.ctrl)
	suite.mockPrimeMemberRepo = user.NewMockPrimeMemberRepository(suite.ctrl)
	suite.mockUserCacheWriter = cache.NewMockCacher(suite.ctrl)
	suite.mockTxBeginner = database.NewMockTxBeginner(suite.ctrl)
	suite.mockTx = database.NewMockTx(suite.ctrl)
	suite.mockClock = clock.NewMockClock(suite.ctrl)
	suite.mockLegacyHelper = NewMocklegacyHelper(suite.ctrl)
	suite.mockAccessToken = auth.NewMockAccessToken(suite.ctrl)
	suite.mockPermissionService = permission.NewMockService(suite.ctrl)

	suite.srv = &userService{
		repo:             suite.mockRepo,
		otpRepo:          suite.mockOTPRepo,
		backupRepo:       suite.mockBackupRepo,
		tokenRepo:        suite.mockTokenRepo,
		refreshTokenRepo: suite.mockRefreshTokenRepo,
		paymentInfoRepo:  suite.mockPaymentInfoRepo,
		primeMemberRepo:  suite.mockPrimeMemberRepo,
		userCacheWriter:  suite.mockUserCacheWriter,
		txBeginner:       suite.mockTxBeginner,
		clock:            suite.mockClock,
		accessToken:      suite.mockAccessToken,
		legacyHelper:     suite.mockLegacyHelper,
		permissionSrv:    suite.mockPermissionService,
	}
}

func (suite *ServiceTestSuite) TearDownTest() {
	defer suite.ctrl.Finish()
}

func (suite *ServiceTestSuite) TestUpdatePassword() {
	var (
		userID = "josie"
	)
	testcases := []struct {
		name      string
		newSecret string
		oldSecret string
		given     func()
		assert    func(err error)
	}{
		{
			name:      "Update password successfully WHEN user exists",
			newSecret: "seCre7", oldSecret: "",
			given: func() {
				suite.mockRepo.EXPECT().GetActiveByID(userID).Return(&dbuser.User{
					ID: userID,
				}, nil)
				suite.mockRepo.EXPECT().UpdateByFields(userID, gomock.Any()).
					DoAndReturn(func(uid string, fields map[dbuser.UsersField]interface{}) (bool, error) {
						hashPwd := fields[dbuser.UsersFieldPassword].(string)
						suite.True(encrypt.ComparePassword(hashPwd, "seCre7"))
						return true, nil
					})
			},
			assert: func(err error) {
				suite.NoError(err)
			},
		},
		{
			name:      "return ResourceNotFound error WHEN user not exists",
			newSecret: "seCre7", oldSecret: "",
			given: func() {
				suite.mockRepo.EXPECT().GetActiveByID(userID).Return(nil, nil)
			},
			assert: func(err error) {
				suite.ErrorIs(err, kktverror.ErrResourceNotFound)
			},
		},
		//TODO check the old secret if password in db is not empty
	}

	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			tc.given()
			err := suite.srv.UpdatePassword(context.Background(), userID, tc.newSecret, tc.oldSecret)
			tc.assert(err)
		})
	}
}

func (suite *ServiceTestSuite) TestUpdateAccount() {
	const (
		userID = "test-userid"
		email  = "<EMAIL>"
		phone  = "+886**********"
	)

	lowerCaseEmail := strings.ToLower(email)
	formattedPhone := formatTaiwanPhoneNumber(phone)
	oldUser := &dbuser.User{
		ID: userID,
	}

	type testCase struct {
		name    string
		account string
		given   func()
		assert  func(newUser *dbuser.User, err error)
	}

	now := time.Now()
	suite.mockClock.EXPECT().Now().Return(now).AnyTimes()
	testCases := []testCase{
		{
			name:    "fail to delete account otp from redis THEN return error",
			account: email,
			given: func() {
				fields := map[dbuser.UsersField]interface{}{
					dbuser.UsersFieldEmail:           lowerCaseEmail,
					dbuser.UsersFieldEmailVerifiedAt: now,
				}

				suite.mockTxBeginner.EXPECT().Begin().Return(suite.mockTx, nil)
				suite.mockRepo.EXPECT().WithTx(suite.mockTx).Return(suite.mockRepo)
				suite.mockRepo.EXPECT().UpdateByFields(userID, fields).Return(true, nil)
				suite.mockOTPRepo.EXPECT().DeleteByUserID(userID).Return(redis.ErrRespNil)
				suite.mockTx.EXPECT().Rollback().Return(nil)
			},
			assert: func(newUser *dbuser.User, err error) {
				suite.Nil(newUser)
				suite.ErrorIs(err, redis.ErrRespNil)
			},
		},
		{
			name:    "update account successfully WHEN input account is not used by other user",
			account: phone,
			given: func() {
				fields := map[dbuser.UsersField]interface{}{
					dbuser.UsersFieldPhone:           formattedPhone,
					dbuser.UsersFieldPhoneVerifiedAt: now,
				}

				suite.mockTxBeginner.EXPECT().Begin().Return(suite.mockTx, nil)
				suite.mockRepo.EXPECT().WithTx(suite.mockTx).Return(suite.mockRepo)
				suite.mockRepo.EXPECT().UpdateByFields(userID, fields).Return(true, nil)
				suite.mockOTPRepo.EXPECT().DeleteByUserID(userID).Return(nil)
				suite.mockTx.EXPECT().Commit().Return(nil)
			},
			assert: func(newUser *dbuser.User, err error) {
				suite.NoError(err)
				suite.Equal(formattedPhone, newUser.Phone.String)
				suite.Equal(now.UTC(), newUser.PhoneVerifiedAt.Time)
			},
		},
		{
			name:    "update account successfully WHEN input account is used by himself",
			account: phone,
			given: func() {
				fields := map[dbuser.UsersField]interface{}{
					dbuser.UsersFieldPhone:           formattedPhone,
					dbuser.UsersFieldPhoneVerifiedAt: now,
				}

				suite.mockTxBeginner.EXPECT().Begin().Return(suite.mockTx, nil)
				suite.mockRepo.EXPECT().WithTx(suite.mockTx).Return(suite.mockRepo)
				suite.mockRepo.EXPECT().UpdateByFields(userID, fields).Return(true, nil)
				suite.mockOTPRepo.EXPECT().DeleteByUserID(userID).Return(nil)
				suite.mockTx.EXPECT().Commit().Return(nil)
			},
			assert: func(newUser *dbuser.User, err error) {
				suite.NoError(err)
				suite.Equal(formattedPhone, newUser.Phone.String)
				suite.Equal(now.UTC(), newUser.PhoneVerifiedAt.Time)
			},
		},
	}

	for _, tc := range testCases {
		suite.Run(tc.name, func() {
			tc.given()
			tc.assert(suite.srv.UpdateAccount(oldUser, tc.account))
		})
	}

}

func (suite *ServiceTestSuite) TestCreate() {
	var (
		email      = "<EMAIL>"
		phone      = "+************"
		pwd        = "this is a secret"
		primeEmail = "<EMAIL>"

		now = time.Date(2020, 4, 29, 0, 0, 0, 0, datetimer.LocationTaipei)
	)

	var (
		mockRepoCreateUserSucc = func(account, platform, role, userType, source string) *gomock.Call {
			return suite.mockRepo.EXPECT().Create(gomock.AssignableToTypeOf(&dbuser.User{})).
				DoAndReturn(func(u *dbuser.User) (*dbuser.User, error) {
					u.ID = "test-userid"
					u.CreatedAt = null.TimeFrom(now)
					u.UpdatedAt = null.TimeFrom(now)

					if strings.Contains(account, "@") {
						suite.Equal(account, u.Email.String)
						suite.False(u.Phone.Valid)
						suite.Equal(now, u.EmailVerifiedAt.Time)
						suite.False(u.PhoneVerifiedAt.Valid)
					} else {
						suite.Equal(account, u.Phone.String)
						suite.False(u.Email.Valid)
						suite.Equal(now, u.PhoneVerifiedAt.Time)
						suite.False(u.EmailVerifiedAt.Valid)
					}

					tomorrow := datetime.BeginOfDay(now.In(datetimer.LocationTaipei)).AddDate(0, 0, 1)
					if role == dbuser.RoleFreeTrial.String() && userType == dbuser.TypeGeneral.String() {
						membership := dbuser.MembershipFreeTrial
						suite.Equal(dbuser.RoleFreeTrial.String(), u.Role)
						suite.Equal(dbuser.TypeGeneral.String(), u.Type)
						expiredAt := tomorrow.AddDate(0, 0, FreeTrialDays).UTC()
						suite.Equal(expiredAt, u.ExpiredAt.Time)
						suite.Equal(membership, u.Membership)
					} else {
						suite.Equal(dbuser.RolePremium.String(), u.Role)
						suite.Equal(dbuser.TypePrime.String(), u.Type)
						expiredAt := time.Date(2023, 12, 31, 23, 59, 59, 0, time.UTC).UTC()
						suite.Equal(expiredAt, u.ExpiredAt.Time.UTC())
					}
					suite.True(encrypt.ComparePassword(u.Password.String, pwd))
					suite.EqualValues(dbuser.UserOriginProviderKKTV, u.OriginProvider.String)
					suite.Equal(source, u.CreatedBy.String)

					return u, nil
				})
		}

		lockSucc = func(account string) {
			lockKey := key.GetLockKey("create_user:" + account)
			suite.mockUserCacheWriter.EXPECT().SetNX(lockKey, 1, 10*time.Second).Return(int64(1), nil)
			suite.mockUserCacheWriter.EXPECT().Del(lockKey).Return(nil)
		}
		lockFail = func(account string) {
			lockKey := key.GetLockKey("create_user:" + account)
			suite.mockUserCacheWriter.EXPECT().SetNX(lockKey, 1, 10*time.Second).Return(int64(0), nil)
		}
	)

	type testCase struct {
		name, account, source string

		given  func()
		assert func(u *dbuser.User, err error)
	}

	testCases := []testCase{
		{
			name:    "given phone doesn't exist THEN create user successfully",
			account: phone,
			source:  "Android",
			given: func() {
				lockSucc(phone)
				suite.mockRepo.EXPECT().GetUsersByPhone(phone).Return(nil, nil)
				mockRepoCreateUserSucc(phone, "android",
					dbuser.RoleFreeTrial.String(),
					dbuser.TypeGeneral.String(),
					"android")
				suite.mockBackupRepo.EXPECT().GetUserFromKKIDBackup(phone).Return(nil, nil)
			},
			assert: func(u *dbuser.User, err error) {
				suite.NoError(err)
				suite.NotNil(u)
				suite.Equal("test-userid", u.ID)
			},
		},
		{
			name:    "locked by others THEN return error",
			account: phone,
			source:  "Android",
			given: func() {
				lockFail(phone)
			},
			assert: func(u *dbuser.User, err error) {
				suite.ErrorIs(err, kktverror.ErrResourceConflict)
			},
		},
		{
			name:    "given email exist THEN create user successfully",
			account: email,
			source:  "Web",
			given: func() {
				lockSucc(email)
				suite.mockRepo.EXPECT().GetUsersByEmail(email).Return(nil, nil)
				mockRepoCreateUserSucc(email, "web",
					dbuser.RoleFreeTrial.String(),
					dbuser.TypeGeneral.String(),
					"web")
				suite.mockBackupRepo.EXPECT().GetUserFromKKIDBackup(email).Return(nil, nil)
			},
			assert: func(u *dbuser.User, err error) {
				suite.NoError(err)
				suite.NotNil(u)
				suite.Equal("test-userid", u.ID)
				suite.Equal(dbuser.MembershipFreeTrial, u.Membership)
			},
		},
		{
			name:    "given email is occupied THEN return error",
			account: email,
			source:  "",
			given: func() {
				lockSucc(email)
				suite.mockRepo.EXPECT().GetUsersByEmail(email).Return([]*dbuser.User{
					{ID: "test-user-id"},
				}, nil)
				suite.mockBackupRepo.EXPECT().GetUserFromKKIDBackup(email).Return(nil, nil)
			},
			assert: func(u *dbuser.User, err error) {
				suite.ErrorIs(err, ErrAccountExists)
				suite.Nil(u)
			},
		},
		{
			name:    "insert fail THEN return error",
			account: email,
			source:  "Web",
			given: func() {
				lockSucc(email)
				suite.mockRepo.EXPECT().GetUsersByEmail(email).Return(nil, nil)
				suite.mockRepo.EXPECT().Create(gomock.AssignableToTypeOf(&dbuser.User{})).Return(nil, errors.New("duplicated key"))
				suite.mockBackupRepo.EXPECT().GetUserFromKKIDBackup(email).Return(nil, nil)
			},
			assert: func(u *dbuser.User, err error) {
				suite.Error(err)
				suite.Nil(u)
			},
		},
		{
			name:    "insert prime user success",
			account: primeEmail,
			source:  "Web",
			given: func() {
				lockSucc(primeEmail)
				suite.mockRepo.EXPECT().GetUsersByEmail(primeEmail).Return(nil, nil)
				suite.mockBackupRepo.EXPECT().GetUserFromKKIDBackup(primeEmail).Return(&dbuser.KkidBackupUser{
					KKidSub: "test-kkid-sub",
					MsnoSub: "test-msno-sub",
					Phone:   "",
					Email:   primeEmail,
					IsPrime: true,
				}, nil).Times(2)
				suite.mockLegacyHelper.EXPECT().getKKBOXBillingInfo("test-msno-sub").Return(&legacyKKBOXBilling{
					kkbilling.KKBilling{
						Data: kkbilling.BillingData{
							ServiceInfo: []kkbilling.ServiceItem{
								{
									Service: "prime",
									State:   "premium",
									DueDate: **********,
								},
							},
						},
					},
				}, nil).AnyTimes()
				mockRepoCreateUserSucc(primeEmail, "android",
					dbuser.RolePremium.String(),
					dbuser.TypePrime.String(),
					"web")
			},
			assert: func(u *dbuser.User, err error) {
				suite.NoError(err)
				suite.NotNil(u)
				suite.Equal("test-userid", u.ID)
			},
		},
		{
			name:    "Given source is pxpayplus, and equal to user's created_by",
			account: email,
			source:  "pxpayplus",
			given: func() {
				lockSucc(email)
				suite.mockRepo.EXPECT().GetUsersByEmail(email).Return(nil, nil)
				suite.mockBackupRepo.EXPECT().GetUserFromKKIDBackup(email).Return(nil, nil).Times(2)
				mockRepoCreateUserSucc(email, "Web",
					dbuser.RoleFreeTrial.String(),
					dbuser.TypeGeneral.String(),
					"pxpayplus")
			},
			assert: func(u *dbuser.User, err error) {
				suite.NoError(err)
				suite.NotNil(u)
				suite.Equal("pxpayplus", u.CreatedBy.String)
			},
		},
	}
	suite.mockClock.EXPECT().Now().Return(now).AnyTimes()
	for _, tc := range testCases {
		suite.Run(tc.name, func() {
			tc.given()
			newUser, err := suite.srv.Create(tc.account, pwd, tc.source)
			tc.assert(newUser, err)
		})
		suite.TearDownTest()
	}
}

func (suite *ServiceTestSuite) TestGenerateAccessToken() {
	const (
		ip          = "127.0.0.1"
		userAgent   = "Apache-HttpClient/4.5.13 (Java/17.0.3)"
		accessToken = "test-access-token"
		userID      = "test-user-id"
	)

	now := time.Now()
	token := &dbuser.Token{
		ID:        "test-token-id",
		UserID:    userID,
		Token:     accessToken,
		SourceIP:  ip,
		UserAgent: userAgent,
		ExpiredAt: null.TimeFrom(now.AddDate(0, 0, TokenExpireDays)),
	}
	refreshToken := &dbuser.RefreshToken{
		TokenID:      token.ID,
		RefreshToken: "test-refresh-token",
		ExpiredAt:    null.TimeFrom(now.AddDate(0, 0, RefreshTokenExpireDays)),
	}

	var testcases = []struct {
		name   string
		user   *dbuser.User
		given  func()
		assert func(actual *presenter.AccessToken, err error)
	}{
		{
			name: "generate auth token WITHOUT error WHEN user was prime",
			user: &dbuser.User{
				ID:   userID,
				Role: dbuser.RoleExpired.String(),
				Type: dbuser.TypeGeneral.String(),
				MediaSource: map[string]any{
					"kkbox": map[string]any{
						"id":  123,
						"sub": "test-sub",
					},
				},
				Membership: dbuser.MembershipExpired,
			},
			given: func() {
				suite.mockClock.EXPECT().Now().Return(now)
				suite.mockPrimeMemberRepo.EXPECT().GetByMsnoSub("test-sub").Return(&dbuser.KKBOXBillingPrimeMember{PkgName: "test-pkg"}, nil)

				authInfo := &auth.UserInfo{
					ID:             userID,
					Role:           dbuser.RoleExpired,
					Type:           dbuser.TypeGeneral,
					HasBoughtPrime: true,
					Membership:     dbuser.MembershipExpired,
				}
				suite.mockAccessToken.EXPECT().Generate(authInfo, now, token.ExpiredAt.Time).Return(accessToken, nil)

				suite.mockTxBeginner.EXPECT().Begin().Return(suite.mockTx, nil)
				suite.mockTokenRepo.EXPECT().WithTx(suite.mockTx).Return(suite.mockTokenRepo)
				suite.mockTokenRepo.EXPECT().Create(gomock.AssignableToTypeOf(&dbuser.Token{})).Return(token, nil)
				suite.mockRefreshTokenRepo.EXPECT().WithTx(suite.mockTx).Return(suite.mockRefreshTokenRepo)
				suite.mockRefreshTokenRepo.EXPECT().Create(gomock.AssignableToTypeOf(&dbuser.RefreshToken{})).Return(refreshToken, nil)
				suite.mockTx.EXPECT().Commit().Return(nil)

			},
			assert: func(actual *presenter.AccessToken, err error) {
				suite.NoError(err)
				suite.assert.Equal(&presenter.AccessToken{
					Token:                 token.Token,
					ExpiredAt:             token.ExpiredAt.Time.Unix(),
					RefreshToken:          refreshToken.RefreshToken,
					RefreshTokenExpiredAt: refreshToken.ExpiredAt.Time.Unix(),
				}, actual)
			},
		},
		{
			name: "generate auth token WITHOUT error WHEN user was not prime",
			user: &dbuser.User{
				ID:   userID,
				Role: dbuser.RoleFreeTrial.String(),
				Type: dbuser.TypeGeneral.String(),
				MediaSource: map[string]any{
					"kkbox": map[string]any{
						"id": 123,
					},
				},
				Membership: dbuser.MembershipFreeTrial,
			},
			given: func() {
				suite.mockClock.EXPECT().Now().Return(now)

				userInfo := &auth.UserInfo{
					ID:             userID,
					Role:           dbuser.RoleFreeTrial,
					Type:           dbuser.TypeGeneral,
					HasBoughtPrime: false,
					Membership:     dbuser.MembershipFreeTrial,
				}
				suite.mockAccessToken.EXPECT().Generate(userInfo, now, token.ExpiredAt.Time).Return(accessToken, nil)

				suite.mockTxBeginner.EXPECT().Begin().Return(suite.mockTx, nil)
				suite.mockTokenRepo.EXPECT().WithTx(suite.mockTx).Return(suite.mockTokenRepo)
				suite.mockTokenRepo.EXPECT().Create(gomock.AssignableToTypeOf(&dbuser.Token{})).Return(token, nil)
				suite.mockRefreshTokenRepo.EXPECT().WithTx(suite.mockTx).Return(suite.mockRefreshTokenRepo)
				suite.mockRefreshTokenRepo.EXPECT().Create(gomock.AssignableToTypeOf(&dbuser.RefreshToken{})).Return(refreshToken, nil)
				suite.mockTx.EXPECT().Commit().Return(nil)
			},
			assert: func(actual *presenter.AccessToken, err error) {
				suite.NoError(err)
				suite.assert.Equal(&presenter.AccessToken{
					Token:                 token.Token,
					ExpiredAt:             token.ExpiredAt.Time.Unix(),
					RefreshToken:          refreshToken.RefreshToken,
					RefreshTokenExpiredAt: refreshToken.ExpiredAt.Time.Unix(),
				}, actual)
			},
		},
	}

	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			tc.given()

			req := httptest.NewRequest("POST", "/v4/user/login", nil)
			req.Header.Set("X-Forwarded-For", ip)
			req.Header.Set("User-Agent", userAgent)
			tc.assert(suite.srv.GenerateAccessToken(tc.user, req))
		})
	}
}

func (suite *ServiceTestSuite) TestResetPassword() {
	const (
		email    = "<EMAIL>"
		password = "123456"
		userID   = "test-user-id"
	)

	type testCase struct {
		name     string
		account  string
		password string
		given    func()
		assert   func(newUser *dbuser.User, err error)
	}

	now := time.Now()
	oldUser := &dbuser.User{
		ID: userID,
	}

	testCases := []testCase{
		{
			name:     "reset password successfully",
			account:  email,
			password: password,
			given: func() {
				suite.mockClock.EXPECT().Now().Return(now).AnyTimes()
				suite.mockRepo.EXPECT().UpdateByFields(userID, gomock.Any()).Return(true, nil)
			},
			assert: func(newUser *dbuser.User, err error) {
				suite.NoError(err)
				suite.Equal(now.UTC(), newUser.EmailVerifiedAt.Time)
			},
		},
	}
	for _, tc := range testCases {
		suite.Run(tc.name, func() {
			tc.given()
			tc.assert(suite.srv.ResetPassword(oldUser, tc.account, tc.password))
		})
	}
}

func (suite *ServiceTestSuite) TestGetUsersByAccount() {
	const (
		email = "<EMAIL>"
		phone = "+************"
	)

	testcases := []struct {
		name    string
		account string
		given   func()
		assert  func(users []*dbuser.User, err error)
	}{
		{
			name:    "given phone THEN got users with duplicate name",
			account: phone,
			given: func() {
				suite.mockRepo.EXPECT().GetUsersByPhone(phone).Return([]*dbuser.User{
					{
						ID:    "test-user-id-1",
						Phone: null.StringFrom(phone),
					},
					{
						ID:    "test-user-id-2",
						Phone: null.StringFrom(phone),
					},
				}, nil)
			},
			assert: func(users []*dbuser.User, err error) {
				suite.NoError(err)
				for _, user := range users {
					suite.assert.Equal(user.Phone.String, phone)
				}
			},
		},
		{
			name:    "given email THEN got users with duplicate email",
			account: email,
			given: func() {
				suite.mockRepo.EXPECT().GetUsersByEmail(email).Return([]*dbuser.User{
					{
						ID:    "test-user-id-3",
						Email: null.StringFrom(email),
					},
					{
						ID:    "test-user-id-4",
						Email: null.StringFrom(email),
					},
				}, nil)
			},
			assert: func(users []*dbuser.User, err error) {
				suite.NoError(err)
				for _, user := range users {
					suite.assert.Equal(user.Email.String, email)
				}
			},
		},
	}

	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			tc.given()
			tc.assert(suite.srv.GetUsersByAccount(tc.account))
		})
	}
}

func (suite *ServiceTestSuite) TestValidateAccountChangeable() {
	const userID = "test-user-id"
	testcases := []struct {
		name    string
		user    *dbuser.User
		account string
		given   func()
		assert  func(changeable bool, err error)
	}{
		{
			name: "given user whose payment type is tstar THEN get value of changeable is false",
			user: &dbuser.User{
				ID:         userID,
				Phone:      null.StringFrom("**********"),
				Membership: dbuser.MembershipPremiumOnly,
			},
			account: "**********",
			given: func() {
				suite.mockPermissionService.EXPECT().IsPaidMember(dbuser.MembershipPremiumOnly).Return(true)
				suite.mockPaymentInfoRepo.EXPECT().GetPaymentInfoByUserID(userID).Return(&dbuser.PaymentInfo{
					UserID:      userID,
					PaymentType: null.StringFrom("tstar"),
				}, nil)
			},
			assert: func(changeable bool, err error) {
				suite.assert.Equal(false, changeable)
				suite.NoError(err)
			},
		},
		{
			name: "given user whose payment type is aptg THEN get value of changeable is false",
			user: &dbuser.User{
				ID:         userID,
				Phone:      null.StringFrom("**********"),
				Membership: dbuser.MembershipPremiumOnly,
			},
			account: "+************",
			given: func() {
				suite.mockPermissionService.EXPECT().IsPaidMember(dbuser.MembershipPremiumOnly).Return(true)
				suite.mockPaymentInfoRepo.EXPECT().GetPaymentInfoByUserID(userID).Return(&dbuser.PaymentInfo{
					UserID:      userID,
					PaymentType: null.StringFrom("aptg"),
				}, nil)
			},
			assert: func(changeable bool, err error) {
				suite.assert.Equal(false, changeable)
				suite.NoError(err)
			},
		},
		{
			name: "given user whose payment type is telecom THEN get value of changeable is false",
			user: &dbuser.User{
				ID:         userID,
				Phone:      null.StringFrom("**********"),
				Membership: dbuser.MembershipPremiumOnly,
			},
			account: "+************",
			given: func() {
				suite.mockPermissionService.EXPECT().IsPaidMember(dbuser.MembershipPremiumOnly).Return(true)
				suite.mockPaymentInfoRepo.EXPECT().GetPaymentInfoByUserID(userID).Return(&dbuser.PaymentInfo{
					UserID:      userID,
					PaymentType: null.StringFrom("telecom"),
				}, nil)
			},
			assert: func(changeable bool, err error) {
				suite.assert.Equal(false, changeable)
				suite.NoError(err)
			},
		},
		{
			name: "given user using original phone THEN get value of changeable is true",
			user: &dbuser.User{
				ID:         userID,
				Phone:      null.StringFrom("**********"),
				Membership: dbuser.MembershipPremiumOnly,
			},
			account: "**********",
			given:   func() {},
			assert: func(changeable bool, err error) {
				suite.assert.Equal(true, changeable)
				suite.NoError(err)
			},
		},
		{
			name: "given user using email THEN get value of changeable is true",
			user: &dbuser.User{
				ID:         userID,
				Phone:      null.StringFrom("<EMAIL>"),
				Membership: dbuser.MembershipPremiumOnly,
			},
			account: "<EMAIL>",
			given:   func() {},
			assert: func(changeable bool, err error) {
				suite.assert.Equal(true, changeable)
				suite.NoError(err)
			},
		},
		{
			name: "given expired user THEN get value of changeable is true",
			user: &dbuser.User{
				ID:         userID,
				Membership: dbuser.MembershipExpired,
			},
			given: func() {},
			assert: func(changeable bool, err error) {
				suite.assert.Equal(true, changeable)
				suite.NoError(err)
			},
		},
	}

	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			tc.given()
			tc.assert(suite.srv.ValidateAccountChangeable(tc.user, tc.account))
		})
	}
}
