// Code generated by MockGen. DO NOT EDIT.
// Source: amplitude_service.go

// Package user is a generated GoMock package.
package user

import (
	http "net/http"
	reflect "reflect"

	dbuser "github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	gomock "github.com/golang/mock/gomock"
)

// MockAmplitudeService is a mock of AmplitudeService interface.
type MockAmplitudeService struct {
	ctrl     *gomock.Controller
	recorder *MockAmplitudeServiceMockRecorder
}

// MockAmplitudeServiceMockRecorder is the mock recorder for MockAmplitudeService.
type MockAmplitudeServiceMockRecorder struct {
	mock *MockAmplitudeService
}

// NewMockAmplitudeService creates a new mock instance.
func NewMockAmplitudeService(ctrl *gomock.Controller) *MockAmplitudeService {
	mock := &MockAmplitudeService{ctrl: ctrl}
	mock.recorder = &MockAmplitudeServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAmplitudeService) EXPECT() *MockAmplitudeServiceMockRecorder {
	return m.recorder
}

// SendAccountLoggedInEvent mocks base method.
func (m *MockAmplitudeService) SendAccountLoggedInEvent(user *dbuser.User, r *http.Request) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendAccountLoggedInEvent", user, r)
}

// SendAccountLoggedInEvent indicates an expected call of SendAccountLoggedInEvent.
func (mr *MockAmplitudeServiceMockRecorder) SendAccountLoggedInEvent(user, r interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendAccountLoggedInEvent", reflect.TypeOf((*MockAmplitudeService)(nil).SendAccountLoggedInEvent), user, r)
}

// SendAccountSignedUpEvent mocks base method.
func (m *MockAmplitudeService) SendAccountSignedUpEvent(user *dbuser.User, r *http.Request) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendAccountSignedUpEvent", user, r)
}

// SendAccountSignedUpEvent indicates an expected call of SendAccountSignedUpEvent.
func (mr *MockAmplitudeServiceMockRecorder) SendAccountSignedUpEvent(user, r interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendAccountSignedUpEvent", reflect.TypeOf((*MockAmplitudeService)(nil).SendAccountSignedUpEvent), user, r)
}

// SendAccountSignupSurveyProfileUpdatedEvent mocks base method.
func (m *MockAmplitudeService) SendAccountSignupSurveyProfileUpdatedEvent(userID string, req *submitSignupSurveyReq, r *http.Request) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendAccountSignupSurveyProfileUpdatedEvent", userID, req, r)
}

// SendAccountSignupSurveyProfileUpdatedEvent indicates an expected call of SendAccountSignupSurveyProfileUpdatedEvent.
func (mr *MockAmplitudeServiceMockRecorder) SendAccountSignupSurveyProfileUpdatedEvent(userID, req, r interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendAccountSignupSurveyProfileUpdatedEvent", reflect.TypeOf((*MockAmplitudeService)(nil).SendAccountSignupSurveyProfileUpdatedEvent), userID, req, r)
}

// SendAccountTransactionCancelledEvent mocks base method.
func (m *MockAmplitudeService) SendAccountTransactionCancelledEvent(reason, paymentType, productID, packageName, productName string, user *dbuser.User, r *http.Request) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendAccountTransactionCancelledEvent", reason, paymentType, productID, packageName, productName, user, r)
}

// SendAccountTransactionCancelledEvent indicates an expected call of SendAccountTransactionCancelledEvent.
func (mr *MockAmplitudeServiceMockRecorder) SendAccountTransactionCancelledEvent(reason, paymentType, productID, packageName, productName, user, r interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendAccountTransactionCancelledEvent", reflect.TypeOf((*MockAmplitudeService)(nil).SendAccountTransactionCancelledEvent), reason, paymentType, productID, packageName, productName, user, r)
}

// SendAccountUpdatedEvent mocks base method.
func (m *MockAmplitudeService) SendAccountUpdatedEvent(action, account string, user *dbuser.User, r *http.Request) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SendAccountUpdatedEvent", action, account, user, r)
}

// SendAccountUpdatedEvent indicates an expected call of SendAccountUpdatedEvent.
func (mr *MockAmplitudeServiceMockRecorder) SendAccountUpdatedEvent(action, account, user, r interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendAccountUpdatedEvent", reflect.TypeOf((*MockAmplitudeService)(nil).SendAccountUpdatedEvent), action, account, user, r)
}
