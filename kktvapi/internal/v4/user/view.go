package user

import (
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/v4/presenter"
	"github.com/KKTV/kktv-api-v3/pkg/datatype"
)

type subscribePromotion struct {
	PromoteType PromoteType `json:"promote_type"`
}

type accountStatus struct {
	IsMember             bool                `json:"is_member"`
	IsFreeTrial          bool                `json:"is_free_trial"`
	IsFreemium           bool                `json:"is_freemium"`
	IsHavingSubscription bool                `json:"is_having_subscription"`
	SubscribePromotion   *subscribePromotion `json:"subscribe_promotion,omitempty"`
	// NeedCheckPayment to indicate whether the user needs to check the payment status because the user payment is failed when trying to debit.
	// FIXME: for now it only works for Bill<PERSON>'s payment but not for legacy product, we should refactor the code to make it work for both.
	NeedCheckPayment bool `json:"need_check_payment"`
}

type getMembershipsResp struct {
	Memberships   presenter.Memberships `json:"roles"`
	AccountStatus accountStatus         `json:"account_status"`
	Authorities   []string              `json:"authorities"`
}

type serviceGracePeriodInfo struct {
	Start   datatype.DateTime `json:"start"`
	End     datatype.DateTime `json:"end"`
	CtaLink string            `json:"cta_link"`
	OrderID string            `json:"order_id"`
}

type servicePaymentInfo struct {
	Status string `json:"status"`
	Type   string `json:"type"`
}

type myServiceResp struct {
	GracePeriod *serviceGracePeriodInfo `json:"grace_period"`
	Payment     *servicePaymentInfo     `json:"payment"`
}

type cancelSubscriptionResp struct {
	LastExpiredAt int64
	ProductId     string
	PackageTitle  string
	ProductName   string
}

type getMeResp struct {
	ID    string `json:"id"`
	Email string `json:"email,omitempty"`
	Phone string `json:"phone,omitempty"`
}
