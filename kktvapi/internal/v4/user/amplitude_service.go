//go:generate mockgen -source amplitude_service.go -destination amplitude_service_mock.go -package user
package user

import (
	"encoding/json"
	"net/http"
	"strings"

	"github.com/KKTV/kktv-api-v3/pkg/amplitudelib"
	plog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
)

type AmplitudeService interface {
	SendAccountSignedUpEvent(user *dbuser.User, r *http.Request)
	SendAccountLoggedInEvent(user *dbuser.User, r *http.Request)
	SendAccountUpdatedEvent(action, account string, user *dbuser.User, r *http.Request)
	SendAccountTransactionCancelledEvent(reason, paymentType, productID, packageTitle, productName string, user *dbuser.User, r *http.Request)
	SendAccountSignupSurveyProfileUpdatedEvent(userID string, req *submitSignupSurveyReq, r *http.Request)
}

type amplitudeService struct {
	client amplitudelib.Client
}

func NewAmplitudeService(env string) AmplitudeService {
	return &amplitudeService{
		client: amplitudelib.NewClient(env),
	}
}

func (s *amplitudeService) SendAccountSignedUpEvent(user *dbuser.User, r *http.Request) {
	event := &amplitudelib.SignUpEvent{}
	event.ScanFromRequest(r)
	event.UserID = user.ID
	event.SignUpDate = user.CreatedAt.Time
	event.Membership = user.Role
	event.SignUpType = dbuser.UserOriginProviderKKTV.String()
	event.VerifiedPhone = user.Phone.String
	event.VerifiedEmail = user.Email.String
	event.PhoneVerifiedDate = user.PhoneVerifiedAt
	event.EmailVerifiedDate = user.EmailVerifiedAt
	event.RequestBy = user.CreatedBy.String

	go func() {
		if err := s.client.SendEvent(event); err != nil {
			plog.Warn("sign up: send amplitude event failed").
				Str("user_id", user.ID).
				Interface("event", event).
				Err(err).
				Send()
		}
	}()
}

func (s *amplitudeService) SendAccountLoggedInEvent(user *dbuser.User, r *http.Request) {
	signUpType := user.OriginProvider.String
	switch user.OriginProvider.String {
	case dbuser.UserOriginProviderAccountKit.String():
		signUpType = "phone number"
	case dbuser.UserOriginProviderKKBOX.String():
		signUpType = "kkid"
	}

	loginEvent := &amplitudelib.LoginEvent{}
	loginEvent.ScanFromRequest(r)
	loginEvent.UserID = user.ID
	loginEvent.SignUpType = signUpType
	go func() {
		if err := s.client.SendEvent(loginEvent); err != nil {
			plog.Warn("send amplitude account login event failed").Err(err).Send()
		}
	}()
}

func (s *amplitudeService) SendAccountUpdatedEvent(action, account string, user *dbuser.User, r *http.Request) {
	event := &amplitudelib.AccountUpdatedEvent{}
	event.ScanFromRequest(r)
	event.UserID = user.ID

	if strings.Contains(account, "@") {
		event.VerifiedEmail = user.Email.String
		event.EmailVerifiedDate = user.EmailVerifiedAt
	} else {
		event.VerifiedPhone = user.Phone.String
		event.PhoneVerifiedDate = user.PhoneVerifiedAt
	}

	go func() {
		if err := s.client.SendEvent(event); err != nil {
			plog.Warn(action+": send amplitude event failed").
				Str("user_id", user.ID).
				Interface("event", event).
				Err(err).
				Send()
		}
	}()
}

func (s *amplitudeService) SendAccountTransactionCancelledEvent(reason, paymentType, productID, packageTitle, productName string, user *dbuser.User, r *http.Request) {
	event := &amplitudelib.AccountTransactionCancelledEvent{}
	event.ScanFromRequest(r)
	event.UserID = user.ID
	event.PaymentType = paymentType
	event.ExpiredDate = user.ExpiredAt
	event.Reason = reason
	event.Membership = user.Membership.String()
	event.PackageTitle = packageTitle
	event.ProductId = productID
	event.ProductName = productName
	go func() {
		if err := s.client.SendEvent(event); err != nil {
			plog.Warn("account transaction canceled: send amplitude event failed").
				Str("user_id", user.ID).
				Interface("event", event).
				Err(err).
				Send()
		}
	}()
}

func (s *amplitudeService) SendAccountSignupSurveyProfileUpdatedEvent(userID string, req *submitSignupSurveyReq, r *http.Request) {
	event := &amplitudelib.AccountSurveyProfileUpdatedEvent{}
	event.ScanFromRequest(r)
	event.UserID = userID
	event.Gender = req.UserInfo.Gender
	event.Birthday = req.UserInfo.Birthday
	event.Nickname = req.UserInfo.Nickname
	event.Phone = req.UserInfo.Phone
	event.Email = req.UserInfo.Email
	event.Job = req.UserInfo.Job

	var preferences map[string]interface{}
	b, _ := json.Marshal(req.Preferences)
	_ = json.Unmarshal(b, &preferences)
	event.Preferences = preferences

	go func() {
		if err := s.client.SendEvent(event); err != nil {
			plog.Warn("submit survey: send amplitude account signup survey profile updated event failed").
				Str("user_id", userID).
				Interface("req", req).
				Interface("event", event).
				Err(err).
				Send()
		}
	}()
}
