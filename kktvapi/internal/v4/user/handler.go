package user

import (
	"encoding/json"
	"errors"
	"net/http"
	"strings"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp/coldstart"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/auth"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/deeplink"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/ematic"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/middleware"
	mwauditing "github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/middleware/auditing"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/onetimepassword"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/rest"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/survey"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/user"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/validation"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/v4/presenter"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/auditing"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/kktverror"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/order"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/permission"
	"github.com/KKTV/kktv-api-v3/pkg/clock"
	"github.com/KKTV/kktv-api-v3/pkg/datatype"
	"github.com/KKTV/kktv-api-v3/pkg/encrypt"
	"github.com/KKTV/kktv-api-v3/pkg/httpreq"
	plog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/model/authority"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	modelmw "github.com/KKTV/kktv-api-v3/pkg/model/middleware"
	"github.com/KKTV/kktv-api-v3/pkg/render"
	"github.com/duke-git/lancet/v2/datetime"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/go-zoo/bone"
)

const (
	// seconds
	RetryInterval        = 2 * 60
	VerificationOTP      = "otp"
	VerificationPassword = "password"
)

var AllowCancelationPaymentType = []string{
	"credit_card",
	"telecom",
}

type Handler struct {
	userService       UserService
	otpService        OTPService
	amplitudeService  AmplitudeService
	surveyService     SurveyService
	permissionService permission.Service
	orderService      order.Service

	repo            user.UserRepository
	otpRepo         onetimepassword.OTPRepository
	paymentInfoRepo user.PaymentInfoRepository
	surveyRepo      survey.Repository
	userSurveyRepo  user.UserSurveyRepository

	actionToken  auth.ActionToken
	clock        clock.Clock
	legacyHelper legacyHelper
}

func NewHandler() *Handler {
	h := &Handler{
		userService:       NewUserService(),
		otpService:        NewOTPService(),
		amplitudeService:  NewAmplitudeService(config.Env),
		permissionService: container.PermissionService(),
		orderService:      container.OrderService(),
		repo:              user.NewUserRepository(),
		otpRepo:           onetimepassword.NewOTPRepository(),
		paymentInfoRepo:   user.NewPaymentInfoRepository(),
		surveyRepo:        survey.NewRepository(),
		userSurveyRepo:    user.NewUserSurveyRepository(),
		actionToken:       auth.NewActionToken(config.JWTAuthSignedString),
		clock:             clock.New(),
	}
	legacyHelper := &legacyHelp{
		h:           h,
		paymentRepo: user.NewPaymentInfoRepository(),
	}
	h.legacyHelper = legacyHelper
	return h
}

func (h *Handler) GenerateOTP(w http.ResponseWriter, r *http.Request) {
	var req GenerateOTPReq
	jsonDecoder := json.NewDecoder(r.Body)
	if err := jsonDecoder.Decode(&req); err != nil || req.Account == "" {
		resp := rest.Error(ErrRespInvalidParameter.Message, ErrRespInvalidParameter.Code)
		render.JSONBadRequest(w, resp)
		return
	}

	if err := validation.Validate(req); err != nil {
		resp := rest.Error(ErrRespAccountFormat.Message, ErrRespAccountFormat.Code)
		render.JSONBadRequest(w, resp)
		return
	}

	req.Account = formatAccount(req.Account)
	if !isValidAccount(req.Account) {
		resp := rest.Error(ErrRespAccountFormat.Message, ErrRespAccountFormat.Code)
		render.JSONBadRequest(w, resp)
		return
	}
	if !canSendOTP(req.Account) {
		resp := rest.Error(ErrRespUnsupportedOTPSendTo.Message, ErrRespUnsupportedOTPSendTo.Code)
		render.JSONBadRequest(w, resp)
		return
	}

	var userID string
	kkUser, ok := r.Context().Value("user").(model.JwtUser)
	if !ok {
		resp := rest.Error(ErrUserNotLogin.Message, ErrUserNotLogin.Code)
		render.JSON(w, http.StatusUnauthorized, resp)
		return
	}
	userID = kkUser.Sub

	if h.otpService.IsExist(userID, req.Account) {
		resp := rest.Error(ErrRespOTPRequestTooMuch.Message, ErrRespOTPRequestTooMuch.Code)
		render.JSONBadRequest(w, resp)
		return
	}

	otpCode := h.otpService.GenerateCode()
	if err := h.otpService.StoreCodeWithTTL(userID, req.Account, otpCode); err != nil {
		resp := rest.Error(ErrRespUnknown.Message, ErrRespUnknown.Code)
		plog.Error("generate otp: store code").Str("account", req.Account).Str("otp code", otpCode).Err(err).Send()
		render.JSONInternalServerErr(w, resp)
		return
	}

	if err := h.otpService.SendCode(req.Account, otpCode); err != nil {
		resp := rest.Error(ErrRespUnknown.Message, ErrRespUnknown.Code)
		plog.Error("generate otp: send code").Str("account", req.Account).Str("otp code", otpCode).Err(err).Send()
		render.JSONInternalServerErr(w, resp)
		return
	}

	data := struct {
		RetryInterval time.Duration `json:"retry_interval"`
	}{
		RetryInterval: RetryInterval,
	}
	resp := rest.Ok()
	resp.Data = data
	render.JSONOk(w, resp)
}

func (h *Handler) VerifyOTP(w http.ResponseWriter, r *http.Request) {
	var req VerifyOTPReq
	jsonDecoder := json.NewDecoder(r.Body)
	if err := jsonDecoder.Decode(&req); err != nil || req.Account == "" || req.OTP == "" {
		resp := rest.Error(ErrRespInvalidParameter.Message, ErrRespInvalidParameter.Code)
		render.JSONBadRequest(w, resp)
		return
	}

	req.Account = formatAccount(req.Account)
	if !isValidAccount(req.Account) {
		resp := rest.Error(ErrRespInvalidParameter.Message, ErrRespInvalidParameter.Code)
		render.JSONBadRequest(w, resp)
		return
	}

	var userID string
	kkUser, ok := r.Context().Value("user").(model.JwtUser)
	if !ok {
		resp := rest.Error(ErrUserNotLogin.Message, ErrUserNotLogin.Code)
		render.JSON(w, http.StatusUnauthorized, resp)
		return
	}
	userID = kkUser.Sub

	if err := h.otpService.Verify(userID, req.Account, req.OTP); errors.Is(err, ErrVerifyFail) {
		resp := rest.Error(ErrRespOTPIncorrect.Message, ErrRespOTPIncorrect.Code)
		render.JSONBadRequest(w, resp)
		return
	} else if err != nil {
		plog.Error("v4 user handler: verify otp error").Err(err).Str("user_id", userID).Interface("req", req).Send()
		resp := rest.Error(ErrRespUnknown.Message, ErrRespUnknown.Code)
		render.JSONInternalServerErr(w, resp)
		return
	}

	now := h.clock.Now()
	exp := now.Add(OTPCodeExistInterval)
	src := auth.ActionTokenSource{
		UserID:       userID,
		Account:      req.Account,
		Verification: VerificationOTP,
	}

	t, err := h.actionToken.Generate(src, now, exp)
	if err != nil {
		plog.Error("v4 user handler: fail to generate action token").Err(err).Str("user_id", userID).Interface("req", req).Send()
		resp := rest.Error(ErrRespUnknown.Message, ErrRespUnknown.Code)
		render.JSONInternalServerErr(w, resp)
		return
	}
	resp := rest.Ok()
	resp.Data = presenter.ActionToken{
		ActionToken: t,
		ExpiredAt:   exp.Unix(),
	}
	render.JSONOk(w, resp)
}

// SetAccount set user's account
// 用於以下流程
// 1. 備援帳密
// 2. 取回帳號
func (h *Handler) SetAccount(w http.ResponseWriter, r *http.Request) {
	var (
		statusCode int
		resp       rest.Resp
	)

	defer func() {
		render.JSON(w, statusCode, resp)
	}()

	accessUser := r.Context().Value(middleware.KeyAccessUser).(modelmw.AccessUser)
	userID := accessUser.UserID

	var req setAccountReq
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		statusCode, resp = http.StatusBadRequest, rest.Error(ErrRespInvalidParameter.Message, ErrRespInvalidParameter.Code)
		return
	}
	if err := validation.Validate(req); err != nil {
		statusCode, resp = http.StatusBadRequest, rest.Error(ErrRespAccountFormat.Message, ErrRespAccountFormat.Code)
		return
	}

	req.Account = formatAccount(req.Account)
	if !isValidAccount(req.Account) {
		statusCode, resp = http.StatusBadRequest, rest.Error(ErrRespAccountFormat.Message, ErrRespAccountFormat.Code)
		return
	}

	verifyFunc := func(claims *auth.ActionTokenClaims) error {
		if (claims.Account != req.Account) || (claims.UserID != userID) || (claims.Verification != VerificationOTP) {
			return auth.ErrTokenInvalid
		}
		return nil
	}

	// verify otp verification
	if err := h.actionToken.Compare(req.ActionToken, verifyFunc); errors.Is(err, auth.ErrTokenExpired) {
		statusCode, resp = http.StatusBadRequest, rest.Error(ErrRespOTPExpired.Message, ErrRespOTPExpired.Code)
		return
	} else if err != nil {
		statusCode, resp = http.StatusBadRequest, rest.Error(ErrRespAccountUnverified.Message, ErrRespAccountUnverified.Code)
		return
	}

	// verify if the account already used by others
	if users, err := h.userService.GetUsersByAccount(req.Account); err != nil {
		statusCode, resp = http.StatusInternalServerError, rest.Error(ErrRespUnknown.Message, ErrRespUnknown.Code)
		plog.Error("set account: failed to GetUsersByAccount").
			Str("user_id", userID).
			Str("account", req.Account).
			Err(err).
			Send()
		return
	} else if !validateAccountExists(req.Account, userID, users) {
		statusCode, resp = http.StatusBadRequest, rest.Error(ErrRespAccountExists.Message, ErrRespAccountExists.Code)
		return
	}

	var (
		oldUser *dbuser.User
		err     error
	)

	if oldUser, err = h.repo.GetActiveByID(userID); err != nil {
		statusCode, resp = http.StatusInternalServerError, rest.Error(ErrRespUnknown.Message, ErrRespUnknown.Code)
		plog.Error("set account: failed to GetActiveByID").
			Str("user_id", userID).
			Str("account", req.Account).
			Err(err).
			Send()
		return
	} else if oldUser == nil {
		resp, statusCode = rest.Error(ErrRespUserNotFound.Message, ErrRespUserNotFound.Code), http.StatusNotFound
		return
	}

	// verify the user's payment type
	var changeable bool
	if changeable, err = h.userService.ValidateAccountChangeable(oldUser, req.Account); err != nil {
		statusCode, resp = http.StatusInternalServerError, rest.Error(ErrRespUnknown.Message, ErrRespUnknown.Code)
		plog.Error("set account: failed to ValidateAccountChangeable").
			Str("user_id", userID).
			Str("account", req.Account).
			Err(err).
			Send()
		return
	} else if !changeable {
		statusCode, resp = http.StatusBadRequest, rest.Error(ErrRespAccountUnchangeable.Message, ErrRespAccountUnchangeable.Code)
		return
	}

	var newUser *dbuser.User
	if newUser, err = h.userService.UpdateAccount(oldUser, req.Account); err != nil {
		plog.Error("set account: failed to UpdateAccount").
			Str("user_id", userID).
			Str("account", req.Account).
			Err(err).
			Send()
		statusCode, resp = http.StatusInternalServerError, rest.Error(ErrRespUnknown.Message, ErrRespUnknown.Code)
		return
	}

	var logDiffs []dbuser.AuditLogDifference
	if logDiffs, err = auditing.GetDiffFields(oldUser, newUser); err != nil || len(logDiffs) == 0 {
		plog.Warn("set account: failed to getAccountAuditLogDiffs").
			Interface("old_user", oldUser).
			Interface("new_user", newUser).
			Err(err).
			Send()
	}

	mwauditing.Log(r.Context(), func(builder *mwauditing.Auditing) {
		builder.TargetUpdated("user", oldUser.ID).
			DetailDiff(logDiffs...).
			Note("user set account")
	}, mwauditing.KeepLog())

	h.amplitudeService.SendAccountUpdatedEvent("set account", req.Account, newUser, r)
	statusCode, resp = http.StatusOK, rest.Ok()
}

// UpdateAccount update user's account
// 變更帳號
func (h *Handler) UpdateAccount(w http.ResponseWriter, r *http.Request) {
	var (
		statusCode int
		resp       rest.Resp
	)

	defer func() {
		render.JSON(w, statusCode, resp)
	}()

	jwtUser, ok := r.Context().Value("user").(model.JwtUser)
	if !ok || jwtUser.IsGuest() {
		statusCode, resp = http.StatusUnauthorized, rest.Error(ErrUserNotLogin.Message, ErrUserNotLogin.Code)
		return
	}

	var req updateAccountReq
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		statusCode, resp = http.StatusBadRequest, rest.Error(ErrRespInvalidParameter.Message, ErrRespInvalidParameter.Code)
		return
	}
	if err := validation.Validate(req); err != nil {
		statusCode, resp = http.StatusBadRequest, rest.Error(ErrRespAccountFormat.Message, ErrRespAccountFormat.Code)
		return
	}

	req.Account = formatAccount(req.Account)
	if !isValidAccount(req.Account) {
		statusCode, resp = http.StatusBadRequest, rest.Error(ErrRespAccountFormat.Message, ErrRespAccountFormat.Code)
		return
	}

	verifyFunc := func(claims *auth.ActionTokenClaims) error {
		if (claims.UserID != jwtUser.Sub) || (claims.Verification != VerificationPassword) {
			return auth.ErrTokenInvalid
		}
		return nil
	}

	// verify password verification
	if err := h.actionToken.Compare(req.ActionToken, verifyFunc); err != nil {
		statusCode, resp = http.StatusBadRequest, rest.Error(ErrRespInvalidParameter.Message, ErrRespInvalidParameter.Code)
		return
	}

	// verify otp verification
	if accountOTP, err := h.otpRepo.Get(jwtUser.Sub); err != nil {
		statusCode, resp = http.StatusInternalServerError, rest.Error(ErrRespUnknown.Message, ErrRespUnknown.Code)
		plog.Error("update account: failed to get account otp from cache").
			Str("user_id", jwtUser.Sub).
			Err(err).
			Send()
		return
	} else if accountOTP == nil {
		statusCode, resp = http.StatusBadRequest, rest.Error(ErrRespOTPExpired.Message, ErrRespOTPExpired.Code)
		return
	} else if (accountOTP.Account != req.Account) || !accountOTP.IsVerified {
		statusCode, resp = http.StatusBadRequest, rest.Error(ErrRespAccountUnverified.Message, ErrRespAccountUnverified.Code)
		return
	}

	// verify if the account already used by others
	if users, err := h.userService.GetUsersByAccount(req.Account); err != nil {
		statusCode, resp = http.StatusInternalServerError, rest.Error(ErrRespUnknown.Message, ErrRespUnknown.Code)
		plog.Error("update account: failed to GetUsersByAccount").
			Str("user_id", jwtUser.Sub).
			Str("account", req.Account).
			Err(err).
			Send()
		return
	} else if !validateAccountExists(req.Account, jwtUser.Sub, users) {
		statusCode, resp = http.StatusBadRequest, rest.Error(ErrRespAccountExists.Message, ErrRespAccountExists.Code)
		return
	}

	var (
		oldUser *dbuser.User
		err     error
	)

	if oldUser, err = h.repo.GetActiveByID(jwtUser.Sub); err != nil {
		statusCode, resp = http.StatusInternalServerError, rest.Error(ErrRespUnknown.Message, ErrRespUnknown.Code)
		plog.Error("set account: failed to GetActiveByID").
			Str("user_id", jwtUser.Sub).
			Str("account", req.Account).
			Err(err).
			Send()
		return
	} else if oldUser == nil {
		resp, statusCode = rest.Error(ErrRespUserNotFound.Message, ErrRespUserNotFound.Code), http.StatusNotFound
		return
	}

	// verify the user's payment type
	var changeable bool
	if changeable, err = h.userService.ValidateAccountChangeable(oldUser, req.Account); err != nil {
		statusCode, resp = http.StatusInternalServerError, rest.Error(ErrRespUnknown.Message, ErrRespUnknown.Code)
		plog.Error("update account: failed to ValidateAccountChangeable").
			Str("user_id", jwtUser.Sub).
			Str("account", req.Account).
			Err(err).
			Send()
		return
	} else if !changeable {
		statusCode, resp = http.StatusBadRequest, rest.Error(ErrRespAccountUnchangeable.Message, ErrRespAccountUnchangeable.Code)
		return
	}

	var newUser *dbuser.User
	if newUser, err = h.userService.UpdateAccount(oldUser, req.Account); err != nil {
		plog.Error("update account: failed to UpdateAccount").
			Str("user_id", jwtUser.Sub).
			Str("account", req.Account).
			Err(err).
			Send()
		statusCode, resp = http.StatusInternalServerError, rest.Error(ErrRespUnknown.Message, ErrRespUnknown.Code)
		return
	}

	var logDiffs []dbuser.AuditLogDifference
	if logDiffs, err = auditing.GetDiffFields(oldUser, newUser); err != nil || len(logDiffs) == 0 {
		plog.Warn("update account: failed to getAccountAuditLogDiffs").
			Interface("old_user", oldUser).
			Interface("new_user", newUser).
			Err(err).
			Send()
	}

	mwauditing.Log(r.Context(), func(builder *mwauditing.Auditing) {
		builder.TargetUpdated("user", oldUser.ID).
			DetailDiff(logDiffs...).
			Note("user updated account")
	}, mwauditing.KeepLog())

	h.amplitudeService.SendAccountUpdatedEvent("update account", req.Account, newUser, r)
	statusCode, resp = http.StatusOK, rest.Ok()
}

// SetPassword set user's password
// 用於以下流程
// 1. 備援帳密
// 2. 取回帳號
func (h *Handler) SetPassword(w http.ResponseWriter, r *http.Request) {
	var resp rest.Resp
	var statusCode int
	defer func() {
		render.JSON(w, statusCode, resp)
	}()

	var userID string
	if jwtUser, ok := r.Context().Value("user").(model.JwtUser); ok && !jwtUser.IsGuest() {
		userID = jwtUser.Sub
	} else {
		resp, statusCode = rest.Error(ErrUserNotLogin.Message, ErrUserNotLogin.Code), http.StatusUnauthorized
		return
	}

	var req setPasswdReq
	jsonDecoder := json.NewDecoder(r.Body)
	if err := jsonDecoder.Decode(&req); err != nil {
		resp, statusCode = rest.Error(ErrRespInvalidParameter.Message, ErrRespInvalidParameter.Code), http.StatusBadRequest
		return
	} else if err = validation.Validate(req); err != nil {
		resp, statusCode = rest.Error(ErrRespPasswordFormat.Message, ErrRespPasswordFormat.Code), http.StatusBadRequest
		return
	}

	if req.Password != req.RepeatPassword {
		resp, statusCode = rest.Error(ErrRespPasswordMismatch.Message, ErrRespPasswordMismatch.Code), http.StatusBadRequest
		return
	}

	verifyFunc := func(claims *auth.ActionTokenClaims) error {
		if (claims.UserID != userID) || (claims.Verification != VerificationOTP) {
			return auth.ErrTokenInvalid
		}
		return nil
	}

	// verify otp verification
	if err := h.actionToken.Compare(req.ActionToken, verifyFunc); err != nil {
		resp, statusCode = rest.Error(ErrRespInvalidParameter.Message, ErrRespInvalidParameter.Code), http.StatusBadRequest
		return
	}

	err := h.userService.UpdatePassword(r.Context(), userID, req.Password, "")
	if errors.Is(err, kktverror.ErrResourceNotFound) {
		resp, statusCode = rest.Error(ErrRespUserNotFound.Message, ErrRespUserNotFound.Code), http.StatusNotFound
		return
	} else if err != nil {
		resp, statusCode = rest.Error(ErrRespUnknown.Message, ErrRespUnknown.Code), http.StatusInternalServerError
		return
	}

	mwauditing.Log(r.Context(), func(builder *mwauditing.Auditing) {
		builder.Note("user set password")
	}, mwauditing.KeepLog())

	resp, statusCode = rest.Ok(), http.StatusOK
}

// UpdatePassword update user's password
// 變更密碼
func (h *Handler) UpdatePassword(w http.ResponseWriter, r *http.Request) {
	var resp rest.Resp
	var statusCode int
	defer func() {
		render.JSON(w, statusCode, resp)
	}()

	var userID string
	if jwtUser, ok := r.Context().Value("user").(model.JwtUser); ok && !jwtUser.IsGuest() {
		userID = jwtUser.Sub
	} else {
		resp, statusCode = rest.Error(ErrUserNotLogin.Message, ErrUserNotLogin.Code), http.StatusUnauthorized
		return
	}

	var req updatePasswdReq
	jsonDecoder := json.NewDecoder(r.Body)
	if err := jsonDecoder.Decode(&req); err != nil {
		resp, statusCode = rest.Error(ErrRespInvalidParameter.Message, ErrRespInvalidParameter.Code), http.StatusBadRequest
		return
	} else if err = validation.Validate(req); err != nil {
		resp, statusCode = rest.Error(ErrRespPasswordFormat.Message, ErrRespPasswordFormat.Code), http.StatusBadRequest
		return
	}

	if req.Password != req.RepeatPassword {
		resp, statusCode = rest.Error(ErrRespPasswordMismatch.Message, ErrRespPasswordMismatch.Code), http.StatusBadRequest
		return
	}

	verifyFunc := func(claims *auth.ActionTokenClaims) error {
		if (claims.UserID != userID) || (claims.Verification != VerificationPassword) {
			return auth.ErrTokenInvalid
		}
		return nil
	}

	// verify password verification
	if err := h.actionToken.Compare(req.ActionToken, verifyFunc); err != nil {
		resp, statusCode = rest.Error(ErrRespInvalidParameter.Message, ErrRespInvalidParameter.Code), http.StatusBadRequest
		return
	}

	err := h.userService.UpdatePassword(r.Context(), userID, req.Password, "")
	if errors.Is(err, kktverror.ErrResourceNotFound) {
		resp, statusCode = rest.Error(ErrRespUserNotFound.Message, ErrRespUserNotFound.Code), http.StatusNotFound
		return
	} else if err != nil {
		plog.Error("v4UserHandler: UpdatePassword: service UpdatePassword fail").Err(err).
			Str("user_id", userID).Send()
		resp, statusCode = rest.Error(ErrRespUnknown.Message, ErrRespUnknown.Code), http.StatusInternalServerError
		return
	}

	mwauditing.Log(r.Context(), func(builder *mwauditing.Auditing) {
		builder.Note("user updated password")
	}, mwauditing.KeepLog())

	resp, statusCode = rest.Ok(), http.StatusOK
}

// VerifyAccount verify user's account before creating new account or updating current account
// 用於以下流程
// 1. 備援帳密
// 2. 取回帳號
// 3. 變更帳號
// 4. 註冊 (guest)
func (h *Handler) VerifyAccount(w http.ResponseWriter, r *http.Request) {
	var (
		statusCode int
		resp       rest.Resp
	)

	defer func() {
		render.JSON(w, statusCode, resp)
	}()

	accessUser, ok := r.Context().Value(middleware.KeyAccessUser).(modelmw.AccessUser)
	userID := accessUser.UserID
	if !ok {
		statusCode, resp = http.StatusUnauthorized, rest.Error(ErrUserNotLogin.Message, ErrUserNotLogin.Code)
		return
	}

	var (
		req verifyAccountReq
		err error
	)

	if err = json.NewDecoder(r.Body).Decode(&req); err != nil {
		statusCode, resp = http.StatusBadRequest, rest.Error(ErrRespInvalidParameter.Message, ErrRespInvalidParameter.Code)
		return
	}
	if err = validation.Validate(req); err != nil {
		statusCode, resp = http.StatusBadRequest, rest.Error(ErrRespAccountFormat.Message, ErrRespAccountFormat.Code)
		return
	}

	req.Account = formatAccount(req.Account)
	if !isValidAccount(req.Account) {
		statusCode, resp = http.StatusBadRequest, rest.Error(ErrRespAccountFormat.Message, ErrRespAccountFormat.Code)
		return
	}

	var users []*dbuser.User
	if users, err = h.userService.GetUsersByAccount(req.Account); err != nil {
		statusCode, resp = http.StatusInternalServerError, rest.Error(ErrRespUnknown.Message, ErrRespUnknown.Code)
		plog.Error("verify account: failed to GetUsersByAccount").
			Str("user_id", userID).
			Str("account", req.Account).
			Err(err).
			Send()
		return
	}

	if !accessUser.IsMember() {
		if len(users) > 0 {
			statusCode, resp = http.StatusBadRequest, rest.Error(ErrRespAccountExists.Message, ErrRespAccountExists.Code)
			return
		}
	} else {
		if !validateAccountExists(req.Account, userID, users) {
			statusCode, resp = http.StatusBadRequest, rest.Error(ErrRespAccountExists.Message, ErrRespAccountExists.Code)
			return
		}

		var loginUser *dbuser.User
		if loginUser, err = h.repo.GetActiveByID(userID); err != nil {
			statusCode, resp = http.StatusInternalServerError, rest.Error(ErrRespUnknown.Message, ErrRespUnknown.Code)
			plog.Error("set account: failed to GetActiveByID").
				Str("user_id", userID).
				Str("account", req.Account).
				Err(err).
				Send()
			return
		} else if loginUser == nil {
			resp, statusCode = rest.Error(ErrRespUserNotFound.Message, ErrRespUserNotFound.Code), http.StatusNotFound
			return
		}

		var changeable bool
		if changeable, err = h.userService.ValidateAccountChangeable(loginUser, req.Account); err != nil {
			statusCode, resp = http.StatusInternalServerError, rest.Error(ErrRespUnknown.Message, ErrRespUnknown.Code)
			plog.Error("verify account: failed to ValidateAccountChangeable").
				Str("user_id", userID).
				Str("account", req.Account).
				Err(err).
				Send()
			return
		} else if !changeable {
			statusCode, resp = http.StatusBadRequest, rest.Error(ErrRespAccountUnchangeable.Message, ErrRespAccountUnchangeable.Code)
			return
		}
	}

	statusCode, resp = http.StatusOK, rest.Ok()
}

func (h *Handler) SignUp(w http.ResponseWriter, r *http.Request) {
	var req SignUpReq
	jsonDecoder := json.NewDecoder(r.Body)
	if err := jsonDecoder.Decode(&req); err != nil {
		resp := rest.Error(ErrRespInvalidParameter.Message, ErrRespInvalidParameter.Code)
		plog.Error("sign up: failed to decode request").
			Err(err).
			Send()
		render.JSONBadRequest(w, resp)
		return
	} else if err := validation.Validate(req); err != nil && strings.Contains(err.Error(), "eqfield") {
		resp := rest.Error(ErrRespPasswordMismatch.Message, ErrRespPasswordMismatch.Code)
		render.JSONBadRequest(w, resp)
		return
	} else if err != nil {
		resp := rest.Error(ErrRespInvalidParameter.Message, ErrRespInvalidParameter.Code)
		plog.Warn("sign up: validate error").
			Err(err).
			Send()
		render.JSONBadRequest(w, resp)
		return
	}

	req.Account = formatAccount(req.Account)
	if !isValidAccount(req.Account) {
		resp := rest.Error(ErrRespInvalidParameter.Message, ErrRespInvalidParameter.Code)
		render.JSONBadRequest(w, resp)
		return
	}

	verifyFunc := func(claims *auth.ActionTokenClaims) error {
		if claims.Account != req.Account {
			return auth.ErrTokenInvalid
		}
		return nil
	}

	if err := h.actionToken.Compare(req.ActionToken, verifyFunc); err != nil {
		resp := rest.Error(ErrRespInvalidParameter.Message, ErrRespInvalidParameter.Code)
		plog.Warn("sign up: compare error").
			Err(err).
			Send()
		render.JSONBadRequest(w, resp)
		return
	}

	// There are several condition to obtain `source`
	// 1. User could be created by pxpayplus.
	// The `source` parameter must be provided by web/mobile web.
	// 2. Use platform provided by KKTV custom header if req.Source is not provided
	var source string
	if req.Source == "" {
		source = r.Header.Get(httpreq.HeaderPlatform)
	} else {
		source = req.Source
	}

	if user, err := h.userService.Create(req.Account, req.Password, source); errors.Is(err, ErrAccountExists) {
		resp := rest.Error(ErrRespAccountExists.Message, ErrRespAccountExists.Code)
		render.JSONBadRequest(w, resp)
		return
	} else if errors.Is(err, kktverror.ErrResourceConflict) {
		resp := rest.Error(ErrUserIsCreating.Message, ErrUserIsCreating.Code)
		render.JSON(w, http.StatusConflict, resp)
		return
	} else if err != nil {
		resp := rest.Error(ErrRespUnknown.Message, ErrRespUnknown.Code)
		plog.Error("v4UserHandler: signUp: service create user error").
			Str("account", req.Account).
			Err(err).
			Send()
		render.JSONInternalServerErr(w, resp)
		return
	} else if user != nil {
		h.amplitudeService.SendAccountSignedUpEvent(user, r)

		if user.Email.Valid {
			if ematicClient := ematic.GetClient(); ematicClient != nil {
				go ematicClient.Signup(user.Email.String, user.ExpiredAt.Time.String())
			}
		}

		mwauditing.Log(r.Context(),
			func(builder *mwauditing.Auditing) {
				builder.Note("user signed up").
					TargetCreated("user", user.ID).DetailWhole(user)
			},
			mwauditing.KeepLog())
	}

	// [BEGIN] in order to avoid the user to login immediately after sign up but failed for replication delay.
	time.Sleep(500 * time.Millisecond)
	// [END]

	resp := rest.Ok()
	render.JSONOk(w, resp)
}

// Login user login via account (phone or email) and password.
// If there are duplicate accounts, the verified one will be logged in.
// If duplicate accounts are all verified, it will return bad request.
func (h *Handler) Login(w http.ResponseWriter, r *http.Request) {
	var (
		statusCode int
		resp       rest.Resp
	)

	defer func() {
		render.JSON(w, statusCode, resp)
	}()

	var (
		err error
		req loginReq
	)

	if err = json.NewDecoder(r.Body).Decode(&req); err != nil {
		statusCode, resp = http.StatusBadRequest, rest.Error(ErrRespInvalidParameter.Message, ErrRespInvalidParameter.Code)
		return
	}
	if err = validation.Validate(req); err != nil {
		statusCode, resp = http.StatusBadRequest, rest.Error(ErrRespInvalidParameter.Message, ErrRespInvalidParameter.Code)
		return
	}

	req.Account = formatAccount(req.Account)
	if !isValidAccount(req.Account) {
		statusCode, resp = http.StatusUnauthorized, rest.Error(ErrRespLoginFailed.Message, ErrRespLoginFailed.Code)
		return
	}

	var users []*dbuser.User
	if users, err = h.userService.GetUsersByAccount(req.Account); err != nil {
		statusCode, resp = http.StatusInternalServerError, rest.Error(ErrRespUnknown.Message, ErrRespUnknown.Code)
		plog.Error("v4UserHandler: Login: failed to userService.GetUsersByAccount").
			Str("account", req.Account).
			Err(err).
			Send()
		return
	}
	if len(users) == 0 {
		statusCode, resp = http.StatusUnauthorized, rest.Error(ErrRespLoginFailed.Message, ErrRespLoginFailed.Code)
		return
	}

	loginUser := users[0]
	if !loginUser.Password.Valid || !encrypt.ComparePassword(loginUser.Password.String, req.Password) {
		statusCode, resp = http.StatusUnauthorized, rest.Error(ErrRespLoginFailed.Message, ErrRespLoginFailed.Code)
		return
	}

	logErr := func(topic string, err error) {
		plog.Error("v4UserHandler: Login: "+topic).
			Str("user_id", loginUser.ID).
			Str("account", req.Account).
			Err(err).
			Send()
	}

	// generate jwt token
	var authToken *presenter.AccessToken
	if authToken, err = h.userService.GenerateAccessToken(loginUser, r); err != nil {
		statusCode, resp = http.StatusInternalServerError, rest.Error(ErrRespUnknown.Message, ErrRespUnknown.Code)
		logErr("failed to userService.GenerateAccessToken", err)
		return
	}

	if _, err = h.repo.UpdateByFields(loginUser.ID, map[dbuser.UsersField]interface{}{
		dbuser.UserFieldLastLoginAt: h.clock.Now(),
	}); err != nil {
		logErr("failed to repo.UpdateByFields", err)
	}

	go func() {
		logWarn := func(topic string, err error) {
			plog.Warn("v4UserHandler: Login: "+topic).
				Str("user_id", loginUser.ID).
				Str("account", req.Account).
				Err(err).
				Send()
		}

		// migrate cold start data to user
		if deviceID := r.Header.Get(httpreq.HeaderDeviceID); deviceID != "" {
			coldstart.MigrateDevicePreferenceToUser(deviceID, loginUser.ID)
		}

		// send amplitude login event
		h.amplitudeService.SendAccountLoggedInEvent(loginUser, r)

		// try binding billing telecom customer if user has phone account
		if loginUser.Phone.Valid {
			if err = h.legacyHelper.bindBillingCustomer(loginUser, req.Account); err != nil {
				logWarn("failed to legacyHelper.bindBillingCustomer", err)
			}
		}

		// send ematic sign in event
		// if !loginUser.Email.Valid {
		// 	return
		// } else if ematicClient := ematic.GetClient(); ematicClient == nil {
		// 	return
		// } else if err = ematicClient.Signin(loginUser.Email.String); err != nil {
		// 	logWarn("failed to ematicClient.Signin", err)
		// }
	}()

	statusCode, resp = http.StatusOK, rest.Ok()
	resp.Data = authToken
}

// VerifyPasswordResetAccount verify if account exists for password reset
func (h *Handler) VerifyPasswordResetAccount(w http.ResponseWriter, r *http.Request) {
	var (
		statusCode int
		resp       rest.Resp
		validUsers []dbuser.User
	)

	defer func() {
		render.JSON(w, statusCode, resp)
	}()

	var req verifyPasswordResetAccountReq
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		statusCode, resp = http.StatusBadRequest, rest.Error(ErrRespInvalidParameter.Message, ErrRespInvalidParameter.Code)
		return
	}
	if err := validation.Validate(req); err != nil {
		statusCode, resp = http.StatusBadRequest, rest.Error(ErrRespAccountFormat.Message, ErrRespAccountFormat.Code)
		return
	}

	req.Account = formatAccount(req.Account)
	if !isValidAccount(req.Account) {
		statusCode, resp = http.StatusBadRequest, rest.Error(ErrRespAccountFormat.Message, ErrRespAccountFormat.Code)
		return
	}

	if users, err := h.userService.GetUsersByAccount(req.Account); err != nil {
		statusCode, resp = http.StatusInternalServerError, rest.Error(ErrRespUnknown.Message, ErrRespUnknown.Code)
		plog.Error("verify password reset account: failed to GetUsersByAccount").
			Str("account", req.Account).
			Err(err).
			Send()
		return
	} else if len(users) > 1 {
		email, _ := parseFromAccount(req.Account)
		if email.Valid {
			validUsers = filter(users, filterEmailValidUser)
		} else {
			validUsers = filter(users, filterPhoneValidUser)
		}

		if len(validUsers) == 1 {
			statusCode, resp = http.StatusOK, rest.Ok()
			return
		}

		statusCode, resp = http.StatusBadRequest, rest.Error(ErrRespAccountDuplicate.Message, ErrRespAccountDuplicate.Code)
		return
	} else if len(users) == 0 {
		statusCode, resp = http.StatusNotFound, rest.Error(ErrRespUserNotFound.Message, ErrRespUserNotFound.Code)
		return
	}

	statusCode, resp = http.StatusOK, rest.Ok()
}

// VerifyPassword verify if request password is the same as login user's password
func (h *Handler) VerifyPassword(w http.ResponseWriter, r *http.Request) {
	var (
		statusCode int
		resp       rest.Resp
	)

	defer func() {
		render.JSON(w, statusCode, resp)
	}()

	var userID string
	if jwtUser, ok := r.Context().Value("user").(model.JwtUser); ok && !jwtUser.IsGuest() {
		userID = jwtUser.Sub
	} else {
		statusCode, resp = http.StatusUnauthorized, rest.Error(ErrUserNotLogin.Message, ErrUserNotLogin.Code)
		return
	}

	var req verifyPasswordReq
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		statusCode, resp = http.StatusBadRequest, rest.Error(ErrRespInvalidParameter.Message, ErrRespInvalidParameter.Code)
		return
	}
	if err := validation.Validate(req); err != nil {
		statusCode, resp = http.StatusBadRequest, rest.Error(ErrRespPasswordFormat.Message, ErrRespPasswordFormat.Code)
		return
	}

	if loginUser, err := h.repo.GetActiveByID(userID); err != nil {
		statusCode, resp = http.StatusInternalServerError, rest.Error(ErrRespUnknown.Message, ErrRespUnknown.Code)
		plog.Error("verify password: failed to GetActiveByID").
			Str("user_id", userID).
			Err(err).
			Send()
		return
	} else if loginUser == nil {
		statusCode, resp = http.StatusNotFound, rest.Error(ErrRespUserNotFound.Message, ErrRespUserNotFound.Code)
		return
	} else if !loginUser.Password.Valid || !encrypt.ComparePassword(loginUser.Password.String, req.Password) {
		statusCode, resp = http.StatusBadRequest, rest.Error(ErrRespWrongPassword.Message, ErrRespWrongPassword.Code)
		plog.Info("verify password: entered wrong password").
			Str("user_id", userID).
			Send()
		return
	}

	now := h.clock.Now()
	exp := now.Add(OTPCodeExistInterval)
	src := auth.ActionTokenSource{
		UserID:       userID,
		Verification: VerificationPassword,
	}
	token, err := h.actionToken.Generate(src, now, exp)
	if err != nil {
		statusCode, resp = http.StatusInternalServerError, rest.Error(ErrRespUnknown.Message, ErrRespUnknown.Code)
		plog.Error("verify password: failed to generate action token").
			Str("user_id", userID).
			Err(err).
			Send()
		return
	}

	data := struct {
		ActionToken string `json:"action_token"`
	}{
		ActionToken: token,
	}

	statusCode = http.StatusOK
	resp.Data = data
	return
}

func (h *Handler) PasswordReset(w http.ResponseWriter, r *http.Request) {
	var (
		statusCode int
		resp       rest.Resp
		validUsers []dbuser.User
	)
	defer func() {
		render.JSON(w, statusCode, resp)
	}()

	var req ResetPasswordReq
	jsonDecoder := json.NewDecoder(r.Body)
	if err := jsonDecoder.Decode(&req); err != nil {
		statusCode, resp = http.StatusBadRequest, rest.Error(ErrRespInvalidParameter.Message, ErrRespInvalidParameter.Code)
		return
	} else if err = validation.Validate(req); err != nil && strings.Contains(err.Error(), "eqfield") {
		statusCode, resp = http.StatusBadRequest, rest.Error(ErrRespPasswordMismatch.Message, ErrRespPasswordMismatch.Code)
		return
	} else if err != nil {
		statusCode, resp = http.StatusBadRequest, rest.Error(ErrRespInvalidParameter.Message, ErrRespInvalidParameter.Code)
		plog.Error("reset password: validate error").Err(err).Send()
		return
	}

	req.Account = formatAccount(req.Account)
	if !isValidAccount(req.Account) {
		statusCode, resp = http.StatusBadRequest, rest.Error(ErrRespInvalidParameter.Message, ErrRespInvalidParameter.Code)
		return
	}

	verifyFunc := func(claims *auth.ActionTokenClaims) error {
		if claims.Account != req.Account {
			return auth.ErrTokenInvalid
		}
		return nil
	}

	if err := h.actionToken.Compare(req.ActionToken, verifyFunc); err != nil {
		statusCode, resp = http.StatusBadRequest, rest.Error(ErrRespInvalidParameter.Message, ErrRespInvalidParameter.Code)
		plog.Error("reset password: action token compare failed").
			Str("account", req.Account).
			Err(err).
			Send()
		return
	}

	var (
		oldUser *dbuser.User
		newUser *dbuser.User
	)

	if existsUsers, err := h.userService.GetUsersByAccount(req.Account); err != nil {
		plog.Error("reset password: Get users error").
			Str("account", req.Account).
			Err(err).
			Send()
		statusCode, resp = http.StatusInternalServerError, rest.Error(ErrRespUnknown.Message, ErrRespUnknown.Code)
		return
	} else if len(existsUsers) == 0 {
		resp, statusCode = rest.Error(ErrRespUserNotFound.Message, ErrRespUserNotFound.Code), http.StatusNotFound
		return
	} else if len(existsUsers) == 1 {
		oldUser = existsUsers[0]
		if newUser, err = h.userService.ResetPassword(oldUser, req.Account, req.Password); err != nil {
			plog.Error("reset password: unknown error").Err(err).
				Str("user_id", existsUsers[0].ID).
				Str("account", req.Account).
				Send()
			statusCode, resp = http.StatusInternalServerError, rest.Error(ErrRespUnknown.Message, ErrRespUnknown.Code)
			return
		}
	} else {
		email, _ := parseFromAccount(req.Account)
		if email.Valid {
			validUsers = filter(existsUsers, filterEmailValidUser)
		} else {
			validUsers = filter(existsUsers, filterPhoneValidUser)
		}

		if len(validUsers) == 0 {
			statusCode, resp = http.StatusBadRequest, rest.Error(ErrRespAccountDuplicate.Message, ErrRespAccountDuplicate.Code)
			return
		}

		if len(validUsers) == 1 {
			oldUser = &validUsers[0]
			if newUser, err = h.userService.ResetPassword(oldUser, req.Account, req.Password); err != nil {
				plog.Error("reset password: unknown error").
					Str("user_id", validUsers[0].ID).
					Str("account", req.Account).
					Err(err).
					Send()
				statusCode, resp = http.StatusInternalServerError, rest.Error(ErrRespUnknown.Message, ErrRespUnknown.Code)
				return
			}
		}
	}

	if newUser != nil {
		logDiffs, err := auditing.GetDiffFields(oldUser, newUser)
		if err != nil || len(logDiffs) == 0 {
			plog.Warn("reset password: failed to getAccountAuditLogDiffs").
				Interface("old_user", oldUser).
				Interface("new_user", newUser).
				Err(err).
				Send()
		}

		mwauditing.Log(r.Context(), func(builder *mwauditing.Auditing) {
			builder.TargetUpdated("user", oldUser.ID).
				DetailDiff(logDiffs...).
				Note("user reset password")
		}, mwauditing.KeepLog())

		h.amplitudeService.SendAccountUpdatedEvent("reset password", req.Account, newUser, r)
	}

	statusCode, resp = http.StatusOK, rest.Ok()
}

func (h *Handler) CancelSubscription(w http.ResponseWriter, r *http.Request) {
	var resp rest.Resp
	var statusCode int

	defer func() {
		render.JSON(w, statusCode, resp)
	}()

	var userID string
	if jwtUser, ok := r.Context().Value("user").(model.JwtUser); ok && !jwtUser.IsGuest() {
		userID = jwtUser.Sub
	} else {
		resp, statusCode = rest.Error(ErrUserNotLogin.Message, ErrUserNotLogin.Code), http.StatusUnauthorized
		return
	}
	reason := r.URL.Query().Get("reason")

	plog.Info("v4 unsubscribe: params").Str("user_id", userID).Str("reason", reason).Send()
	if reason == "" {
		statusCode, resp = http.StatusBadRequest, rest.Error(ErrRespInvalidParameter.Message, ErrRespInvalidParameter.Code)
		return
	}

	paymentInfo, err := h.paymentInfoRepo.GetPaymentInfoByUserID(userID)
	if err != nil {
		statusCode, resp = http.StatusBadRequest, rest.Error(ErrRespInvalidParameter.Message, ErrRespInvalidParameter.Code)
		return
	}

	paymentType := paymentInfo.PaymentType.String

	// 目前 Web 僅允許 credit_card, telecom 可以透過 api 取消訂閱
	if !slice.Contain(AllowCancelationPaymentType, paymentType) {
		statusCode, resp = http.StatusBadRequest, rest.Error(ErrRespInvalidParameter.Message, ErrRespInvalidParameter.Code)
		return
	}

	var cancelResp cancelSubscriptionResp
	if cancelResp, err = h.userService.CancelSubscription(userID, reason, paymentInfo); err != nil {
		plog.Error("v4 unsubscribe: failed to cancel subscription").Err(err).Send()
		statusCode, resp = http.StatusInternalServerError, rest.Error(ErrRespUnknown.Message, ErrRespUnknown.Code)
		return
	}

	var user *dbuser.User
	if user, err = h.repo.GetByUserID(userID); err != nil {
		statusCode, resp = http.StatusInternalServerError, rest.Error(ErrRespUnknown.Message, ErrRespUnknown.Code)
		return
	}

	go h.amplitudeService.SendAccountTransactionCancelledEvent(reason, paymentType, cancelResp.ProductId, cancelResp.PackageTitle, cancelResp.ProductName, user, r)

	statusCode, resp = http.StatusOK, rest.Ok()
	resp.Data = map[string]interface{}{
		"expired_at": cancelResp.LastExpiredAt - 86400,
	}
}

func (h *Handler) SubmitSurvey(w http.ResponseWriter, r *http.Request) {
	var (
		statusCode int
		resp       rest.Resp
	)
	defer func() {
		render.JSON(w, statusCode, resp)
	}()

	var userID string
	if jwtUser, ok := r.Context().Value("user").(model.JwtUser); ok && !jwtUser.IsGuest() {
		userID = jwtUser.Sub
	} else {
		resp, statusCode = rest.Error(ErrUserNotLogin.Message, ErrUserNotLogin.Code), http.StatusUnauthorized
		return
	}

	identifier := dbuser.SurveyIdentifier(bone.GetValue(r, "identifier"))
	if !slice.Contain(dbuser.AllowedSurveyIdentifiers, identifier) {
		statusCode, resp = http.StatusBadRequest, rest.Error(ErrRespInvalidParameter.Message, ErrRespInvalidParameter.Code)
		return
	}

	h.surveyService = NewSurveyService(identifier)
	if err := h.surveyService.SetRequest(r.Body); err != nil {
		statusCode, resp = http.StatusBadRequest, rest.Error(ErrRespInvalidParameter.Message, ErrRespInvalidParameter.Code)
		return
	}

	var (
		err    error
		survey *dbuser.Survey
	)
	if survey, err = h.surveyRepo.GetByIdentifier(identifier); err != nil {
		statusCode, resp = http.StatusInternalServerError, rest.Error(ErrRespUnknown.Message, ErrRespUnknown.Code)
		plog.Error("v4 submit survey: failed to get survey").
			Str("user_id", userID).
			Str("identifier", identifier.String()).
			Err(err).
			Send()
		return
	} else if survey == nil || survey.Status == dbuser.SurveyStatusInactive {
		statusCode, resp = http.StatusBadRequest, rest.Error(ErrRespInvalidParameter.Message, ErrRespInvalidParameter.Code)
		return
	}

	// 檢查是否填寫過問卷
	// TODO: perf https://github.com/KKTV/kktv-api-v3/pull/1862#discussion_r1222437445
	var userSurvey *dbuser.UserSurvey
	if userSurvey, err = h.userSurveyRepo.GetByUserIDAndSurveyID(userID, survey.ID); err != nil {
		statusCode, resp = http.StatusInternalServerError, rest.Error(ErrRespUnknown.Message, ErrRespUnknown.Code)
		plog.Error("submit survey: failed to GetByUserIDAndSurveyID").
			Str("user_id", userID).
			Err(err).
			Send()
		return
	} else if userSurvey != nil {
		statusCode, resp = http.StatusBadRequest, rest.Error(ErrRespAlreadySubmittedSurvey.Message, ErrRespAlreadySubmittedSurvey.Code)
		return
	}

	var user *dbuser.User
	if user, err = h.repo.GetActiveByID(userID); err != nil {
		statusCode, resp = http.StatusInternalServerError, rest.Error(ErrRespUnknown.Message, ErrRespUnknown.Code)
		plog.Error("v4 submit survey: failed to get user").
			Str("user_id", userID).
			Int64("survey_id", survey.ID).
			Err(err).
			Send()
		return
	} else if user == nil {
		statusCode, resp = http.StatusNotFound, rest.Error(ErrRespUserNotFound.Message, ErrRespUserNotFound.Code)
		return
	}

	if userSurvey, err = h.surveyService.CreateUserSurvey(user, survey); err != nil {
		statusCode, resp = http.StatusInternalServerError, rest.Error(ErrRespUnknown.Message, ErrRespUnknown.Code)
		plog.Error("v4 submit survey: failed to create user survey").
			Str("user_id", userID).
			Int64("survey_id", survey.ID).
			Err(err).
			Send()
		return
	}

	h.surveyService.SendAmplitudeEvent(user.ID, r)

	mwauditing.Log(r.Context(), func(builder *mwauditing.Auditing) {
		builder.Note("user submitted signup survey").
			TargetCreated("user", userID).
			DetailWhole(userSurvey)
	}, mwauditing.KeepLog())

	statusCode, resp = http.StatusOK, rest.Ok()
	return
}

func (h *Handler) GetMyMemberships(w http.ResponseWriter, r *http.Request) {
	access, ok := r.Context().Value(middleware.KeyAccessUser).(modelmw.AccessUser)
	if !ok {
		render.JSON(w, http.StatusUnauthorized, rest.Error(ErrRespUnauthorized.Message, ErrRespUnauthorized.Code))
		return
	}
	resp := rest.Ok()
	membership := access.Memberships
	membershipView := make(presenter.Memberships, 0, len(membership))
	isHavingSubscription := false
	needCheckPayment := false
	var ownedAuthorities []authority.Authority
	if access.IsMember() {
		u, err := h.repo.GetActiveByID(access.UserID)
		if err != nil {
			plog.Error("v4UserHandler: GetMyMembership: repo fail to GetActiveByID").Err(err).Str("user_id", access.UserID).Send()
			render.JSON(w, http.StatusInternalServerError, rest.Error(ErrRespUnknown.Message, ErrRespUnknown.Code))
			return
		} else if u == nil {
			render.JSON(w, http.StatusNotFound, rest.Error(ErrRespUserNotFound.Message, ErrRespUserNotFound.Code))
			return
		}

		membership = u.Membership
		membershipView = make(presenter.Memberships, len(membership))
		for i := range membershipView {
			membershipView[i] = presenter.RoleModel{
				Role: membership[i].Role.String(),
			}
		}
		ownedAuthorities, err = h.permissionService.ListMemberAuthorities(u)
		if err != nil {
			plog.Error("v4UserHandler: GetMyMembership: permissionService fail to ListMemberAuthorities").Err(err).Str("user_id", access.UserID).Send()
			render.JSON(w, http.StatusInternalServerError, rest.Error(ErrRespUnknown.Message, ErrRespUnknown.Code))
			return
		}
		isHavingSubscription = h.isUserHavingSubscription(u, ownedAuthorities)

		needCheckPayment, err = h.isNeedCheckPayment(u)
		if err != nil {
			plog.Error("v4UserHandler: GetMyMembership: fail to check payment").Err(err).Str("user_id", access.UserID).Send()
			render.JSON(w, http.StatusInternalServerError, rest.Error(ErrRespUnknown.Message, ErrRespUnknown.Code))
			return
		}
	} else {
		ownedAuthorities = h.permissionService.ListGuestAuthorities()
	}
	authorities := make([]string, 0, len(ownedAuthorities))
	for _, au := range ownedAuthorities {
		authorities = append(authorities, au.String())
	}

	isMember := membership.IsMember()
	isFreeTrial := isMember && membership.ContainRole(dbuser.MemberRoleFreeTrial)
	isFreemium := !isMember || membership.Equal(dbuser.MembershipExpired)
	isNonPaid := isFreemium || (isFreeTrial && !isHavingSubscription)

	resp.Data = getMembershipsResp{
		Memberships: membershipView,
		AccountStatus: accountStatus{
			IsMember:             isMember,
			IsFreeTrial:          isFreeTrial,
			IsFreemium:           isFreemium,
			IsHavingSubscription: isHavingSubscription,
			NeedCheckPayment:     needCheckPayment,
			SubscribePromotion:   getSubscribePromotion(isMember, isNonPaid),
		},
		Authorities: authorities,
	}
	render.JSON(w, http.StatusOK, resp)
}

// GetMyProductService get user's product service information. Only member should have such information.
func (h *Handler) GetMyProductService(w http.ResponseWriter, r *http.Request) {
	access := r.Context().Value(middleware.KeyAccessUser).(modelmw.AccessUser)
	myServiceInfo := new(myServiceResp)

	if paymentInfo, err := h.paymentInfoRepo.GetByUserID(access.UserID); err != nil {
		plog.Error("v4UserHandler: GetMyProductService: paymentInfoRepo GetByUserID fail").Err(err).Str("user_id", access.UserID).Send()
		render.JSON(w, http.StatusInternalServerError, rest.Error(ErrRespUnknown.Message, ErrRespUnknown.Code))
		return
	} else if paymentInfo != nil {
		myServiceInfo.Payment = &servicePaymentInfo{
			Status: dbuser.PaymentStatusPaid.String(),
			Type:   paymentInfo.PaymentType.ValueOrZero(),
		}
	}

	if orderStatus, err := h.orderService.GetUserOrderStatus(access.UserID); err != nil {
		plog.Error("v4UserHandler: GetMyProductService: service GetUserOrderStatus fail").Err(err).Str("user_id", access.UserID).Send()
		render.JSON(w, http.StatusInternalServerError, rest.Error(ErrRespUnknown.Message, ErrRespUnknown.Code))
		return
	} else if gp := orderStatus.GracePeriod; gp != nil {
		var ctaLink string
		// payment should not be null.
		// suppose that if user is in grace period, he must have payment info because he subscribed before
		if myServiceInfo.Payment != nil {
			myServiceInfo.Payment.Status = dbuser.PaymentStatusFailed.String()
			if link, ok := gracePeriodCtaLink[myServiceInfo.Payment.Type]; ok {
				ctaLink = link
			} else {
				ctaLink = deeplink.MyAccountPage()
			}
		}

		myServiceInfo.GracePeriod = &serviceGracePeriodInfo{
			Start:   datatype.DateTime(gp.StartAt),
			End:     datatype.DateTime(gp.EndAt),
			CtaLink: ctaLink,
			OrderID: gp.OrderID,
		}
	}
	resp := rest.Ok()
	resp.Data = myServiceInfo
	render.JSONOk(w, resp)
}

func (h *Handler) isUserHavingSubscription(u *dbuser.User, ownedAuthorities []authority.Authority) bool {
	if u.ExpiredAt.Valid && u.ExpiredAt.Time.Before(h.clock.Now()) { // expired user should not have subscription
		return false
	}
	if u.AutoRenew {
		return true
	}
	// if a user is having subscription, his authority.ProductPackageGeneralPurchase will be erased
	isHavingSubscription := !slice.Contain(ownedAuthorities, authority.ProductPackageGeneralPurchase)
	return isHavingSubscription
}

// for now, the only case that need to check payment is for user who is in grace period
func (h *Handler) isNeedCheckPayment(user *dbuser.User) (bool, error) {
	if !user.Membership.Equal(dbuser.MembershipExpired) {
		return false, nil
	}

	gracePeriodEndAt := datetime.BeginOfDay(user.ExpiredAt.Time).AddDate(0, 0, daysForGracePeriod)
	maybeInGracePeriod := h.clock.Now().Before(gracePeriodEndAt)
	if !maybeInGracePeriod {
		return false, nil
	}
	orderStatus, err := h.orderService.GetUserOrderStatus(user.ID)
	if err != nil {
		return false, err
	}
	return orderStatus.GracePeriod != nil, nil
}

func getSubscribePromotion(isMember, nonPaid bool) *subscribePromotion {
	if !isMember {
		return &subscribePromotion{
			PromoteType: PromoteTypeSignup,
		}
	}
	if nonPaid {
		return &subscribePromotion{
			PromoteType: PromoteTypeUpgrade,
		}
	}
	return nil
}

// GetMe returns the current user's information
func (h *Handler) GetMe(w http.ResponseWriter, r *http.Request) {
	access := r.Context().Value(middleware.KeyAccessUser).(modelmw.AccessUser)

	u, err := h.repo.GetActiveByID(access.UserID)
	if err != nil {
		plog.Error("v4UserHandler: GetMe: failed to get user").Str("user_id", access.UserID).Err(err).Send()
		render.JSONInternalServerErr(w, rest.Error(ErrRespUnknown.Message, ErrRespUnknown.Code))
		return
	}
	if u == nil {
		render.JSONNotFound(w, rest.Error(ErrRespUserNotFound.Message, ErrRespUserNotFound.Code))
		return
	}

	resp := rest.Ok()
	resp.Data = getMeResp{
		ID:    u.ID,
		Email: u.Email.ValueOrZero(),
		Phone: u.Phone.ValueOrZero(),
	}

	render.JSONOk(w, resp)
}
