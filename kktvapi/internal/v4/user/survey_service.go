package user

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strings"
	"sync"

	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/container"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/user"
	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/validation"
	"github.com/KKTV/kktv-api-v3/pkg/billing"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"gopkg.in/guregu/null.v3"
)

var (
	surveySrv     surveyService
	onceSurveySrv sync.Once
)

type SurveyService interface {
	SetRequest(body io.ReadCloser) error
	CreateUserSurvey(user *dbuser.User, survey *dbuser.Survey) (*dbuser.UserSurvey, error)
	SendAmplitudeEvent(userID string, r *http.Request)
}

// surveyService is the base struct for all survey services.
type surveyService struct {
	userSurveyRepo   user.UserSurveyRepository
	amplitudeService AmplitudeService
	billingClient    billing.Client
}

type signUpSurveyService struct {
	surveyService
	req *submitSignupSurveyReq
}

func NewSurveyService(identifier dbuser.SurveyIdentifier) SurveyService {
	onceSurveySrv.Do(func() {
		surveySrv = surveyService{
			userSurveyRepo:   user.NewUserSurveyRepository(),
			amplitudeService: NewAmplitudeService(config.Env),
			billingClient:    container.BillingClient(),
		}
	})

	switch identifier {
	case dbuser.SurveyIdentifierSignup:
		return &signUpSurveyService{
			surveyService: surveySrv,
		}
	}

	return nil
}

func (s *signUpSurveyService) SetRequest(body io.ReadCloser) error {
	var req submitSignupSurveyReq
	if err := json.NewDecoder(body).Decode(&req); err != nil {
		return fmt.Errorf("signup survey service: failed to decode request body: %w", err)
	}

	msg := "signup survey service: validation failed: %w"
	if err := validation.Validate(req); err != nil {
		return fmt.Errorf(msg, err)
	}

	contactEmail := strings.ToLower(req.UserInfo.Email)
	if contactEmail != "" && !isValidAccount(contactEmail) {
		return fmt.Errorf(msg, errors.New("invalid contact email"))
	}

	contactPhone := formatTaiwanPhoneNumber(req.UserInfo.Phone)
	if contactPhone != "" && !isValidAccount(contactPhone) {
		return fmt.Errorf(msg, errors.New("invalid contact phone"))
	}

	req.UserInfo.Email = contactEmail
	req.UserInfo.Phone = contactPhone
	s.req = &req
	return nil
}

func (s *signUpSurveyService) CreateUserSurvey(user *dbuser.User, survey *dbuser.Survey) (*dbuser.UserSurvey, error) {
	userSurvey := &dbuser.UserSurvey{
		UserID:   user.ID,
		SurveyID: survey.ID,
	}

	contactEmail := s.req.UserInfo.Email
	if contactEmail != "" {
		userSurvey.ContactEmail = null.StringFrom(contactEmail)
		// s.signupOrUnsubscribeEmatic(user)
	}

	contactPhone := s.req.UserInfo.Phone
	if contactPhone != "" {
		userSurvey.ContactPhone = null.StringFrom(contactPhone)
	}

	var err error
	if userSurvey, err = s.userSurveyRepo.Create(userSurvey); err != nil {
		return nil, err
	}

	// 只有 free trial 才可以獲得免費天數
	if user.Role == dbuser.RoleFreeTrial.String() {
		createOrderPayload := map[string]string{
			"identifier":         user.ID,
			"placed_from":        "client",
			"product_identifier": survey.AwardBillingProductIdentifier,
			"quantity":           "1",
		}
		if user.EmailVerifiedAt.Valid && user.Email.Valid {
			createOrderPayload["email"] = user.Email.String
		}
		if user.PhoneVerifiedAt.Valid && user.Phone.Valid {
			phone := formatTaiwanPhoneNumber(user.Phone.String)
			createOrderPayload["phone"] = strings.TrimLeft(phone, "+")
		}
		if _, err = s.billingClient.CreateOrder(createOrderPayload, user.ID); err != nil {
			return nil, fmt.Errorf("signup survey service: failed to create order: %w", err)
		}
	}

	return userSurvey, nil
}

func (s *signUpSurveyService) SendAmplitudeEvent(userID string, r *http.Request) {
	s.amplitudeService.SendAccountSignupSurveyProfileUpdatedEvent(userID, s.req, r)
}

// func (s *signUpSurveyService) signupOrUnsubscribeEmatic(user *dbuser.User) {
// 	contactEmail := s.req.UserInfo.Email
// 	if contactEmail == "" {
// 		return
// 	}

// 	ematicClient := ematic.GetClient()
// 	userEmail := strings.ToLower(user.Email.String)
// 	logWarn := func(topic string, err error) {
// 		plog.Warn("signup survey service: "+topic).
// 			Str("user_id", user.ID).
// 			Str("user_email", userEmail).
// 			Str("contact_email", userEmail).
// 			Err(err).
// 			Send()
// 	}

// 	if userEmail != contactEmail {
// 		go func() {
// 			if err := ematicClient.Signup(contactEmail, user.ExpiredAt.Time.String()); err != nil {
// 				logWarn("ematic signup failed", err)
// 			}
// 		}()
// 	}
// }
