package user

import "github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/rest"

var (
	ErrRespInvalidParameter       = &rest.Err{Code: "400.1", Message: "Invalid parameter"}
	ErrRespAccountFormat          = &rest.Err{Code: "400.2", Message: "Invalid account format"}              // 帳號格式錯誤
	ErrRespAccountExists          = &rest.Err{Code: "400.3", Message: "The account already exists"}          // 帳號已存在
	ErrRespAccountUnverified      = &rest.Err{Code: "400.4", Message: "The account is unverified"}           // 帳號尚未通過認證
	ErrRespOTPIncorrect           = &rest.Err{Code: "400.5", Message: "The OTP is incorrect"}                // 認證碼錯誤
	ErrRespOTPExpired             = &rest.Err{Code: "400.6", Message: "The OTP has expired"}                 // 認證碼過期
	ErrRespPasswordFormat         = &rest.Err{Code: "400.7", Message: "Invalid password format"}             // 密碼格式錯誤
	ErrRespPasswordMismatch       = &rest.Err{Code: "400.8", Message: "Password confirmation doesn't match"} // 密碼與確認密碼不同
	ErrRespOTPRequestTooMuch      = &rest.Err{Code: "400.9", Message: "The OTP requests too much times"}     // 認證碼不能重複取得
	ErrRespAccountDuplicate       = &rest.Err{Code: "400.10", Message: "There are duplicate accounts"}
	ErrRespWrongPassword          = &rest.Err{Code: "400.11", Message: "Wrong password"}                                       // 密碼錯誤
	ErrRespAccountUnchangeable    = &rest.Err{Code: "400.12", Message: "User's account is unchangeable"}                       // 帳號不可變更
	ErrRespAlreadySubmittedSurvey = &rest.Err{Code: "400.13", Message: "User has already submitted the survey"}                // 已完成問卷
	ErrRespUnsupportedOTPSendTo   = &rest.Err{Code: "400.14", Message: "Unsupported email or mobile phone number to send OTP"} // 不支援發送OTP的帳號格式
	ErrUserIsCreating             = &rest.Err{Code: "409.1", Message: "Target user is creating"}                               // 使用者正在建立中
	ErrUserNotLogin               = &rest.Err{Code: "401.0", Message: "User not login"}
	ErrRespLoginFailed            = &rest.Err{Code: "401.1", Message: "Login failed"} // 登入資訊錯誤
	ErrRespUnauthorized           = &rest.Err{Code: "401.2", Message: "Unauthorized"} // 未授權
	ErrRespUserNotFound           = &rest.Err{Code: "404.1", Message: "User not found"}
	ErrRespUnknown                = &rest.Err{Code: "500.0", Message: "Unknown error"}
)
