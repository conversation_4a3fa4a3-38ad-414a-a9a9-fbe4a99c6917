//go:generate mockgen -source otp_service.go -destination otp_service_mock.go -package user
package user

import (
	"errors"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/KKTV/kktv-api-v3/kktvapi/internal/pkg/onetimepassword"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/message"
	"github.com/KKTV/kktv-api-v3/pkg/cache"
	"github.com/KKTV/kktv-api-v3/pkg/clock"
	plog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/model/cacheuser"
	"github.com/KKTV/kktv-api-v3/pkg/rand"
)

const (
	OTPCodeRetryInterval = 2 * 60 * time.Second
	OTPCodeExistInterval = 5 * 60 * time.Second
	OTPLength            = 6
	OTPRequestLimit      = 10
)

var (
	otpSrv     OTPService
	onceOtpSrv sync.Once
)

var (
	ErrVerifyFail = errors.New("verify fail")
)

const (
	otpSubject      = "KKTV 驗證碼"
	smsOTPTemplate  = `[KKTV] 您的驗證碼是：%s，此驗證碼 5 分鐘內有效，如非本人操作請忽略，請勿分享給任何人`
	mailOTPTemplate = `感謝您建立【KKTV】的帳號
請在 5 分鐘內於 KKTV 輸入以下驗證碼，完成建立帳號。
如非本人操作請忽略，請勿分享給任何人。

驗證碼：%s
	`
)

type OTPService interface {
	GenerateCode() string
	StoreCodeWithTTL(userID, account, code string) error
	IsExist(UserID, account string) bool
	SendCode(account, code string) error
	Verify(userID, account, code string) error
}

type otpService struct {
	otpRepo     onetimepassword.OTPRepository
	rand        rand.Rand
	smsClient   message.SMSer
	emailClient message.Mailer
	clock       clock.Clock
}

func NewOTPService() OTPService {
	onceOtpSrv.Do(func() {
		otpSrv = &otpService{
			otpRepo:     onetimepassword.NewOTPRepository(),
			rand:        rand.New(),
			smsClient:   message.NewSMSer(),
			emailClient: message.NewMailer(),
			clock:       clock.New(),
		}
	})
	return otpSrv
}

func (s *otpService) GenerateCode() string {
	code, _ := s.rand.RandomNumber(OTPLength)
	return code
}

func (s *otpService) IsExist(UserID, account string) bool {
	if accountOTP, err := s.otpRepo.Get(UserID); errors.Is(err, cache.ErrCacheMiss) {
		return false
	} else if err != nil {
		return true
	} else if accountOTP != nil {
		if account == accountOTP.Account && time.Since(accountOTP.CreatedAt) < OTPCodeRetryInterval {
			return true
		}
	}

	return false
}

func (s *otpService) StoreCodeWithTTL(userID, account, code string) error {
	var accountOTP cacheuser.AccountOTP
	accountOTP.Account = account
	accountOTP.Code = code
	createdAt, _ := time.Parse(time.RFC3339, s.clock.Now().Format(time.RFC3339))
	accountOTP.CreatedAt = createdAt

	if err := s.otpRepo.Set(userID, accountOTP, OTPCodeExistInterval); err != nil {
		return err
	}

	if err := s.otpRepo.SetTimes(userID, OTPCodeExistInterval); err != nil {
		return err
	}

	return nil
}

func (s *otpService) SendCode(account, code string) error {
	if strings.Contains(account, "@") {
		return s.sendEmail(account, code)
	}
	return s.sendPhone(account, code)
}

func (s *otpService) Verify(userID, account, code string) error {
	if times, err := s.otpRepo.GetTimes(userID); err != nil {
		plog.Warn("verify otp error: can no get otp times").Str("userID", userID).Send()
		return ErrVerifyFail
	} else if times >= OTPRequestLimit {
		plog.Warn("verify otp error: too many times").Str("userID", userID).Send()
		return ErrVerifyFail
	}

	if accountOTP, err := s.otpRepo.Get(userID); err != nil {
		return err
	} else {
		if accountOTP == nil {
			plog.Warn("verify otp error: can no get otp object").Str("userID", userID).Send()
			return ErrVerifyFail
		}

		if err := s.otpRepo.IncrementTimes(userID); err != nil {
			return err
		}

		if accountOTP.Account == account && accountOTP.Code == code {
			accountOTP.IsVerified = true
			if err := s.otpRepo.Set(userID, *accountOTP, OTPCodeExistInterval); err != nil {
				return err
			}
			return nil
		}

		return ErrVerifyFail
	}
}

func (s *otpService) sendEmail(email, code string) error {
	msg := fmt.Sprintf(mailOTPTemplate, code)
	return s.emailClient.SendMessage(email, otpSubject, msg)
}

func (s *otpService) sendPhone(phone, code string) error {
	msg := fmt.Sprintf(smsOTPTemplate, code)
	return s.smsClient.SendMessage(phone, msg)
}
