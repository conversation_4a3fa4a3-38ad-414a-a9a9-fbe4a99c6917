# KKTV Console web

## Better know before you start

1. Ignore 'npm audit' messages, it's fine.


## Before start

There is some tools should be installed before you start to develop

1. npm
```shell
brew install npm
```

2. nvm - version control of nodejs. Please install the `nvm` by following the [installation instruction](https://github.com/nvm-sh/nvm#install--update-script)

## Switching between local/test/prod ENV

In the vue.js project, we use [dotenv-expand](https://cli.vuejs.org/guide/mode-and-env.html) to management multi-env

you can make your own environment configuration by copying the `.env.sample`,

```bash
cp .env.sample .env.local

cat .env.local

#NODE_ENV=local
#VUE_APP_API_URL=https://test-api.kktv.com.tw
#VUE_APP_CLIENT_WEB_BASE_URL=https://test-web.kktv.com.tw
#VUE_APP_GOOGLE_OAUTH_URL='${VUE_APP_API_URL}/v3/auth/console/access_token?provider=google'
```

If you have multi env mode file, to tell vue.js app to use the config you want to specify, you can use `vue-cli-service serve --mode {env}`

```bash
# if you want to use the .env.local.test
 vue-cli-service serve --mode local.test
```

## Usage

Install required libraries
```bash
npm install
```

Start to develop
``` bash
# serve with hot reload at localhost:8080
npm run serve

# Specify to connect the Production API server with hot reload at localhost:8080
npm run serve:prod

# build for production with minification
npm run build

# build for .env.test configuration
npm run build:test

# run linter
npm run lint

# run unit tests
npm run test:unit

# run e2e tests
npm run test:e2e

```

For a detailed explanation on how things work, check out the [Vue CLI Guide](https://cli.vuejs.org/guide/).

## What's included

Within the download you'll find the following directories and files:

```
console/
├── public/              # pure static assets (directly copied)
│   └── index.html           # index.html template
├── src/                 # project root
│   ├── assets/                 # module assets (processed by webpack)
│   │   └── scss/               # user styles
│   ├── components/             # ui components
│   ├── containers/             # ui containers
│   ├── router/                 # routing 
│   ├── shared/                 # utils
│   ├── views/                  # ui views
│   ├── _nav.js                 # sidebar nav config
│   ├── App.vue                 # main app component
│   └── main.js                 # app entry file
├── test/
│   └── unit/            # unit tests
│   └── e2e/             # e2e tests
├── .eslintrc.js         # eslint config
├── .gitignore           # defaults for gitignore
├── .postcssrc.js        # postcss config
├── CHANGELOG.md
├── README.md
├── babel.config.js      # babel config
├── jest.config.js       # jest config
├── vue.config.js        # vue-cli config
├── LICENSE
└── package.json         # build scripts and dependencies
```


## Documentation

CoreUI's documentation, is hosted on our website [CoreUI](http://coreui.io/)

