<template>
  <b-row>
    <b-col cols="12" xl="12">
      <transition name="slide">
      <b-card no-header>
        <template slot="header">
          <b-button v-if="q!=''" @click="$router.go(-1)">&lt;</b-button>
          Family:  {{ q }}

        <b-button v-if="q!=''" @click="onOrder(q)" >
          <i class="fa" :class="{'fa-refresh': loading, 'fa-spin': loading}"></i>
          查訂單
        </b-button>
        </template>
          <b-row>
            <b-col sm="12">
            <form v-on:submit.prevent="onFetch">
            <b-form-group description="">
              <b-input-group>
                <b-form-input type="text" id="name" v-model="q"></b-form-input>
                <!-- Attach Right button -->
                <b-input-group-append>
                  <b-button variant="primary" @click="onFetch" >
                    <i class="fa" :class="{'fa-refresh': loading, 'fa-spin': loading}"></i>
                    Submit
                    </b-button>
                </b-input-group-append>

              </b-input-group>
            </b-form-group>
            </form>

            <b-alert :show="dismissCountDown"
                   dismissible
                   :variant="alertCss"
                   @dismissed="dismissCountDown=0"
                   @dismiss-count-down="countDownChanged">
                   {{ alertMsg}}
            </b-alert>
            <small v-html="'輸入 family_id 也就是家庭方案付款人的 user_id'"></small>
            </b-col>
          </b-row>

        <b-table v-if="item.family" striped small fixed responsive="sm" :items="items" :fields="fields">
          <template slot="value" slot-scope="data">
            <strong v-if="data.item.key=='next_order_date'">目前合約到期日 {{ new Date(data.item.value*1000).toLocaleString() }}</strong>
            <strong v-else-if="data.item.key=='family'">
              共享人員列表 (第一位為付款人)
              <li v-for="inner in data.item.value">
                <b-button @click="onUser(inner.id)">{{ inner.id }}</b-button><br />
                <small>{{ inner }}</small>
              </li>

            </strong>
            <strong v-else>{{data.item.value}}</strong>
          </template>
        </b-table>
        <h3 v-else>目前此 family ID 無任何有效合約，想了解更多，請查看此 family ID 訂單</h3>


      </b-card>
      </transition>
    </b-col>
  </b-row>
</template>

<script>
import { mapState } from 'vuex'
import api from '@/api'

export default {
  name: 'Family',
  props: {
    caption: {
      type: String,
      default: 'Order'
    },
    hover: {
      type: Boolean,
      default: true
    },
    striped: {
      type: Boolean,
      default: true
    },
    bordered: {
      type: Boolean,
      default: false
    },
    small: {
      type: Boolean,
      default: true
    },
    fixed: {
      type: Boolean,
      default: false
    }
  },
  data: () => {
    return {
      items: [],
      item: {},
      hadFamily: false,
      payment: {payment_type: '', user_id: ''},
      reason: '',
      showDetail: false,
      alertCss: '',
      alertMsg: '',
      dismissCountDown: 0,
      q: '',
      fields: [
        {key: 'key'},
        {key: 'value'}
      ],
      paymentHint: {
        'iap': 'IAP (iOS)',
        'iab': 'IAB (Android)',
        'credit_card': '信用卡',
        'cvs_code': '超商代碼繳費',
        'coupon': 'COUPON (兌換序號)',
        'telecom': '電信付款',
        'mod': 'MOD (中華電信)',
        'bandott': 'BANDOTT (便當機上盒)',
        'tstar': 'TSTAR (台灣之星)'
      },
      currentPage: 1,
      // perPage: 5,
      perPage: 0,
      totalRows: 0
    }
  },
  computed: {
    ...mapState(
      ['loading']
    )
  },
  mounted () {
    if (this.$route.query.q) {
      this.q = this.$route.query.q
      this.onFetch()
    }
  },
  methods: {
    getBadge (status) {
      // ok fail error cancel in_process refund
      var css
      switch (status) {
        case 'ok':
          css = 'success'
          break
        case 'fail':
          css = 'danger'
          break
        case 'error':
          css = 'warning'
          break
        case 'cancel':
          css = 'warning'
          break
        case 'in_process':
          css = 'outline-success'
          break
        case 'refund':
          css = 'secondary'
          break
      }
      return css
    },
    getRowCount (items) {
      return items.length
    },
    orderLink (id) {
      return `order/${id.toString()}`
    },
    rowClicked (item) {
      const orderLink = this.orderLink(item.id)
      this.$router.push({path: orderLink})
    },
    countDownChanged (dismissCountDown) {
      this.dismissCountDown = dismissCountDown
    },
    showAlert () {
      this.dismissCountDown = 5
    },
    onUser(user_id) {
      this.$router.push({ path: '/user/'+ user_id});
    },
    onOrder(user_id) {
      this.$router.push({ path: '/user/order', query: {q: user_id} });
    },
    onFetch () {
      this.$router.push({ path: '/user/family', query: {q: this.q} });
      api.request('get', '/v3/console/family?q='+this.q)
        .then((response) => {
          if (response.data && response.data.data && response.data.data.family) {
            this.item = response.data.data.family

            const familyDetails = this.item ? Object.entries(this.item) : [['id', 'Not fount']]
            this.items = familyDetails.map(([key, value]) => {
              console.log(key, value)
              return {key: key, value: value}
            })

            if (response.data.data.family.family_limit){
              this.hadFamily = true
            } else {
              this.hadFamily = false
            }
          }
        })
    }
  }
}
</script>

<style scoped>
.card-body >>> table > tbody > tr > td {
  cursor: pointer;
}
</style>
