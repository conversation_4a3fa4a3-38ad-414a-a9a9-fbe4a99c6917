<template>
  <b-row>
    <b-col cols="12" xl="12">
      <b-card no-header>
        <template slot="header">
          <b-button @click="$router.go(-1)">&lt;</b-button>
          Order: {{ item.id }}
          <b-button @click="onUser(item.user_id)">User</b-button>&nbsp;
          <b-button @click="onProduct(product)">Product</b-button>&nbsp;
          <b-button @click="onAuditLog($route.params.order_id)">Audit Log</b-button>
        </template>

        <b-row>
          <b-col sm="12">
            <b-form inline @submit.stop.prevent>
              <label class="mr-sm-2" for="inline-form-custom-select-pref">變更狀態為</label>
              <b-form-group style="width:10%" :state="statusState" invalid-feedback="*必選欄位" :tooltip="true">
                <b-form-select style="width:100%" :state="statusState" v-model="newStatus" :options="['ok', 'cancel', 'refund']"></b-form-select>
              </b-form-group>
              &nbsp;
              <label class="mr-sm-2" for="inline-form-custom-select-pref">異動原因</label>
              <b-form-group style="width:20%" :state="reasonState" invalid-feedback="*必填欄位" :tooltip="true">
                <b-form-input style="width:100%" type="text" v-model="reason" :state="reasonState" required></b-form-input>
              </b-form-group>
              &nbsp;
              <b-button variant="warning" class="fa" v-b-tooltip.hover title="異動訂單狀態為您新選取的選項，您必須有 user 的權限" @click="onUpdate()">
                <i class="fa" />
                更改訂單狀態
              </b-button>
            </b-form><div style="color:red;padding-top: 10px;"> * 如果要取消 IAB/IAP 訂單，請先確認用戶已退訂，謝謝！</div>

            <b-alert :show="dismissCountDown"
              dismissible
              :variant="alertCss"
              @dismissed="dismissCountDown=0"
              @dismiss-count-down="countDownChanged">
              {{ alertMsg }}
            </b-alert>
          </b-col>
        </b-row>
        <br/>
        <b-table striped small fixed responsive="sm" :items="items" :fields="fields">
          <template slot="value" slot-scope="data">
            <b-badge v-if="data.item.key == 'status'" :variant="getBadge(data.item.value)">{{data.item.value ? data.item.value : '未實現' }}</b-badge>
            <strong v-else >{{data.item.value}}</strong>
          </template>
        </b-table>
      </b-card>
    </b-col>
  </b-row>
</template>

<script>
import { mapState } from 'vuex'
import api from '@/api'

export default {
  name: 'OrderDetail',
  props: {
    caption: {
      type: String,
      default: 'Order Detail'
    },
    hover: {
      type: Boolean,
      default: true
    },
    striped: {
      type: Boolean,
      default: true
    },
    bordered: {
      type: Boolean,
      default: false
    },
    small: {
      type: Boolean,
      default: false
    },
    fixed: {
      type: Boolean,
      default: false
    }
  },
  data: () => {
    return {
      alertCss: '',
      alertMsg: '',
      dismissCountDown: 0,
      items: [],
      item: {},
      newStatus: null,
      reasonState: null,
      statusState: null,
      reason: '',
      fields: [
        {key: 'key'},
        {key: 'value'}
      ],
      product: ''
    }
  },
  computed: {
    ...mapState(
      ['loading']
    )
  },
  mounted () {
    console.log('OrderDetail Mounted')
    this.onFetch()
  },
  methods: {
    countDownChanged (dismissCountDown) {
      this.dismissCountDown = dismissCountDown
    },
    showAlert () {
      this.dismissCountDown = 5
    },
    getBadge (status) {
      // ok fail error cancel in_process refund
      var css
      switch (status) {
        case 'ok':
          css = 'success'
          break
        case 'fail':
          css = 'danger'
          break
        case 'error':
          css = 'warning'
          break
        case 'cancel':
          css = 'warning'
          break
        case 'in_process':
          css = 'outline-success'
          break
        case 'refund':
          css = 'secondary'
          break
      }
      return css
    },
    paymentHint (payment_type) {
      // ('iap', 'iAP'),
      // ('iab', 'iab'),
      // ('credit_card', u'信用卡'),
      // ('cvs_code', u'超商代碼繳費'),
      // ('coupon', 'Coupon'),
      // ('telecom', u'電信付款'),
      // ('mod', 'MOD'),
      // ('bandott', 'bandott'),
      // ('tstar', 'tstar'),
      var hint
      switch (payment_type) {
        case 'iab':
          hint = 'Android'
          break
        case 'iap':
          hint = 'Apple'
          break
        case 'credit_card':
          hint = '信用卡'
          break
        case 'cvs_code':
          hint = '超商代碼繳費'
          break
        case 'coupon':
          hint = 'Coupon'
          break
        case 'mod':
          hint = 'Mod'
          break
        case 'bandott':
          hint = 'Bandott 便當'
          break
        case 'tstar':
          hint = '台灣之星'
          break
        case 'telecom':
          hint = '電信付款'
          break
        default:
          hint = '未知'
      }
      return hint
    },
    onUser (user_id) {
      this.$router.push({ path: '/user/' + user_id});
    },
    onProduct (product) {
      this.$router.push({ path: '/product/', query: {q: product} });
    },
    onAuditLog(user_id) {
      this.$router.push({ path: '/user/order/audit_log/' + this.$route.params.order_id});
    },
    onUpdate () {
      this.resetFormStates()
      if (!this.item.id) {
        return
      }
      if (!this.newStatus) {
        this.statusState = 'invalid'
        return
      }
      if (!this.reason) {
        this.reasonState = 'invalid'
        return
      }
      var putUrl = `/v3/console/order`
      var obj = {
        id: this.item.id,
        status: this.newStatus,
        reason: this.reason
      }
      api.request('put', putUrl, obj)
        .then((response) => {
          console.log('OK')
          this.alertCss = 'success'
          this.alertMsg = '儲存成功'
          this.showAlert()
          this.onFetch()
        }).catch(error => {
          if (error.response && error.response.data && error.response.data.status && error.response.data.status.message) {
            this.alertMsg =  error.response.data.status.message
          }
          this.alertCss = 'warning'
          this.showAlert()
        })
    },
    onFetch () {
      var id = this.$route.params.order_id
      this.$router.push({ path: '/user/order/'+id });
      api.request('get', '/v3/console/order?q='+id)
      .then((response) => {
        if (response.data && response.data.data && response.data.data.orders) {
          const ordersData = response.data.data.orders
          const order = ordersData.find( order => order.id.toString() === id)
          this.item = order

          const orderDetails = order ? Object.entries(order) : [['id', 'Not fount']]
          const dateKey = [
            'start_date', 'end_date', 'order_date', 'created_at', 'realized_at', 'canceled_at'
          ]
          this.items = orderDetails.map(([key, value]) => {
            if (key == 'product_id') {
              api.request('get', '/v3/console/product')
                .then((response) => {
                  if (response.data && response.data.data && response.data.data.products) {
                    const productData = response.data.data.products
                    const product = productData.find(product => product.id === value.toString())
                    this.product = product.name
                  }
                })
            } else if (key === 'payment_type') {
              value = value + " " + this.paymentHint(value)
            } else if (key === 'status') {
              value = value ? value : '未實現'
            }

            if (dateKey.includes(key) && value != null) {
              value = new Date(value).toLocaleString()
            }
            return {key: key, value: value}
          })
        } else {
          this.alertMsg = '沒搜尋到任何結果'
          this.showAlert()
        }
      })
    },
    resetFormStates() {
      this.reasonState = null
      this.statusState = null
    },
  }
}
</script>

<style scoped>
.card-body >>> table > tbody > tr > td {
  cursor: pointer;
}
</style>
