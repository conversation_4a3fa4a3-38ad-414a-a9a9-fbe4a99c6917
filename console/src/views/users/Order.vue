<template>
  <b-row>
    <b-col cols="12" xl="12">
      <transition name="slide">
      <b-card no-header>
        <template slot="header">
          <b-button v-if="q!=''" @click="$router.go(-1)">&lt;</b-button>
          Order:  {{ q }}
        </template>
          <b-row>
            <b-col sm="12">
            <form v-on:submit.prevent="onFetch">
            <b-form-group description="">
              <b-input-group>
                <b-form-input type="text" id="name" v-model="q"></b-form-input>
                <!-- Attach Right button -->
                <b-input-group-append>
                  <b-button variant="primary" @click="onFetch" >
                    <i class="fa" :class="{'fa-refresh': loading, 'fa-spin': loading}"></i>
                    Submit
                    </b-button>
                </b-input-group-append>

              </b-input-group>
            </b-form-group>
            </form>

            <b-alert :show="dismissCountDown"
                   dismissible
                   :variant="alertCss"
                   @dismissed="dismissCountDown=0"
                   @dismiss-count-down="countDownChanged">
                   {{ alertMsg}}
            </b-alert>

            <small v-html="'輸入 user_id, order_id, <span style=\'font-weight: bold;color: #e67300;\'>超商繳費代碼 或是 IAB GPA 訂單號 </span>, 單筆查詢，點擊可看 Order 詳細資料；或您也可以由 User 的細節頁面點擊進入該 User 所有 Order 列表'"></small>
            </b-col>
          </b-row>

          <b-row v-if="payment.user_id">
            <b-col sm="12">

            <form v-if="unSubscribeAvaliable" v-on:submit.prevent="onUnSubscribe">
              <b-input-group>
              付款方式&nbsp;<b>{{ paymentHint[payment.payment_type] || '未知'}}</b>&nbsp;<b v-if="payment.payment_type == 'telecom'">{{ mpIdHint[payment.telecom_mp_id] || '未知 ' + payment.telecom_mp_id}}</b>
                <b-form-input type="text" id="reason" v-model="reason" placeholder="如要退租，請輸入退租理由，謝謝"></b-form-input>
                <b-input-group-append>
                  <b-button variant="primary" @click="onUnSubscribe" >
                    <i class="fa" :class="{'fa-refresh': loading, 'fa-spin': loading}"></i>
                    取消訂閱
                    </b-button>
                </b-input-group-append>
              </b-input-group>
            </form>
            </b-col>
          </b-row>

        <b-form-checkbox v-model="showDetail" value="checked" unchecked-value="">顯示詳細資料</b-form-checkbox>

        <b-table v-if="items" :hover="hover" :striped="striped" :bordered="bordered"
        :small="small" :fixed="fixed" responsive="sm" :items="items" :fields="fields"
        :current-page="currentPage" :per-page="perPage" v-b-tooltip.hover title="點擊訂單編號 ID 可以看細節喔" >
          <template slot="id" slot-scope="data">
            <b-button  variant="primary" @click="rowClicked(data.item)">{{data.item.id}}</b-button>&nbsp;
            <b-button v-if="data.item.invoice" variant="primary" @click="onInvoice(data.item.id)">發票</b-button>
            <b-card v-if="data.item.invoiceItem">
              <b>KKTV, Inc.</b>
              <li>發票號碼: {{ data.item.invoiceItem.invoice_number}}</li>
              <li>隨機碼: {{ data.item.invoiceItem.random_number}}</li>
              <li>開立時間: {{ new Date(data.item.invoiceItem.created_at).toLocaleString() }}</li>
              <li>金額: {{ data.item.invoiceItem.total_amount}}</li>
            </b-card>
            <span v-if="showDetail">
            <br />
            {{ data.item.user_id }} <br />
            <textarea v-if="showDetail && data.item.info" cols="45" rows="5" readonly v-model="data.item.info"></textarea>
            </span>
          </template>
          <template slot="payment_type" slot-scope="data">
            {{ paymentHint[data.item.payment_type] || '未知' }}
            {{ data.item.external_order_id }}
            <br />
            {{ data.item.item_name }}
          </template>
          <template slot="order_date" slot-scope="data">
            {{ new Date(data.item.order_date).toLocaleString() }}
          </template>
          <template slot="duration" slot-scope="data">
            {{ new Date(data.item.start_date).toLocaleDateString() }} 至<br />
            {{ new Date(data.item.end_date).toLocaleDateString() }}
          </template>
          <template slot="created_at" slot-scope="data">
            {{ new Date(data.item.created_at).toLocaleString() }} (created_at)
            <span v-if="data.item.canceled_at"><br />{{new Date(data.item.canceled_at).toLocaleString()}} (canceled_at)</span>
            <span v-if="data.item.realized_at"><br />{{new Date(data.item.realized_at).toLocaleString()}} (realized_at)</span>
          </template>
          <template slot="price" slot-scope="data">
            {{data.item.price}} <br />
            {{data.item.price_no_tax}} (未稅) <br />
            {{ data.item.tax_rate }}%(稅率)
          </template>
          <template slot="status" slot-scope="data">
            <h4>
            <b-badge :variant="getBadge(data.item.status)">{{data.item.status ? data.item.status : '未實現' }}</b-badge>
             <br /><b-badge :variant="getBadge('error')">{{data.item.in_grace > 0 ? '寬限期' : '' }}</b-badge>
            </h4>
          </template>
        </b-table>
        <!-- <nav>
          <b-pagination size="sm" :total-rows="getRowCount(items)" :per-page="perPage" v-model="currentPage" prev-text="Prev" next-text="Next" hide-goto-end-buttons/>
        </nav> -->
      </b-card>
      </transition>
    </b-col>
  </b-row>
</template>

<script>
import { mapState } from 'vuex'
import api from '@/api'

export default {
  name: 'Order',
  props: {
    caption: {
      type: String,
      default: 'Order'
    },
    hover: {
      type: Boolean,
      default: true
    },
    striped: {
      type: Boolean,
      default: true
    },
    bordered: {
      type: Boolean,
      default: false
    },
    small: {
      type: Boolean,
      default: true
    },
    fixed: {
      type: Boolean,
      default: false
    }
  },
  data: () => {
    return {
      items: [],
      payment: {payment_type: '', user_id: ''},
      unSubscribeAvaliable: false,
      reason: '',
      showDetail: false,
      alertCss: '',
      alertMsg: '',
      dismissCountDown: 0,
      q: '',
      fields: [
        {key: 'id', label: '訂單編號'},
        {key: 'payment_type', label: '付款方式'},
        {key: 'order_date', label: '訂單時間'},
        {key: 'duration', label: '付費週期'},
        {key: 'created_at', label: '時間'},
        {key: 'price', label: '價格'},
        {key: 'status', label: '狀態'}
      ],
      unSubDict: {
        'telecom': true,
        'credit_card': true
      },
      paymentHint: {
        'iap': 'IAP (iOS)',
        'iab': 'IAB (Android)',
        'credit_card': '信用卡',
        'cvs_code': '超商代碼繳費',
        'coupon': 'COUPON (兌換序號)',
        'telecom': '電信付款',
        'mod': 'MOD (中華電信)',
        'bandott': 'BANDOTT (便當機上盒)',
        'tstar': 'TSTAR (台灣之星)'
      },
      mpIdHint: {
        'CYC_TCC'   : '台哥大（月租）',
        'CYC_FET'   : '遠傳（月租）',
        'CYC_OTP839': '中華 839（月租）',
        'CYC_OTPCHT': '中華市話（月租）',
        'CYC_HINET' : 'HiNet（月租）',
        'CYC_TSTAR' : '台灣之星（月租）', // should nerver have this
        'TCC'       : '台哥大',
        'FET'       : '遠傳',
        'FETCSP'    : '遠傳預付卡',
        'OTP839'    : '中華839',
        'OTPCHT'    : '中華市話',
        'APT'       : '亞太',
        'VIBO'      : '威寶',
        'HINET'     : 'HiNet',
        'SONET'     : 'net',
        'ESUN'      : '支付寶',
        'BILL99'    : '快錢',
      },
      currentPage: 1,
      // perPage: 5,
      perPage: 0,
      totalRows: 0
    }
  },
  computed: {
    ...mapState(
      ['loading']
    )
  },
  mounted () {
    if (this.$route.query.q) {
      this.q = this.$route.query.q
      this.onFetch()
    }
  },
  methods: {
    getBadge (status) {
      // ok fail error cancel in_process refund
      var css
      switch (status) {
        case 'ok':
          css = 'success'
          break
        case 'fail':
          css = 'danger'
          break
        case 'error':
          css = 'warning'
          break
        case 'cancel':
          css = 'warning'
          break
        case 'in_process':
          css = 'outline-success'
          break
        case 'refund':
          css = 'secondary'
          break
      }
      return css
    },
    getRowCount (items) {
      return items.length
    },
    orderLink (id) {
      return `order/${id.toString()}`
    },
    rowClicked (item) {
      const orderLink = this.orderLink(item.id)
      this.$router.push({path: orderLink})
    },
    countDownChanged (dismissCountDown) {
      this.dismissCountDown = dismissCountDown
    },
    showAlert () {
      this.dismissCountDown = 5
    },
    onInvoice (orderID) {
      var url = '/v3/console/invoice?q='+ orderID
      api.request('get', url)
        .then((response) => {
          if (response.data && response.data.data &&
          response.data.data.invoice) {
            // glue data
            var invoice = response.data.data.invoice
            for (var i=0; i < this.items.length; i++){
              if (this.items[i].id == invoice.order_id) {
                this.items[i].invoiceItem = invoice
                this.$set(this.items, i, this.items[i])
                break
              }
            }
          }
        })
    },
    onUnSubscribe () {
      var that = this

      if (!this.reason) {
          this.alertCss = 'warning'
          this.alertMsg = '請填寫退租理由'
          this.showAlert()
          return
      }
      console.log('unsubscribe')
      var userID = this.payment.user_id
      var params = {userID: userID, reason: this.reason}
      var queryString = Object.keys(params).map(function(key) { return key + '=' + params[key] }).join('&')
      console.log(userID, queryString)
      api.request('post', '/v3/console/order/unsubscribe?'+ queryString)
        .then(() => {
          that.alertCss = 'success'
          that.alertMsg = "取消訂閱成功"
          that.showAlert()
          that.onFetch()
        }).catch( (error) => {
          that.alertCss = 'danger'
          that.alertMsg = "取消訂閱失敗"
          if (error.response.data.status.message) {
            that.alertMsg = "取消訂閱失敗 " + error.response.data.status.message
          }
          that.showAlert()
        })
    },
    onFetch () {
      this.$router.push({ path: '/user/order', query: {q: this.q} });
      api.request('get', '/v3/console/order?q='+this.q)
        .then((response) => {
          if (response.data && response.data.data &&
          response.data.data.orders) {
            this.items = response.data.data.orders
            if (response.data.data.payment) {
              this.payment = response.data.data.payment
              // only payment_type == 'telecom' && last order is null
              var lastOrder = response.data.data.orders.length > 0 ? response.data.data.orders[0].status == null  : null
              if (this.unSubDict[this.payment.payment_type] && lastOrder) {
                this.unSubscribeAvaliable = true
              } else {
                this.unSubscribeAvaliable = false
              }
            }
          } else {
            this.items = []
          }
        })
    }
  }
}
</script>

<style scoped>
.card-body >>> table > tbody > tr > td {
  cursor: pointer;
}
</style>
