<template>
  <b-row>
    <b-col cols="12" xl="12">
      <transition name="slide">

      <b-card no-header>
        <template slot="header">
          <b-button @click="goBack">&lt;</b-button>&nbsp;
          User id:  {{ $route.params.id }}
        </template>
          <b-row>
            <b-col sm="12">
            <form v-on:submit.prevent="onFetch">
            <b-form-group>
              <b-input-group>
                <!-- <b-form-input type="text" id="name" placeholder="Enter user_id" v-model="q"></b-form-input> -->
                <!-- Attach Right button -->
                <!-- <b-input-group-append>
                  <b-button variant="primary" @click="onFetch" >
                    <i class="fa" :class="{'fa-refresh': loading, 'fa-spin': loading}"></i>
                    Submit
                    </b-button>
                </b-input-group-append> -->

              </b-input-group>

              <b-alert :show="dismissCountDown"
                    dismissible
                    variant="warning"
                    @dismissed="dismissCountDown=0"
                    @dismiss-count-down="countDownChanged">
              這位大大，您的權限不足
              </b-alert>

            </b-form-group>
            </form>

            </b-col>
          </b-row>

        <b-table v-if="items" :hover="hover" :striped="striped" :bordered="bordered"
        :small="small" :fixed="fixed" responsive="sm" :items="items" :fields="fields"
        :current-page="currentPage" :per-page="perPage">
          <template slot="title_id" slot-scope="data">
            {{ ('00' + (data.index+1)).slice(-2) }}
            <b-button @click="onDelete(data.item)"><i class="fa fa-trash"></i> Delete</b-button>
            &nbsp;
            <strong>{{data.item}}</strong>
            &nbsp;
            <span v-if="titleMap[data.item]">
              <img v-if="titleMap[data.item].cover" :src="titleMap[data.item].cover" width="50" />
              {{titleMap[data.item].name }}
            </span>
          </template>
        </b-table>
      </b-card>
      </transition>
    </b-col>
  </b-row>
</template>

<script>
import { mapState } from 'vuex'
import api from '@/api'

export default {
  name: 'Favirite',
  props: {
    caption: {
      type: String,
      default: 'Favorite Tiles'
    },
    hover: {
      type: Boolean,
      default: true
    },
    striped: {
      type: Boolean,
      default: true
    },
    bordered: {
      type: Boolean,
      default: false
    },
    small: {
      type: Boolean,
      default: false
    },
    fixed: {
      type: Boolean,
      default: false
    }
  },
  data: () => {
    return {
      items: [],
      titleMap: {},
      dismissCountDown: 0,
      q: '',
      fields: [
        {key: 'title_id'},
      ],
      currentPage: 1,
      // perPage: 5,
      perPage: 0,
      totalRows: 0
    }
  },
  computed: {
    ...mapState(
      ['loading']
    )
  },
  mounted () {
    this.onFetch()
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    getBadge (status) {
      return status === 'Active' ? 'success'
        : status === 'Inactive' ? 'secondary'
          : status === 'Pending' ? 'warning'
            : status === 'Banned' ? 'danger' : 'primary'
    },
    getRowCount (items) {
      return items.length
    },
    countDownChanged (dismissCountDown) {
      this.dismissCountDown = dismissCountDown
    },
    showAlert () {
      this.dismissCountDown = 5
    },
    onDelete (titleid) {
      const id = this.$route.params.id
      var deleteURL = '/v3/console/user/favorite/'+id + '/' + titleid
      console.log(deleteURL)
      api.request('delete', deleteURL)
        .then((response) => {
          if (response.data && response.data.data &&
          response.data.data.titles) {
            this.items = response.data.data.titles
          } else {
            this.items = []
          }
        }).catch(error => {
          this.showAlert()
        })
    },
    onTitleMap () {
      var q = ""
      q = this.items.join(" ")
      api.request('get', '/v3/console/titlehint?q='+q)
        .then((response) => {
          if (response.data && response.data.data &&
          response.data.data.titles) {
            var items = response.data.data.titles
            for (var i=0; i < items.length; i++){
                var item = items[i]
                this.$set(this.titleMap, item.id, item)
            }
          }
        })

    },
    onFetch () {
      const id = this.$route.params.id
      api.request('get', '/v3/console/user/favorite/'+id)
        .then((response) => {
          if (response.data && response.data.data &&
          response.data.data.titles) {
            this.items = response.data.data.titles
            this.onTitleMap()
          }
        })
    }

  }
}
</script>

<style scoped>
.card-body >>> table > tbody > tr > td {
  cursor: pointer;
}
</style>
