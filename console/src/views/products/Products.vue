<template>
  <b-row>
    <b-col cols="12" xl="12">
      <transition name="slide">
      <b-card :header="caption">
          <b-row>
            <b-col sm="12">
            <form v-on:submit.prevent="onFetch">
            <b-form-group description="">
              <b-input-group>
                <b-form-input type="text" id="name" v-model="q" placeholder="輸入名稱或是代碼以在介面上過濾"></b-form-input>
                <!-- Attach Right button -->
                <b-input-group-append>
                  <b-button variant="primary" @click="onEdit({})">新增</b-button>
                </b-input-group-append>
              </b-input-group>
            </b-form-group>
            </form>
            <small v-html="'輸入 name or payment type ，預設只顯示啟用中的產品，<span style=\'font-weight: bold;color: #e67300;\'>要顯示全部，請選顯示全部</span>'"></small>
            <br />
            <b-form-checkbox v-model="showAll" value="checked" unchecked-value="false" inline>顯示全部(包含未啟用)</b-form-checkbox>
            <b-form-checkbox v-model="isCampaign" value="checked" unchecked-value="false" inline>只顯示campaign</b-form-checkbox>
            <b-alert :show="dismissCountDown"
                   dismissible
                   :variant="alertCss"
                   @dismissed="dismissCountDown=0"
                   @dismiss-count-down="countDownChanged">
                   {{ alertMsg}}
            </b-alert>


            </b-col>
          </b-row>

        <b-table v-if="items" :hover="hover" :striped="striped" :bordered="bordered" :small="small" :fixed="fixed" responsive="sm" :items="filterItems" :fields="fields" :current-page="currentPage" :per-page="perPage">
          <template slot="action" slot-scope="data">
            <b-button  @click="onEdit(data.item)"><i class="fa fa-edit"></i></b-button>
          </template>
          <template slot="item_name" slot-scope="data">
            {{data.item.item_name}} <br />
            {{data.item.name}}<br />
            id: {{ data.item.id }}
            <br v-if='data.item.external_product_id' />{{data.item.external_product_id}}
          </template>
          <template slot="price" slot-scope="data">
            {{data.item.price}} <br />
            (未稅價格 {{data.item.price_no_tax}})
          </template>
          <template slot="tax_rate" slot-scope="data">
            {{data.item.tax_rate}}%
          </template>
          <template slot="duration" slot-scope="data">
            {{data.item.duration}}
            <br v-if='data.item.free_duration' />免費 {{ data.item.free_duration}}
          </template>
          <template slot="auto_renew" slot-scope="data">
            <b-badge :variant="getBadge(data.item.auto_renew)">{{data.item.auto_renew}}</b-badge>
          </template>
          <template slot="as_subscribe" slot-scope="data">
            <b-badge :variant="getBadge(data.item.as_subscribe)">{{data.item.as_subscribe}}</b-badge>
          </template>
          <template slot="active" slot-scope="data">
            <b-badge :variant="getBadge(data.item.active)">{{data.item.active}}</b-badge>
          </template>
        </b-table>
        <!-- <nav>
          <b-pagination size="sm" :total-rows="getRowCount(items)" :per-page="perPage" v-model="currentPage" prev-text="Prev" next-text="Next" hide-goto-end-buttons/>
        </nav> -->
      </b-card>
      </transition>
    </b-col>
    <b-modal v-model="showDetail" ref="formModal"  size="xl" :title="detailTitle" @ok="onOK" @cancel="onCancel">
        <b-form-group>
          <b-form-group label="付款方式" description="請先選取付款方式，才會自動計算手續費">
            <b-form-select v-model="detailObj.payment_type" :options="paymentOptions" :state="formState.payment_type" @change="onChangePaymentType"></b-form-select>
            <b-form-invalid-feedback :state="formState.payment_type">
              請選擇payment_type
            </b-form-invalid-feedback>
          </b-form-group>

          <b-form-group label="產品代號" description="必須是獨一無二的代碼，不可以有特殊字元，請用因英文數字半形，不可以和其他產品代號重複">
            <b-form-input v-model="detailObj.name" type="text"  :state="formState.name" v-b-tooltip.hover title="必須是獨一無二的，不可以重複"></b-form-input>
            <b-form-invalid-feedback :state="formState.name">
              必填欄位
            </b-form-invalid-feedback>
          </b-form-group>

          <b-row>
            <b-col>
              <b-form-group label="價格">
                <b-form-input v-model="detailObj.price" type="number" :state="formState.price" @input="onChangePrice" @change="onChangePrice"></b-form-input>
                <b-form-invalid-feedback :state="formState.price">
                  請設定值
                </b-form-invalid-feedback>
              </b-form-group>
            </b-col>

            <b-col v-if="detailObj.payment_type !== 'iap' && detailObj.payment_type !== 'iab'">
              <b-form-group label="稅率" description="選取付款方式後，會自動幫您計算，最後請確認">
                <b-form-input v-model="detailObj.tax_rate" type="number" @change="onChangeTaxRate"></b-form-input>
              </b-form-group>
            </b-col>

            <b-col>
              <b-form-group label="未稅價格" description="輸入「價格」與「稅率」後，系統會自動幫您計算，最後請確認。( **IAB/IAP 因稅率為 0，請直接輸入固定的「未稅價格」 )">
                <b-form-input v-model="detailObj.price_no_tax" :state="formState.price_no_tax" type="number"></b-form-input>
                <b-form-invalid-feedback :state="formState.price_no_tax">
                  請設定值
                </b-form-invalid-feedback>
              </b-form-group>
            </b-col>

            <b-col v-if="detailObj.payment_type !== 'iap' && detailObj.payment_type !== 'iab'">
              <b-form-group label="手續費率(%)" description="請輸入手續費率(%)">
                <b-form-input v-model="detailObj.fee_rate" type="number" step="0.01" min="0" @change="onChangeFeeRate"></b-form-input>
              </b-form-group>
            </b-col>

            <b-col>
              <b-form-group label="手續費" description="輸入「價格」與「手續費率」後，系統會自動幫您計算，最後請確認。( **IAB/IAP 因手續費率按方案浮動，請直接輸入固定的「手續費」 )">
                <div v-if="detailObj.payment_type !== 'iap' && detailObj.payment_type !== 'iab'" v-text="detailObj.fee"></div>
                <b-form-input v-if="detailObj.payment_type == 'iap' || detailObj.payment_type == 'iab'" v-model="detailObj.fee" type="number"></b-form-input>
              </b-form-group>
            </b-col>
          </b-row>

          <b-row>
          </b-row>

          <b-form-group label="第三方廠商產品代碼">
            <b-form-input v-model="detailObj.external_product_id" type="text" v-b-tooltip.hover title="這個代碼用於外部廠商配合需求"></b-form-input>
          </b-form-group>

          <b-form-group label="付費週期">
            <b-row class="my-1">
              <b-col sm="6">
                <b-form-input v-model="detailObj.duration_value" type="number"></b-form-input>
                  <b-form-invalid-feedback :state="formState.duration_unit">
                    沒有設定付費週期
                  </b-form-invalid-feedback>
              </b-col>
              <b-col sm="6">
                <b-form-select v-model="detailObj.duration_unit" :state="formState.duration_unit" :options="['', 'day', 'mon', 'year']"></b-form-select>
                  <b-form-invalid-feedback :state="formState.duration_unit">
                    沒有設定付費週期
                  </b-form-invalid-feedback>
              </b-col>
            </b-row>
          </b-form-group>

          <b-form-group label="免費週期" description="免費週期，只有在首購優惠有勾選下，才有效">
            <b-row class="my-1">
              <b-col sm="6">
                <b-form-input v-model="detailObj.free_duration_value" :state="formState.duration" type="number"></b-form-input>
                  <b-form-invalid-feedback :state="formState.duration">
                    不能同時設定免費及優惠週期
                  </b-form-invalid-feedback>
              </b-col>
              <b-col sm="6">
                <b-form-select v-model="detailObj.free_duration_unit" :state="formState.free_duration_unit" :options="['', 'day', 'mon', 'year']"></b-form-select>
                  <b-form-invalid-feedback :state="formState.free_duration_unit">
                    沒有設定免費週期的時間
                  </b-form-invalid-feedback>
              </b-col>
            </b-row>
          </b-form-group>

          <b-card title="優惠期間" bg-variant="light">
            <b-form-group label="優惠週期" description="和免費週期互斥，只能設定其中一項，優惠週期的時間單位如果和付費週期單位相同時，必須可以整除，ex: 付費週期是 2 mons ，這時候優惠週期如果也是用， 月 mons，必須是 2 mons 的倍數，你可以設定相同的 2 mons 或是 4 mons ，會依照倍數，去產生優惠價格的訂單，或是，優惠週期和付費週期的時間單位不同，系統，就產生單筆，優惠訂單">
              <b-row class="my-1">
                <b-col sm="6">
                  <b-form-input v-model="detailObj.discount_duration_value" :state="formState.duration" type="number"></b-form-input>
                    <b-form-invalid-feedback :state="formState.duration">
                      不能同時設定免費及優惠週期
                    </b-form-invalid-feedback>
                </b-col>
                <b-col sm="6">
                  <b-form-select v-model="detailObj.discount_duration_unit" :state="formState.discount_duration_unit" :options="['', 'day', 'mon', 'year']"></b-form-select>
                    <b-form-invalid-feedback :state="formState.discount_duration_unit">
                      沒有設定優惠週期的時間
                    </b-form-invalid-feedback>
                </b-col>
              </b-row>
            </b-form-group>

            <b-row>
              <b-col>
                <b-form-group label="優惠價格">
                  <b-form-input v-model="detailObj.discount_price" :state="formState.discount_price" type="number" @change="onChangeDiscountPrice"></b-form-input>
                        <b-form-invalid-feedback :state="formState.discount_price">
                          沒有設定優惠週期的價格
                        </b-form-invalid-feedback>
                </b-form-group>
              </b-col>

              <b-col>
                <b-form-group label="優惠價(未稅)" description="輸入「優惠價格」與「稅率」後，系統會自動幫您計算，最後請確認。( **IAB/IAP 因稅率為 0，請直接輸入固定的「優惠價(未稅)」 )">
                  <b-form-input v-model="detailObj.discount_price_no_tax" :state="formState.discount_price_no_tax" type="number"></b-form-input>
                  <b-form-invalid-feedback :state="formState.discount_price_no_tax">
                    請設定值
                  </b-form-invalid-feedback>
                </b-form-group>
              </b-col>

              <b-col>
                <b-form-group label="優惠價手續費" description="輸入「優惠價格」與「手續費率」後，系統會自動幫您計算，最後請確認。( **IAB/IAP 因手續費率按方案浮動，請直接輸入固定的「優惠價手續費」 )">
                  <b-form-input v-model="detailObj.discount_fee" type="number"></b-form-input>
                </b-form-group>
              </b-col>
            </b-row>
          </b-card>

          <b-card title="家庭方案" v-if="detailObj.payment_type=='credit_card'" bg-variant="light">

            <b-form-group v-if="detailObj.payment_type=='credit_card'" label="家庭方案人數" description="如非家庭方案請留空。( **方案上架後請不要任意修改人數，每次家庭群組新增移除人數都會依據此人數設定 )">
              <b-form-input v-model="detailObj.family" type="number">
              </b-form-input>
            </b-form-group>

          </b-card>

          <b-card v-if="detailObj.payment_type=='credit_card'" title="信用卡購買限制" bg-variant="light">
            <b-form-group label="使用信用卡 Prefix 限制" description="使用信用卡限制前綴號碼，每輸入一組 卡號前綴 請換行，不設定即無信用卡使用限制">
              <b-form-textarea  v-model="detailObj.credit_card_prefix" placeholder="" rows="3" max-rows="6" >
              </b-form-textarea>

            </b-form-group>
            <b-card title="資格限制">
              <b-form-group id="qualification" label="資格限制方式" description="請選擇方案的資格限制方式">
                <b-form-select v-model="qualification" :options="qualifications">
                </b-form-select>
              </b-form-group>

              <b-form-group v-show="qualification=='email_domain'" id="qualification_email_domain_whitelist" label="Email Domain 白名單" description="系統會驗證用戶於付款畫面上方的「購買驗證用 Email」欄位所輸入的 Email 的 Domain Name 來判斷用戶購買資格 (可以使用 Domain 後綴匹配，例如：.edu.tw，或使用完整 Domain 匹配，例如：ntu.edu.tw)，每輸入一組 Domain 請換行，不設定即無 Email Domain 限制">
                <b-form-textarea  v-model="detailObj.qualification_email_domain_whitelist" placeholder="" rows="3" max-rows="6" >
                </b-form-textarea>
              </b-form-group>

              <b-tooltip v-show="qualification=='email_domain'" target="qualification_email_domain_whitelist" placement="right" triggers="hover">
                <b-img src="img/tooltip/product-tooltip-1.png" fluid></b-img>
              </b-tooltip>

              <b-form-group v-show="qualification" id="qualification_description" label="資格限制描述" description="此產品若需要用戶於付款畫面輸入此設定頁中「資格限制」內所設定的各種驗證資訊來驗證用戶是否可以購買(例如：學生方案驗證 Email Domain)，則付款畫面會在資格驗證的輸入欄位下方顯示「資格限制描述』。 **編寫描述時請用斷行隔開段落，超連結請參考範例格式 [Abc](https://google.com)">
                <b-form-textarea  v-model="detailObj.bundle.qualification_description" placeholder="" rows="3" max-rows="6"></b-form-textarea>
              </b-form-group>

              <b-tooltip v-show="qualification" target="qualification_description" placement="right" triggers="hover">
                <b-img src="img/tooltip/product-tooltip-2.png" fluid></b-img>
              </b-tooltip>
            </b-card>
            <b-form-group label="是否需要輸入活動 RedeemCode 才可購買" description="如有勾選，在 Redeem 系統下，Redeem Group 必須有此 Product ID 的 Redeem Code">
              <b-form-checkbox v-model="detailObj.bundle.needRedeemCode">需要輸入活動 RedeemCode 才可購買</b-form-checkbox>
            </b-form-group>
          </b-card>

          <b-row>
            <b-col>
              <b-form-group label="品項名稱" description="請務必填寫">
                <b-form-input v-model="detailObj.item_name" :state="formState.item_name" type="text"></b-form-input>
                  <b-form-invalid-feedback :state="formState.item_name">
                    沒有設定品項名稱
                  </b-form-invalid-feedback>
              </b-form-group>
            </b-col>

            <b-col>
              <b-form-group label="品項單位" description="開發票的需求，請務必填寫">
                <b-form-input v-model="detailObj.item_unit" v-b-tooltip.hover title="最多兩個字" maxlength="2" :state="formState.item_unit" type="text" required></b-form-input>
                  <b-form-invalid-feedback :state="formState.item_unit">
                    沒有設定品項單位
                  </b-form-invalid-feedback>
              </b-form-group>
            </b-col>
          </b-row>


          <b-row>
            <b-col>
              <b-form-group label="國家">
                <b-form-select v-model="detailObj.country" :options="['TW']"></b-form-select>
              </b-form-group>
            </b-col>

            <b-col>
              <b-form-group label="幣別">
                <b-form-select v-model="detailObj.currency" :options="['NTD']"></b-form-select>
              </b-form-group>
            </b-col>
          </b-row>

          <b-form-group label="產品類別">
              <b-form-select v-model="detailObj.category" :options="['Organic','Channel','MultiPlan', 'Other']"></b-form-select>
              <b-form-invalid-feedback :state="formState.category">
                沒有設定產品類別
              </b-form-invalid-feedback>
          </b-form-group>

          <b-card v-if="detailObj.payment_type == 'credit_card'" title="信用卡付款或活動專屬資訊(campaign)" bg-variant="light">

            <b-row class="my-1">
              <b-col sm="6">
                <b-form-group label="Description" description="敘述，請用斷行隔開段落">
                  <b-form-textarea  v-model="detailObj.bundle.description" placeholder="" rows="3" max-rows="6"></b-form-textarea>
                </b-form-group>
              </b-col>
              <b-col sm="6">
                <b-form-group label="Package Description" description="產品活動敘述，請用斷行隔開段落，超連結請參考範例格式  [Abc](https://google.com)">
                  <b-form-textarea  v-model="detailObj.bundle.package_description" placeholder="" rows="3" max-rows="6"></b-form-textarea>
                </b-form-group>
              </b-col>
            </b-row>

            <b-row class="my-1">
              <b-col sm="3">
                <b-form-group label="Title" description="Title">
                  <b-form-input v-model="detailObj.bundle.title" type="text" required></b-form-input>
                </b-form-group>
              </b-col>
              <b-col sm="3">
                <b-form-group label="Sub Title" description="Sub Title">
                  <b-form-input v-model="detailObj.bundle.subtitle" type="text" required></b-form-input>
                </b-form-group>
              </b-col>
              <b-col sm="3">
                <b-form-group label="Text Color" description="Text Color">
                  <b-form-input v-model="detailObj.bundle.text_color" type="text" required></b-form-input>
                </b-form-group>
              </b-col>
              <b-col sm="3">
                <b-form-group label="URL Name" description="campaign URL Name，用於活動頁面 url ，請務必填寫">
                  <b-form-input v-model="detailObj.bundle.url_name" type="text" required></b-form-input>
                  <b-form-invalid-feedback :state="formState.url_name">
                    沒有設定 URL Name
                  </b-form-invalid-feedback>
                </b-form-group>
              </b-col>
            </b-row>

            <b-row class="my-1">
              <b-col sm="4">
                <b-form-group label="Campaign Image" description="圖片寬度最少要 375px，接受 image/jpeg, image/png 格式，2 個圖檔合計大小不可超過 1MB">
                    <b-form-file v-model="campaign_image" ref="image-input" accept="image/jpeg,image/png" required></b-form-file>
                </b-form-group>
                <b-form-group label="預覽圖片" :description="detailObj.campaign_image">
                  <b-img v-if="detailObj.campaign_image_preview" :src="detailObj.campaign_image_preview" thumbnail :width=200></b-img>
                </b-form-group>
              </b-col>
              <b-col sm="4">
                <b-form-group label="Background Image" description="Background Image，接受 image/jpeg, image/png 格式，2 個圖檔合計大小不可超過 1MB">
                  <b-form-file v-model="background_image" ref="image-input" accept="image/jpeg,image/png"></b-form-file>
                </b-form-group>
                <b-form-group label="預覽圖片" :description="detailObj.background_image">
                  <b-img v-if="detailObj.background_image_preview" :src="detailObj.background_image_preview" thumbnail :width=200></b-img>
                </b-form-group>
              </b-col>
              <b-col sm="4">
                <b-form-group label="Background Color" description="Background Color">
                  <b-form-input v-model="detailObj.bundle.background_color" type="text"></b-form-input>
                </b-form-group>
              </b-col>
            </b-row>

          </b-card>

          <b-form-checkbox v-model="detailObj.active">啟用</b-form-checkbox>
          <b-form-checkbox v-model="detailObj.auto_renew">自動更新(訂閱型產品)</b-form-checkbox>
          <b-form-checkbox v-model="detailObj.as_subscribe">適用首購優惠</b-form-checkbox>

          <b-form-group label="排序" description="數字越小，排越前面">
            <b-form-input v-model="detailObj.sort" type="number"></b-form-input>
          </b-form-group>
        </b-form-group>
    </b-modal>
  </b-row>
</template>
<script>
import api from '@/api'

export default {
  name: 'Products',
  props: {
    hover: {
      type: Boolean,
      default: true
    },
    striped: {
      type: Boolean,
      default: true
    },
    bordered: {
      type: Boolean,
      default: false
    },
    small: {
      type: Boolean,
      default: false
    },
    fixed: {
      type: Boolean,
      default: false
    }
  },
  data: () => {
    return {
      items: [],
      caption: "Products",
      isCampaign: false,
      showAll: false,
      showDetail: false,
      detailTitle: 'Hi detail',
      qualification: "",
      qualifications: [
        { text: '無資格限制', value: '' },
        { text: 'Email Domain', value: 'email_domain' },
        { text: 'Phone', value: 'phone' }
      ],
      detailObj: {
        id: null,
        payment_type: "",
        tax_rate: 0,
        fee_rate: 0,
        fee:0,
        price_no_tax: 0,
        credit_card_prefix: "",
        qualification_email_domain_whitelist: "",
        family: 0,
        bundle: {},
        discount_price: 0,
        discount_fee: 0,
        discount_price_no_tax: 0
      },
      paymentOptions: [
        { value: 'iap', text: 'IAP (iOS)' },
        { value: 'iab', text: 'IAB (Android)' },
        { value: 'credit_card', text: '信用卡' },
        { value: 'cvs_code', text: '超商代碼繳費' },
        { value: 'coupon', text: 'COUPON (兌換序號)' },
        { value: 'telecom', text: '電信付款' },
        { value: 'mod', text: 'MOD (中華電信)' },
        { value: 'bandott', text: 'BANDOTT (便當機上盒)' },
        { value: 'tstar', text: 'TSTAR (台灣之星)' }
      ],
      background_image: null,
      campaign_image: null,
      campaignOptions: [
        { value: 'credit_card', text: '信用卡' }
      ],
      paymentCode: {
        'iap': '01',
        'iab': '02',
        'credit_card': '00',
        'cvs_code': '03',
        'coupon': '04',
        'telecom': '05',
        'mod': '06',
        'bandott': '07',
        'tstar': '05'
      },
      alertCss: '',
      alertMsg: '',
      dismissCountDown: 0,
      formState: {
        name: null,
        payment_type: null,
        item_unit: null,
        item_name: null,
        price: null,
        price_no_tax: null,
        duration: null,
        duration_unit: null,
        free_duration_unit: null,
        discount_duration_unit: null,
        discount_price: null,
        category:null
      },
      q: '',
      fields: [
        {key: 'action', label: ''},
        {key: 'item_name', label: '名稱及代碼'},
        {key: 'price', label: '價格'},
        {key: 'tax_rate', label: '稅率'},
        {key: 'duration', label: '週期'},
        {key: 'payment_type', label: '付款方式'},
        {key: 'auto_renew', label: '自動更新訂閱'},
        {key: 'as_subscribe', label: '首購優惠'},
        {key: 'active', label: '啟用'},
        {key: 'info', label: ''}
      ],
      currentPage: 1,
      // perPage: 5,
      perPage: 0,
      totalRows: 0
    }
  },
  mounted () {
    this.prepare()
  },
  computed: {
    filterItems () {
      var objs = this.items
      var qs

      if (this.isCampaign === "checked") {
        objs = objs.filter(function(item) { return item.is_campaign == true})
      } else {
        objs = objs.filter(function(item) { return item.is_campaign == false})
      }

      if (this.showAll === "checked") {
        // query for all
        if (this.q === '') {
          return objs
        } else {
          qs = this.q
          return objs.filter(function (item) {
            return item.name.includes(qs) || item.item_name.includes(qs) || item.payment_type.includes(qs)
          })
        }
      } else {
        // query for active item
        if (this.q === '') {
          return objs.filter(function(item) {
            return item.active === true
          })
        } else {
          qs = this.q
          return objs.filter(function (item) {
            return item.active === true && (item.name.includes(qs) || item.item_name.includes(qs) || item.payment_type.includes(qs))
          })
        }

      }

    }
  },
  watch: {
    '$route': function (val, oldVal) {
      console.log('route change')
      this.prepare()
    },
    'titleID': function (val, oldVal) {
      if (val != '' && val.length > 1 ) {
        this.filterSuggestions()
        this.isOpen = true

      } else {
        this.isOpen = false

      }
    }
  },
  methods: {
    getBadge (status) {
      var css
      switch (status) {
        case true:
          css = 'success'
          break
        case false:
          css = 'warning'
          break
      }
      return css
    },
    getRowCount (items) {
      return items.length
    },
    userLink (id) {
      return `user/${id.toString()}`
    },
    rowClicked (item) {
      const userLink = this.userLink(item.id)
      this.$router.push({path: userLink})
    },
    countDownChanged (dismissCountDown) {
      this.dismissCountDown = dismissCountDown
    },
    showAlert () {
      this.dismissCountDown = 5
    },
    resetImage () {
      if (this.$refs['image-input']) {
        this.$refs['image-input'].reset()
      }
      this.background_image = null
      this.campaign_image = null
    },
    prepare() {
      // init
      this.resetFormStates()
      this.resetDetailObj()
      this.resetImage()
      // var that = this
      this.caption = "Products"

      this.onFetch()
    },
    onFetch () {
      api.request('get', '/v3/console/product')
        .then((response) => {
          if (response.data && response.data.data &&
          response.data.data.products) {
            this.items = response.data.data.products
            // add is_campaign flag
            for (var i=0; i < this.items.length; i++){
              var item = this.items[i]
              if (item.bundle && item.bundle.url_name) {
                // console.log(item)
                this.items[i].is_campaign = true
              } else {
                this.items[i].is_campaign = false
              }
            }
          }
        })
    },
    onOK (modalEvent) {
      modalEvent.preventDefault()
      if (!this.isValid()) {
        return
      }
      var that = this
      // handle duration && free_duration
      if (this.detailObj.duration_value && this.detailObj.duration_unit != '') {
        this.detailObj.duration = this.detailObj.duration_value + ' ' + this.detailObj.duration_unit + 's'
      }

      if (this.detailObj.free_duration_value && this.detailObj.free_duration_unit != '' && this.detailObj.free_duration_value > 0) {
        this.detailObj.free_duration = this.detailObj.free_duration_value + ' ' + this.detailObj.free_duration_unit + 's'
      } else {
        // empty interval for postgresql field
        this.detailObj.free_duration = '00:00:00'
      }

      if (this.detailObj.discount_duration_value && this.detailObj.discount_duration_unit != '') {
        this.detailObj.discount_duration = this.detailObj.discount_duration_value + ' ' + this.detailObj.discount_duration_unit + 's'
      } else {
        // empty interval for postgresql field
        this.detailObj.discount_duration = '00:00:00'
      }

      if (this.detailObj.payment_type) {
        this.detailObj.payment_type_code = this.paymentCode[this.detailObj.payment_type]
      }
      if (!this.detailObj.bundle) {
        this.detailObj.bundle = {}
      }
      if (this.detailObj.credit_card_prefix) {
        // cast to array
        this.detailObj.bundle['prefix'] = this.detailObj.credit_card_prefix.split(/\r?\n/)
      } else {
        // clean prefix
        delete this.detailObj.bundle['prefix']
      }

      if (this.qualification) {
        // email domain
        if (this.qualification == "email_domain" && this.detailObj.qualification_email_domain_whitelist) {
          this.detailObj.bundle['qualification'] = 'email_domain'
          // cast to array
          this.detailObj.bundle['email_domains'] = this.detailObj.qualification_email_domain_whitelist.split(/\r?\n/)
        } else {
          // clean email domain
          delete this.detailObj.bundle['email_domains']
        }
        // phone
        if (this.qualification == "phone") {
          this.detailObj.bundle['qualification'] = 'phone'
        }
      } else {
        delete this.detailObj.bundle['qualification']
      }

      this.detailObj.price = this.detailObj.price + '' // cast to string for money field
      this.detailObj.price_no_tax = this.detailObj.price_no_tax ? this.detailObj.price_no_tax + '' : '0' // cast to string for money field
      this.detailObj.discount_price = this.detailObj.discount_price ? this.detailObj.discount_price + '' : '0'
      this.detailObj.tax_rate = parseInt(this.detailObj.tax_rate) // cast to int for int64
      this.detailObj.discount_price_no_tax = this.detailObj.discount_price_no_tax ? this.detailObj.discount_price_no_tax + '' : '0' // cast to string for money field
      this.detailObj.discount_fee = parseInt(this.detailObj.discount_fee) // cast to int for int64
      this.detailObj.fee = parseInt(this.detailObj.fee) // cast to int for int64
      this.detailObj.sort = parseInt(this.detailObj.sort) // cast to int for int64

      // family
      console.log(this.detailObj.family, this.detailObj.family > 1)
      if (this.detailObj.family && this.detailObj.family > 1) {
        this.detailObj.bundle['family'] = parseInt(this.detailObj.family)
        // reset free_duration discount_duration
        // this.detailObj.free_duration = '00:00:00'
        // this.detailObj.discount_duration = '00:00:00'
      } else {
        delete this.detailObj.bundle['family']
      }

      //description
      if (this.detailObj.bundle.description) {
        // cast to array
        this.detailObj.bundle['description'] = this.detailObj.bundle.description
      } else {
        // clean prefix
        delete this.detailObj.bundle['description']
      }

      //package_description
      if (this.detailObj.bundle.package_description) {
        // cast to array
        this.detailObj.bundle['package_description'] = this.detailObj.bundle.package_description
      } else {
        // clean prefix
        delete this.detailObj.bundle['package_description']
      }

      //qualification_description
      if (this.detailObj.bundle.qualification_description) {
        this.detailObj.bundle['qualification_description'] = this.detailObj.bundle.qualification_description
      } else {
        delete this.detailObj.bundle['qualification_description']
      }

      //title
      if (this.detailObj.bundle.title) {
        // cast to array
        this.detailObj.bundle['title'] = this.detailObj.bundle.title
      } else {
        // clean prefix
        delete this.detailObj.bundle['title']
      }

      //sub_title
      if (this.detailObj.bundle.subtitle) {
        // cast to array
        this.detailObj.bundle['subtitle'] = this.detailObj.bundle.subtitle
      } else {
        // clean prefix
        delete this.detailObj.bundle['subtitle']
      }

      //text_color
      if (this.detailObj.bundle.text_color) {
        // cast to array
        this.detailObj.bundle['text_color'] = this.detailObj.bundle.text_color
      } else {
        // clean prefix
        delete this.detailObj.bundle['text_color']
      }

      //url_name
      if (this.detailObj.bundle.url_name) {
        // cast to array
        this.detailObj.bundle['url_name'] = this.detailObj.bundle.url_name
      } else {
        // clean prefix
        delete this.detailObj.bundle['url_name']
      }

      //campaign_image
      if (this.detailObj.bundle.campaign_image) {
        // cast to array
        this.detailObj.bundle['campaign_image'] = this.detailObj.bundle.campaign_image
      } else {
        // clean prefix
        delete this.detailObj.bundle['campaign_image']
      }

      //background_image
      if (this.detailObj.bundle.background_image) {
        // cast to array
        this.detailObj.bundle['background_image'] = this.detailObj.bundle.background_image
      } else {
        // clean prefix
        delete this.detailObj.bundle['background_image']
      }

      //background_color
      if (this.detailObj.bundle.background_color) {
        // cast to array
        this.detailObj.bundle['background_color'] = this.detailObj.bundle.background_color
      } else {
        // clean prefix
        delete this.detailObj.bundle['background_color']
      }

      console.log(this.detailObj)

      const url = '/v3/console/product'

      api.request('post', url, this.detailObj )
        .then((response) => {
          console.log(response)

          if (this.campaign_image || this.background_image) {
            const imgUploadUrl = '/v3/console/productimage?id=' + this.detailObj.id
            let formData = new FormData()
            if (this.campaign_image) {
              formData.append('file', this.campaign_image)
            }
            if (this.background_image) {
              formData.append('bkimage', this.background_image)
            }
            api.request('post', imgUploadUrl, formData, { headers: { 'Content-Type': 'multipart/form-data' }})
              .then(function(){
                that.alertCss = 'success'
                that.alertMsg = '儲存成功'
                that.showAlert()
                that.onFetch()
              })
              .catch(function(){
                that.alertCss = 'warning'
                that.alertMsg = '圖片儲存失敗'
                that.showAlert()
                that.onFetch()
              })
          } else {
            that.alertCss = 'success'
            that.alertMsg = '儲存成功'
            that.showAlert()
            that.onFetch()
          }

          that.onCancel()
          that.$refs.formModal.hide()
          that.onFetch()
        }).catch(function(){
          that.alertCss = 'warning'
          that.alertMsg = '儲存失敗'
          that.showAlert()
          that.$refs.formModal.hide()
          that.onFetch()
        })
    },
    onChangePaymentType () {
      switch (this.detailObj.payment_type) {
      case "credit_card":
          this.detailObj.bundle.needRedeemCode = this.detailObj.bundle && this.detailObj.bundle.needRedeemCode ? true : false
          break
      case "mod":
      case "bandott":
      case "tstar":
      case "cvs_code":
      case "telecom":
      case "iap":
      case "iab":
          break
      default:
          break
      }
    },
    calculateFee() {
      // 根據價格計算手續費
      let feeRate, price
      price = parseInt(this.detailObj.price)
      feeRate = this.detailObj.fee_rate
      if (!['iap', 'iab'].includes(this.detailObj.payment_type)) {
        this.detailObj.fee = Math.round(price * feeRate / 100)
      }
    },
    calculateDiscountFee() {
      let feeRate, discountPrice
      discountPrice = parseInt(this.detailObj.discount_price)
      feeRate = this.detailObj.fee_rate
      if (!['iap', 'iab'].includes(this.detailObj.payment_type)) {
        this.detailObj.discount_fee = Math.floor(discountPrice * (feeRate / 100))
      }
    },
    calculatePriceNoTax() {
      // 根據價格計算未稅價
      var price, tax_rate
      price = parseInt(this.detailObj.price)
      tax_rate = parseInt(this.detailObj.tax_rate)
      if (!['iap', 'iab'].includes(this.detailObj.payment_type)) {
        this.detailObj.price_no_tax = Math.round(price/((100+tax_rate)/100))
      }
    },
    calculateDiscountPriceNoTax() {
      // 根據稅率計算未稅優惠價
      var discountPrice, tax_rate
      discountPrice = parseInt(this.detailObj.discount_price)
      tax_rate = parseInt(this.detailObj.tax_rate)
      if (!['iap', 'iab'].includes(this.detailObj.payment_type)) {
        this.detailObj.discount_price_no_tax = Math.round(discountPrice/((100+tax_rate)/100))
      }
    },
    onChangePrice () {
      // 改價格的時候，根據價格重新計算未稅價、手續費
      this.calculatePriceNoTax()
      this.calculateFee()
    },
    onChangeTaxRate() {
      // 改稅率的時候，根據稅率重新計算未稅價、未稅優惠價格
      this.calculatePriceNoTax()
      this.calculateDiscountPriceNoTax()
    },
    onChangeFeeRate() {
      // 改手續費率的時候，根據手續率重新計算手續費、優惠價格手續費
      this.calculateFee()
      this.calculateDiscountFee()
    },
    onChangeDiscountPrice() {
      // 改優惠價格的時候，根據優惠價格重新計算未稅優惠價格、優惠價格手續費
      this.calculateDiscountPriceNoTax()
      this.calculateDiscountFee()
    },
    resetFormStates() {
      this.formState = {
        name: null,
        payment_type: null,
        item_unit: null,
        item_name: null,
        price: null,
        price_no_tax: null,
        duration: null,
        duration_unit: null,
        free_duration_unit: null,
        discount_duration_unit: null,
        discount_price: null
      }
    },
    isValid() {
      var isOK = true
      this.resetFormStates()

      console.log('start validate')
      if (!this.detailObj.name) {
        this.formState.name = 'invalid'
        isOK = false
      }

      if (!this.detailObj.payment_type) {
        this.formState.payment_type = 'invalid'
        isOK = false
      }

      if (!this.detailObj.price || this.detailObj.price <= 0) {
        this.formState.price = 'invalid'
        isOK = false
      }

      if (!this.detailObj.price_no_tax || this.detailObj.price_no_tax <= 0) {
        this.formState.price_no_tax = 'invalid'
        isOK = false
      }

      if (this.detailObj.free_duration_value && this.detailObj.discount_duration_value && this.detailObj.free_duration_value != 0 && this.detailObj.discount_duration_value != 0) {
        this.formState.duration = 'invalid'
        isOK = false
      }

      if (this.detailObj.discount_duration_value && this.detailObj.discount_price <= 0) {
        this.formState.discount_price = 'invalid'
        isOK = false
      }

      if (!this.detailObj.duration_value || !this.detailObj.duration_unit) {
        this.formState.duration_unit = 'invalid'
        isOK = false
      }

      if (this.detailObj.free_duration_value > 0 && !this.detailObj.free_duration_unit) {
        this.formState.free_duration_unit = 'invalid'
        isOK = false
      }
      if (this.detailObj.discount_duration_value > 0 && !this.detailObj.discount_duration_unit) {
        this.formState.discount_duration_unit = 'invalid'
        isOK = false
      }
      if (!this.detailObj.item_name) {
        this.formState.item_name = 'invalid'
        isOK = false
      }
      if (!this.detailObj.item_unit) {
        this.formState.item_unit = 'invalid'
        isOK = false
      }

      if (!this.detailObj.category) {
        this.formState.category= 'invalid'
        isOK = false
      }

      return isOK
    },
    onCancel () {
      this.detailObj = {
        id: null,
        payment_type: "",
        tax_rate: 0,
        price_no_tax: 0,
        credit_card_prefix: "",
        qualification: "",
        qualification_email_domain_whitelist: "",
        family: 0,
        bundle: {}
      }
      this.resetImage()
    },
    resetDetailObj () {
      this.onCancel()
    },
    onEdit (item) {
      // resetFormStates
      this.resetFormStates()
      // clone it
      if (item.id) {
        this.detailObj = JSON.parse(JSON.stringify(item))
      }

      if (item.id) {
        // update
        this.detailTitle =  '修改 ' + item.name
        this.detailObj.price = parseInt(this.detailObj.price.replace(/\$|,/gi,''))
        this.detailObj.price_no_tax = parseInt(this.detailObj.price_no_tax.replace(/\$|,/gi,''))
        this.detailObj.discount_price = parseInt(this.detailObj.discount_price.replace(/\$|,/gi,''))
        this.detailObj.discount_price_no_tax = parseInt(this.detailObj.discount_price_no_tax.replace(/\$|,/gi,''))
        var durationArray = this.detailObj.duration.split(' ')
        var free_durationArray = this.detailObj.free_duration.split(' ')
        var discount_durationArray = this.detailObj.discount_duration.split(' ')
        this.detailObj.credit_card_prefix = ''
        this.qualification = ''
        this.detailObj.qualification_email_domain_whitelist = ''

        if (durationArray.length === 2) {
          // should be correct
          this.detailObj.duration_value = parseInt(durationArray[0])
          // days, mons, years
          this.detailObj.duration_unit = durationArray[1].replace('s', '')
        }

        if (free_durationArray.length === 2) {
          // should be correct
          this.detailObj.free_duration_value = parseInt(free_durationArray[0])
          this.detailObj.free_duration_unit = free_durationArray[1].replace('s', '')
        }

        if (discount_durationArray.length === 2) {
          this.detailObj.discount_duration_value = parseInt(discount_durationArray[0])
          this.detailObj.discount_duration_unit = discount_durationArray[1].replace('s', '')
        }

        if (this.detailObj.bundle && this.detailObj.bundle.prefix) {
          this.detailObj.credit_card_prefix = this.detailObj.bundle.prefix.join("\n")
        }

        if (this.detailObj.bundle && this.detailObj.bundle.qualification) {
          this.qualification = this.detailObj.bundle.qualification
        }

        if (this.detailObj.bundle && this.detailObj.bundle.email_domains) {
          this.detailObj.qualification_email_domain_whitelist = this.detailObj.bundle.email_domains.join("\n")
        }

        if (this.detailObj.bundle && this.detailObj.bundle.family) {
          this.detailObj.family = this.detailObj.bundle.family
        }

        if (this.detailObj.bundle && this.detailObj.bundle.campaign_image) {
          this.campaign_image = this.detailObj.bundle.campaign_image
          this.detailObj.campaign_image_preview = this.detailObj.bundle.campaign_preview
        }

        if (this.detailObj.bundle && this.detailObj.bundle.background_image) {
          this.background_image = this.detailObj.bundle.background_image
          this.detailObj.background_image_preview = this.detailObj.bundle.background_preview

        }

      } else {
        // insert
        this.detailTitle =  '新增產品'
        this.detailObj = {
          id: null,
          payment_type: "",
          tax_rate: 0,
          fee_rate: 0,
          fee:0,
          price_no_tax: 0,
          credit_card_prefix: "",
          qualification: "",
          qualification_email_domain_whitelist: "",
          family: 0,
          bundle: {needRedeemCode: false},
          discount_price: 0,
          discount_fee: 0,
          discount_price_no_tax: 0
        }
      }

      if (! this.detailObj.bundle) {
        this.detailObj.bundle = {}
      }
      this.showDetail = true
    }
  }
}
</script>

<style>
.tooltip-inner {
  max-width: 300px;
  width: 300px;
}
</style>
<style scoped>
.card-body >>> table > tbody > tr > td {
  cursor: pointer;
}
</style>
