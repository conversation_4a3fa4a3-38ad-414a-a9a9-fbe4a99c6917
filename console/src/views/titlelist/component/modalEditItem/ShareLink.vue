<template>
  <div>
    <b-form-group label="分享連結 Share Link"
      description="自動生成，若要指定請手動修改後點擊儲存">
      <template #label v-if="required">
        <span class="required-field">*</span>分享連結 Share Link
      </template>
      <div class="input-group">
        <div class="input-group-prepend">
          <span class="input-group-text" id="baseShareLink">{{ clientBaseUrl }}</span>
        </div>
        <b-form-input type="text" v-model="formRecord.meta.share_id" class="form-control" />
        <b-button class="share-link-save" v-if="this.record.id" variant="primary" @click="updateLink">Save</b-button>
      </div>
    </b-form-group>
    <ShowAlert :showAlert.sync="showAlert" :alertMessage="alertMessage" :alertType="alertType" />
  </div>
</template>

<script>
import RESERVED_SHARE_ID_LIST from "@/utils/reservedShareIdList";
import api from "@/api";
import ShowAlert from "@/views/titlelist/component/ShowAlert";

export default {
  name: 'ShareLink',
  components: {
    ShowAlert
  },
  props: {
    formRecord: Object,
    required: Boolean,
    clientBaseUrl: {
      type: String,
      required: true
    },
    record: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      showAlert: false,
      alertMessage: '',
      alertType: ''
    }
  },
  methods: {
    showError(message) {
      this.alertType = 'error';
      this.alertMessage = message;
      this.showAlert = true;
    },
    showSuccess(message) {
      this.alertType = 'success';
      this.alertMessage = message;
      this.showAlert = true;
    },
    async updateLink() {
      const shareId = this.formRecord.meta.share_id ? this.formRecord.meta.share_id.toLowerCase() : ''
      if (RESERVED_SHARE_ID_LIST.find(reservedShareId => reservedShareId === shareId)) {
        this.showError(`儲存失敗，${shareId} 為系統保留的片單 share id`);
        return
      } else if (shareId === '' || this.formRecord.id === '') {
        this.showError('儲存失敗，share id 不能為空');
        return
      } else {
        try {
          await api.request(
            'put', 
            `/v3/console/titlelist_shareId?id=${this.formRecord.id}&shareId=${shareId}`
          );
          this.showSuccess('更新成功');
        } catch (err) {
          this.showError('更新失敗');
          console.error('更新失敗:', err);
        }
      }      
    }
  }
}
</script>

<style scoped>
.share-link-save {
  margin-left: 4px;
}
</style>