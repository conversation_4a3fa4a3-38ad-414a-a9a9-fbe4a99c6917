<template>
  <div>
    <div class="visible-duration-container">
      <b-form-group label="上架時間 Visible Since" :state="validStates.duration" invalid-feedback="請檢查時間">
        <template #label v-if="required">
          <span class="required-field">*</span>上架時間 Visible Since
        </template>
        <flat-pickr v-model="formRecord.visible_since" :config="flatpickrConfig">
        </flat-pickr>
      </b-form-group>
      <b-form-group label="下架時間 Visible Until"  :state="validStates.duration" invalid-feedback="請檢查時間">
        <template #label v-if="required">
          <span class="required-field">*</span>下架時間 Visible Until
        </template>
        <flat-pickr v-model="formRecord.visible_until" :config="flatpickrConfig">
        </flat-pickr>
      </b-form-group>
    </div>
  </div>
</template>

<script>
import { datePickerConf } from '../ui-config';

export default {
  name: 'VisibleDuration',
  props: {
    validStates: Object,
    formRecord: Object,
    required: Boolean
  },
  data() {
    return {
      flatpickrConfig: datePickerConf
    }
  },
  inject: ['registerValidation'],
  provide() {
    return {
      registerValidation: this.registerValidation
    }
  },
  mounted() {
    const unregister = this.registerValidation(this)
    this.$once('hook:beforeDestroy', unregister)
  },
  methods: {
    validate() {
      if (!this.formRecord.visible_since || !this.formRecord.visible_until) {
        if (this.validStates) {
          this.validStates.duration = false
        }
        return false
      }

      const since = Date.parse(this.formRecord.visible_since)
      const until = Date.parse(this.formRecord.visible_until)

      const isValid = since <= until
      console.log('validate duration', isValid)

      if (this.validStates) {
        this.validStates.duration = isValid
      }

      return isValid
    }
  }
}
</script>

<style scoped>
.visible-duration-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 15px;
}
.required-field {
  color: red;
  margin-right: 4px;
}
</style>

