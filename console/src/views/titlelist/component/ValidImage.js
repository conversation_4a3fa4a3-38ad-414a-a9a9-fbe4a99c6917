
export function getSize(file) {
  return new Promise((resolve, reject) => {
    let _URL = window.URL || window.webkitURL;
    let img = new Image();

    img.onload = () => resolve({height: img.height, width: img.width});
    img.onerror = reject;
    console.log("file:", file, "type:", typeof file)
    img.src = _URL.createObjectURL(file);
  });
}

export function validateBackgroundImage(bgImg) {
  return (16 / 9) === (bgImg.width / bgImg.height);
}

export function validateOGImage(ogImg) {
  if ((40 / 21) !== (ogImg.width / ogImg.height)) {
    return false
  }
  return !((ogImg.width > 1200) || (ogImg.height > 630));
}