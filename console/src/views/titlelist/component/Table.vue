<template>
  <b-table v-if="items" :hover="hover" :striped="striped" :bordered="bordered" :small="small" :fixed="fixed"
           responsive="sm" :items="filterItems"
           :fields="fields" :current-page="paging.currentPage" :per-page="paging.perPage"
           style="overflow-x:auto;">
    <template slot="action" slot-scope="data">
      <b-button @click="onEdit(data.item)"><i class="fa fa-edit"></i></b-button>&nbsp;
      <b-button @click="onDelete(data.item)"><i class="fa fa-trash"></i></b-button>
    </template>
    <template slot="enabled" slot-scope="data">
      <b-badge :variant="getBadge(data.item.enabled)">{{ data.item.enabled }}</b-badge>
    </template>
    <template slot="duration" slot-scope="data">
              <span style="white-space: nowrap;">
                {{ getLocalTime(data.item.visible_since) }} ～<br/>
                {{ getLocalTime(data.item.visible_until) }}
              </span>
    </template>
    <template slot="bg_image" slot-scope="data">
      <b-img :src="data.item.meta.background_image_url" width="100" alt=""></b-img>
    </template>
    <template slot="image" slot-scope="data">
      <b-img :src="data.item.image" width="100" alt=""></b-img>
    </template>
    <template slot="type" slot-scope="data">
      {{ data.item.list_type }}
    </template>
    <template slot="topic" slot-scope="data">
              <span style="white-space: nowrap;">
                {{ data.item.topic }}<br/>
                <b>{{ data.item.title }}</b><br/>
                {{ data.item.summary }} <br/>
              </span>
    </template>
    <template slot="pinned" slot-scope="data">
              <span v-if="data.item.meta.pinned">
                {{ pinnedDisplay[data.item.meta.pinned] || 'undefined' }}<br/>
              </span>
      <span v-else> - </span>
    </template>
    <template slot="collection" slot-scope="data">
              <span>
                {{ parseCollectionsToString(data.item.meta.collections) }}
              </span>
    </template>
    <template slot="title_count" slot-scope="data">
              <span v-if="data.item.meta && data.item.meta.title_id">
                {{ data.item.meta.title_id.length }}
              </span>
      <span v-else>0</span>
    </template>
    <template slot="title" slot-scope="data">
              <span v-if="data.item.list_type === 'link'">
                App: <a :href="data.item.uri" target="_blank">{{ data.item.uri }}</a><br/>
                Web: <a :href="data.item.url" target="_blank">{{ data.item.url }}</a>
              </span>
      <span v-if="data.item.list_type === 'title'">
                <a :href="'https://www.kktv.me/titles/'+data.item.title_id" target="_blank">{{ data.item.title_id }}</a>
              </span>
    </template>
    <template slot="pinned_appear" slot-scope="data">
              <span v-if="data.item.meta.pinned === 'first'">
                固定順序:{{ data.item.order }}
              </span>
      <span v-else>隨機</span>
      <span v-else>0</span>
    </template>
  </b-table>
</template>

<script>

import api from "@/api";

export default {
  name: "Table",
  props: {
    fields: Array,
    filters: Object,
    browseKey: {
      type: String, required: true
    },
    titlelistTypes: {
      type: Array, required: true
    },
    hover: {
      type: Boolean, default: true
    },
    striped: {
      type: Boolean, default: true
    },
    bordered: {
      type: Boolean, default: false
    },
    small: {
      type: Boolean, default: false
    },
    fixed: {
      type: Boolean, default: false
    },
    paging: {
      type: Object,
      default() {
        return {
          currentPage: 1,
          perPage: 0
        }
      }
    }
  },
  data() {
    return {
      items: [],
      pinnedDisplay: {
        'new_finale': '全集新上架',
        'airing': '新劇跟播',
        'guaranteed_visible': '保證曝光'
      }
    }
  },
  methods: {
    getLocalTime(time) {
      return new Date(time).toLocaleString('default', {
        hour12: false,
        year: 'numeric',
        month: 'short',
        day: '2-digit',
        hour: 'numeric',
        minute: 'numeric',
      })
    },
    getBadge(status) {
      return status === true ? 'success' : 'warning'
    },
    parseCollectionsToString(collections) {
      return (collections) ? collections.map(collection => collection.split(':')[1]).join(',').replace('featured', '精選') : collections
    },
    loadItems() {
      this.items = []
      const listType = this.titlelistTypes.join(',')
      api.request('get', '/v3/console/metatitlelist?list_type=' + listType)
        .then((response) => {
          if (response.data && response.data.data && response.data.data.titlelist) {
            let items = response.data.data.titlelist
            this.items = items.filter(item => {  // only keep the items that has current collectionKey
              return item.meta.collections && item.meta.collections.includes(this.browseKey)
            })
          }
        })
    },
    onDelete(item) {
      this.$emit("item-delete", item)
    },
    onEdit(item) {
      this.$emit('item-edit', item)
    }

  },
  mounted() {
    const $this = this
    this.loadItems()
    this.$on("reload", () => {
      $this.loadItems()
    })
  },
  computed: {
    filterItems() {
      let items = []
      // if showAll is checked, return all items
      if (this.filters.listState !== null) {
        items = this.items.filter(item => {
          return item.enabled === this.filters.listState
        })
      } else {
        items = this.items;
      }
      // if displayDate is given, filter items by displayDate
      if (this.filters.pickedDate) {
        let displayDate = Date.parse(this.filters.pickedDate)
        items = items.filter(item => {
          return (Date.parse(item.visible_since) <= displayDate && Date.parse(item.visible_until) >= displayDate)
        })
      }
      // if query string is given, filter items by query string
      if (this.filters.topic !== '') {
        const qs = this.filters.topic
        items = items.filter(item => {
          return (item.title && item.title.includes(qs)) || (item.topic && item.topic.includes(qs))
        })
      }
      return items
    }
  }
}
</script>

<style scoped>

</style>
