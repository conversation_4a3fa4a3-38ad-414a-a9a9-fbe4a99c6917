<template>
  <b-form-group description="輸入字串可以過濾出 Topic 或 Caption 符合的片單">
    <b-input-group>
      <b-input-group-prepend>
        <span class="input-group-text"><i class="fa fa-search fa-sm"></i></span>
      </b-input-group-prepend>
      <b-form-input type="search" id="name" v-model="topic"></b-form-input>
    </b-input-group>
  </b-form-group>
</template>

<script>
export default {
  name: "TopicSearchTextInput",
  data() {
    return {
      topic: null
    }
  },
  watch: {
    topic: {
      handler(val) {
        this.$emit('update', val)
      }
    }
  }
}
</script>

<style scoped>

</style>
