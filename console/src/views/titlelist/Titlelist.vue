<template>
  <b-row>
    <b-col cols="12" xl="12">
      <transition name="slide">
        <b-card :header="collectionName + ' > '+ listType + ' 片單'">
          <form v-on:submit.prevent="reloadTable">
            <filter-section @filterChange="onFilterChange" ref="filter-section"/>

            <b-row> <!-- Alert -->
              <b-col sm="12">
                <b-alert :show="alertDialog.dismissCountDown"
                         dismissible
                         :variant="alertDialog.css"
                         @dismissed="alertDialog.dismissCountDown=0"
                         @dismiss-count-down="countDownChanged">
                  {{ alertDialog.msg }}
                </b-alert>
              </b-col>
            </b-row>

            <b-row>
              <b-col sm="6">
                <b-button variant="primary" @click="onModalShow(newEmptyItem())">新增</b-button>
              </b-col>
              <b-col v-if="canSetDefaultTitlelist" sm="6">
                <b-form-group description="修改預設片單的id">
                  <b-input-group>
                    <b-form-input type="text" id="default-titlelist" v-model="titlelistDefault"></b-form-input>
                    <b-input-group-append>
                      <b-button variant="primary" @click="onStoreDefaultTitlelist()">儲存</b-button>
                    </b-input-group-append>
                  </b-input-group>
                </b-form-group>
                <!-- end  -->
              </b-col>
            </b-row>
          </form>

          <list-table ref="list-table"
                      :fields="tableFields" :filters="filters" :browse-key="browseKey"
                      :titlelist-types="supportTypeList"
                      @item-delete="onDeleteRecord" @item-edit="onModalShow"/>
        </b-card>
      </transition>
    </b-col>
    <component
      :is="modalComponent"
      ref="edit-modal"
      :pinned-options="pinnedOptions"
      :show="showModal"
      :record="editTarget"
      :titlelist-types="supportTypeList"
      :valid-states="validStates"
      :type="listType"
      :client-base-url="clientWebBaseURL"
      @show="showModal = $event"
      @submit="handleSubmit"
      @update-share-id="onStoreTitleShareId"
    />
  </b-row>
</template>

<script>

import api from "@/api";
import draggable from 'vuedraggable'
import filterSection from "@/views/titlelist/layout/FilterSection";
import listTable from "@/views/titlelist/component/Table";
import editModal from "@/views/titlelist/component/DetailEditModal";
import highlightModal from "@/views/titlelist/component/HighlightModal";
import choiceModal from "@/views/titlelist/component/ChoiceModal";
import RESERVED_SHARE_ID_LIST from "@/utils/reservedShareIdList";

export default {
  components: {
    draggable,
    filterSection,
    listTable,
    editModal,
    highlightModal,
    choiceModal
  },
  data: function () {
    return {
      filters: {
        pickedDate: null,
        topic: "",
        listState: true
      },
      validStates: {
        caption: null,
        titleIDs: null,
        shareID: null,
        desc: null,
        bgImg: null,
        ogImg: null,
      },
      alertDialog: {
        css: '',
        msg: '',
        dismissCountDown: 0
      },
      titlelistDefault: null,
      clientWebBaseURL: '',
      clientBaseUrl: null,
      showModal: false,
      tableFields: [
        {key: 'action', label: ''},
        {key: 'enabled', label: '啟用'},
        {key: 'duration'},
        {key: 'topic', label: 'Topic / Caption / Summary'},
        {key: 'collection', label: '分類'},
      ],
      editTarget: {}
    }
  },
  mounted() {
    this.prepare()
  },
  watch: {
    '$route': function (val, oldVal) {
      this.prepare()
    },
  },
  methods: {
    prepare() {
      this.resetForm()
      this.reloadTable()
      this.reloadDefaultTitlelist()
    },
    onModalShow(item) {
      this.editTarget = Object.assign({}, item)
      this.showModal = true
    },
    onFilterChange(evt) {
      switch (evt.key) {
        case "date":
          this.filters.pickedDate = evt.value;
          break;
        case "enabled":
          this.filters.listState = evt.value;
          break;
        case "topic":
          this.filters.topic = evt.value;
          break;
      }
    },
    onSubmitFail(errFields) {
      console.error("validate fail ", errFields)
      for (const f of errFields) {
        this.validStates[f] = 'invalid'
      }
    },
    resetForm() {
      for (const key in this.validStates) {
        this.validStates[key] = null
      }
    },
    commonValidation(titlelist) {
      let errFields = []
      if (!titlelist.meta.collections || titlelist.meta.collections.length === 0) {
        errFields.push('collections')
      }

      if (!titlelist.title) {
        errFields.push('caption')
      }

      const since = Date.parse(titlelist.visible_since)
      const until = Date.parse(titlelist.visible_until)
      if (since > until) {
        errFields.push('duration')
      }
      return errFields
    },
    async validate(data) {
      console.error("should be override")
      process.exit();
      return [""]
    },
    async handleSubmit(submitData) {
      this.resetForm()

      const that = this;

      // handle attributes
      let errFields = await this.validate(submitData)
      if (errFields) {
        this.onSubmitFail(errFields)
        return
      }

      let titlelist = submitData.info;
      let body = _.cloneDeep(titlelist)

      if (this.listType !== 'airing') {
        body.visible_since = new Date(titlelist.visible_since).toISOString()
        body.visible_until = new Date(titlelist.visible_until).toISOString()
      } else {
        // 若 list_type == airing && 新增的時候, visible_since & visible_until 要給預設值
        // visible_since 給今天, visible_until 給 2050-12-31 23:59:59
        // share_id 給固定值: 'anime-airing'
        if (!titlelist.id) {
          body.visible_since = new Date().toISOString()
          body.visible_until = new Date('2050-12-31T23:59:59Z').toISOString()
          body.meta.share_id = 'anime-airing'
          body.enabled = true
          body.meta.collections = ['genre:動漫']
          body.list_type = 'airing'
        }
      }
      body.order = parseInt(titlelist.order)

      //TODO just with `#`, remove the adding `#` action in API
      if (body.dominant_color) {
        body.dominant_color = body.dominant_color.replace('#', '')
      } else {
        body.dominant_color = null
      }

      let imgUploadSucc = function () {
        that.alertSucc('儲存成功')
      }
      let imgUploadFail = function () {
        that.alertFail('圖片儲存失敗')
      }

      const url = '/v3/console/metatitlelist'
      let method = 'post'
      if (titlelist.id) {
        method = 'put'
      }

      api.request(method, url, body)
        .then((response) => {
          const titlelistID = response.data.data.id
          if (submitData.img) {
            const imgUploadUrl = '/v3/console/metatitlelistimage?id=' + titlelistID
            let formData = new FormData()
            formData.append('file', submitData.img)
            api.request('post', imgUploadUrl, formData, {headers: {'Content-Type': 'multipart/form-data'}})
              .then(imgUploadSucc)
              .catch(imgUploadFail)
              .finally(that.reloadTable)
          }

          if (submitData.bgImg) {
            const imgUploadUrl = '/v3/console/titlelist_meta_image?id=' + titlelistID + '&imageType=backgroundImage'
            let formData = new FormData()
            formData.append('file', submitData.bgImg)
            api.request('post', imgUploadUrl, formData, {headers: {'Content-Type': 'multipart/form-data'}})
              .then(imgUploadSucc)
              .catch(imgUploadFail)
              .finally(that.reloadTable)
          }

          if (submitData.ogImg) {
            const imgUploadUrl = '/v3/console/titlelist_meta_image?id=' + titlelistID + '&imageType=ogImage'
            let formData = new FormData()
            formData.append('file', submitData.ogImg)
            api.request('post', imgUploadUrl, formData, {headers: {'Content-Type': 'multipart/form-data'}})
              .then(imgUploadSucc)
              .catch(imgUploadFail)
          }

          if (submitData.editorAvatar) {
            const imgUploadUrl = '/v3/console/titlelist_meta_image?id=' + titlelistID + '&imageType=editorAvatar'
            let formData = new FormData()
            formData.append('file', submitData.editorAvatar)
            api.request('post', imgUploadUrl, formData, {headers: {'Content-Type': 'multipart/form-data'}})
              .then(imgUploadSucc)
              .catch(imgUploadFail)
          }
          that.alertSucc('儲存成功')
        })
        .catch(error => {
          console.error("fail to update titlelist", error)
          that.alertFail('這位大大，您的權限不足')
        })
        .finally(() => {
          that.reloadTable()
          that.modalClose()
        })
    },
    modalClose() {
      this.showModal = false
      this.resetForm()
      this.$refs["edit-modal"].reset()
    },
    alertSucc(msg) {
      this.alertDialog.dismissCountDown = 5
      this.alertDialog.css = 'success';
      this.alertDialog.msg = msg || '儲存成功'
    },
    alertFail(msg) {
      this.alertDialog.dismissCountDown = 5
      this.alertDialog.css = 'warning';
      this.alertDialog.msg = msg || '儲存失敗'
    },
    newEmptyItem() {
      const now = new Date()
      const browseKey = this.browseKey
      return {
        'meta': {'title_id': [], 'collections': [browseKey], 'video_url': ''},
        'visible_since': now, 'visible_until': now,
        'enabled': true
      }
    },
    onStoreDefaultTitlelist() {
      let that = this
      api.request('post', '/v3/console/default_titlelist?defaultId=' + this.titlelistDefault)
        .then(() => this.alertSucc('儲存成功'))
        .catch((err) => {
          that.alertFail('儲存失敗')
          console.error('fail to update default titlelist', err)
        })
        .finally(() => {
          that.reloadDefaultTitlelist()
        })
    },
    countDownChanged(dismissCountDown) {
      this.alertDialog.dismissCountDown = dismissCountDown
    },
    onDeleteRecord(item) {
      let confirm = window.confirm("Are you sure you want to delete this title list ?")
      if (confirm && item && item.id) {
        let that = this
        const url = '/v3/console/metatitlelist?id=' + item.id
        console.log(url)
        api.request('delete', url)
          .then(() => this.alertSucc('刪除成功'))
          .catch((err) => {
            that.alertFail('刪除失敗')
            console.error('fail to delete', err)
          })
          .finally(() => {
            that.reloadTable()
          })
      }
    },
    onStoreTitleShareId(evt) {
      // 因 DB 內部分 share id 有含大寫字母，因此 share id 轉小寫後不傳到 API，僅用來做 reserved share id 驗證，避免更新到舊有資料
      let share_id = evt.shareID ? evt.shareID.toLowerCase() : ''
      if (RESERVED_SHARE_ID_LIST.find(reservedShareId => reservedShareId === share_id)) {
        alert(`儲存失敗，${share_id} 為系統保留的片單 share id`)
        return
      }
      api.request('put', '/v3/console/titlelist_shareId?id=' + evt.titleID + '&shareId=' + evt.shareID)
        .then(() => {
          this.validStates.shareID = true
          this.$bvToast.toast(
            '分享連結 Share Link 已更新',
            {
              title: '更新成功',
              variant: 'success',
            }
          )
        })
        .catch((err) => {
          this.$bvToast.toast(
            '分享連結 Share Link 更新失敗',
            {
              title: '更新失敗',
              variant: 'danger',
            }
          )
        })
    },
    reloadTable() {
      if (this.supportTypeList.length === 0) {
        return
      }
      this.$refs["list-table"].loadItems()
    },
    reloadDefaultTitlelist() {
      api.request('get', '/v3/console/default_titlelist')
        .then((response) => {
          this.clientBaseUrl = response.data.data && response.data.data.base_url ? response.data.data.base_url : null
          this.titlelistDefault = response.data.data && response.data.data.default_titlelist_id ? response.data.data.default_titlelist_id : null
          this.clientWebBaseURL = response.data.data && response.data.data.base_url ? response.data.data.base_url : null
        })
    }
  },
  computed: {
    browseKey() {
      return this.$route.params.browseKey
    },
    collectionType() {
      const slice = this.browseKey.split(':')
      return slice[0]
    },
    collectionName() {
      const slice = this.browseKey.split(':')
      return slice[1]
    },
    modalComponent() {
      const modalMap = {
        'highlight': 'highlight-modal',
        'choice': 'choice-modal',
      }
      return modalMap[this.listType] || 'edit-modal'
    }
  },
  props: {
    canSetDefaultTitlelist: {type: Boolean, default: false},
    listType: {type: String},
    supportTypeList: {
      type: Array,
      default: () => []
    },
    pinnedOptions: {
      type: Array,
      default: () => []
    }
  }
}

export function getSize(file) {
  return new Promise((resolve, reject) => {
    let _URL = window.URL || window.webkitURL;
    let img = new Image();

    img.onload = () => resolve({height: img.height, width: img.width});
    img.onerror = reject;
    console.log("file:", file, "type:", typeof file)
    img.src = _URL.createObjectURL(file);
  });
}

export function validateBackgroundImage(bgImg) {
  return (16 / 9) === (bgImg.width / bgImg.height);
}

export function validateOGImage(ogImg) {
  if ((40 / 21) !== (ogImg.width / ogImg.height)) {
    return false
  }
  return !((ogImg.width > 1200) || (ogImg.height > 630));
}

</script>

<style src="./asset/style.css">

</style>
