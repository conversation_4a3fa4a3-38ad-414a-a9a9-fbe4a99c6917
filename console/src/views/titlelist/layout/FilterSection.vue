<template>
  <b-row>
    <b-col xl="4" >
      <chosen-date :date-picker-config="datePickerConf" @update="onUpdateDate($event)"
                   ref="chosen-date"/>
    </b-col>
    <b-col xl="2">
      <enabled-select @update="onUpdateEnableSelect($event)"/>
    </b-col>
    <b-col xl="6">
      <topic-search-text-input @update="onUpdateSearchTopic($event)"/>
    </b-col>
  </b-row>
</template>

<script>
import chosenDate from "@/views/titlelist/component/ChosenDate";
import enabledSelect from "@/views/titlelist/component/EnabledSelect";
import topicSearchTextInput from "@/views/titlelist/component/TopicSearchTextInput";
import {datePickerConf as dpConf} from "@/views/titlelist/component/ui-config"

export default {
  name: "FilterSection",
  components: {
    chosenDate, enabledSelect, topicSearchTextInput
  },
  data() {
    return {
      datePickerConf: dpConf,
    }
  },
  methods: {
    onUpdateDate(v) {
      this.$emit('filterChange', {key: "date", value: v});
    },
    onUpdateEnableSelect(v) {
      this.$emit('filterChange', {key: "enabled", value: v});
    },
    onUpdateSearchTopic(v) {
      this.$emit('filterChange', {key: "topic", value: v});
    },
  },
  mounted() {
    const cd = this.$refs['chosen-date']
    this.$on('reset', () => {
      cd.$emit('reset');
    });
    this.onUpdateDate(cd.getDefaultDisplayDate())
  }
}
</script>

<style scoped>

</style>
