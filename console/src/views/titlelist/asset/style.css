.card-body >>> table > tbody > tr > td {
    cursor: pointer;
}

.required-field {
    color: red;
    margin-right: 4px;
}

.form-notice-text {
    margin-top: 0.25rem;
    font-size: 0.875em;
    color: #6c757d;
}

.section-title {
    font-size: 14px;
    letter-spacing: 1px;
    font-weight: 600;
    margin-bottom: 20px;
}

.section-container {
    display: grid;
    width: 100%;
    grid-template-columns: 2fr 3fr;
    gap: 40px;
}

.section-container-item {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 15px;
}

.image-section {
    display: flex;
    flex-direction: column;
    gap: 10px;
}