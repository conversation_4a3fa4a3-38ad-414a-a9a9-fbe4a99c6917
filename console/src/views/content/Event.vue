<template>
  <b-row>
    <b-col cols="12" xl="12">
      <transition name="slide">

      <b-card no-header>
        <template slot="header">
          {{ caption }}
        </template>
          <b-row>
            <b-col sm="12">
            <form v-on:submit.prevent="onAdd">
              <div class="form-row">
                <div class="col">
                  <b-form-group label="訊息內容"
                   description="">
                    <b-form-input type="text" required placeholder="" v-model="newMessage">
                    </b-form-input>
                  </b-form-group>
                </div>
                <div class="col">
                  <b-form-group label="URL">
                    <b-input-group>
                      <b-form-input type="text" v-model="newUrl"></b-form-input>
                        <b-button variant="primary" @click="onAdd" v-b-tooltip.hover title="加入下列編輯列表，可以編輯，但尚未存入後端">
                          <i></i>
                          Add
                          </b-button> &nbsp;
                        <b-button variant="success" @click="onSave" v-b-tooltip.hover title="將目前所有修改，存入後端，並且生效">
                          <i class="fa" :class="{'fa-refresh': loading, 'fa-spin': loading}"></i>
                          Save All
                          </b-button>
                    </b-input-group>
                  </b-form-group>
                </div>
              </div>
            </form>

            <small v-html="'輸入完，點 Add 即會加入，預設時間現在，編輯好後，點 Save，才正式儲存並生效，<span style=\'font-weight: bold;color: #e67300;\'>過期已發佈的資訊，請刪除，大家減碳救地球</span>'"></small>

            <b-alert :show="dismissCountDown"
                   dismissible
                   :variant="alertCss"
                   @dismissed="dismissCountDown=0"
                   @dismiss-count-down="countDownChanged">
                   {{ alertMsg}}
            </b-alert>

            </b-col>
          </b-row>

          <b-table v-if="items" :hover="hover" :striped="striped" :bordered="bordered" :small="small" :fixed="fixed" responsive="sm" :items="items" :fields="fields" :current-page="currentPage" :per-page="perPage">
            <template slot="action" slot-scope="data">
              {{ ('00' + (data.index+1)).slice(-2) }}
              <b-button @click="onDelete(data.index)"><i class="fa fa-trash"></i></b-button>&nbsp;
              <img v-if="data.item.image" :src="data.item.image" class="img-thumbnail" />
            </template>
            <template slot="image" slot-scope="data">
              <b-form-file v-model="data.item.file" class="mb-2" accept="image/jpeg" placeholder="" drop-placeholder="Drop file" />
              <b-button type="submit" variant="primary" @click="imageUpload(data.index)" v-b-tooltip.hover title="將此圖上傳，並將目前所有修改，存入後端，並且生效">儲存及上傳</b-button>
            </template>
            <template slot="message" slot-scope="data">
                <strike v-if="data.item.end_time && data.item.end_time*1000 < new Date().getTime()">
                  <b-form-input type="text" v-model="data.item.message"></b-form-input>
                </strike>
                <span v-if="data.item.end_time && data.item.end_time*1000 >= new Date().getTime()">
                  <b-form-input type="text" v-model="data.item.message" maxlength="50" v-b-tooltip.hover title="長度限制 50 字"></b-form-input>
                </span>
            </template>
            <template slot="url" slot-scope="data">
                  <b-form-input type="text" v-model="data.item.url"></b-form-input>
            </template>
            <template slot="roles" slot-scope="data">
              <b-form-checkbox-group id="checkboxes2" name="roles" v-model="data.item.roles">
                <template v-for="role in roleOptions" >
                  <b-form-checkbox :value="role.value">
                    {{ role.key }}
                  </b-form-checkbox>
                  <br />
                </template>
              </b-form-checkbox-group>
            </template>
            <template slot="start" slot-scope="data">
                <flat-pickr v-model="data.item.start" :config="config"
                @on-close="onTimeChange(data.index, 'start')">
                </flat-pickr>
            </template>
            <template slot="end" slot-scope="data">
                <flat-pickr v-model="data.item.end" :config="config"
                @on-close="onTimeChange(data.index, 'end')">
                </flat-pickr>
            </template>
          </b-table>
      </b-card>
      </transition>
    </b-col>
  </b-row>
</template>

<script>

import { mapState } from 'vuex'
import api from '@/api'

export default {
  name: 'Event',
  components: {
  },
  props: {
    caption: {
      type: String,
      default: 'Announce'
    },
    hover: {
      type: Boolean,
      default: true
    },
    striped: {
      type: Boolean,
      default: true
    },
    bordered: {
      type: Boolean,
      default: false
    },
    small: {
      type: Boolean,
      default: false
    },
    fixed: {
      type: Boolean,
      default: false
    }
  },
  data: () => {
    return {
      items: [],
      titleMap: {},
      roleOptions : [],
      title: '',
      titlename: '',
      titlekey: '',
      dismissCountDown: 0,
      newMessage: '',
      newUrl: '',
      alertCss: '',
      alertMsg: '',
      config: {
          // wrap: true, // set wrap to true only when using 'input-group'
          // allowInput: true,
          enableTime: true,
          altFormat: 'Y-m-d H:i',
          altInput: true,
          dateFormat: 'Y-m-d H:i'
      },
      q: '',
      fields: [
        {key: 'action', label: ''},
        {key: 'image', label: '圖片'},
        {key: 'message', label: '訊息內容'},
        {key: 'url', label: 'URL'},
        {key: 'roles', label: '可見會員類型'},
        {key: 'start', label: '開始'},
        {key: 'end', label: '結束'}
      ],
      currentPage: 1,
      // perPage: 5,
      perPage: 0,
      totalRows: 0
    }
  },
  computed: {
    ...mapState(
      ['loading']
    )
  },
  mounted () {
    this.onFetch()
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    countDownChanged (dismissCountDown) {
      this.dismissCountDown = dismissCountDown
    },
    showAlert () {
      this.dismissCountDown = 5
    },
    onTimeChange (index, target) {
      switch (target) {
        case 'start' :
          var timeStr = this.items[index].start
          this.items[index].start = new Date(timeStr.replace(/-/g, "/"))
          this.items[index].start_time = parseInt(this.items[index].start.getTime()/1000)
          break;
        case 'end':
          var timeStr = this.items[index].end
          this.items[index].end = new Date(timeStr.replace(/-/g, "/"))
          this.items[index].end_time = parseInt(this.items[index].end.getTime()/1000)
          break;
      }
      this.$forceUpdate()
    },
    onAdd () {
      if (this.newMessage !== '') {
        // prepare new item
        var newItem = {}
        newItem.message = this.newMessage
        newItem.url = this.newUrl
        newItem.start_time = parseInt(new Date().getTime()/1000)
        newItem.end_time = parseInt(new Date().getTime()/1000)
        newItem.id = new Date().getTime() + "" // cast to string
        this.items.push(newItem)
        // cleanup
        this.newMessage = ''
        this.newUrl = ''
        // render
        this.cookItem()
      }
    },
    onDelete (index) {
      var that = this
      var imageId = this.items[index].id
      var imageExist = this.items[index].image
      console.log(index, new Date().getTime())
      this.items = this.items.filter((item, idx) => {
        console.log(idx, item)
        return idx !== index
      })
      // delete this image first from s3
      if (imageExist) {
        console.log('ImageID', imageId)
        const url = '/v3/console/eventimage?id=' +imageId
        console.log(url)
        api.request('delete', url)
        .then(function(){
          that.alertCss = 'success'
          that.alertMsg = '圖片先行刪除'
          that.showAlert()
        })
        .catch(function(){
          that.alertCss = 'warning'
          that.alertMsg = '圖片刪除失敗'
          that.showAlert()
        })
      }
    },
    onSave (item) {
      var that = this
      const url = '/v3/console/event'
      console.log('onSave', this.items)
      api.request('put', url, {events: this.items} )
        .then((response) => {
          console.log(response)
          if (item.file) {
            console.log('got file')
            let formData = new FormData()
            formData.append('file', item.file)
            const url = '/v3/console/eventimage?id=' + item.id
            console.log(url)
            api.request('post', url, formData, { headers: { 'Content-Type': 'multipart/form-data' }}
                ).then(function(){
              that.alertCss = 'success'
              that.alertMsg = '儲存成功'
              that.showAlert()
              that.onFetch()
            })
            .catch(function(){
              that.alertCss = 'warning'
              that.alertMsg = '圖片儲存失敗'
              that.showAlert()
              that.onFetch()
            })
          } else {
            that.alertCss = 'success'
            that.alertMsg = '儲存成功'
            that.showAlert()
            that.onFetch()
          }
        }).catch(error => {
          that.alertCss = 'warning'
          that.alertMsg = '這位大大，您的權限不足'
          that.showAlert()
        })
    },
    imageUpload(index) {
      if (this.items[index].file) {
        this.onSave(this.items[index])

      } else {
          this.alertCss = 'warning'
          this.alertMsg = '您未選取任何圖片'
          this.showAlert()
      }
    },
    imageReset(index) {
      console.log('Reset imgage', index)
      this.items[index].file = null
      console.log(this.items[index].file)
    },
    cookItem () {
      for(var i=0; i < this.items.length; i++) {
        this.items[i].start = new Date(this.items[i].start_time*1000)
        this.items[i].end = new Date(this.items[i].end_time*1000)
        this.items[i].file = null
      }
    },
    onFetch () {
      api.request('get', '/v3/console/event/list')
        .then((response) => {
          console.log(response)
          if (response.data && response.data.data && response.data.data.events) {
              this.items = response.data.data.events
              this.cookItem()
          }
        })
      api.request('get', '/v3/console/user/member_roles')
        .then((response) => {
          console.log(response.data.data)
          if (response.data && response.data.data) {
            this.roleOptions = response.data.data
          }
        })
    }
  }
}

</script>

<style scoped>
.card-body >>> table > tbody > tr > td {
  cursor: pointer;
}
img{
    width:100%;
    max-width:60px;
}
</style>
