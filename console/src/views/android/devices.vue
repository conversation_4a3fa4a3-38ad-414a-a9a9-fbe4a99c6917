<template>
  <b-row>
    <b-col cols="12" xl="12">
      <transition name="slide">
      <b-card :header="caption">
          <b-row>
            <b-col sm="12">
            <form v-on:submit.prevent="onFetch">
            <b-form-group>
              <b-input-group>
                <b-input-group-append>
                  <b-button variant="primary" @click="onFetch" >
                    <i class="fa" :class="{'fa-refresh': loading, 'fa-spin': loading}"></i>
                    重新載入現在 android devices 清單
                    </b-button>
                  &nbsp;
                  <b-button variant="warning" @click="onSave" >
                    <i class="fa" :class="{'fa-refresh': loading, 'fa-spin': loading}"></i>
                    更新寫入現在 android devices 清單
                    </b-button>
                </b-input-group-append>
              </b-input-group>
            </b-form-group>
            </form>

            <b-alert :show="dismissCountDown"
                   dismissible
                   variant="warning"
                   @dismissed="dismissCountDown=0"
                   @dismiss-count-down="countDownChanged">
              {{ alertMsg}}
            </b-alert>
            </b-col>
          </b-row>

          <b-form-textarea
            id="textarea"
            v-model="devices"
            placeholder="Enter Android devices"
            rows="30"
          >
          </b-form-textarea>

      </b-card>
      </transition>
    </b-col>
  </b-row>
</template>
<script>
import { mapState } from 'vuex'
import api from '@/api'

export default {
  name: 'AndroidDevices',
  props: {
    caption: {
      type: String,
      default: 'Android Devices'
    }
  },
  data: () => {
    return {
      devices: '',
      alertPermission: '這位大大，您的權限不足',
      alertJSON: 'json 格式錯誤',
      alertMsg : '',
      q: '',
      dismissCountDown: 0
    }
  },
  mounted () {
      this.onFetch()
  },

  computed: {
    ...mapState(
      ['loading']
    )
  },
  methods: {
    countDownChanged (dismissCountDown) {
      this.dismissCountDown = dismissCountDown
    },
    showAlert () {
      this.dismissCountDown = 5
    },
    onSave () {
      try {
        var result = JSON.parse(this.devices)
      } catch(e) {
        this.alertMsg = this.alertJSON
        this.showAlert()
        return
      }
      api.request('put', '/v3/console/android/devices', {android_devices: this.devices})
        .then((response) => {
          if (response.data && response.data.data &&
          response.data.data.android_devices) {
            this.devices = response.data.data.android_devices
          }
        }).catch(error => {
          this.alertMsg = this.alertPermission
          this.showAlert()
        })
    },
    onFetch () {
      api.request('get', '/v3/console/android/devices')
        .then((response) => {
          if (response.data && response.data.data &&
          response.data.data.android_devices) {
            this.devices = response.data.data.android_devices
          }
        }).catch(error => {
          this.alertMsg = this.alertPermission
          this.showAlert()
        })
    }
  }
}
</script>

<style scoped>
.card-body >>> table > tbody > tr > td {
  cursor: pointer;
}
</style>
