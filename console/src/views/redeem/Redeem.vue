<template>
  <b-row>
    <b-col cols="12" xl="12">
      <transition name="slide">
      <b-card :header="caption">
          <b-row>
          <b-col sm="12">
          <form v-on:submit.prevent="onFetch">
          <b-form-group description="輸入要查詢的 coupon ID (序號) 或 GroupID (每組序號對應的群組 ID，也就是下面列表的 Id 按鈕，點即可進入列表)">
            <b-input-group>
              <b-form-input type="text" id="name" v-model="q"></b-form-input>
              <!-- Attach Right button -->
              <b-input-group-append>
                <b-button variant="primary" @click="onFetch" >
                  <i class="fa" :class="{'fa-refresh': loading, 'fa-spin': loading}"></i>
                  Search
                  </b-button>
              </b-input-group-append>
              &nbsp;
              <b-input-group-append>
                <b-button variant="warning" @click="onEdit({})" v-b-tooltip.hover title="您必須有對應的權限，否則無法新增修改">新增序號</b-button>
              </b-input-group-append>
            </b-input-group>
          </b-form-group>
          </form>

          <b-alert :show="dismissCountDown"
             dismissible
             :variant="alertCss"
             @dismissed="dismissCountDown=0"
             @dismiss-count-down="countDownChanged">
             {{ alertMsg}}
          </b-alert>
          </b-col>

          <b-modal v-model="showDetail" ref="formModal"  size="lg" :title="detailTitle" @ok="onOK" @cancel="onCancel">
              <b-form-group>
                <b-row>
                  <b-col>
                    <b-form-group label="Prefix" description="兩個大寫字母，全家販售序號固定為FM">
                      <b-form-input v-if="detailObj.id" v-model="detailObj.prefix" readonly type="text" :state="formState.prefix"></b-form-input>
                      <b-form-input v-else v-model="detailObj.prefix" type="text" :state="formState.prefix"></b-form-input>
                      <b-form-invalid-feedback :state="formState.prefix">
                        兩個大寫字母，全家販售序號專用固定為FM
                      </b-form-invalid-feedback>
                    </b-form-group>
                  </b-col>

                  <b-col>
                    <b-form-group label="價格">
                      <b-form-input v-model="detailObj.price" type="number" :state="formState.price"></b-form-input>
                      <b-form-invalid-feedback :state="formState.price">
                        不能小於0
                      </b-form-invalid-feedback>
                    </b-form-group>
                  </b-col>

                  <b-col>
                    <b-form-group label="未稅價格">
                      <b-form-input v-model="detailObj.price_no_tax" :state="formState.price_no_tax" type="number"></b-form-input>
                      <b-form-invalid-feedback :state="formState.price_no_tax">
                        不能小於0
                      </b-form-invalid-feedback>
                    </b-form-group>
                  </b-col>

                  <b-col>
                    <b-form-group label="手續費">
                      <b-form-input v-model="detailObj.fee" type="number"></b-form-input>
                      <b-form-invalid-feedback :state="formState.fee">
                        不能小於0
                      </b-form-invalid-feedback>
                    </b-form-group>
                  </b-col>
                  <b-col>
                    <b-form-group label="疊加限制次數">
                      <b-form-input v-model="detailObj.usage_limit_per_user" type="number"></b-form-input>
                    </b-form-group>
                  </b-col>
                </b-row>

                <b-form-group label="週期">
                  <b-row class="my-1">
                    <b-col sm="6">
                      <b-form-input v-model="detailObj.duration_value" type="number"></b-form-input>
                        <b-form-invalid-feedback :state="formState.duration_unit">
                          沒有設定週期
                        </b-form-invalid-feedback>
                    </b-col>
                    <b-col sm="6">
                      <b-form-select v-model="detailObj.duration_unit" :state="formState.duration_unit" :options="['', 'day', 'mon', 'year']"></b-form-select>
                        <b-form-invalid-feedback :state="formState.duration_unit">
                          沒有設定週期
                        </b-form-invalid-feedback>
                    </b-col>
                  </b-row>
                </b-form-group>

                <b-form-group label="免費週期" description="免費週期">
                  <b-row class="my-1">
                    <b-col sm="6">
                      <b-form-input v-model="detailObj.free_duration_value" type="number"></b-form-input>
                    </b-col>
                    <b-col sm="6">
                      <b-form-select v-model="detailObj.free_duration_unit" :state="formState.free_duration_unit" :options="['', 'day', 'mon', 'year']"></b-form-select>
                        <b-form-invalid-feedback :state="formState.free_duration_unit">
                          請設定免費週期的時間及正確的數值
                        </b-form-invalid-feedback>
                    </b-col>
                  </b-row>
                </b-form-group>

                <b-row>
                  <b-col sm="6">
                    <b-form-group label="生效日期">
                      <flat-pickr v-model="detailObj.valid_since" :config="flatpickrConfig">
                      </flat-pickr>
                    </b-form-group>
                  </b-col>
                  <b-col sm="6">
                    <b-form-group label="有效期限">
                      <flat-pickr v-model="detailObj.expires_at" :config="flatpickrConfig">
                      </flat-pickr>
                    </b-form-group>
                  </b-col>
                </b-row>

                <b-form-group label="說明" description="JIRA票號連結前請加一個半形分號">
                <b-form-textarea  v-model="detailObj.description" placeholder="請輸入敘述;JIRA 票 URL 連結" rows="3" max-rows="6" >
                </b-form-textarea>
                  <b-form-invalid-feedback :state="formState.description">
                    必須說明敘述
                  </b-form-invalid-feedback>
                </b-form-group>

                <b-form-group label="產品ID" description="有價序號，及序號是綁定某特定產品ID的銷售序號才需要設定，有價序號，只能設定 6，其他綁定產品，請設定產品ID (不能為保留的 4 及 6)">
                <b-form-input v-model="detailObj.product_id" :state="formState.product_id" type="number" @change="onChangeProductID"></b-form-input>
                  <b-form-invalid-feedback :state="formState.product_id">
                    免費序號請設定產品 ID 4 價格為 0，有價序號請設定產品 ID 6 價格大於 0 ，其他產品 ID 為綁定某特定產品 ID的銷售序號，週期，及免費週期留白，不需要設定
                  </b-form-invalid-feedback>
                </b-form-group>

                <b-form-checkbox v-model="detailObj.allow_reuse">序號可以重複被使用</b-form-checkbox>

                <b-row v-if="!detailObj.id">
                  <b-col>
                    <b-form-checkbox v-model="detailObj.is_for_fami_port" @change="onChangeFami">是全家超商銷售序號</b-form-checkbox>
                    <b-form-group label="序號數量">
                      <b-form-input v-model="detailObj.count" :state="formState.count" type="number"></b-form-input>
                      <b-form-invalid-feedback :state="formState.count">
                        不能小於0
                      </b-form-invalid-feedback>
                    </b-form-group>
                  </b-col>
                </b-row>
                <b-form-group label="活動群組" description="同一個活動群組的redeem不可被相同user使用，留空則無限制">
                  <b-form-input v-model="detailObj.campaign_group" type="text"></b-form-input>
                </b-form-group>
              </b-form-group>
          </b-modal>

          </b-row>

        <b-table v-if="items" :hover="hover" :striped="striped" :bordered="bordered" :small="small" :fixed="fixed" responsive="sm" :items="filterItems" :fields="fields" :current-page="currentPage" :per-page="perPage">
          <template slot="is_active" slot-scope="data">

            <b-button  @click="onEdit(data.item)"><i class="fa fa-edit"></i></b-button>&nbsp;
            <strong v-if="data.item.is_active">✔</strong>
          </template>
          <template slot="id" slot-scope="data">
            <b-button variant="primary" v-b-tooltip.hover title="點擊進入此 GroupID 詳細資料頁面，及所有序號列表" @click="rowClicked(data.item.id)">{{ data.item.id }}</b-button>
          </template>
          <template slot="description" slot-scope="data">
            {{data.item.description}}
            <span v-if="data.item.jira_link">
              <br /><a :href="data.item.jira_link" _target="blank">Jira 需求票</a>
            </span>
            <span v-if="data.item.trello_link">
              <br /><a :href="data.item.trello_link" _target="blank">Trello 需求票</a>
            </span>
          </template>
          <template slot="apply" slot-scope="data">
            {{data.item.applyStr}}
          </template>
          <template slot="duration" slot-scope="data">
            {{data.item.durationStr}}
          </template>
          <template slot="start_date" slot-scope="data">
            {{new Date(data.item.start_date).toISOString().split('T')[0]}}
          </template>
          <template slot="end_date" slot-scope="data">
            {{new Date(data.item.end_date).toISOString().split('T')[0]}}
          </template>
          <template slot="is_free" slot-scope="data">
            <strong v-if="data.item.is_free">免費</strong>
            <strong v-else>有價</strong>
          </template>
          <template slot="usage_limit_per_user" slot-scope="data">
            <strong v-if="data.item.usage_limit_per_user">{{data.item.usage_limit_per_user}}</strong>
            <strong v-else>無</strong>
          </template>
        </b-table>
        <!-- <nav>
          <b-pagination size="sm" :total-rows="getRowCount(items)" :per-page="perPage" v-model="currentPage" prev-text="Prev" next-text="Next" hide-goto-end-buttons/>
        </nav> -->
      </b-card>
      </transition>
    </b-col>
  </b-row>
</template>
<script>
import { mapState } from 'vuex'
import api from '@/api'

export default {
  name: 'Redeem',
  props: {
    caption: {
      type: String,
      default: 'Coupon Status'
    },
    hover: {
      type: Boolean,
      default: true
    },
    striped: {
      type: Boolean,
      default: true
    },
    bordered: {
      type: Boolean,
      default: false
    },
    small: {
      type: Boolean,
      default: false
    },
    fixed: {
      type: Boolean,
      default: false
    }
  },
  data: () => {
    return {
      items: [],
      showDetail: false,
      detailTitle: 'Hi detail',
      detailObj: {
        id: null,
        prefix: ''
      },
      flatpickrConfig: {
        enableTime: true,
        altFormat: 'Y-m-d H:i',
        altInput: true,
        dateFormat: 'Z',
        time_24hr: true,
        allowInput: true,
      },
      alertCss: '',
      alertMsg: '',
      dismissCountDown: 0,
      formState: {
        description: null,
        price: null,
        price_no_tax: null,
        fee: null,
        is_for_fami_port: null,
        prefix: null,
        count: null,
        duration: null,
        duration_unit: null,
        free_duration_unit: null
      },
      q: '',
      fields: [
        {key: 'is_active', label: '有效'},
        {key: 'id'},
        {key: 'description', label: '說明'},
        {key: 'used_count', label: '兌換數量'},
        {key: 'total_count', label: '總數'},
        {key: 'apply', label: '兌換率'},
        {key: 'duration', label: '兌換天數'},
        {key: 'start_date', label: '生效日期'},
        {key: 'end_date', label: '有效期限'},
        {key: 'is_free', label: '價格'},
        {key: 'usage_limit_per_user', label: '疊加限制'},
      ],
      currentPage: 1,
      // perPage: 5,
      perPage: 0,
      totalRows: 0
    }
  },
  computed: {
    filterItems () {
        var qs = this.q
        for (var i=0; i < this.items.length; i++) {

          this.items[i].durationStr = this.items[i].duration

          if (this.items[i].durationStr.includes('days')) {
              this.items[i].durationStr = this.items[i].durationStr.replace('days', '天')
          }

          if (this.items[i].durationStr.includes('mons') || this.items[i].durationStr.includes('mon')) {
              this.items[i].durationStr = this.items[i].durationStr.replace('mons', '月')
              this.items[i].durationStr = this.items[i].durationStr.replace('mon', '月')
          }

          if (this.items[i].durationStr.includes('year')) {
              this.items[i].durationStr = this.items[i].durationStr.replace('year', '年')
          }

          if (this.items[i].is_active) {
            this.items[i]._rowVariant = 'success'
          }

          if (this.items[i].allow_reuse) {
            this.items[i].applyStr = 'NA'
          } else {
            this.items[i].applyStr = (this.items[i].used_count / this.items[i].total_count * 100).toFixed(1) + '%'
          }

        }
        return this.items
    },
    ...mapState(
      ['loading']
    )
  },
  mounted () {
    console.log('Redeem Mounted')
    if (this.$route.query.q) {
      this.q = this.$route.query.q
    }
    this.onFetch()
  },
  methods: {
    getRowCount (items) {
      return items.length
    },
    forwardLink (groupid) {
      return `/redeem/detail?q=${groupid.toString()}`
    },
    rowClicked (groupid) {
      const userLink = this.forwardLink(groupid)
      this.$router.push({path: userLink})
    },
    onCancel () {
      this.detailObj = {
        id: null
      }
    },
    onEdit (item) {
      // resetFormStates
      this.resetFormStates()
      // clone it
      this.detailObj = Object.assign({id: null, prefix: ''}, item)

      if (item.id) {
        // update
        this.detailTitle =  '修改 ' + item.description
        var durationArray = this.detailObj.duration.split(' ')
        var free_durationArray = this.detailObj.free_duration.split(' ')

        if (durationArray.length === 2) {
          // should be correct
          this.detailObj.duration_value = parseInt(durationArray[0])
          // days, mons, years
          this.detailObj.duration_unit = durationArray[1].replace('s', '')
        }
        if (free_durationArray.length === 2) {
          // should be correct
          this.detailObj.free_duration_value = parseInt(free_durationArray[0])
          this.detailObj.free_duration_unit = free_durationArray[1].replace('s', '')
        }

        this.detailObj.description = item.description + ';' + item.jira_link
        this.detailObj.valid_since = item.start_date
        this.detailObj.expires_at = item.end_date
      } else {
        // insert
        this.detailTitle =  '新增序號'
        this.detailObj.price = 0
        this.detailObj.price_no_tax = 0
        this.detailObj.fee = 0
        this.detailObj.count = 0
        this.detailObj.is_for_fami_port = false
      }
      this.showDetail = true
    },
    onChangeFami(newVal) {
      if (newVal) {
        // is_for_fami_port
        this.detailObj.prefix = 'FM'
      }
    },
    onChangeProductID(newVal) {
      if (newVal != 4 && newVal != 6) {
        // for product purchase
        this.detailObj.duration_value = null
        this.detailObj.free_duration_value = null
      }
    },
    isValid() {
      var isOK = true
      this.resetFormStates()
      console.log('start validate')

      if (!this.detailObj.description ) {
        this.formState.description = 'invalid'
        isOK = false
      }

      if (this.detailObj.price && this.detailObj.price < 0) {
        this.formState.price = 'invalid'
        isOK = false
      }

      if (this.detailObj.is_for_fami_port && this.detailObj.price <= 0) {
        this.formState.price = 'invalid'
        isOK = false
      }

      if (!/^[A-Z]{2}/.test(this.detailObj.prefix)) {
        this.formState.prefix = 'invalid'
        isOK = false
      }

      if (!this.detailObj.is_for_fami_port && this.detailObj.prefix == 'FM') {
        this.formState.prefix = 'invalid'
        isOK = false
      }

      if (this.detailObj.is_for_fami_port && this.detailObj.prefix != 'FM') {
        this.formState.prefix = 'invalid'
        isOK = false
      }

      if (this.detailObj.price_no_tax && this.detailObj.price_no_tax < 0) {
        this.formState.price_no_tax = 'invalid'
        isOK = false
      }

      if (!this.detailObj.id && (!this.detailObj.count || this.detailObj.count <= 0)) {
        this.formState.count = 'invalid'
        isOK = false
      }

      if (this.detailObj.duration_value && ( this.detailObj.duration_value <= 0 || !this.detailObj.duration_unit)) {
        this.formState.duration_unit = 'invalid'
        isOK = false
      }

      if (this.detailObj.free_duration_value && ( this.detailObj.free_duration_value <= 0 || !this.detailObj.free_duration_unit)) {
        this.formState.free_duration_unit = 'invalid'
        isOK = false
      }

      if (this.detailObj.product_id) {
        if (this.detailObj.product_id == 4 && this.detailObj.price > 0) {
          this.formState.product_id = 'invalid'
          isOK = false
        }

        if (this.detailObj.product_id == 6 && this.detailObj.price <= 0) {
          this.formState.product_id = 'invalid'
          isOK = false
        }
      }

      return isOK
    },
    onOK (modalEvent) {
      modalEvent.preventDefault()
      if (!this.isValid()) {
        return
      }
      var that = this
      // handle duration
      if (this.detailObj.duration_value && this.detailObj.duration_unit != '') {
        this.detailObj.duration = this.detailObj.duration_value + ' ' + this.detailObj.duration_unit + 's'
      } else {
          this.detailObj.duration = '00:00:00'
      }

      if (this.detailObj.free_duration_value && this.detailObj.free_duration_unit != '' && this.detailObj.free_duration_value > 0) {
        this.detailObj.free_duration = this.detailObj.free_duration_value + ' ' + this.detailObj.free_duration_unit + 's'
      } else {
        // empty interval for postgresql field
        this.detailObj.free_duration = '00:00:00'
      }

      if (!this.detailObj.price) {
        this.detailObj.price = 0
      }
      if (!this.detailObj.price_no_tax) {
        this.detailObj.price_no_tax = 0
      }
      if (!this.detailObj.fee) {
        this.detailObj.fee = 0
      }

      if (!this.detailObj.usage_limit_per_user) {
        this.detailObj.usage_limit_per_user = null
      }
      this.detailObj.price = parseInt(this.detailObj.price)
      this.detailObj.price_no_tax = parseInt(this.detailObj.price_no_tax)
      this.detailObj.fee = parseInt(this.detailObj.fee)
      this.detailObj.count = parseInt(this.detailObj.count)

      const url = '/v3/console/redeem'
      api.request('post', url, this.detailObj )
        .then((response) => {
          that.onCancel()
          that.$refs.formModal.hide()
          that.onFetch()
        }).catch(function(){
          that.alertCss = 'warning'
          that.alertMsg = '儲存失敗'
          that.showAlert()
          that.$refs.formModal.hide()
          that.onFetch()
        })
    },
    resetFormStates() {
      this.formState = {
        description : null,
        price: null,
        price_no_tax: null,
        fee: null,
        is_for_fami_port: null,
        prefix: null,
        count: null,
        duration: null,
        duration_unit: null,
        free_duration_unit: null
      }
    },
    countDownChanged (dismissCountDown) {
      this.dismissCountDown = dismissCountDown
    },
    showAlert () {
      this.dismissCountDown = 5
    },
    onFetch () {
      if (this.q !== '') {
        console.log('To Detail')
        this.$router.push({ path: '/redeem/detail', query: {q: this.q} });
        return
      }
      api.request('get', '/v3/console/redeem?q='+this.q)
        .then((response) => {
          if (response.data && response.data.data &&
          response.data.data.coupons) {
            this.items = response.data.data.coupons
          }
        })
    }

  }
}
</script>

<style scoped>
.card-body >>> table > tbody > tr > td {
  cursor: pointer;
}
</style>
