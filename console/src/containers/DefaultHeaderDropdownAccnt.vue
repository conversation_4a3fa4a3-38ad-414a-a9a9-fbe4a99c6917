<template>
  <AppHeaderDropdown right no-caret>
    <template slot="header">
      <img
        :src="user && user.avatar"
        class="img-avatar"
        :alt="user && user.name" />
    </template>\
    <template slot="dropdown">
      <!-- <b-dropdown-divider /> -->
      <b-dropdown-item v-if="user && user.name && user.email"><i class="fa fa-shield" />{{ user.name }} {{ user.email }}</b-dropdown-item>
      <b-dropdown-item @click.prevent="onLogout()"><i class="fa fa-lock" />Logout</b-dropdown-item>
    </template>
  </AppHeaderDropdown>
</template>

<script>
import { mapState } from 'vuex'
import { HeaderDropdown as AppHeaderDropdown } from '@coreui/vue'
import auth from '../auth'

export default {
  name: 'DefaultHeaderDropdownAccnt',
  components: {
    AppHeaderDropdown
  },
  data: () => {
    return { itemsCount: 42 }
  },
  methods: {
    onLogout () {
      auth.logout()
      this.$router.push('/login')
    }
  },
  computed: {
    ...mapState([
      'user'
    ])
  }
}
</script>
