export default {
  TOGGLE_LOADING (state) {
    state.loading = !state.loading
  },
  SET_LOADING (state, loading) {
    state.loading = loading
  },
  TOGGLE_SEARCHING (state) {
    state.searching = (state.searching === '') ? 'loading' : ''
  },
  SET_USER (state, user) {
    state.user = user
  },
  SET_TOKEN (state, token) {
    state.token = token
  },
  SET_WEBSOCKET (state, ws) {
    state.ws = ws
  }
}
