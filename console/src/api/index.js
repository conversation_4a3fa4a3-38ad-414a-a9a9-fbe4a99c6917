import axios from 'axios'
import {apiBaseURL} from '@/config'

export default {
  request (method, uri, data = null, moreHeaders = {}) {
    if (!method) {
      console.error('API function call requires method argument')
      return
    }

    if (!uri) {
      console.error('API function call requires uri argument')
      return
    }

    const url = apiBaseURL + uri
    const headers = Object.assign({'Authorization': 'Bearer ' + localStorage.getItem('token')}, moreHeaders)
    return axios({ method, url, data, headers, timeout: 25000})
  }
}

