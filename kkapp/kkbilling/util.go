package kkbilling

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"errors"
	"fmt"
	"io"
)

// reference
// https://stackoverflow.com/questions/68390508/aes-cbc-encrypt-in-golang-decrypt-in-angular-cryptojs

// PKCS7Padding for Encrypt
func PKCS7Padding(ciphertext []byte, blockSize int) []byte {
	padding := blockSize - len(ciphertext)%blockSize
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(ciphertext, padtext...)
}

// PKCS7UnPadding for Decrypt
func PKCS7UnPadding(origData []byte) []byte {
	length := len(origData)
	unpadding := int(origData[length-1])
	return origData[:(length - unpadding)]
}

//AesCBCEncrypt  filling the 16 bits of the key key, 24, 32 respectively corresponding to AES-128, AES-192, or AES-256.
func AesCBCEncrypt(rawData, key []byte) ([]byte, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		panic(err)
	}

	//fill the original
	blockSize := block.BlockSize()
	rawData = PKCS7Padding(rawData, blockSize)
	// Initial vector IV must be unique, but does not need to be kept secret
	cipherText := make([]byte, blockSize+len(rawData))
	//block size 16
	iv := cipherText[:blockSize]
	fmt.Println("iv", iv)

	if _, err := io.ReadFull(rand.Reader, iv); err != nil {
		panic(err)
	}

	//block size and initial vector size must be the same
	mode := cipher.NewCBCEncrypter(block, iv)
	mode.CryptBlocks(cipherText[blockSize:], rawData)

	return cipherText, nil
}

// AesCBCDncrypt decrypt
func AesCBCDncrypt(encryptData, key []byte) ([]byte, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		panic(err)
	}

	blockSize := block.BlockSize()

	if len(encryptData) < blockSize {
		panic("ciphertext too short")
	}
	iv := encryptData[:blockSize]
	encryptData = encryptData[blockSize:]

	// CBC mode always works in whole blocks.
	if len(encryptData)%blockSize != 0 {
		panic("ciphertext is not a multiple of the block size")
	}

	mode := cipher.NewCBCDecrypter(block, iv)

	// CryptBlocks can work in-place if the two arguments are the same.
	mode.CryptBlocks(encryptData, encryptData)
	// Unfill
	encryptData = PKCS7UnPadding(encryptData)
	return encryptData, nil
}

// Encrypt encrypt
func Encrypt(rawData, key []byte) (string, error) {
	data, err := AesCBCEncrypt(rawData, key)
	if err != nil {
		return "", err
	}
	return base64.StdEncoding.EncodeToString(data), nil
}

// Dncrypt decrypt
// first 16 bytes IV
// rest of rawData is base64 encode encrypt data
func Dncrypt(rawData string, key []byte) (dnData []byte, err error) {

	if len(rawData) < 16 {
		err = errors.New("data error")
		return
	}

	data, err := base64.StdEncoding.DecodeString(rawData[16:])
	if err != nil {
		return
	}

	encryptData := append([]byte(rawData[:16]), data...)
	keySha256 := sha256.Sum256([]byte(key))
	dnData, err = AesCBCDncrypt(encryptData, keySha256[:32])
	if err != nil {
		return
	}
	return
}
