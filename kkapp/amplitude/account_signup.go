package amplitude

import (
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"log"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/pkg/httpreq"
	zlog "github.com/KKTV/kktv-api-v3/pkg/log"
)

// AccountSignup Account Signed Up
type AccountSignup struct {
	UserID             string                 `json:"user_id"`
	Role               string                 `json:"-"`
	Birthday           string                 `json:"-"`
	Gender             string                 `json:"-"`
	PhoneNumber        string                 `json:"-"`
	Email              string                 `json:"-"`
	FbID               string                 `json:"-"`
	FbName             string                 `json:"-"`
	KKSub              string                 `json:"-"`
	KKIdentifier       string                 `json:"-"`
	Platform           string                 `json:"-"`
	OSName             string                 `json:"-"`
	OSVersion          string                 `json:"-"`
	DeviceBrand        string                 `json:"-"`
	DeviceManufacturer string                 `json:"-"`
	DeviceModel        string                 `json:"-"`
	Carrier            string                 `json:"-"`
	EventType          string                 `json:"event_type"`
	Time               time.Time              `json:"time"`
	EventProperties    map[string]interface{} `json:"event_properties"`
}

// Send to amplitude
// please refer
// https://github.com/KKTV/kktv-sns-v0-amplitude/blob/develop/listeners/transaction_completed_v2.js
func (event *AccountSignup) Send() {
	var apikey, role string
	if kkapp.App.Debug {
		apikey = devAPIKey
	} else {
		apikey = prodAPIKey
	}

	if event.Role == "freetrial" {
		role = "free trial"
	} else {
		role = event.Role
	}

	//  send identity refer https://github.com/KKTV/kktv-api-v0-auth/blob/develop/token.js
	{

		userData := fmt.Sprintf(`
{
	"user_id": "%s",
	"platform": "%s",
	"os_name": "%s",
	"os_version": "%s", 
	"device_brand": "%s", 
	"device_manufacturer": "%s", 
	"device_model": "%s", 
	"carrier": "%s",
    "user_properties": {
		"$setOnce": {
			"account signed up date": "%s"
		},
		"$set": {
			"account in guest mode": "false",
			"account signed up type": "%s",
			"account phone number": "%s",
			"account fb id": "%s",
			"account fb name": "%s",
			"account kkbox sub": "%s",
			"account kkbox user identifier": "%s",
			"account current membership": "%s",
			"account type": "users",
			"account gender": "%s",
			"account birthday": "%s",
			"account state": "active",
			"account ever cancelled": "false",
			"account ever transacted": "false",
			"total times logged in": 0,
			"total times logged in on iOS": 0,
			"total times logged in on Android": 0,
			"total times logged in on Web": 0,
			"total transactions count": 0,
			"total transactions amount": 0.0
		}
	}
}`,
			event.UserID,
			event.Platform,
			event.OSName,
			event.OSVersion,
			event.DeviceBrand,
			event.DeviceManufacturer,
			event.DeviceModel,
			event.Carrier,
			event.Time.Format("2006-01-02"),
			event.EventProperties["request by"],
			event.PhoneNumber,
			event.FbID,
			event.FbName,
			event.KKSub,
			event.KKIdentifier,
			role,
			event.Gender,
			event.Birthday)

		form := url.Values{}
		form.Add("api_key", apikey)
		form.Add("identification", userData)
		// payload := bytes.NewBuffer(jsonBytes)
		req, err := http.NewRequest("POST", amplitudeIdentityURL, strings.NewReader(form.Encode()))

		if err != nil {
			zlog.Warn("AccountSignup amplitude event: fail to new identify-api request").Err(err).Send()
			return
		}

		req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
		client := &http.Client{Timeout: time.Duration(time.Second * 10)}
		resp, err := client.Do(req)

		if err != nil {
			zlog.Warn("AccountSignup amplitude event: fail to fire identify-api request").Err(err).Send()
			return
		}

		defer resp.Body.Close()
		statusCode := resp.StatusCode
		body, _ := ioutil.ReadAll(resp.Body)
		if statusCode != 200 {
			zlog.Warn("AccountSignup amplitude event: fail to send identify-api, not 200").
				Interface("event", event).
				Bytes("resp_body", body).Int("status_code", statusCode).Send()
		}
	}

	// send event
	{

		jsonBytes, err := json.Marshal([]interface{}{event})
		if err != nil {
			log.Println("[ERROR] event json error", err)
			return
		}
		form := url.Values{}
		form.Add("api_key", apikey)
		form.Add("event", string(jsonBytes))
		req, err := http.NewRequest("POST", amplitudeHTTPURL, strings.NewReader(form.Encode()))
		if err != nil {
			zlog.Warn("AccountSignup amplitude event: fail to new httpapi request").Err(err).Send()
			return
		}

		req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
		client := &http.Client{Timeout: time.Duration(time.Second * 10)}
		resp, err := client.Do(req)
		if err != nil {
			zlog.Warn("AccountSignup amplitude event: fail to fire httpapi request").Err(err).Send()
			return
		}

		defer resp.Body.Close()
		statusCode := resp.StatusCode
		body, _ := ioutil.ReadAll(resp.Body)

		if statusCode != 200 {
			zlog.Warn("AccountSignup amplitude event: fail to send http API, not 200").
				Interface("event", event).
				Bytes("resp_body", body).Int("status_code", statusCode).Send()
		}

	}
}

func (event *AccountSignup) ScanFromRequest(r *http.Request) {
	event.Platform = r.Header.Get(httpreq.HeaderPlatform)
	platform, _ := GetPlatformAndDeviceMode(r)
	if event.Platform == "" { //fallback to parse from UA if such Platform is miss from client
		event.Platform = platform
	}
	event.OSName = r.Header.Get(httpreq.HeaderOS)
	event.OSVersion = r.Header.Get(httpreq.HeaderOSVersion)
	event.DeviceBrand = r.Header.Get(httpreq.HeaderDeviceBrand)
	event.DeviceManufacturer = r.Header.Get(httpreq.HeaderDeviceManuFact)
	event.DeviceModel = r.Header.Get(httpreq.HeaderDeviceModel)
	event.Carrier = r.Header.Get(httpreq.HeaderCarrier)
}

// NewAccountSignup create a new event
func NewAccountSignup(userID string, role string, gender string, birthday string, phone string, requestBy string, request *http.Request) (event *AccountSignup, err error) {
	if userID == "" || role == "" {
		return nil, errors.New("no user info")
	}
	event = new(AccountSignup)
	event.EventType = "Account Signed Up"
	event.UserID = userID
	event.Role = role
	event.Gender = gender
	event.PhoneNumber = phone
	event.Birthday = birthday

	event.Time = time.Now()
	event.ScanFromRequest(request)

	property := make(map[string]interface{})
	property["log version"] = "0.7.0"
	property["account signed up type"] = "kkid" // default

	property["request by"] = "kktv clients"
	if requestBy != "" {
		property["request by"] = requestBy
		property["account signed up type"] = requestBy
	}

	property["account user id"] = userID
	property["signing process duration"] = time.Now().Unix()
	event.EventProperties = property
	return
}
