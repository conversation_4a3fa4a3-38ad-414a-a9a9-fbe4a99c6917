package amplitude

import (
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"log"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/pkg/httpreq"
	zlog "github.com/KKTV/kktv-api-v3/pkg/log"
)

// AccountLogin Account Logged In
type AccountLogin struct {
	UserID             string                 `json:"user_id"`
	Role               string                 `json:"-"`
	Birthday           string                 `json:"-"`
	Gender             string                 `json:"-"`
	PhoneNumber        string                 `json:"-"`
	Email              string                 `json:"-"`
	FbID               string                 `json:"-"`
	FbName             string                 `json:"-"`
	KKSub              string                 `json:"-"`
	KKIdentifier       string                 `json:"-"`
	DeviceMode         string                 `json:"-"`
	Platform           string                 `json:"-"`
	OSName             string                 `json:"-"`
	OSVersion          string                 `json:"-"`
	Devi<PERSON><PERSON>rand        string                 `json:"-"`
	DeviceManufacturer string                 `json:"-"`
	DeviceModel        string                 `json:"-"`
	Carrier            string                 `json:"-"`
	EventType          string                 `json:"event_type"`
	Time               time.Time              `json:"time"`
	EventProperties    map[string]interface{} `json:"event_properties"`
}

// Send to amplitude
func (event *AccountLogin) Send() {
	var apikey string
	if kkapp.App.Debug {
		apikey = devAPIKey
	} else {
		apikey = prodAPIKey
	}

	{

		userData := fmt.Sprintf(`
{
	"user_id": "%s",
	"platform": "%s",
	"os_name": "%s",
	"os_version": "%s", 
	"device_brand": "%s", 
	"device_manufacturer": "%s", 
	"device_model": "%s", 
	"carrier": "%s",
	"user_properties": {
		"$set": {
			"account in guest mode": "false",
			"account signed up type": "%s",
			"account phone number": "%s",
			"account fb id": "%s",
			"account fb name": "%s",
			"account state": "active"
		},
		"$add": {
			"total times logged in": 1,
			"total times logged in on %s": 1,
		}
	}
}`,
			event.UserID,
			event.Platform,
			event.OSName,
			event.OSVersion,
			event.DeviceBrand,
			event.DeviceManufacturer,
			event.DeviceModel,
			event.Carrier,
			event.EventProperties["account signed up type"],
			event.PhoneNumber,
			event.FbID,
			event.FbName,
			event.Platform,
		)

		form := url.Values{}
		form.Add("api_key", apikey)
		form.Add("identification", userData)
		zlog.Debug("sending Amplitude Login event").Str("api_key", apikey).Str("event", userData).Send()
		req, err := http.NewRequest("POST", amplitudeIdentityURL, strings.NewReader(form.Encode()))

		if err != nil {
			// handle error
			log.Println("[ERROR]", err)
			return
		}

		req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
		client := &http.Client{Timeout: time.Duration(time.Second * 10)}
		resp, err := client.Do(req)

		if err != nil {
			// handle error
			log.Println("err:", err)
			return
		}

		defer resp.Body.Close()
		statusCode := resp.StatusCode
		body, _ := ioutil.ReadAll(resp.Body)

		if statusCode != 200 {
			zlog.Warn("AccountLogin amplitude event: fail to send identify API, not 200").
				Interface("event", event).
				Bytes("resp_body", body).Int("status_code", statusCode).Send()
		}
	}

	// send event
	{

		jsonBytes, err := json.Marshal([]interface{}{event})

		if err != nil {
			log.Println("[ERROR] event json error", err)
			return
		}
		form := url.Values{}
		form.Add("api_key", apikey)
		form.Add("event", string(jsonBytes))
		req, err := http.NewRequest("POST", amplitudeHTTPURL, strings.NewReader(form.Encode()))

		if err != nil {
			// handle error
			log.Println("[ERROR]", err)
			return
		}

		req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
		client := &http.Client{Timeout: time.Duration(time.Second * 10)}
		resp, err := client.Do(req)
		if err != nil {
			// handle error
			log.Println("err:", err)
			return
		}

		defer resp.Body.Close()
		statusCode := resp.StatusCode
		body, rErr := ioutil.ReadAll(resp.Body)
		if rErr != nil || statusCode != 200 {
			zlog.Warn("AccountLogin amplitude event: fail to send http API, not 200").
				Interface("event", event).
				Bytes("resp_body", body).Int("status_code", statusCode).Send()
		}

	}
}

// GetPlatformAndDeviceMode use User-Agent to pick the platform and device mode
// device mode:  android, android_tv, android_tv_cast,  web,  ios, apple_tv
// user agent:
// "com.kktv.ios.kktv/1.2.3"
// "com.kktv.ios.kktv/0.0.1 (build:1; device:Apple TV; buildName:1; model:AppleTV5,3; os:tvOS; osv:12.1.1; appId:KKTV; locale:en_TW; lang:en"
// "com.kktv.kktv/0.9.27-dev_rc.204 (build:1;"
//  com.kktv.kktv.tv/1.50.0 (getUserAgent:140; model:BRAVIA 4K GB; device:BRAVIA_ATV2; os:Android; osv:Sony/BRAVIA_ATV2_TW/BRAVIA_ATV2:9/PTT1.190515.001.S43/671651:user/release-keys; appId:KKTV;)
func GetPlatformAndDeviceMode(r *http.Request) (platform, deviceMode string) {
	ug := r.Header.Get("User-Agent")
	deviceMode = "web" // default
	platform = "Web"

	switch {
	case strings.Contains(ug, "Android"):
		platform = "Android"
		if strings.Contains(ug, "com.kktv.kktv.tv") { // android tv
			deviceMode = "android_tv"
		} else {
			deviceMode = "android"
		}

	case strings.Contains(ug, "iOS"): // iphone, ipad
		deviceMode = "ios"
		platform = "iOS"

	case strings.Contains(ug, "tvOS"): // apple tv
		deviceMode = "apple_tv"
		platform = "iOS"

	case strings.Contains(ug, "axios"): // web client server
		deviceMode = "web"
		platform = "Web"
	}
	return
}

func (event *AccountLogin) ScanFromRequest(r *http.Request) {
	event.Platform = r.Header.Get(httpreq.HeaderPlatform)
	platform, deviceMode := GetPlatformAndDeviceMode(r)
	if event.Platform == "" { //fallback to parse from UA if such Platform is miss from client
		event.Platform = platform
	}
	event.DeviceMode = deviceMode

	event.OSName = r.Header.Get(httpreq.HeaderOS)
	event.OSVersion = r.Header.Get(httpreq.HeaderOSVersion)
	event.DeviceBrand = r.Header.Get(httpreq.HeaderDeviceBrand)
	event.DeviceManufacturer = r.Header.Get(httpreq.HeaderDeviceManuFact)
	event.DeviceModel = r.Header.Get(httpreq.HeaderDeviceModel)
	event.Carrier = r.Header.Get(httpreq.HeaderCarrier)
}

// NewAccountLogin create a new event
func NewAccountLogin(userID string, signUpType string, request *http.Request) (event *AccountLogin, err error) {
	if userID == "" {
		return nil, errors.New("no user info")
	}
	event = new(AccountLogin)
	event.EventType = "Account Logged In"
	event.UserID = userID
	event.Time = time.Now()

	event.ScanFromRequest(request)

	property := make(map[string]interface{})
	property["log version"] = "0.7.0"
	property["account signed up type"] = "kkid" // "kkid" "phone number" "facebook"
	if signUpType != "" {
		property["account signed up type"] = signUpType
	}

	property["account user id"] = userID
	property["signing process duration"] = time.Now().Unix()
	property["device mode"] = event.DeviceMode
	event.EventProperties = property
	return
}
