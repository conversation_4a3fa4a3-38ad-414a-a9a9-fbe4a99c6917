package kkpayment

import (
	"bytes"
	"crypto/sha256"
	_ "embed"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/datastore"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/wrapper"
	"github.com/KKTV/kktv-api-v3/pkg/amplitudelib"
	"github.com/KKTV/kktv-api-v3/pkg/billing"
	"github.com/KKTV/kktv-api-v3/pkg/clock"
	"github.com/KKTV/kktv-api-v3/pkg/database"
	"github.com/KKTV/kktv-api-v3/pkg/datetimer"
	"github.com/KKTV/kktv-api-v3/pkg/decrypt"
	"github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/KKTV/kktv-api-v3/pkg/secret"
	"github.com/KKTV/kktv-api-v3/pkg/testutils"
	"github.com/go-zoo/bone"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"github.com/unrolled/render"
)

var _ = func() error {
	secret.Init("test")
	return nil
}()

type BillingSubscriptionChangedIntegrationTestSuite struct {
	suite.Suite
	r           *require.Assertions
	app         *bone.Mux
	dbContainer *testutils.Container
	dbUserPool  database.DB

	ctrl                *gomock.Controller
	mockBillingClient   *billing.MockClient
	mockClock           *clock.MockClock
	mockAmplitudeClient *amplitudelib.MockClient

	secretKey   []byte
	paymentRepo wrapper.PaymentInfoService
	userRepo    wrapper.UserService
}

func TestBillingSubscriptionChangedIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(BillingSubscriptionChangedIntegrationTestSuite))
}

func (suite *BillingSubscriptionChangedIntegrationTestSuite) SetupSuite() {
	log.Init("test")
	suite.r = suite.Require()
	suite.secretKey = getSecretKey()
	suite.app = bone.New()
	suite.ctrl = gomock.NewController(suite.T())
	suite.mockClock = clock.NewMockClock(suite.ctrl)
	suite.mockAmplitudeClient = amplitudelib.NewMockClient(suite.ctrl)

	dbContainer, connPool := testutils.SetupTestDatabase(testutils.DBUser)
	suite.dbContainer = dbContainer
	suite.dbUserPool = connPool.Master().Unsafe()
	userRedisPool := datastore.NewRedisPool([]string{"localhost:6379"})

	suite.userRepo = wrapper.NewUserService(suite.dbUserPool)
	suite.paymentRepo = wrapper.NewPaymentInfoService(suite.dbUserPool)

	secret.Init("test")
	suite.mockBillingClient = billing.NewMockClient(suite.ctrl)
	kkapp.App = &kkapp.Context{
		Clock:           suite.mockClock,
		RedisUser:       userRedisPool,
		Render:          render.New(),
		DbUser:          connPool,
		BillingClient:   suite.mockBillingClient,
		AmplitudeClient: suite.mockAmplitudeClient,
	}

	suite.app.PostFunc("/v3/billing/webhooks/subscription_changed", BillingSubscriptionChangedWebhook)
}

func getSecretKey() []byte {
	secretAccessKey := secret.Values.BillingSecretAccessKey
	keySha256 := sha256.Sum256([]byte(secretAccessKey))
	return keySha256[:32]
}

func (suite *BillingSubscriptionChangedIntegrationTestSuite) TearDownTest() {
}

func (suite *BillingSubscriptionChangedIntegrationTestSuite) TearDownAllSuite() {
	suite.ctrl.Finish()
	err := suite.dbContainer.Close()
	if err != nil {
		suite.T().Errorf("Failed to terminate db container: %v", err)
	}
}

func TestEncrypt(t *testing.T) {
	req := require.New(t)
	r := billing.SubscriptionChangedWebhookData{
		CustomerContractData: billing.CustomerContractData{
			Identifier: "josie",
		},
	}
	rawData, err := json.Marshal(r)
	req.NoError(err)
	b, err := encrypt(getSecretKey(), rawData)
	req.NoError(err)

	body := billing.WebhookReqBody{Data: b}
	req.NoError(body.DecryptData())
}

func encrypt(key []byte, rawData []byte) (string, error) {
	iv := []byte("UCKgmhb+jWg43xx8")
	encryptedData, err := decrypt.Aes256Encrypt(key, rawData, iv)
	if err != nil {
		return "", fmt.Errorf("failed to encrypt data: %w", err)
	}
	encryptedBase64 := base64.StdEncoding.EncodeToString(encryptedData)
	result := append(iv, []byte(encryptedBase64)...)

	return string(result), nil
}

func (suite *BillingSubscriptionChangedIntegrationTestSuite) encrypt(obj any) string {
	rawData, err := json.Marshal(obj)
	if err != nil {
		suite.FailNow("Failed to marshal object")
	}
	key := suite.secretKey
	result, err := encrypt(key, rawData)
	if err != nil {
		suite.FailNow("Failed to encrypt data")
	}
	return result
}

type contractSubscriptionStatus struct {
	billing.APIChangedState[string]
	SubscribedProduct billing.Product `json:"subscribed_product"`
}

func (suite *BillingSubscriptionChangedIntegrationTestSuite) TestBillingSubscriptionChangedWebhook() {

	var (
		membershipObj = func(roleName dbuser.MemberRole) []interface{} {
			return []interface{}{
				map[string]interface{}{"role": roleName.String()},
			}
		}
	)
	var (
		genRequestPayload = func(obj any) []byte {
			encData := suite.encrypt(obj)
			return []byte(fmt.Sprintf(`{"access_key_id":"DBE4758542535CB16BF9","data":"%v"}`, encData))
		}
	)

	suite.givenDBData()
	suite.mockClock.EXPECT().Now().Return(time.Date(2024, 4, 29, 0, 0, 0, 0, datetimer.LocationTaipei)).AnyTimes()

	testcases := []struct {
		name       string
		reqBody    []byte
		given      func()
		thenAssert func(code int, body []byte)
	}{
		{
			name: "received PREMIUM subscription to expired user, should update user membership and extend expired date",
			/* origin payload
			{"identifier":"55ecae54-d95d-4f00-8449-e9a208e09e4b","contract":{"current_transaction":{},"next_transaction":{"product":{"identifier":"kktv.vip.cc.1m.newpromo.sub","name":"信用卡每月扣款首購首月80","payment_type":"credit_card","plan":"KKTV VIP (單人)","interval_amount":"月","recurring":false,"introductory_offer":true,"trial":false,"pre_order":false,"price":80,"introductory_offerable":false},"calculate_base_time":1713801600},"last_order_number":"20240415806331","subscription_status":{"is":"ongoing","was":"na"},"subscribed_product":{"identifier":"KKTV.Monthly.cc.149","name":"信用卡月繳方案","payment_type":"credit_card","plan":"KKTV VIP (單人)","interval_amount":"月","recurring":true,"introductory_offer":false,"trial":false,"pre_order":false,"price":149,"introductory_offerable":true},"last_expired_at":1716480000,"valuable_transaction_count":1,"valuable_transaction_amount":80,"has_free_trial":true,"has_introductory_offer":false,"capabilities":{"roles":["premium"],"family_member_count":1,"simultaneous_playback_device_count":0}},"changes":{"subscription_status":{"was":"na","is":"ongoing"},"subscribed_product":{"was":null,"is":"KKTV.Monthly.cc.149"}},"orders":{"previous":null,"last_fulfilled":null},"iat":1713171972}
			*/
			reqBody: []byte("{\"access_key_id\":\"DBE4758542535CB16BF9\",\"data\":\"UCKgmhb+jWg43xx8TmqLtc/1V/X2eXtZFp4KPpz24OkI1+dGi8YhjMphBZG7ohsU0LK6PdsPkTIenygwe/cnpQgTkNOI2NptGSyGI6+RfvHDhACH9UXPdKaiw2Js/iNHkL9HpUk0nuSp2Fm9sG0NAwkfmvaMRGUWzbons8ET/14DjDcdn+eD4Dx9qzd1/GA+2chszZlyodF3Fn3P1bCtmcUHQHJ/AHHfZeKAHJtR0ksksiWE3XnQh7UAF69xEFH8jjB3HUkGfJqy+d10mX6wLjrn/l0jO2sSBdP1yLeskivVWKdIvCLroesgBbChLH1vzEJB5oYZuGtn+sI44JmrRQfNNnoKUSGTH5gWu5/OwWkF9vy82bCstPGGAPIHKLNt7Bmkmn6SGmLtvwmmSdn3yrDRV17jUBqh56vMH8FYkNnSPzUVd5qOoDHdJQUmbE0UjBV8prBR6fDGc+bWtzhbhTYurLymrd4NZAWijMmkBq0K88/YqFqjC/E8VOsCSOBkTzihTfc1k8BYD0CsRT3+xk2QwvzuHBVCsoqX/PnopHwyJu9hTlpPNtUfLiejbcLfO5S6Y34Fypt+poJcmAxTz3xEvYyf4xofZlpq6qygN9hTaSnMAzEOe58ovybYDrOwEUsyhdxveIJoskIzi/SfGzFIaN6ZRIlqnDnke4bfmmfQwLDeXuF0H3U2JGqb3n4z0RP/z9FSk8mdacu3HJheg7O4GGDwUbr0wioxVFH9NnhyJlwDxM3k1xtub2990C/kG+3mxxQnV/wNyeog3KAO7Sqh1irdlm7gTi1ma/vP5AYrEmwGLp2ZMwZsKiSLzvFPIVkxehZN4ftu4jOu8d+TyNn25CrIAHwGkM41/gDL8+mw/yOFqhDxQddH23n9sZw2tKYjRc0wd3r1v4qoa/KQACRTod1MdRO3Qog6bJWfjwnTcKGqtFQcyEcXqOeFu5iO4SV4cR3GLEmwnpgxMu5+V9eziVDe4gLMWPg9ieZYS4M/ucGd/wWML6BUBIzXcO0//y4bRDbug5VmTCOutiNMbZKpd4YvHTOXEjJhGa5NgZJVjhSY6tFWS86u72QtAP2zW9NlA28iuTXacpiFPkEV2eBvv2jF9wYW9x6u2107xg+5lp03DUjvLghRCfsLZS0hY6dYH5ZksAqx+FePbhqBw1eVqZW6boe96/1AJ7nAXM5Rk4gSPr1jlvK9oZiZGKYkEAld7nnp4RBVNSHrWKMIR1z6jjP29KZ7ReH6620gjrsWf9ohVud1YyxI5cZb8kmiRT5Rkw4cOLb8r/zqgwzEcq/E/qzTuQWf8ab+7wNaE3ZqfvGav+wAZI7cIbExAe5Ya+bN9+ciF9cK/Ym+xDH9hZbEmWiowE5FwniPa9fewD+Oi5i8Fj717BnHRWMxMtaO2BDpsIpA2RtA8JUns3EQHhdcNetCeIILUgBB/3f41ehMcyy9v4SCEqKORwotQ8mkY/8DDl8VXOLlQ0tmBVJZZIHPKDfLb4Q19HWGMLa2k5ub7vHWwqAXWyScWV6Il2wKO2PACGMDcItCXp53xzEs2WrrUzAXzAlaO835NCB6V0cskVGfGcFV90PrfgKPRwavXqRSkTRRNks84Kga+QljE86EJQWFtTqf/4MrE1RB76cuFUglNzJG9igfp0/XUq4hdbpGT6PcVtChNB1sn7dmOdaTwIBcRAM79oZeVA==\"}"),
			given:   func() {},
			thenAssert: func(code int, body []byte) {
				suite.Equal(http.StatusOK, code)
				// check user's role, membership, expired date is updated
				userID := "55ecae54-d95d-4f00-8449-e9a208e09e4b"
				suite.assertThatUserIsUpdated(userID, "premium", "general", true, dbuser.Membership{{Role: dbuser.MemberRolePremium}}, time.Unix(1716480000, 0))
				suite.assertThatPaymentInfoUpdated(userID)
				suite.assertThatAuditLogCreated(userID, []dbuser.AuditLogDifference{
					{Field: "role", Old: "expired", New: "premium"},
					{Field: "auto_renew", Old: false, New: true},
					{Field: "expired_at", Old: "2016-11-21T00:00:00Z", New: "2024-05-23T16:00:00Z"},
					{Field: "membership", Old: membershipObj(dbuser.MemberRoleExpired), New: membershipObj(dbuser.MemberRolePremium)},
				})
			},
		},
		{
			name: "received paid:anime subscription to freeTrial user, should update user membership and extend expired date",
			reqBody: func() []byte {
				product := billing.Product{
					Identifier: "kktv.animepass.cc.monthly.149",
					Name:       "Anime Pass", PaymentType: "credit_card",
					Recurring: true,
					Price:     149,
				}
				data := billing.SubscriptionChangedWebhookData{
					CustomerContractData: billing.CustomerContractData{
						Identifier: "josie",
						Contract: billing.Contract{
							SubscriptionStatus: contractSubscriptionStatus{
								APIChangedState:   billing.APIChangedState[string]{Is: "ongoing", Was: "na"},
								SubscribedProduct: product,
							},
							SubscribedProduct: product,
							LastExpiredAt:     1716480000,
							Capabilities:      billing.APICapabilities{Roles: []string{"paid:anime"}},
						},
					},
					Changes: billing.SubscriptionChanges{
						SubscriptionStatus: billing.APIChangedState[string]{Is: "ongoing", Was: "na"},
						SubscribedProduct:  billing.APIChangedState[string]{Is: "kktv.animepass.cc.monthly.149", Was: ""},
					},
				}
				return genRequestPayload(data)
			}(),
			thenAssert: func(code int, body []byte) {
				suite.r.Equal(http.StatusOK, code)

				userID := "josie"
				suite.assertThatUserIsUpdated(
					userID, "premium", "general", true, dbuser.Membership{{Role: dbuser.MemberRolePaidAnime}}, time.Unix(1716480000, 0))
				suite.assertThatPaymentInfoUpdated(userID)
				suite.assertThatAuditLogCreated(userID, []dbuser.AuditLogDifference{
					{Field: "role", Old: dbuser.MemberRoleFreeTrial.String(), New: dbuser.MemberRolePremium.String()},
					{Field: "auto_renew", Old: false, New: true},
					{Field: "expired_at", Old: "2024-05-01T00:00:00Z", New: "2024-05-23T16:00:00Z"},
					{Field: "membership", Old: membershipObj(dbuser.MemberRoleFreeTrial), New: membershipObj(dbuser.MemberRolePaidAnime)},
				})
			},
		},
		{
			name: "received StateChangedToExpired to premium user, should update expired date",
			reqBody: func() []byte {
				data := billing.SubscriptionChangedWebhookData{
					CustomerContractData: billing.CustomerContractData{
						Identifier: "expired-user-id",
						Contract: billing.Contract{
							LastExpiredAt:   1699977600,
							LastOrderNumber: "20231030603190",
							SubscriptionStatus: contractSubscriptionStatus{
								APIChangedState: billing.APIChangedState[string]{Is: "na"},
							},
							SubscribedProduct: billing.Product{PaymentType: creditCardPaymentType},
						},
					},
					Changes: billing.SubscriptionChanges{
						State: billing.APIChangedState[billing.ContractState]{Was: billing.ContractStateActive, Is: billing.ContractStateExpired},
					},
				}
				return genRequestPayload(data)
			}(),
			given: func() {
				suite.mockAmplitudeEventSent()
			},
			thenAssert: func(code int, body []byte) {
				suite.r.Equal(http.StatusOK, code)
				userID := "expired-user-id"
				suite.assertThatUserIsUpdated(
					userID, "expired", "general", false, dbuser.MembershipExpired, time.Unix(1699977600, 0))
				suite.assertThatPaymentInfoUpdated(userID)
				suite.assertThatAuditLogCreated(userID, []dbuser.AuditLogDifference{
					{Field: "role", Old: dbuser.MemberRolePremium.String(), New: dbuser.MemberRoleExpired.String()},
					{Field: "auto_renew", Old: true, New: false},
					{Field: "expired_at", Old: "2024-05-29T00:00:00Z", New: "2023-11-14T16:00:00Z"},
					{Field: "membership", Old: membershipObj(dbuser.MemberRolePaidAnime), New: membershipObj(dbuser.MemberRoleExpired)},
				})
			},
		},
		{
			name: "received Family plan to premium user, SHOULD update expired date AND upgrade family members",
			reqBody: func() []byte {
				product := billing.Product{
					Identifier: "kktv.family.cc.monthly.599", Name: "Family Happy",
					PaymentType: "credit_card", Recurring: true, Price: 599,
				}
				data := billing.SubscriptionChangedWebhookData{
					CustomerContractData: billing.CustomerContractData{
						Identifier: "family-user-id",
						Contract: billing.Contract{
							CurrentTransaction: struct {
								Product           billing.Product `json:"product"`
								CalculateBaseTime int64           `json:"calculate_base_time"`
							}{
								Product:           product,
								CalculateBaseTime: 1713801600,
							},
							SubscriptionStatus: contractSubscriptionStatus{
								APIChangedState:   billing.APIChangedState[string]{Is: "ongoing", Was: "na"},
								SubscribedProduct: product,
							},
							SubscribedProduct: product, LastExpiredAt: 1716480000,
							Capabilities: billing.APICapabilities{
								FamilyMemberCount: 4,
								Roles:             []string{"premium"},
							},
						},
					},
					Changes: billing.SubscriptionChanges{
						SubscriptionStatus: billing.APIChangedState[string]{Is: "ongoing", Was: "na"},
						SubscribedProduct:  billing.APIChangedState[string]{Is: "kktv.family.cc.monthly.599", Was: ""},
					},
				}
				return genRequestPayload(data)
			}(),
			thenAssert: func(code int, body []byte) {
				suite.r.Equal(http.StatusOK, code)
				userID := "family-user-id"
				expired := time.Unix(1716480000, 0)
				suite.assertThatUserIsUpdated(
					userID, "premium", "general", true, dbuser.MembershipPremiumOnly, expired)
				suite.assertThatPaymentInfoUpdated(userID)
				suite.assertThatAuditLogCreated(userID, []dbuser.AuditLogDifference{
					{Field: "role", Old: dbuser.MemberRoleFreeTrial.String(), New: dbuser.MemberRolePremium.String()},
					{Field: "auto_renew", Old: false, New: true},
					{Field: "expired_at", Old: "2024-04-29T00:00:00Z", New: "2024-05-23T16:00:00Z"},
					{Field: "membership", Old: membershipObj(dbuser.MemberRoleFreeTrial), New: membershipObj(dbuser.MemberRolePremium)},
				})
				familyUsers, err := suite.userRepo.ListFamilyMembersByHostUserID(userID)
				suite.r.NoError(err)
				for _, u := range familyUsers {
					suite.checkUserDetail(u, "premium", "general", expired, true, dbuser.MembershipPremiumOnly)
				}
			},
		},
	}
	for _, tc := range testcases {
		suite.Run(tc.name, func() {
			if tc.given != nil {
				tc.given()
			}

			req := httptest.NewRequest(http.MethodPost, "/v3/billing/webhooks/subscription_changed", bytes.NewBuffer(tc.reqBody))
			w := httptest.NewRecorder()
			suite.app.ServeHTTP(w, req)

			tc.thenAssert(w.Code, w.Body.Bytes())
		})
	}

}

func (suite *BillingSubscriptionChangedIntegrationTestSuite) mockAmplitudeEventSent() *gomock.Call {
	return suite.mockAmplitudeClient.EXPECT().SendEvent(gomock.Any()).Return(nil)
}

func (suite *BillingSubscriptionChangedIntegrationTestSuite) assertThatAuditLogCreated(userID string, diffs []dbuser.AuditLogDifference) {
	auditLogs := suite.findAuditLogsByUserID(userID)
	suite.r.Len(auditLogs, 1)
	// check detail diff
	suite.r.ElementsMatch(diffs, auditLogs[0].Detail.Diff)
}

func (suite *BillingSubscriptionChangedIntegrationTestSuite) assertThatUserIsUpdated(userID string, role, typ string, autoRenew bool, membership dbuser.Membership, expiredAt time.Time) {
	u, err := suite.userRepo.GetActiveByID(userID)
	suite.r.NoError(err)
	suite.checkUserDetail(u, role, typ, expiredAt, autoRenew, membership)
}

func (suite *BillingSubscriptionChangedIntegrationTestSuite) checkUserDetail(u *dbuser.User, role string, typ string, expiredAt time.Time, autoRenew bool, membership dbuser.Membership) {
	suite.r.Equal(role, u.Role)
	suite.r.Equal(typ, u.Type)
	suite.r.True(expiredAt.Equal(u.ExpiredAt.Time))
	suite.r.Equal(autoRenew, u.AutoRenew)
	suite.r.Equal(membership, u.Membership)
}

func (suite *BillingSubscriptionChangedIntegrationTestSuite) assertThatPaymentInfoUpdated(userID string) {
	pi, err := suite.paymentRepo.GetByUserID(userID)
	suite.r.NoError(err)
	suite.r.Equal("credit_card", pi.PaymentType.String)
	suite.r.Equal(userID, pi.UserID)
}

func (suite *BillingSubscriptionChangedIntegrationTestSuite) findAuditLogsByUserID(userID string) []dbuser.AuditLog {
	var auditLogs []dbuser.AuditLog
	suite.Require().NoError(suite.dbUserPool.Select(&auditLogs,
		`SELECT * FROM audit_logs WHERE target_id = $1 AND target_type = $2 AND mod_action = $3`, userID, "user", "update"))
	return auditLogs
}

//go:embed testdata/BillingSubscriptionChanged.sql
var testData string

func (suite *BillingSubscriptionChangedIntegrationTestSuite) givenDBData() {
	conn := kkapp.App.DbUser.Master().Unsafe()
	_, err := conn.Exec(testData)
	if err != nil {
		suite.r.FailNow("Failed to insert test data", err)
	}
}
