package kkpayment

import (
	"strings"

	"github.com/KKTV/kktv-api-v3/kkapp/model/dbuser"
	plog "github.com/KKTV/kktv-api-v3/pkg/log"
)

const maxOrderRetryTimes = 20

// retryIfSqlDuplicated<PERSON>ey will execute the f function and retry only if there is a duplicated key error occur
func retryIfSqlDuplicatedKey(f func(newID string) error, firstTimeID string, reachMaxRetryCallback func(isMaxRetry bool)) (err error) {
	codeGen := dbuser.NewRandomGenerator()
	// insert the new order
	newID := firstTimeID
	maxRetry := true
	for i := 0; i < maxOrderRetryTimes; i++ {
		err = f(newID)
		if retry := err != nil && strings.Contains(err.Error(), "duplicate key value violates unique constraint"); !retry {
			maxRetry = false
			break
		}
		// refer to dbuser.GenerateOrderID, the last 6 digits are random numbers
		newID = firstTimeID[:len(firstTimeID)-6] + codeGen.NumDigits(6)
		plog.Warn("kkpayment/order: retrying because of order with duplicated id").
			Str("new_order_id", newID).Str("duplicated_order_id", firstTimeID).Send()
	}
	reachMaxRetryCallback(maxRetry)
	return
}
