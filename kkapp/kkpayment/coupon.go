package kkpayment

import (
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/facebook"
	"github.com/KKTV/kktv-api-v3/kkapp/kkutil"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbredeem"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbuser"
	"github.com/KKTV/kktv-api-v3/pkg/amplitudelib"
	"github.com/KKTV/kktv-api-v3/pkg/datetimer"
	plog "github.com/KKTV/kktv-api-v3/pkg/log"
	usermodel "github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/go-zoo/bone"
	"gopkg.in/guregu/null.v3"
)

const (
	couponPaymentType              = "coupon"
	couponPricelessProductID int64 = 4
	couponPriceProductID     int64 = 6
	couponPaymentOrderPrefix       = "04"
)

var (
	sqlcoupon = map[string]string{

		"createorder": `INSERT INTO orders
  (id, user_id, product_id, payment_type, start_date, end_date, order_date, price, price_no_tax, tax_rate, fee, info) VALUES
 (:id,:user_id,:product_id,:payment_type,:start_date,:end_date,:order_date,:price,:price_no_tax,:tax_rate,:fee,:info);`,

		"updateorder": `UPDATE orders SET status = :status, info = :info, realized_at = :realized_at, price = :price WHERE id = :id;`,

		"productByName": `SELECT id, name, duration, free_duration, price::numeric::int, price_no_tax::numeric::int, fee, tax_rate, payment_type, payment_type_code,
		auto_renew, as_subscribe, item_name, item_unit, external_product_id, discount_duration, discount_price::numeric::int, bundle FROM
		products WHERE name = $1 AND payment_type = $2 AND active = true;`,

		"productByID": `SELECT id, name, duration, free_duration, price::numeric::int, price_no_tax::numeric::int, fee, tax_rate, payment_type, payment_type_code,
		auto_renew, as_subscribe, item_name, item_unit, external_product_id, discount_duration, discount_price::numeric::int, bundle FROM
		products WHERE id = $1 AND payment_type = $2 AND active = true;`,

		"user": `SELECT u.id, u.email, u.phone, u.gender, u.birthday, date_part('epoch', u.expired_at)::int as expired_at,
 u.created_at, u.role, u.auto_renew, u.type, u.created_by,
 payment.email as payment_email, payment.payment_type,
 EXISTS ( SELECT 1 FROM orders WHERE user_id = $1 AND status IN ('ok', 'error') AND product_id IN (
	SELECT id FROM products WHERE as_subscribe = true) LIMIT 1) AS as_subscribe FROM users u LEFT JOIN
 payment_info payment ON payment.user_id = u.id WHERE u.id = $1 `,

		"code": `SELECT c.id, c.code, g.id as group_id, g.prefix, g.price, g.duration, g.usage_limit_per_user,
 g.allow_reuse, g.valid_since, g.expires_at, g.price_no_tax, g.description, g.free_duration, g.fee,
 g.channel, g.product_id, c.user_id, c.issue_user_id, c.revoked_at, g.campaign_group FROM
 coupon_codes c JOIN coupon_groups g ON c.group_id = g.id WHERE c.code = $1`,
		"campaign_count": `select count(id) from coupon_groups cg where id in (
			select group_id from coupon_codes cc where user_id = $1
		) 	and campaign_group  = $2;`,
	}
)

type AccumulatedDuration struct {
	Months int64 `json:"months"`
	Days   int64 `json:"days"`
}

type CounponApiResponse struct {
	Role      string `json:"role"`
	ExpiredAt int64  `json:"expired_at"`
	OrderID   string `json:"order_id"`
	Product   struct {
		PaymentType string              `json:"payment_type"`
		Duration    AccumulatedDuration `json:"duration"`
	} `json:"product"`
}

// CouponCodeGroup struct
type CouponCodeGroup struct {
	// ID from coupon_codes, others from coupon_groups
	ID                int64       `db:"id" json:"id"`
	Prefix            string      `db:"prefix" json:"prefix"`
	Price             int64       `db:"price" json:"price"`
	Duration          string      `db:"duration" json:"duration"`
	UsageLimitPerUser null.Int    `db:"usage_limit_per_user" json:"usage_limit_per_user"`
	AllowReuse        bool        `db:"allow_reuse" json:"allow_reuse"`
	ValidSince        time.Time   `db:"valid_since" json:"valid_since"`
	ExpiresAt         time.Time   `db:"expires_at" json:"expires_at"`
	CreatedAt         time.Time   `db:"created_at" json:"created_at"`
	UpdatedAt         time.Time   `db:"updated_at" json:"updated_at"`
	PriceNoTax        int64       `db:"price_no_tax" json:"price_no_tax"`
	Description       string      `db:"description" json:"description"`
	FreeDuration      string      `db:"free_duration" json:"free_duration"`
	Fee               int64       `db:"fee" json:"fee"`
	Channel           null.String `db:"channel" json:"channel"`
	ProductID         null.Int    `db:"product_id" json:"product_id"`
	CampaignGroup     null.String `db:"campaign_group" json:"campaign_group"`

	// from coupon_codes
	Code        string      `db:"code" json:"code"`
	GroupID     int64       `db:"group_id" json:"group_id"`
	UserID      null.String `db:"user_id" json:"user_id"`
	IssueUserID null.String `db:"issue_user_id" json:"issue_user_id"`
	DeviceID    null.String `db:"device_id" json:"device_id"`
	RevokedAt   null.Time   `db:"revoked_at" json:"revoked_at"`
}

// CouponUser
type CouponUser struct {
	ID           string      `db:"id" json:"id"`
	Email        null.String `db:"email"`
	Phone        null.String `db:"phone"`
	Gender       null.String `db:"gender"`
	Birthday     null.String `db:"birthday"`
	Role         string      `db:"role" json:"role"`
	Type         string      `db:"type" json:"type"`
	CreatedAt    null.Time   `db:"created_at" json:"createdAt"`
	CreatedBy    null.String `db:"created_by" json:"createdBy"`
	ExpiredAt    int64       `db:"expired_at" json:"expiredAt"`
	AutoRenew    bool        `db:"auto_renew" json:"autoRenew"`
	AsSubscribe  bool        `db:"as_subscribe"`
	PaymentEmail null.String `db:"payment_email"`
	PaymentType  null.String `db:"payment_type"`

	ProductName      string
	DurationUnit     string
	DurationInt      int
	CouponCode       string
	TransactionOrder *dbuser.Order
	CouponCodeGroup  *CouponCodeGroup
}

// CreateOrder create cooupon order
func (user *CouponUser) CreateOrder() (order *dbuser.Order, err error) {
	log.Println("[INFO] create order", user.ID)
	var orderDate, endDate time.Time
	order = new(dbuser.Order)
	loc, _ := time.LoadLocation("Asia/Taipei")
	now := time.Now().In(loc)
	// default orderDate
	orderDate = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, loc).AddDate(0, 0, 1).UTC()

	if user.ExpiredAt > orderDate.Unix() {
		orderDate = time.Unix(user.ExpiredAt, 0)
	}

	order.OrderDate = orderDate
	order.StartDate = orderDate

	// duration
	endDate = kkutil.AddDuration(orderDate, user.DurationUnit, user.DurationInt)
	// free duration
	freeDurationUnit, freeDurationInt := kkutil.ParseDuration(user.CouponCodeGroup.FreeDuration)
	if freeDurationInt > 0 {
		endDate = kkutil.AddDuration(endDate, freeDurationUnit, freeDurationInt)
	}

	order.EndDate = endDate

	// price >= 0 product_id is 6 redeem.price
	// price == 0 product_id is 4 redeem.priceless
	if user.CouponCodeGroup.Price > 0 {
		order.ProductID = couponPriceProductID
	} else {
		order.ProductID = couponPricelessProductID
	}

	order.Price = user.CouponCodeGroup.Price
	order.PriceNoTax = user.CouponCodeGroup.PriceNoTax
	order.Fee = user.CouponCodeGroup.Fee

	order.PaymentType = couponPaymentType
	order.UserID = user.ID
	// coupon payment order ID prefix is 04
	order.ID = dbuser.GenerateOrderID(couponPaymentOrderPrefix, order.OrderDate)
	info := make(map[string]interface{})
	info["redeemCode"] = user.CouponCode
	info["description"] = strings.Split(user.CouponCodeGroup.Description, ";")[0]

	if user.CouponCodeGroup.Channel.Valid {
		info["saleChannel"] = user.CouponCodeGroup.Channel.String
	}

	infoBytes, _ := json.Marshal(info)
	order.Info = null.String{NullString: sql.NullString{String: string(infoBytes), Valid: true}}

	err = user.InsertOrder(order)
	if err != nil {
		log.Println("[ERROR]", err)
		err = errors.New("Database error")
		return nil, err
	}
	return order, err
}

// InsertOrder insert the user order
func (user *CouponUser) InsertOrder(order *dbuser.Order) (err error) {
	// insert the new order
	db := kkapp.App.DbUser.Master()
	_, err = db.NamedExec(sqlcoupon["createorder"], order)
	return
}

// ValidatePayment the user payment
func (user *CouponUser) ValidatePayment() (err error) {
	if user.Role != model.UserRolePremium {
		return nil
	}

	if user.Role == model.UserRolePremium && user.Type == model.UserTypeKKboxPrime {
		// role is premium && type is prime
		return errors.New("prime user cannot use redeem code")
	}

	// premium only could use redeem price greater than 0
	if user.Role == model.UserRolePremium && user.CouponCodeGroup.Price == 0 {
		return errors.New("vip user cannot use redeem code")
	}

	if !user.PaymentType.Valid {
		// no any payemnt_info yet
		return
	}

	errMsg := fmt.Sprintf("%s user cannot use redeem code", user.PaymentType.String)
	if user.AutoRenew {
		return errors.New(errMsg)
	}
	return nil
}

// ValidateCode the redeem code
func (user *CouponUser) ValidateCode() (err error) {
	db := kkapp.App.DbRedeem.Slave()

	// load it if not loaded yet
	if user.CouponCodeGroup == nil {
		couponGroup := new(CouponCodeGroup)
		err = db.Get(couponGroup, sqlcoupon["code"], user.CouponCode)
		if err != nil {
			log.Println("[ERROR] coupon", err)
			// wrap certain error message
			return errors.New("The input redeem code does not exist.")
		}
		user.CouponCodeGroup = couponGroup
	}
	return nil
}

// IsAvailable check is this code available for this user
func (user *CouponUser) IsAvailable() (err error) {

	if !user.CouponCodeGroup.AllowReuse &&
		(user.CouponCodeGroup.UserID.Valid || user.CouponCodeGroup.RevokedAt.Valid) {
		return errors.New("Redeem code has been used or not available.")
	}
	now := time.Now()
	if now.Before(user.CouponCodeGroup.ValidSince) {
		return errors.New("Redeem code is not yet valid.")
	}
	if now.After(user.CouponCodeGroup.ExpiresAt) {
		return errors.New("Redeem code has expired.")
	}

	var usageLimit int
	if user.CouponCodeGroup.AllowReuse {
		usageLimit = 1
	} else if user.CouponCodeGroup.UsageLimitPerUser.Valid {
		usageLimit = int(user.CouponCodeGroup.UsageLimitPerUser.Int64)
	}

	if usageLimit > 0 {
		// need to check usage limit
		if user.CouponCodeGroup.AllowReuse {
			if user.getRedemptionCount() >= usageLimit {
				return errors.New("Already used limited number of codes in this group.")
			}
		} else {
			if user.getUsedCountInGroup() >= usageLimit {
				return errors.New("Already used limited number of codes in this group.")
			}
		}
	}

	// persistance duration at user instance after check available
	user.DurationUnit, user.DurationInt = kkutil.ParseDuration(user.CouponCodeGroup.Duration)
	return nil
}

// getRedemptionCount for this redeem code
func (user *CouponUser) getRedemptionCount() (count int) {
	db := kkapp.App.DbRedeem.Slave()
	db.Select(&count, `SELECT COUNT(1) AS redemption_count FROM redemption_logs WHERE code = $1 AND user_id = $2`, user.CouponCode, user.ID)
	return
}

// getRedemptionCount for this redeem code
func (user *CouponUser) getUsedCountInGroup() (count int) {
	db := kkapp.App.DbRedeem.Slave()
	db.Get(&count, `SELECT COUNT(1) AS used_count FROM coupon_codes WHERE group_id = $1 AND user_id = $2`, user.CouponCodeGroup.GroupID, user.ID)
	return
}

// logRedeemCode update this redeem code
func (user *CouponUser) logRedeemCode() (err error) {
	db := kkapp.App.DbRedeem.Master()
	if user.CouponCodeGroup.AllowReuse {
		_, err = db.Exec(`INSERT INTO redemption_logs (user_id, code) VALUES ($1, $2)`, user.ID, user.CouponCode)
	} else {
		_, err = db.Exec(`UPDATE coupon_codes SET user_id = $1, updated_at = NOW() WHERE code = $2`, user.ID, user.CouponCode)
	}
	return err
}

// Realize the user order
func (user *CouponUser) Realize(order *dbuser.Order) error {
	if order == nil {
		return errors.New("Invalid parameters")
	}
	plog.Info("CouponUser: Realize: start").Interface("order", order).Str("user_id", user.ID).Send()

	user.TransactionOrder = order
	duration := order.EndDate.Sub(order.StartDate)

	var expiredDate time.Time
	if user.ExpiredAt < time.Now().Unix() {
		loc, _ := time.LoadLocation("Asia/Taipei")
		now := time.Now().In(loc)
		expiredDate = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, loc).AddDate(0, 0, 1).Add(duration).UTC()
	} else {
		expiredDate = time.Unix(user.ExpiredAt, 0).Add(duration).UTC()
	}
	user.ExpiredAt = expiredDate.Unix()

	order.Status = null.String{NullString: sql.NullString{String: "ok", Valid: true}}
	order.RealizedAt = null.Time{Time: time.Now().UTC().Truncate(time.Second), Valid: true}

	paymentInfo := new(dbuser.PaymentInfo)
	paymentInfo.UserID = user.ID
	paymentInfo.PaymentType = null.String{NullString: sql.NullString{String: couponPaymentType, Valid: true}}

	// 1. transaction start
	// 2. update order
	// 3. update user info
	// 4. update payment_info
	// 5. commit or rollback
	db := kkapp.App.DbUser.Master()
	tx, err := db.Beginx()
	if err != nil {
		return err
	}

	defer func() {
		if err != nil {
			_ = tx.Rollback()
		}
		plog.Info("CouponUser: Realize: end").Interface("order", order).Str("user_id", user.ID).Send()
	}()

	_, err = tx.NamedExec(sqlcoupon["updateorder"], order)
	if err != nil {
		err = fmt.Errorf("update order fail: %w", err)
		return err
	}

	// only auto_renew == false user could use redeem code
	err = updateUser(tx, user.ID, usermodel.RolePremium.String(), usermodel.MembershipPremiumOnly, expiredDate, false, usermodel.TypeGeneral.String())
	if err != nil {
		err = fmt.Errorf("update user fail: %w", err)
		return err
	}

	_, err = tx.NamedExec(sqlshare["upsertpayment"], paymentInfo)
	if err != nil {
		err = fmt.Errorf("upsert paymentInfo fail: %w", err)
		return err
	}
	err = tx.Commit()
	if err != nil {
		err = fmt.Errorf("commit fail: %w", err)
		return err
	}
	return nil
}

// Deal the user order
func (user *CouponUser) Deal() (err error) {
	var order *dbuser.Order

	// coupon code
	if user.CouponCodeGroup == nil {
		err = user.ValidateCode()
		if err != nil {
			log.Printf("[ERROR] userid:%s - validate coupon code failed err:%v \n", user.ID, err)
			return
		}
	}

	// create order
	// not create order if coupon_group do have product_id
	// and not 4 or 6
	var isProductRedeem bool
	if user.CouponCodeGroup.ProductID.Valid &&
		user.CouponCodeGroup.ProductID.Int64 != couponPriceProductID &&
		user.CouponCodeGroup.ProductID.Int64 != couponPricelessProductID {
		isProductRedeem = true
	}

	// coupon code available
	err = user.IsAvailable()
	if err != nil {
		log.Printf("[ERROR] userid:%s - coupon code is not available - err:%v \n", user.ID, err)
		return
	}

	// not product redeem code
	// couponPriceProductID || couponPricelessProductID
	if !isProductRedeem {
		err = user.ValidatePayment()
		if err != nil {
			log.Printf("[ERROR] userid:%s - validate payment failed - err:%v \n", user.ID, err)
			return
		}

		order, err = user.CreateOrder()
		if err != nil {
			log.Printf("[ERROR] userid:%s - create coupon order failed - err:%v \n", user.ID, err)
			return
		}
	}

	// realize if have order
	if order != nil {
		err = user.Realize(order)
		if err != nil {
			plog.Error("CouponUser: Deal: realize order fail").Err(err).
				Str("user_id", user.ID).Interface("order", order).Send()
			err = errors.New("Database error")
			return
		}
	}

	user.logRedeemCode()

	// send amplitude if order not nil
	if order != nil {
		var dbCouponGroup dbredeem.DBCouponGroup
		dbCouponGroup.ID = int(user.CouponCodeGroup.GroupID)
		dbCouponGroup.Price = int(user.CouponCodeGroup.Price)

		dbCouponGroup.Duration = toDashDuration(user.CouponCodeGroup.Duration)
		dbCouponGroup.FreeDuration = toDashDuration(user.CouponCodeGroup.FreeDuration)

		go sendRedeemedEventToTrackingService(user.ID, user.CouponCode, dbCouponGroup)

		var conversionEvents facebook.ConversionEvents
		orderPrice := float32(order.Price)
		price := float32(dbCouponGroup.Price)
		var quantity int32 = 1
		conversionEvents.Append(facebook.ConversionEvent{
			EventName:      "KKTV_RedeemComplete_Conv",
			EventTime:      time.Now().Unix(),
			EventID:        order.ID,
			EventSourceURL: "https://www.kktv.me",
			UserData: &facebook.UserData{
				Email:          user.Email.String,
				Phone:          user.Phone.String,
				Gender:         user.Gender.String,
				Birthday:       user.Birthday.String,
				Country:        "tw",
				SubscriptionID: order.ID,
			},
			CustomData: &facebook.CustomData{
				Currency:    "twd",
				ContentName: strconv.Itoa(dbCouponGroup.ID),
				Value:       &orderPrice,
				Contents: []facebook.CustomDataContent{
					{
						ID:        user.CouponCode,
						Quantity:  &quantity,
						ItemPrice: &price,
					},
				},
			},
		})
		if kkapp.App.Env == "prod" {
			go facebook.SendConversionEvents(conversionEvents)
		}

	}
	return
}

func sendRedeemedEventToTrackingService(userID, couponCode string, group dbredeem.DBCouponGroup) {
	evt := &amplitudelib.AccountCouponRedeemedEvent{}
	evt.UserID = userID
	evt.CouponType = "free gift"
	if group.Price > 0 {
		evt.CouponType = "for sale"
	}
	evt.PreviousPaymentType = fmt.Sprintf("coupon, %s", evt.CouponType)
	evt.TotalTransactionsAmount = int64(group.Price)
	evt.TotalTransactionsAmountViaCoupon = int64(group.Price)
	evt.Price = int64(group.Price)
	evt.Code = couponCode
	evt.GroupID = int64(group.ID)
	evt.CouponExp = group.ExpiresAt.Add(-1 * time.Second).In(datetimer.LocationTaipei).Format("2006-01-02")
	evt.SetDurationFromCouponGroupDuration(group.Duration, group.FreeDuration)
	if err := kkapp.App.AmplitudeClient.SendEvent(evt); err != nil {
		plog.Warn("CouponUser: send event to Amplitude fail").Err(err).Interface("event", evt).Send()
	}
}

// NewCouponUser get a CouponUser via userID and coupon code
func NewCouponUser(userID, code string) (user *CouponUser, err error) {
	user = new(CouponUser)
	db := kkapp.App.DbUser.Slave()
	err = db.Get(user, sqlcoupon["user"], userID)
	if err != nil {
		return nil, err
	}

	user.CouponCode = code
	return user, nil
}

// PostCouponUser for /v3/redeem/code post handler
func PostCouponUser(w http.ResponseWriter, r *http.Request) {
	var err error
	var userid string

	response := model.MakeOk()
	user := r.Context().Value("user")
	// redeem_code from url
	redeem_code := bone.GetValue(r, "redeem_code")

	defer func() {
		if err != nil {
			log.Println("[ERROR] coupon fail", err)
			errStr := err.Error()
			response.Status.Type = "Forbidden"

			switch errStr {
			case "prime user cannot use redeem code":
				response.Status.Subtype = "DeniedPrimeUser"
			case "iap user cannot use redeem code":
				response.Status.Subtype = "DeniedIAPUser"
			case "iab user cannot use redeem code":
				response.Status.Subtype = "DeniedIABUser"
			case "telecom user cannot use redeem code":
				response.Status.Subtype = "DeniedTelecomUser"
			case "mod cannot use redeem code":
				response.Status.Subtype = "DeniedMODUser"
			case "bandott user cannot use redeem code":
				response.Status.Subtype = "DeniedBandottUser"
			case "credit_card user cannot use redeem code":
				response.Status.Subtype = "DeniedCreditcardUser"
			case "tstar user cannot use redeem code":
				response.Status.Subtype = "DeniedTstarUser"
			case "vip user cannot use redeem code":
				response.Status.Subtype = "DeniedVIPUser"
			case "Redeem code is not yet valid.":
				response.Status.Subtype = "RedeemCodeIsNotYetValid"
			case "Already used limited number of codes in this group.":
				response.Status.Subtype = "UsageLimitReached"

			case "Invalid parameters", "The input redeem code does not exist.":
				response.Status.Type = "NotFound"
				response.Status.Subtype = "RedeemCodeNotFound"

			case "Redeem code has been used or not available.":
				response.Status.Type = "Gone"
				response.Status.Subtype = "RedeemCodeIsNotAvailable"

			case "Redeem code has expired.":
				response.Status.Type = "Gone"
				response.Status.Subtype = "RedeemCodeHasExpired"
			default:
				response.Status.Subtype = "INTERNAL SERVER ERROR"
			}

			response.Status.Message = errStr
			kkapp.App.Render.JSON(w, http.StatusBadRequest, response)
			return
		}

		// no error
		kkapp.App.Render.JSON(w, http.StatusOK, response)
	}()

	if user != nil {
		if userModel, ok := user.(model.JwtUser); ok && !userModel.IsGuest() {
			userid = userModel.Sub
		}
	}

	if userid == "" {
		log.Println("[ERROR] coupon user")
		err = errors.New("Invalid parameters")
		return
	}

	u, err := NewCouponUser(userid, redeem_code)
	if err != nil {
		return
	}

	// coupon code
	err = u.ValidateCode()

	if err != nil {
		log.Printf("[ERROR] userid:%s - validate coupon code failed err:%v \n", u.ID, err)
		return
	}

	// block user to apple the purchade redeem code
	if u.CouponCodeGroup != nil &&
		u.CouponCodeGroup.ProductID.Valid &&
		u.CouponCodeGroup.ProductID.Int64 != couponPriceProductID &&
		u.CouponCodeGroup.ProductID.Int64 != couponPricelessProductID {
		err = errors.New("The input redeem code does not exist.")
		return
	}

	if u.IsUserUseTheSameCampaignRedeem() {
		err = errors.New("Already used limited number of codes in this group.")
		return
	}
	err = u.Deal()

	log.Println("[INFO] coupon apply result", err, u.ID, redeem_code)

	if err == nil && u.TransactionOrder != nil {
		var redeemDays int64
		redeemDays = int64(u.TransactionOrder.EndDate.Sub(u.TransactionOrder.StartDate).Hours() / 24)
		var redeemResp CounponApiResponse
		redeemResp.Role = model.UserRolePremium
		redeemResp.OrderID = u.TransactionOrder.ID
		redeemResp.ExpiredAt = u.ExpiredAt
		redeemResp.Product.PaymentType = "coupon"
		redeemResp.Product.Duration = AccumulatedDuration{Months: 0, Days: redeemDays}
		response.Data = redeemResp
	}
}

func (user *CouponUser) IsUserUseTheSameCampaignRedeem() bool {
	var count int
	if user.CouponCodeGroup.CampaignGroup.Valid {
		db := kkapp.App.DbRedeem.Slave()
		db.Get(&count, sqlcoupon["campaign_count"], user.ID, user.CouponCodeGroup.CampaignGroup.String)

		if count > 0 {
			return true
		}
	}

	return false
}

// 驗證優惠券(對應方案折價)
// VerifyCouponCode, path: /v3/redeem_verify
func VerifyCouponCode(w http.ResponseWriter, r *http.Request) {
	var err error
	var userid string
	var payLoad = struct {
		RedeemCode  string `json:"redeem_code"`
		ProductName string `json:"product_name"`
		PaymentType string `json:"payment_type"`
	}{}

	response := model.MakeOk()
	user := r.Context().Value("user")

	defer func() {
		if err != nil {
			errStr := err.Error()
			response.Status.Type = "FAIL"
			response.Status.Message = errStr
			kkapp.App.Render.JSON(w, http.StatusBadRequest, response)
		} else {
			kkapp.App.Render.JSON(w, http.StatusOK, response)
		}
	}()

	if user != nil {
		if userModel, ok := user.(model.JwtUser); ok && !userModel.IsGuest() {
			userid = userModel.Sub
		}
	}

	if userid == "" {
		log.Println("[ERROR] userid empty")
		err = errors.New("Invalid parameters")

		return
	}

	jsdecoder := json.NewDecoder(r.Body)
	if err = jsdecoder.Decode(&payLoad); err != nil {
		log.Println("[ERROR] json decode")
		err = errors.New("Invalid empty data")
		return
	}

	if payLoad.RedeemCode == "" || payLoad.ProductName == "" {
		log.Println("[ERROR] empty redeem_code product_name")
		err = errors.New("Invalid empty data")
		return
	}

	couponUser, err := NewCouponUser(userid, payLoad.RedeemCode)

	if err != nil {
		log.Println("[ERROR]", err)
		return
	}

	// coupon code
	err = couponUser.ValidateCode()
	if err != nil {
		log.Println("[ERROR]", err)
		return
	}

	// coupon code available
	err = couponUser.IsAvailable()
	if err != nil {
		log.Println("[ERROR]", err)
		return
	}

	// this coupon code should match same product_name
	var product *Product
	db := kkapp.App.DbUser.Slave()
	product = new(Product)
	// FIXME should only available for credit_card payment
	err = db.Get(product, sqlcredit["productByName"], payLoad.ProductName, creditCardPaymentType)
	if err != nil {
		log.Println("[ERROR]", err)
		return
	}

	if couponUser.CouponCodeGroup.ProductID.Int64 != product.ID {
		log.Println("[ERROR] not same product", product.ID, couponUser.CouponCodeGroup.ProductID.Int64)
		err = errors.New("Not valid product")
		return
	}
}

// toDashDuration parse duration to SQL to_char(duration, 'Y-MM-DD')
func toDashDuration(duration string) (dashString string) {
	var dayAmount, monthAmount, yearAmount int
	unit, durationAmount := kkutil.ParseDuration(duration)

	switch unit {
	case kkutil.DurationUnitDay.String():
		dayAmount = durationAmount
	case kkutil.DurationUnitMonth.String():
		monthAmount = durationAmount
	case kkutil.DurationUnitYear.String():
		yearAmount = durationAmount
	}

	return fmt.Sprintf("%d-%d-%d", yearAmount, monthAmount, dayAmount)
}
