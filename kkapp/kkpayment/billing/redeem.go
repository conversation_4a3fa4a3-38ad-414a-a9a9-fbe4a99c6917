package billing

import (
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"net/http"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/go-playground/validator/v10"
	"github.com/go-zoo/bone"
)

var (
	validate = validator.New()
)

type RedeemCodeParameter struct {
	UserId string `validate:"required"`
	Code   string `validate:"required"`
}

func msgForTag(fe validator.FieldError) string {
	switch fe.Tag() {
	case "required":
		return fmt.Sprintf("%s is required", fe.Field())
	}
	return fe.Error()
}

func PostBillingRedeemsCodes(w http.ResponseWriter, r *http.Request) {
	redeemCode := bone.GetValue(r, "redeemCode")
	var userId string
	var err error
	var errType string
	user := r.Context().Value("user")
	response := model.MakeOk()

	defer func() {
		if err != nil {
			errStr := err.Error()
			response.Status.Type = "Fail"
			response.Status.Message = errStr
			statusCode := http.StatusBadRequest

			if errType == "Validation Error" {
				response.Status.Subtype = "400.1"
			} else if errStr == "user not found" {
				response.Status.Subtype = "400.2"
			} else if errStr == "Internal server error" {
				response.Status.Subtype = "500.1"
				statusCode = http.StatusInternalServerError
			}

			kkapp.App.Render.JSON(w, statusCode, response)
		} else {
			kkapp.App.Render.JSON(w, http.StatusOK, response)
		}
	}()

	// step1. validation request
	if user != nil {
		if userModel, ok := user.(model.JwtUser); ok && !userModel.IsGuest() {
			userId = userModel.Sub
		}
	} else {
		err = errors.New("user not found")
		return
	}

	// step2. call api
	var params RedeemCodeParameter
	params.UserId = userId
	params.Code = redeemCode
	err = validate.Struct(params)
	if err != nil {
		var ve validator.ValidationErrors
		if errors.As(err, &ve) {
			var errString string
			for i, e := range ve {
				if i == 0 {
					errString = msgForTag(e)
				} else {
					errString = fmt.Sprintf("%s, %s", errString, msgForTag(e))
				}
			}
			err = errors.New(errString)
		}
		errType = "Validation Error"
		return
	}

	billingClient := kkapp.App.BillingClient
	bresp, err := billingClient.RedeemCode(map[string]string{
		"code": redeemCode,
	}, userId)
	if err != nil {
		log.Println("[ERROR] redeem code error, ", err)
		return
	}

	// step3. if success response, output to response.data
	var data interface{}
	err = json.Unmarshal([]byte(bresp.Data), &data)
	response.Data = data
}
