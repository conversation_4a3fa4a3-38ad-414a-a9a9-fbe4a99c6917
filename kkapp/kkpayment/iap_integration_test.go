package kkpayment_test

import (
	"bytes"
	"context"
	"encoding/json"
	"log"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/KKTV/kktv-api-v3/kkapp/kkpayment"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/wrapper/middleware"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	mwmodel "github.com/KKTV/kktv-api-v3/pkg/model/middleware"
	"github.com/go-zoo/bone"
)

func TestIAP_IntegrationTest(t *testing.T) {
	testEnvInit()

	mux := bone.New()
	mux.PostFunc("/v3/payment/iap", kkpayment.PostIAP)
	// test payLoad
	receiptData := `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`
	payLoad := kkpayment.IAPPost{
		UserID:      "8ac862a948eadd086cae69a666604742c893c85ae0efd216d333bb05a461eac9",
		ReceiptData: receiptData,
	}

	jsonStr, _ := json.Marshal(payLoad)

	{
		r, err := http.NewRequest("POST", "/v3/payment/iap", bytes.NewBuffer(jsonStr))
		if err != nil {
			t.Fatal(err)
		}
		ctx := context.WithValue(r.Context(), middleware.KeyAccessUser, mwmodel.AccessUser{
			UserID:      "8ac862a948eadd086cae69a666604742c893c85ae0efd216d333bb05a461eac9",
			Memberships: dbuser.MembershipPremiumOnly,
		})
		r = r.WithContext(ctx)

		r.Header.Set("Content-Type", "application/json")
		// r.Header.Set("X-Real-Ip", "127.0.0.1")
		r.RemoteAddr = "127.0.0.1"

		w := httptest.NewRecorder()
		mux.ServeHTTP(w, r)
		log.Println("---------IAP-------")
		log.Println(w.Body.String())
		log.Println("---------IAP-------")

		// we use an expired iap receipt date which will fail
		if status := w.Code; status != http.StatusBadRequest {
			t.Errorf("PostIAP handler returned wrong status code: got %v want %v",
				status, http.StatusBadRequest)
		}
	}

}
