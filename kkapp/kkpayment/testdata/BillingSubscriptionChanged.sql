-- noinspection SqlNoDataSourceInspectionForFile

INSERT INTO public.users (id, email, phone, expired_at, master, created_at,
                          updated_at, "role", media_source, auto_renew, payment_info, "type", created_by,
                          cold_start_selected_titles, origin_provider, revoked_at,
                          unsubscribed_edm_at, "password", email_verified_at, phone_verified_at, membership)
VALUES ('55ecae54-d95d-4f00-8449-e9a208e09e4b', '<EMAIL>', NULL,
        '2016-11-21 00:00:00.000', NULL, '2016-11-16 23:20:56.172', '2016-11-21 00:08:55.276', 'expired',
        '{"kkbox": {"sub": "testSubJosiE", "kkid": "testKkiDJoSIE", "status": "", "territory": "TW", "identifier": "<EMAIL>"}, "family": ["a167686a-ae58-43f2-93a9-67f0e580efe1", "0420e345-df37-4c23-ad22-184d85c9fb72"], "appsflyer": {"idfa": "00000000-0000-0000-0000-000000000000", "bundleId": "com.kktv.ios.kktv"}, "childlock": "0313"}',
        false,
        NULL, 'general'::users_type, '', NULL, 'facebook'::users_origin_provider, NULL, NULL, NULL, NULL,
        NULL, '[{"role": "expired"}]'),
       ('josie', '<EMAIL>', NULL,
        '2024-05-01 00:00:00.000', NULL, '2016-11-16 23:20:56.172', '2016-11-21 00:08:55.276', 'freetrial',
        '{"kkbox": {"sub": "testJoSIE", "kkid": "testJoSIE", "status": "", "territory": "TW", "identifier": "<EMAIL>"}, "appsflyer": {"idfa": "00000000-0000-0000-0000-000000000000", "bundleId": "com.kktv.ios.kktv"}}',
        false,
        NULL, 'general'::users_type, '', NULL, 'facebook'::users_origin_provider, NULL, NULL, NULL, NULL,
        NULL, '[{"role": "freetrial"}]'),
       ('expired-user-id', '<EMAIL>', NULL,
        '2024-05-29 00:00:00.000', NULL, '2016-11-16 23:20:56.172', '2016-11-21 00:08:55.276', 'premium',
        '{"appsflyer": {"idfa": "00000000-0000-0000-0000-000000000000", "bundleId": "com.kktv.ios.kktv"}}',
        true,
        NULL, 'general'::users_type, '', NULL, 'facebook'::users_origin_provider, NULL, NULL, NULL, NULL,
        NULL, '[{"role": "paid:anime"}]'),
       ('family-user-id', '<EMAIL>', NULL,
        '2024-04-29 00:00:00.000', NULL, '2016-11-16 23:20:56.172', '2016-11-21 00:08:55.276', 'freetrial',
        '{"appsflyer": {"idfa": "00000000-0000-0000-0000-000000000000", "bundleId": "com.kktv.ios.kktv"}, "family": ["test-family-member-1", "test-family-user-2"]}',
        false,
        NULL, 'general'::users_type, '', NULL, 'facebook'::users_origin_provider, NULL, NULL, NULL, NULL,
        NULL, '[{"role": "freetrial"}]'),
       -- test-family-member-1
       ('test-family-member-1', '<EMAIL>', NULL,
        '2024-02-29 00:00:00.000', NULL, '2016-11-16 23:20:56.172', '2016-11-21 00:08:55.276', 'expired',
        '{"appsflyer": {"idfa": "00000000-0000-0000-0000-000000000000", "bundleId": "com.kktv.ios.kktv"}}',
        false,
        NULL, 'general'::users_type, '', NULL, 'facebook'::users_origin_provider, NULL, NULL, NULL, NULL,
        NULL, '[{"role": "expired"}]');

INSERT INTO public.payment_info (user_id, email, credit_card_6no, credit_card_4no, credit_card_token_value,
                                 iap_receipt_data, iap_receipt_data_hash, "payment_type", caring_code, recipient,
                                 recipient_address, carrier_type, carrier_value, created_at, updated_at, phone,
                                 credit_card_token_term, iap_latest_transaction_id, iap_latest_expires_date,
                                 telecom_mp_id, tstar_order_id, tstar_contract_id, mod_subscriber_id,
                                 mod_subscriber_area, iab_receipt_data, iab_order_id, iab_latest_order_id,
                                 iab_latest_expires_date, family_id, backup)
VALUES ('expired-user-id', '<EMAIL>', '400022', '1111',
        'e45b2a9145df04a9f452dba61c08fd3e187b8ad675d0a28a82d660890c54629d', NULL, NULL, 'credit_card', '8585', NULL,
        NULL, NULL, NULL, '2016-08-29 14:43:10.373', NULL, NULL, '<EMAIL>', NULL, NULL, NULL, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
       ('family-user-id', '<EMAIL>', '400022', '1111',
        'e45b2a9145df04a9f452dba61c08fd3e187b8ad675d0a28a82d660890c54629d', NULL, NULL, 'credit_card', '8585', NULL,
        NULL, NULL, NULL, '2016-08-29 14:43:10.373', NULL, NULL, '<EMAIL>', NULL, NULL, NULL, NULL, NULL,
        NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
       ('test-family-member-1', '<EMAIL>', NULL, NULL,
        NULL, NULL, NULL, 'credit_card', NULL, NULL,
        NULL, NULL, NULL, '2016-08-29 14:43:10.373', NULL, NULL, '<EMAIL>', NULL, NULL, NULL, NULL,
        NULL,
        NULL, NULL, NULL, NULL, NULL, NULL, 'family-user-id', NULL);


INSERT INTO public.products ("name", country, price, duration, created_at, updated_at, "payment_type", currency,
                             price_no_tax, tax_rate, item_name, item_unit, auto_renew, active, free_duration,
                             as_subscribe, fee, payment_type_code, purchase_upper_limit_count, external_product_id,
                             deleted_at, sort, discount_duration, discount_price, bundle, category,
                             discount_price_no_tax, discount_fee, fee_rate)
VALUES ('kktv.family.cc.monthly.599', 'TW', 599.00, '1 mon', '2021-08-20 16:02:15.138', NULL, 'credit_card', 'NTD',
        475.00, 5, '四人月繳｜首三月，NT$399/月', '月', true, true, '00:00:00', false, 9, '00', 0, NULL, NULL, 0,
        '3 mons', 399.00,
        '{"title": "四人月繳｜測試", "family": 4, "subtitle": "首三月，NT$399/月", "text_color": "#ffffff", "description": "與親友一同共享追劇樂趣\n多樣內容同步跟播最即時", "background_color": "#5a2837", "package_description": "優惠方案內容為：訂閱型四人月繳方案"}',
        'MultiPlan', 380.00, 7, 1.90);
