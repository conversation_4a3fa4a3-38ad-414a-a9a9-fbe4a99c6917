package kkpayment

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"gopkg.in/guregu/null.v3"
)

// ProductBundle take from kkapp/model/dbuser/product.go
type ProductBundle struct {
	Prefix         []string `json:"prefix,omitempty"`
	Qualification  string   `json:"qualification,omitempty"`
	EmailDomains   []string `json:"email_domains,omitempty"`
	NeedRedeemCode bool     `json:"needRedeemCode,omitempty"`
	Family         int64    `json:"family,omitempty"`
}

// Scan interface for db
func (t *ProductBundle) Scan(src interface{}) error {
	byteValue, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("metas field must be a []byte, got %T instead", src)
	}
	return json.Unmarshal(byteValue, t)

}

// Value interface for db
func (t ProductBundle) Value() (driver.Value, error) {
	return json.Marshal(t)
}

// Product struct for create order
type Product struct {
	ID                      int64       `db:"id" json:"id"`
	Name                    string      `db:"name" json:"name"`
	Country                 string      `db:"country" json:"country"`
	Price                   int64       `db:"price" json:"price"`
	Duration                string      `db:"duration" json:"duration"`
	CreatedAt               time.Time   `db:"created_at" json:"created_at"`
	UpdatedAt               null.Time   `db:"updated_at" json:"updated_at"`
	DeletedAt               null.Time   `db:"deleted_at" json:"deleted_at"`
	PaymentType             string      `db:"payment_type" json:"payment_type"`
	Currency                string      `db:"currency" json:"currency"`
	PriceNoTax              int64       `db:"price_no_tax" json:"price_no_tax"`
	TaxRate                 int64       `db:"tax_rate" json:"tax_rate"`
	ItemName                string      `db:"item_name" json:"item_name"`
	ItemUnit                string      `db:"item_unit" json:"item_unit"`
	AutoRenew               bool        `db:"auto_renew" json:"auto_renew"`
	Active                  bool        `db:"active" json:"active"`
	FreeDuration            string      `db:"free_duration" json:"free_duration"`
	AsSubscribe             bool        `db:"as_subscribe" json:"as_subscribe"`
	Fee                     int64       `db:"fee" json:"fee"`
	PaymentTypeCode         string      `db:"payment_type_code" json:"payment_type_code"`
	PurchaseUpperLimitCount int64       `db:"purchase_upper_limit_count" json:"purchase_upper_limit_count"`
	ExternalProductID       null.String `db:"external_product_id" json:"external_product_id"`

	DiscountDuration   string         `db:"discount_duration" json:"discount_duration"`
	DiscountPrice      int64          `db:"discount_price" json:"discount_price"`
	DiscountPriceNoTax int64          `db:"discount_price_no_tax" json:"discount_price_no_tax"`
	DiscountFee        int64          `db:"discount_fee" json:"discount_fee"`
	Bundle             *ProductBundle `db:"bundle" json:"bundle"`
}

func (product *Product) NeedQualificationCheck() bool {
	switch product.Bundle.Qualification {
	case "email_domain":
		return len(product.Bundle.EmailDomains) > 0
	case "phone":
		return true
	}
	return false
}

// Receipt
type Receipt struct {
	PaymentType     string    `json:"payment_type"`
	OrderID         string    `json:"order_id"`
	IsInGracePeriod bool      `json:"is_in_grace_period"`
	ExpiredAt       time.Time `json:"expired_at"`
}
