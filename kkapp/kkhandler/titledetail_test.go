package kkhandler

import (
	"testing"

	"github.com/KKTV/kktv-api-v3/kkapp/model"
)

// BenchmarkTitileDetailV1 benchmark titledetail.go
func BenchmarkTitleDetailV1(b *testing.B) {
	for i := 0; i < b.N; i++ {
		_, err := model.NewTitleDetails([]string{"00000338", "00000338", "00000338", "00000338"})
		if err != nil {
			b.<PERSON><PERSON>("TileDetail", err)
		}
	}
}

// BenchmarkTitileDetailV2 benchmark titledetail.go
func BenchmarkTitleDetailV2(b *testing.B) {
	for i := 0; i < b.N; i++ {
		_, err := model.NewTitleDetailsV2([]string{"00000338", "00000338", "00000338", "00000338"})
		if err != nil {
			b.<PERSON>al("TileDetail", err)
		}
	}
}

// BenchmarkTitileDetailV3 benchmark titledetail.go
func BenchmarkTitleDetailV3(b *testing.B) {
	for i := 0; i < b.N; i++ {
		_, err := model.NewTitleDetailsV3([]string{"00000338", "00000338", "00000338", "00000338"})
		if err != nil {
			b.Fatal("TileDetail", err)
		}
	}
}

// BenchmarkSingleTitileDetailRedis benchmark
// get single title data from redis
func BenchmarkSingleTitleRedis(b *testing.B) {
	for i := 0; i < b.N; i++ {
		_, err := model.NewUserSingleTitleDetail("", "00000338")
		if err != nil {
			b.Fatal("SingleTitleRedis", err)
		}
	}
}

// BenchmarkSingleTitileDetailRedis benchmark
// get single title data from db which is postgresql currently
func BenchmarkSingleTitleDB(b *testing.B) {
	for i := 0; i < b.N; i++ {
		_, err := model.NewUserSingleTitleDetailDB("", "00000338")
		if err != nil {
			b.Fatal("SingleTitleDB", err)
		}
	}
}
