package kkhandler

import (
	"database/sql"
	"encoding/json"
	"log"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/amplitude"
	"github.com/KKTV/kktv-api-v3/kkapp/appsflyer"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbredeem"
	"gopkg.in/guregu/null.v3"
)

var (
	sqlIR = map[string]string{
		"getEvent":              "SELECT id, name, summary, start_time, end_time, redeem_group_id, vendors, models, devices FROM events WHERE ( NOW() BETWEEN start_time AND end_time ) AND id=$1;",
		"getAllEvents":          "SELECT id, name, summary, start_time, end_time, redeem_group_id, vendors, models, devices FROM events WHERE ( NOW() BETWEEN start_time AND end_time ) ;",
		"hasRedeemCode":         "SELECT code, user_id FROM coupon_codes WHERE group_id=$1 AND issue_user_id=$2 AND device_id=$3;",
		"getIssueCode":          "SELECT id, code FROM coupon_codes WHERE user_id IS NULL AND issue_user_id IS NULL AND device_id IS NULL AND group_id=$1 LIMIT 1;",
		"updateIssueCode":       "UPDATE coupon_codes SET issue_user_id=$1, device_id=$2 WHERE group_id=$3 AND id=$4;",
		"getRedeemLimit":        "SELECT COALESCE(usage_limit_per_user, 0) AS usage_limit_per_user FROM public.coupon_groups WHERE id=$1;",
		"getRedeemTimes":        "SELECT COUNT(*) FROM (SELECT DISTINCT issue_user_id, device_id FROM coupon_codes WHERE group_id=$1 AND issue_user_id=$2) AS tbl ;",
		"checkRegisteredDevice": "SELECT COUNT(*) FROM coupon_codes WHERE group_id=$1 AND device_id=$2",
		"getRedeemGroup":        "SELECT id, prefix, price, to_char(duration, 'Y-MM-DD') AS duration, usage_limit_per_user, allow_reuse, valid_since, expires_at, created_at, updated_at, price_no_tax, description, to_char(free_duration, 'Y-MM-DD') AS free_duration, fee, channel, product_id FROM coupon_groups WHERE id=$1;",
	}

	maxLimit = 5
)

// IssuePayload struct for issue payload
type IssuePayload struct {
	EventID  string `json:"event_id"`
	UserID   string `json:"user_id"`
	Vendor   string `json:"vendor"`
	Model    string `json:"model"`
	DeviceID string `json:"device_id"`
}

// DBEvent struct for mapping events in db
type DBEvent struct {
	ID            string      `json:"id" db:"id"`
	Name          string      `json:"name" db:"name"`
	Summary       string      `json:"summary" db:"summary"`
	StartTime     time.Time   `json:"start_time" db:"start_time"`
	EndTime       time.Time   `json:"end_time" db:"end_time"`
	RedeemGroupID string      `json:"redeem_group_id" db:"redeem_group_id"`
	Vendors       string      `json:"vendors" db:"vendors"`
	Models        string      `json:"models" db:"models"`
	Devices       null.String `json:"devices" db:"devices"`
}

// RespEvent struct for response
type RespEvent struct {
	EventID    int    `json:"event_id"`
	EventName  string `json:"event_name"`
	Summary    string `json:"summary"`
	StartTime  int64  `json:"start_time"`
	EndTime    int64  `json:"end_time"`
	RedeemCode string `json:"redeem_code"`
	IsRedeem   bool   `json:"is_redeem"`
}

// IssueCode struct for issue code
type IssueCode struct {
	ID   int    `json:"id" db:"id"`
	Code string `json:"code" db:"code"`
}

// UserRedeem struct for redeem table
type UserRedeem struct {
	Code   string      `db:"code"`
	UserID null.String `db:"user_id"`
}

// TVModels struct for TV models
type TVModels struct {
	ID        int    `db:"id"`
	Vendor    string `db:"vendor"`
	Models    string `db:"models"`
	DeviceIDs string `db:"device_ids"`
}

// GetRedeemCode handler for Issue Redeem Code API
func GetRedeemCode(w http.ResponseWriter, r *http.Request) {
	var err error
	var p IssuePayload
	var resp RespEvent

	response := model.MakeOk()

	decoder := json.NewDecoder(r.Body)
	if err = decoder.Decode(&p); err != nil {
		response.Status.Message = "decode json payload failed"
		kkapp.App.Render.JSON(w, http.StatusForbidden, response)
		return
	}

	log.Println("[INFO] - Issue Redeem - GetRedeemCode - Payload:", p)

	// Check required params
	ok := checkRequired(&p)
	if !ok {
		response.Status.Message = "the number of required fields is not enough"
		kkapp.App.Render.JSON(w, http.StatusForbidden, response)
		return
	}

	// Check token
	authhead := r.Header.Get("Authorization")
	if strings.HasPrefix(authhead, "Bearer ") {
		token := strings.Split(authhead, "Bearer ")[1]
		_, err := model.NewVerifyToken(token)
		if err != nil {
			response.Status.Message = "token expired"
			kkapp.App.Render.JSON(w, http.StatusForbidden, response)
			return
		}
	} else {
		response.Status.Message = "no token"
		kkapp.App.Render.JSON(w, http.StatusForbidden, response)
		return
	}

	// Check if the user_id of user's token is consistent with param user_id
	user := r.Context().Value("user").(model.JwtUser)

	if user.Sub != p.UserID {
		response.Status.Message = "user id is inconsistent"
		kkapp.App.Render.JSON(w, http.StatusForbidden, response)
		return
	}

	me, err := model.NewSimpleUserInfo(user.Sub)

	// Check user's role
	if me.Role == model.UserRolePremium && me.Type == model.UserTypeKKboxPrime {
		response.Status.Message = "user is a Premium/Prime user"
		kkapp.App.Render.JSON(w, http.StatusForbidden, response)
		return
	}

	// Get Redeem Group ID by event ID
	event, err := getEvent(p.EventID)
	if err != nil {
		// Check if event is empty or not
		if err == sql.ErrNoRows {
			response.Status.Message = "no event"
			kkapp.App.Render.JSON(w, http.StatusForbidden, response)
			return
		}

		log.Println("[ERROR] - Issue Redeem - Trying to get event from DB failed.", err)
		response.Status.Message = "System Error - Trying to get event from DB failed"
		kkapp.App.Render.JSON(w, http.StatusInternalServerError, response)
		return
	}

	log.Println("[INFO] - Issue Redeem - Event Group ID:", event.RedeemGroupID)

	// Check TV Vendor
	if !strings.Contains(strings.ToLower(event.Vendors), strings.ToLower(p.Vendor)) {
		response.Status.Message = "not in TV Vendor"
		kkapp.App.Render.JSON(w, http.StatusForbidden, response)
		return
	}

	// Check TV Model
	if !strings.Contains(strings.ToLower(event.Models), strings.ToLower(p.Model)) {
		response.Status.Message = "not in TV Model"
		kkapp.App.Render.JSON(w, http.StatusForbidden, response)
		return
	}

	// Check if user has got redeem code
	userRedeem, err := hasRedeemCode(event.RedeemGroupID, p.UserID, p.DeviceID)
	if err != nil {
		if err != sql.ErrNoRows {
			log.Println("[ERROR] - Issue Redeem - hasRedeemCode - Trying to get redeem code from DB failed.", err)
			response.Status.Message = "System Error - Trying to get redeem code from DB failed"
			kkapp.App.Render.JSON(w, http.StatusInternalServerError, response)
			return
		}
	}

	// If user has got redeem code before then return the same redeem code
	if userRedeem.Code != "" {
		id, _ := strconv.Atoi(event.ID)
		resp.EventID = id
		resp.EventName = event.Name
		resp.Summary = event.Summary
		resp.StartTime = event.StartTime.UTC().Unix()
		resp.EndTime = event.EndTime.UTC().Unix()
		resp.RedeemCode = userRedeem.Code
		if userRedeem.UserID.Valid && userRedeem.UserID.String != "" {
			resp.IsRedeem = true
		} else {
			resp.IsRedeem = false
		}

		response.Status.Message = "user has redeem code"
		response.Data = resp
		kkapp.App.Render.JSON(w, http.StatusOK, response)
		return
	}

	// Get RedeemLimit
	limit := getRedeemLimit(event.RedeemGroupID)
	log.Println("redeem limit", limit)

	// Check if the redeem times is over the maxLimit
	times := getRedeemTimes(event.RedeemGroupID, user.Sub)
	log.Printf("\n\nuser: %s  redeem times:%v\n\n", user.Sub, times)

	if times >= limit {
		id, _ := strconv.Atoi(event.ID)
		resp.EventID = id
		resp.EventName = event.Name
		resp.Summary = event.Summary
		resp.StartTime = event.StartTime.UTC().Unix()
		resp.EndTime = event.EndTime.UTC().Unix()
		resp.RedeemCode = ""
		resp.IsRedeem = false

		response.Status.Message = "user's redeem times hit the limit"
		response.Data = resp
		kkapp.App.Render.JSON(w, http.StatusOK, response)
		return
	}

	// Check if the device_id is registered
	isRegister := checkRegister(event.RedeemGroupID, p.DeviceID)
	if isRegister > 0 {
		response.Status.Message = "device has been registered"
		kkapp.App.Render.JSON(w, http.StatusForbidden, response)
		return
	}

	// Update issue_user_id and device_id in coupon_codes table
	db := kkapp.App.DbRedeem.Master()
	tx, err := db.Begin()
	if err != nil {
		log.Println("[ERROR] - Issue Redeem - Begin transaction failed", err)
		response.Status.Message = "System Error - Begin transaction failed"
		kkapp.App.Render.JSON(w, http.StatusInternalServerError, response)
		return
	}

	// Preparing to issue redeem code
	rows, err := tx.Query(sqlIR["getIssueCode"], event.RedeemGroupID)
	if err != nil {
		if err == sql.ErrNoRows {
			id, _ := strconv.Atoi(event.ID)
			resp.EventID = id
			resp.EventName = event.Name
			resp.Summary = event.Summary
			resp.StartTime = event.StartTime.UTC().Unix()
			resp.EndTime = event.EndTime.UTC().Unix()
			resp.RedeemCode = ""
			resp.IsRedeem = false

			response.Status.Message = "redeem code is taken out"
			response.Data = resp
			kkapp.App.Render.JSON(w, http.StatusOK, response)
			return
		}

		log.Println("[ERROR] - Issue Redeem - Trying to get unused redeem code from DB failed.", err)
		tx.Rollback()

		response.Status.Message = "System Error - Trying to get unused redeem code from DB failed"
		kkapp.App.Render.JSON(w, http.StatusInternalServerError, response)
		return
	}

	var issueCode IssueCode

	defer rows.Close()
	for rows.Next() {
		err = rows.Scan(&issueCode.ID, &issueCode.Code)
		if err != nil {
			log.Println("[ERROR] - Issue Redeem - Trying to scaning rows value failed.", err)
			tx.Rollback()

			response.Status.Message = "System Error - Trying to scaning rows value failed"
			kkapp.App.Render.JSON(w, http.StatusInternalServerError, response)
			return
		}
	}

	// Update issued code
	_, err = tx.Exec(sqlIR["updateIssueCode"], p.UserID, p.DeviceID, event.RedeemGroupID, issueCode.ID)
	if err != nil {
		log.Println("[ERROR] - Issue Redeem - Update issue code failed.", err)
		tx.Rollback()

		response.Status.Message = "System Error - Update issue code failed"
		kkapp.App.Render.JSON(w, http.StatusInternalServerError, response)
		return
	}

	// Finally, commit it.
	if err := tx.Commit(); err != nil {
		log.Println("[ERROR] - Issue Redeem - Transaction commit failed.", err)

		response.Status.Message = "System Error - Transaction commit failed"
		kkapp.App.Render.JSON(w, http.StatusInternalServerError, response)
		return
	}

	rgroup, err := getRedeemGroup(event.RedeemGroupID)
	if err != nil {
		log.Println("[ERROR] - Issue Redeem  - Get Redeem Group data for Amplitude failed. ", err)
	}

	// Send Amplitude events
	amp, err := amplitude.NewAccountCouponReceived(user.Sub, issueCode.Code, rgroup)
	if err != nil {
		log.Println("[ERROR] - Issue Redeem  - Send Amplitud event of 'Account Coupon Received' failed. ", err)
	}
	go amp.Send(user.Sub, rgroup)

	// Send Appsflyer events if user has appsflyer id
	if strings.Contains(me.MediaSource.String, "appsflyer") {
		af, err := appsflyer.NewAccountCouponReceived(me, rgroup)
		if err != nil {
			log.Println("[ERROR] - Issue Redeem  - Send Appsflyer event of 'Account Coupon Received' failed. ", err)
		}
		go af.Send()
	}

	id, _ := strconv.Atoi(event.ID)
	resp.EventID = id
	resp.EventName = event.Name
	resp.Summary = event.Summary
	resp.StartTime = event.StartTime.UTC().Unix()
	resp.EndTime = event.EndTime.UTC().Unix()
	resp.RedeemCode = issueCode.Code
	resp.IsRedeem = false

	response.Status.Subtype = "success"
	response.Status.Message = "redeem code issued"
	response.Data = resp
	kkapp.App.Render.JSON(w, http.StatusOK, response)
}

func checkRequired(p *IssuePayload) bool {
	if p.EventID == "" {
		return false
	}

	if p.UserID == "" {
		return false
	}

	if p.Vendor == "" {
		return false
	}

	if p.Model == "" {
		return false
	}

	if p.DeviceID == "" {
		return false
	}

	return true
}

func getEvent(eventID string) (e DBEvent, err error) {
	dbSlave := kkapp.App.DbRedeem.Slave()
	err = dbSlave.Get(&e, sqlIR["getEvent"], eventID)
	if err != nil {
		log.Println("[ERROR] - Issue Redeem - Get Redeem Group ID Failed.", err)
		return e, err
	}

	return e, err
}

func getAllEvents() (e []DBEvent, err error) {
	dbSlave := kkapp.App.DbRedeem.Slave()
	err = dbSlave.Select(&e, sqlIR["getAllEvents"])
	if err != nil {
		log.Println("[ERROR] - Issue Redeem - Get all events for remote_config AndroidTV Failed.", err)
		return e, err
	}

	return e, err
}

func hasRedeemCode(groupID string, userID string, deviceID string) (ur UserRedeem, err error) {
	dbSlave := kkapp.App.DbRedeem.Slave()
	err = dbSlave.Get(&ur, sqlIR["hasRedeemCode"], groupID, userID, deviceID)
	if err != nil {
		return ur, err
	}

	return ur, err
}

func getRedeemLimit(groupID string) int {
	slaveDB := kkapp.App.DbRedeem.Slave()
	var limit int
	err := slaveDB.Get(&limit, sqlIR["getRedeemLimit"], groupID)
	if err != nil {
		log.Println("[ERROR] - Issue Redeem - Trying to get redeem limit from DB failed.", err)
		return maxLimit
	}

	if limit == 0 {
		return maxLimit
	}

	return limit
}

func getRedeemTimes(groupID string, userID string) int {
	slaveDB := kkapp.App.DbRedeem.Slave()
	var rcnt int
	err := slaveDB.Get(&rcnt, sqlIR["getRedeemTimes"], groupID, userID)
	if err != nil {
		log.Println("[ERROR] - Issue Redeem - Trying to get redeem times of user from DB failed.", err)
		return 0
	}
	return rcnt
}

func checkRegister(groupID string, deviceID string) int {
	slaveDB := kkapp.App.DbRedeem.Slave()
	var rcnt int
	err := slaveDB.Get(&rcnt, sqlIR["checkRegisteredDevice"], groupID, deviceID)
	if err != nil {
		log.Println("[ERROR] - Issue Redeem - Trying to check Registered Device failed.", err)
		return 0
	}
	return rcnt
}

func getTVModels(deviceIDs string) (models []TVModels, err error) {
	slaveDB := kkapp.App.DbRedeem.Slave()
	err = slaveDB.Select(&models, sqlIR["getTVModels"], deviceIDs)
	if err != nil {
		log.Println("[ERROR] - Issue Redeem - Trying to get TV Models data from DB failed.", err)
		return models, err
	}

	return models, err
}

func getRedeemGroup(groupID string) (group []dbredeem.DBCouponGroup, err error) {
	slaveDB := kkapp.App.DbRedeem.Slave()
	err = slaveDB.Select(&group, sqlIR["getRedeemGroup"], groupID)
	if err != nil {
		log.Println("[ERROR] - Issue Redeem - Trying to get Redeem Group from DB failed.", err)
		return group, err
	}

	return group, err
}
