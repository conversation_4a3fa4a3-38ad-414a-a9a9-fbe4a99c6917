package kkhandler

import (
	"encoding/json"
	"log"
	"net/http"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kkapp/model/playback"
	"github.com/KKTV/kktv-api-v3/pkg/httpreq"
	plog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	modelmw "github.com/KKTV/kktv-api-v3/pkg/model/middleware"
	"github.com/KKTV/kktv-api-v3/pkg/render"
)

var (
	defaultPlaybackMedium = "SVOD"
)

// PostPlaybackToken api for playback token
func PostPlaybackToken(w http.ResponseWriter, r *http.Request) {
	// {
	// episode_id: "00000367010001"
	// medium: "SVOD"
	// title_id: "00000367"
	// }

	var err error
	pb := playback.PlaybackRequest{
		Purpose: playback.PurposePlayback,
	}
	deviceID := r.Header.Get(httpreq.HeaderDeviceID)

	response := model.MakeOk()

	var userID string
	membership := dbuser.NonMember
	if access, ok := r.Context().Value("accessuser").(modelmw.AccessUser); ok {
		userID = access.UserID
		membership = access.Memberships
	}

	jsdecoder := json.NewDecoder(r.Body)

	if err = jsdecoder.Decode(&pb); err != nil {
		log.Println("[ERROR]", err)
		response.Status.Type = "Error"
		response.Status.Message = err.Error()
		render.JSON(w, http.StatusBadRequest, response)
		return
	}

	if pb.TitleID == "" {
		response.Status.Type = "NotFoundError"
		response.Status.Subtype = "TitleNotFound"
		response.Status.Message = "title not found"
		render.JSON(w, http.StatusNotFound, response)
		return
	}

	if pb.Medium == "" {
		pb.Medium = defaultPlaybackMedium
	}

	if pb.EpisodeID == "" {
		response.Status.Type = "NotFoundError"
		response.Status.Subtype = "EpisodeNotFound"
		response.Status.Message = "episode not found"
		render.JSON(w, http.StatusNotFound, response)
		return
	}

	token, err := playback.NewPlayback(kkapp.App.PermissionService, userID, membership, deviceID, pb)
	if err != nil {
		plog.Error("PostPlaybackToken: fail to new playback").Err(err).
			Str("userID", userID).Str("deviceID", deviceID).
			Str("titleID", pb.TitleID).Str("episodeID", pb.EpisodeID).Str("medium", pb.Medium).Send()
		switch err.Error() {
		case "NotFoundError":
			response.Status.Type = "NotFoundError"
			response.Status.Subtype = "EpisodeNotFound"
			response.Status.Message = "episode not found"
			render.JSON(w, http.StatusNotFound, response)
		case "VIPOnly":
			response.Status.Type = "PlaybackNotPermittedError"
			response.Status.Subtype = "VIPOnly"
			response.Status.Message = "Expired user cannot play VIP only title"
			render.JSON(w, http.StatusUnauthorized, response)
		case "GuestNotAllowed":
			response.Status.Type = "PlaybackNotPermittedError"
			response.Status.Subtype = "VIPOnly"
			response.Status.Message = "Expired user cannot play VIP only title"
			render.JSON(w, http.StatusUnauthorized, response)
		case "PERMISSION":
			response.Status.Type = "PlaybackNotPermittedError"
			response.Status.Subtype = "PERMISSION"
			response.Status.Message = ""
			render.JSON(w, http.StatusForbidden, response)
		default:
			response.Status.Type = "PlaybackNotPermittedError"
			response.Status.Subtype = ""
			response.Status.Message = err.Error()
			render.JSON(w, http.StatusUnauthorized, response)
		}
		return
	}

	response.Data = token
	render.JSON(w, http.StatusOK, response)
}
