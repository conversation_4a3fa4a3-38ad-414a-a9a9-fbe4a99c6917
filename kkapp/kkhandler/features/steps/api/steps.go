package api

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"reflect"
	"strings"

	"github.com/cucumber/godog"
	"github.com/go-zoo/bone"
)

type ApiContext struct {
	Debug        bool
	Client       *http.Client
	Headers      map[string]string
	QueryParams  map[string]string
	LastResponse *ApiResponse
	LastRequest  *http.Request
	Scope        map[string]string
	Server       *bone.Mux
}

// ApiResponse Struct that wraps an API response.
// It contains common accessed fields like Status Code and the Payload as well as access to the raw http.Response object
type ApiResponse struct {
	StatusCode int
	Body       string
}

func New(mux *bone.Mux) *ApiContext {
	ac := &ApiContext{
		Client:      &http.Client{},
		Headers:     map[string]string{},
		QueryParams: map[string]string{},
		Debug:       false,
		Scope:       map[string]string{},
		Server:      mux,
	}
	return ac
}

// clientSetHeaderWithValue Step that add a new header to the current request.
func (ctx *ApiContext) ClientSetHeaderWithValue(name string, value string) error {
	ctx.Headers[name] = value
	return nil
}

// clientSendRequestTo Sends a request to the specified endpoint using the specified method.
func (ctx *ApiContext) ClientSendRequestTo(method, uri string) error {
	req, err := http.NewRequest(method, uri, nil)

	if err != nil {
		return err
	}

	// Add headers to request
	for name, value := range ctx.Headers {
		req.Header.Set(name, value)
		fmt.Printf("Header Added: %s=%s\n", name, value)
	}

	// Add query string to request
	q := req.URL.Query()
	for name, value := range ctx.QueryParams {
		q.Add(name, value)
	}

	req.URL.RawQuery = q.Encode()
	ctx.LastRequest = req

	w := httptest.NewRecorder()
	ctx.Server.ServeHTTP(w, req)

	body := w.Body.String()
	fmt.Println("body: ", body)

	ctx.LastResponse = &ApiResponse{
		StatusCode: w.Code,
		Body:       body,
	}

	return nil
}

// clientSendRequestToWithBody Send a request with json body. Ex: a POST request.
func (ctx *ApiContext) ClientSendRequestToWithBody(method, uri string, requestBody *godog.DocString) error {
	var jsonBodyBytes = []byte(requestBody.Content)
	req, err := http.NewRequest(method, uri, bytes.NewBuffer(jsonBodyBytes))

	for name, value := range ctx.Headers {
		req.Header.Set(name, value)
	}

	if err != nil {
		return err
	}

	ctx.LastRequest = req

	w := httptest.NewRecorder()
	ctx.Server.ServeHTTP(w, req)

	body := w.Body.String()
	fmt.Println("body: ", body)

	ctx.LastResponse = &ApiResponse{
		StatusCode: w.Code,
		Body:       body,
	}

	return nil
}

// theResponseCodeShouldBe Check if the http status code of the response matches the specified value.
func (ctx *ApiContext) TheResponseCodeShouldBe(statusCode int) error {
	if statusCode != ctx.LastResponse.StatusCode {
		return fmt.Errorf("expected status code to be %d, but actual is %d.\n Response body: %s", statusCode, ctx.LastResponse.StatusCode, ctx.LastResponse.Body)
	}
	return nil
}

// theResponseShouldMatchJSON Check that response matches the expected JSON.
func (ctx *ApiContext) TheResponseShouldMatchJSON(body *godog.DocString) error {
	actual := strings.Trim(ctx.LastResponse.Body, "\n")

	expected := body.Content

	match, err := isEqualJson(actual, expected)
	if err != nil {
		return err
	}
	if !match {
		return fmt.Errorf("expected json %s, does not match actual: %s", expected, actual)
	}
	return nil
}

func (ac *ApiContext) AddSteps(ctx *godog.ScenarioContext) {
	ctx.Step(`^client set header "([^"]*)" with value "([^"]*)"$`, ac.ClientSetHeaderWithValue)
	ctx.Step(`^client send "([^"]*)" request to "([^"]*)"$`, ac.ClientSendRequestTo)
	ctx.Step(`^client send "([^"]*)" request to "([^"]*)" with body:$`, ac.ClientSendRequestToWithBody)
	ctx.Step(`^the response code should be "(\d+)"$`, ac.TheResponseCodeShouldBe)
	ctx.Step(`^the response should match json:$`, ac.TheResponseShouldMatchJSON)
}

func isEqualJson(s1, s2 string) (bool, error) {
	var o1 interface{}
	var o2 interface{}

	err := json.Unmarshal([]byte(s1), &o1)

	if err != nil {
		return false, err
	}

	err = json.Unmarshal([]byte(s2), &o2)

	if err != nil {
		return false, err
	}

	return reflect.DeepEqual(o1, o2), nil
}
