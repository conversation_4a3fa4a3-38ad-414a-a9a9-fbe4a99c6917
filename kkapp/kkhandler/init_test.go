//go:build integration

package kkhandler

import (
	"log"
	"os"
	"testing"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	"github.com/jarcoal/httpmock"
	"github.com/lib/pq"
)

var (
	_testEmail    = []string{"<EMAIL>"}
	_testPhone    = []string{"+88691234567"}
	_testAllPhone = []string{"+88691234567", "+8869888888"}
	_testAllEmail = []string{"<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"}
)

func TestMain(m *testing.M) {
	// call flag.Parse() here if TestMain uses flags
	log.Println("======================================")
	log.Println("TESTCASE Context INIT && Setup")
	log.Println("======================================")

	if err := config.Init(); err != nil {
		log.Fatal("init config fail, err", err)
	}
	kkapp.ContextInit()
	Before()

	result := m.Run()

	log.Println("======================================")
	log.Println("TESTCASE TearDown")
	log.Println("======================================")
	TearDown()
	os.Exit(result)

}

func Before() {
	_cleanup()

	db := kkapp.App.DbUser.Master()
	// facebook
	for _, email := range _testEmail[:1] {
		db.Exec("INSERT INTO users (id, email, role, type, expired_at) VALUES('1234', $1, 'freetrial', 'general', '2030-01-01 00:00:00');", email)
	}
	// accountkit
	for _, phone := range _testPhone[:1] {
		db.Exec("INSERT INTO users (id, phone, role, type, expired_at) VALUES('1235', $1, 'freetrial', 'general', '2030-01-01 00:00:00');", phone)
	}

	db.Exec(`INSERT INTO users (id, phone, role, type, expired_at, membership) 
		VALUES('1236', '<EMAIL>', 'expired', 'general', '2030-01-01 00:00:00', '[{"role":"expired"}]');`)
}

func TearDown() {
	httpmock.DeactivateAndReset()
	_cleanup()
}

func _cleanup() {
	db := kkapp.App.DbUser.Master()
	db.Exec("DELETE from tokens USING users u WHERE u.email = ANY($1) AND tokens.user_id = u.id", pq.Array(_testAllEmail))
	db.Exec("DELETE from users u WHERE email = ANY($1)", pq.Array(_testAllEmail))
	db.Exec("DELETE from tokens USING users u WHERE u.phone = ANY($1) AND tokens.user_id = u.id", pq.Array(_testAllPhone))
	db.Exec("DELETE from users u WHERE phone = ANY($1)", pq.Array(_testAllPhone))
	db.Exec("DELETE FROM payment_info USING users WHERE users.id = payment_info.user_id AND users.phone = ANY($1)", pq.Array(_testAllPhone))
}
