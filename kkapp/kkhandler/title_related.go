package kkhandler

import (
	"log"
	"net/http"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/go-zoo/bone"
)

// GetRelatedTitle handler for api v3
func GetRelatedTitle(w http.ResponseWriter, r *http.Request) {
	var titleid string
	var err error
	var titles model.CollectionTitles

	response := model.MakeOk()
	titleid = bone.GetValue(r, "titleid")

	log.Println(titleid)

	defer func() {
		kkapp.App.Render.JSON(w, http.StatusOK, response)
	}()

	titles, err = model.NewTitleRelated(titleid)

	if err != nil {
		log.Println("[ERROR]", err)
		// return
	}

	data := make(map[string]interface{})
	data["titles"] = titles.FilterNotExpired()
	response.Data = data
}

// GetRelatedTitleRDC get related titles via RDC recommendation API
func GetRelatedTitleRDC(w http.ResponseWriter, r *http.Request) {
	var titleid string

	response := model.MakeOk()
	titleid = bone.GetValue(r, "titleid")

	log.Println(titleid)

	defer func() {
		kkapp.App.Render.JSON(w, http.StatusOK, response)
	}()

	titles, err := model.NewTitleRelatedRDC(titleid)

	if err != nil {
		log.Println("[ERROR]", err)
		// return
	}

	validatedTitles := []*model.TitleDetail{}
	// remove not validated titles
	for _, item := range titles {
		if item.IsValidated {
			validatedTitles = append(validatedTitles, item)
		}
	}

	data := make(map[string]interface{})
	data["titles"] = validatedTitles
	response.Data = data
}
