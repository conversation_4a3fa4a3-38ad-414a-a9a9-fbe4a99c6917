package kkauth

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strings"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/amplitude"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"gopkg.in/guregu/null.v3"
)

// Phone for post json
type Phone struct {
	Phone string `json:"phone"`
}

// PostInternalTokens handler internal authientication only at DEBUG (test) env
func PostInternalTokens(w http.ResponseWriter, r *http.Request) {
	var err error
	var userPhone Phone
	// response := kkhandler.MakeOkMap()
	response := model.MakeOk()
	param := model.GetParams(r)

	jsdecoder := json.NewDecoder(r.Body)
	err = jsdecoder.Decode(&userPhone)

	if !strings.HasPrefix(userPhone.Phone, "022444") {
		errResp := model.ErrorServer("")
		errResp.Status.Message = fmt.Sprintf("Invalid phone number: %s", userPhone.Phone)
		errResp.Status.Type = "FAIL"
		errResp.Status.Subtype = ""
		kkapp.App.Render.JSON(w, errResp.Code, errResp)
		return
	}

	// pass phone check
	param.Phone = null.String{NullString: sql.NullString{String: userPhone.Phone, Valid: true}}
	param.SrcToken = "phone"
	param.Provider = "accountkit"

	userID, err := model.NewUserAuth(param)

	if err != nil {
		log.Println(err)
		kkapp.App.Render.JSON(w, http.StatusOK, model.ErrorServer(err.Error()))
		return
	}

	log.Println(userID, err)

	db := kkapp.App.DbUser.Master()
	var me model.UserInfo
	if param.Subtype == "SIGNIN" {
		// existed user
		slavedb := kkapp.App.DbUser.Slave()
		me, err = model.NewUserInfo(userID, slavedb)
	} else {
		// new user
		me, err = model.NewUserInfo(userID, db)
		me.IsNew = true
		// amplitude
		event, _ := amplitude.NewAccountSignup(me.Id, me.Role, "", "", "", "", r)
		go event.Send()
	}

	if err != nil {
		log.Println("[ERROR]", err)
		response.Data = nil
		kkapp.App.Render.JSON(w, http.StatusOK, response)
		return
	}

	// auth info
	var authinfo *model.AuthInfo
	if param.Subtype == "SIGNIN" {
		tokenHash := model.GenTokenHash(param.SrcToken, param.SourceIp, param.UserAgent)
		authinfo, _ = model.NewAuthInfoFromTokenHash(userID, tokenHash)
	}

	if authinfo == nil || authinfo.Token == "" {
		// signup, or expired let's generate the authinfo
		param.Role = me.Role
		param.Type = me.Type
		authinfo, err = model.NewAuthInfo(me.Id, param)
	}

	if err != nil {
		log.Println("[ERROR]", err)
		response.Data = nil
		kkapp.App.Render.JSON(w, http.StatusOK, response)
		return
	}

	me.Auth = authinfo
	response.Data = me
	response.Status.Subtype = param.Subtype

	kkapp.App.Render.JSON(w, http.StatusOK, response)
}
