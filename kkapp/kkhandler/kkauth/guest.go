package kkauth

import (
	"net/http"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
)

// GetAuthGuest handler /v3/auth/guest
func GetAuthGuest(w http.ResponseWriter, r *http.Request) {
	scope := r.URL.Query().Get("scope")

	if scope != "KKTV-clients" {
		errResp := model.ErrorGuestScope(scope)
		kkapp.App.Render.JSON(w, errResp.Code, errResp)
		return
	}

	data := make(map[string]interface{})
	okResp := model.MakeOk()
	authinfo, _ := model.NewGuestAuthInfo()

	data["role"] = "guest"
	data["auth"] = authinfo
	okResp.Data = data
	kkapp.App.Render.JSON(w, http.StatusOK, okResp)
}
