package kkauth

import (
	"crypto/hmac"
	"crypto/sha256"
	"database/sql"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"log"
	"net/http"
	"strings"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/amplitude"
	"github.com/KKTV/kktv-api-v3/kkapp/coldstart"
	"github.com/KKTV/kktv-api-v3/kkapp/kkutil"
	"github.com/KKTV/kktv-api-v3/kkapp/model"

	// "github.com/huandu/facebook"
	"golang.org/x/oauth2"
	oauth2fb "golang.org/x/oauth2/facebook"
	"gopkg.in/guregu/null.v3"
)

const (
	fbAuthorizeEndpoint = "https://www.facebook.com/dialog/oauth"
	fbTokenEndpoint     = "https://graph.facebook.com/oauth/access_token"
	fbAppID             = "1100914086609815"
	fbSecret            = "7319f45bc511fbd1a40052c351897511"
	fbMeURLFmt          = "https://graph.facebook.com/v8.0/me?fields=id,name,email,gender,picture,verified_mobile_phone&format=json&access_token=%s&appsecret_proof=%s"
)

var (
	testApp = map[string]string{
		"346207087254421": "4d7460a4d3a20833278393cf4404b608", // dev
		"293302542191831": "0f15a0020d26ea04eb1c86d6b6307524", // inhouse
	}
)

type fbResp struct {
	ID      string `json:"id"`
	Email   string `json:"email"`
	Name    string `json:"name"`
	Picture struct {
		Data struct {
			Height       int64  `json:"height"`
			IsSilhouette bool   `json:"is_silhouette"`
			URL          string `json:"url"`
			Width        int64  `json:"width"`
		} `json:"data"`
	} `json:"picture"`
	VerifiedMobilePhone string   `json:"verified_mobile_phone"`
	Debug               struct{} `json:"__debug__"`
	Error               struct {
		Code      int64  `json:"code"`
		FbtraceID string `json:"fbtrace_id"`
		Message   string `json:"message"`
		Type      string `json:"type"`
	} `json:"error"`
}

// GetFacebook handler facebook authientication callback
func GetFacebook(w http.ResponseWriter, r *http.Request) {
	var err error
	// response := kkhandler.MakeOkMap()
	var usefbAppID, usefbSecret string
	var ok bool
	response := model.MakeOk()
	param := model.GetParams(r)
	usefbAppID = r.URL.Query().Get("fb_app_id")

	// design which fb app
	if usefbSecret, ok = testApp[usefbAppID]; !ok {
		// not found in testApp map, use default fb app id
		usefbAppID = fbAppID
		usefbSecret = fbSecret
	}

	log.Println(param.Code, param.RedirectUri, usefbAppID, usefbSecret)
	if param.Code != "" && param.RedirectUri != "" {
		// exchange token
		fbOauthConfig := &oauth2.Config{
			ClientID:     usefbAppID,
			ClientSecret: usefbSecret,
			RedirectURL:  param.RedirectUri,
			Scopes:       []string{"public_profile", "email", "user_mobile_phone"},
			Endpoint:     oauth2fb.Endpoint,
		}

		token, err := fbOauthConfig.Exchange(oauth2.NoContext, param.Code)
		if err != nil {
			log.Println("[ERROR]", err)
			resp := model.ErrorBadRequest("cannot fetch token")
			kkapp.App.Render.JSON(w, resp.Code, resp)
			return
		}

		log.Println(token, err)
		// spew.Dump(token)
		log.Println("[INFO] token", token)
		param.Token = token.AccessToken
	}

	if param.Token == "" {
		resp := model.ErrorNotFound("token")
		kkapp.App.Render.JSON(w, resp.Code, resp)
		return
	}

	hash := hmac.New(sha256.New, []byte(usefbSecret))
	hash.Write([]byte(param.Token))
	appsecretProof := hex.EncodeToString(hash.Sum(nil))
	log.Println("[INFO] token ", param.Token)
	log.Println("[INFO] appsecret_proof ", appsecretProof)
	fbmeURL := fmt.Sprintf(fbMeURLFmt, param.Token, appsecretProof)
	log.Println(fbmeURL)

	var profile fbResp
	httpresp, err := http.Get(fbmeURL)

	if err != nil {
		log.Println("[ERROR] fb get error", err)
		resp := model.ErrorAuthorized("token")
		kkapp.App.Render.JSON(w, resp.Code, resp)
		return
	}

	defer httpresp.Body.Close()
	respBytes, err := ioutil.ReadAll(httpresp.Body)

	if err != nil {
		log.Println("[ERROR]", err)
		resp := model.ErrorAuthorized("profile")
		kkapp.App.Render.JSON(w, resp.Code, resp)
		return
	}

	json.Unmarshal(respBytes, &profile)

	if profile.Error.Message != "" {
		log.Println("[ERROR]", profile.Error)
		resp := model.ErrorAuthorized("profile")
		kkapp.App.Render.JSON(w, resp.Code, resp)
		return
	}

	phone := profile.VerifiedMobilePhone
	if strings.TrimSpace(phone) != "" {
		param.Phone = null.String{NullString: sql.NullString{String: phone, Valid: true}}
	}

	email := profile.Email
	if strings.TrimSpace(email) != "" {
		param.Email = null.String{NullString: sql.NullString{String: email, Valid: true}}
	}

	name := profile.Name
	if strings.TrimSpace(name) != "" {
		param.Name = null.String{NullString: sql.NullString{String: name, Valid: true}}
	}

	avatarURL := profile.Picture.Data.URL
	if avatarURL != "" {
		param.AvatarUrl = avatarURL
	}

	srcid := profile.ID
	if srcid != "" {
		param.SrcId = srcid
	}

	log.Println(profile.ID)

	// require OAuth Scopes user_gender
	// if gender := profile.Gender {
	// param.Gender = gender
	// }

	param.SrcToken = param.Token
	userID, err := model.NewUserAuthForFacebook(param)

	if err != nil {
		switch {
		case strings.Contains(err.Error(), "Not existed"):
			kkapp.App.Render.JSON(w, http.StatusNotFound, model.ErrorSignup(err.Error()))
		default:
			kkapp.App.Render.JSON(w, http.StatusOK, model.ErrorServer(err.Error()))
		}
		return
	}

	db := kkapp.App.DbUser.Master()
	var me model.UserInfo
	if param.Subtype == "SIGNIN" {
		// existed user
		slavedb := kkapp.App.DbUser.Slave()
		me, err = model.NewUserInfo(userID, slavedb)
		// kkbox id check if needed
		if me.KkboxSub.Valid && err == nil {
			me, err = me.KKBOXIDCheck()
		}
		event, _ := amplitude.NewAccountLogin(me.Id, "facebook", r)
		if me.Phone.Valid {
			event.PhoneNumber = me.Phone.String
		}
		if me.Name.Valid {
			event.FbName = me.Name.String
		}
		if me.FbID.Valid {
			event.FbID = me.FbID.String
		}
		go event.Send()

		// signin edm event
		// if me.Email.Valid {
		// 	go kkapp.App.Ematic.Signin(me.Email.String)
		// }

	} else {
		// new user
		me, err = model.NewUserInfo(userID, db)
		me.IsNew = true
		// amplitude
		event, _ := amplitude.NewAccountSignup(me.Id, me.Role, "", "", "", "", r)
		go event.Send()
	}

	if err != nil {
		log.Println("[ERROR]", err)
		response.Data = nil
		kkapp.App.Render.JSON(w, http.StatusOK, response)
		return
	}

	// auth info
	var authinfo *model.AuthInfo

	if param.Subtype == "SIGNIN" {
		// signin, not first time
		// if from same SrcToken, and not expired don't need to generate it again
		tokenHash := model.GenTokenHash(param.SrcToken, param.SourceIp, param.UserAgent)
		log.Println("[INFO] token_hash", tokenHash)
		// maybe expired, err will be sql: no rows in result set
		authinfo, _ = model.NewAuthInfoFromTokenHash(userID, tokenHash)
	}

	if authinfo == nil || authinfo.Token == "" {
		// signup, or expired let's generate the authinfo
		param.Role = me.Role
		param.Type = me.Type
		authinfo, err = model.NewAuthInfo(me.Id, param)

	}

	// spew.Dump(authinfo)
	if err != nil {
		log.Println("[ERROR]", err)
		response.Data = nil
		kkapp.App.Render.JSON(w, http.StatusOK, response)
		return
	}

	me.Auth = authinfo
	response.Data = me

	// check kkbox accout
	if me.NeedKKBOXBind {
		err = errors.New("NeedKKBOXBind")
		response.Status.Type = "BAD REQUEST"
		response.Status.Subtype = err.Error()
		response.Status.Message = "need kkbox bind"
		kkapp.App.Render.JSON(w, http.StatusBadRequest, response)
		return
	}
	if me.NeedKKBOXLogin {
		err = errors.New("NeedKKBOXLogin")
		response.Status.Type = "BAD REQUEST"
		response.Status.Subtype = err.Error()
		response.Status.Message = "need kkbox login"
		if me.KkidIdentifier != "" {
			response.Status.Info = map[string]string{"original_account": kkutil.BlurEmailPhone(me.KkidIdentifier)}
		}
		kkapp.App.Render.JSON(w, http.StatusBadRequest, response)
		return
	}

	deviceID := r.Header.Get("X-Device-ID")
	if deviceID != "" {
		go coldstart.MigrateDevicePreferenceToUser(deviceID, me.Id)
	}

	response.Status.Subtype = param.Subtype
	kkapp.App.Render.JSON(w, http.StatusOK, response)
}
