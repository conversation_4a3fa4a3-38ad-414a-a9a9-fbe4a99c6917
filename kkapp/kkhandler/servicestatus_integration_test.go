package kkhandler

import (
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/kkmiddleware"
	"github.com/go-zoo/bone"
	"github.com/justinas/alice"
)

// TestGetServiceStatus, this must after kkmiddleware.ServiceStatus
func TestGetServiceStatus_IntegrationTest(t *testing.T) {
	shouldContains := []string{"OK", "ios", "android"}
	mux := bone.New()
	middleware := alice.New(
		kkmiddleware.ServiceStatus(),
		kkmiddleware.RoamBlocker("TW", kkapp.App.IPWhiteList),
	)

	// setup the announce

	announce := fmt.Sprintf(`{"announcements": [{"url": "https://www.kktv.me/", "message": "\u6e2c\u8a66\u7cfb\u7d71\u516c\u544a", "start_time": %d, "end_time": 1847836800}]}`, time.Now().Unix()-300)

	pool := kkapp.App.RedisMeta.Master()
	pool.Cmd("SET", "meta:v1:system-announcements:json", announce)

	mux.Get("/v3/service_status", middleware.ThenFunc(GetServiceStatus))

	r, err := http.NewRequest("GET", "/v3/service_status", nil)

	if err != nil {
		t.Fatal(err)
	}

	w := httptest.NewRecorder()

	mux.ServeHTTP(w, r)

	// log.Println(w.Body.String())

	if status := w.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v",
			status, http.StatusOK)
	}

	for _, word := range shouldContains {
		if !strings.Contains(w.Body.String(), word) {
			t.Errorf("not contains %s", word)
		}

	}

}
