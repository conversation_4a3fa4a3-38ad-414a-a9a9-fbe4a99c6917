package kkhandler

import (
	"log"
	"net/http"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kkapp/model/recommend"
)

// GetMeRecommend get users/me recommend list for DEBUG
func GetMeRecommend(w http.ResponseWriter, r *http.Request) {
	response := model.MakeOk()
	user := r.Context().Value("user").(model.JwtUser)

	defer func() {
		kkapp.App.Render.JSON(w, http.StatusOK, response)
	}()

	if user.IsGuest() || user.Sub == "" {
		response.Data = model.GuestInfo
		return
	}

	log.Println(user.Sub)
	item := new(recommend.Recommend)
	item.UserID = user.Sub
	item.Load()
	item.Worker()
	response.Data = item
}
