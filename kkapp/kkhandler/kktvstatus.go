package kkhandler

import (
	"errors"
	"log"
	"net/http"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbuser"
)

var (
	sqlkktvstatus = map[string]string{
		"lastorder": `SELECT id, user_id, product_id, price::numeric::int, payment_type,
 start_date, end_date, order_date,
 status, created_at, realized_at, canceled_at,
 price_no_tax::numeric::int, tax_rate, invoice, fee, external_order_id, info FROM orders WHERE
 user_id = $1 AND status = 'ok' ORDER BY order_date DESC LIMIT 1;`,
	}
)

// ServiceItem from billing
type ServiceItem struct {
	Service        string `json:"service"`
	State          string `json:"state"`
	DueDate        int64  `json:"due_date"`
	InSubscription bool   `json:"in_subscription,omitempty"`
	LastOrderID    string `json:"last_order_id,omitempty"`
}

// GetUserStatus handler for https://cs.kkbox.com to lookup
func GetUserStatus(w http.ResponseWriter, r *http.Request) {
	// get the old kkbox msno sub
	var u model.UserInfo
	var serviceStatus ServiceItem
	var err error
	var sub, kkid string
	sub = r.URL.Query().Get("sub")
	kkid = r.URL.Query().Get("kkid")

	response := model.MakeOk()

	defer func() {
		if err != nil {
			log.Println("[ERROR]", err)
			response.Status.Type = "Fail"
			response.Status.Subtype = "400.1"
			response.Status.Message = err.Error()
			kkapp.App.Render.JSON(w, http.StatusBadRequest, response)
		} else {
			kkapp.App.Render.JSON(w, http.StatusOK, response)
		}
	}()

	if sub == "" && kkid == "" {
		err = errors.New("No enough info.")
		return
	}

	if sub != "" {
		u, err = model.GetUserBySub(sub)
		if err != nil {
			return
		}
	} else if kkid != "" {
		u, err = model.GetUserByKKIDSub(kkid)
		if err != nil {
			return
		}
	}

	if u.Id == "" {
		err = errors.New("No found")
		return
	} else {
		// fetch last order
		var order *dbuser.Order
		order = new(dbuser.Order)
		dbSlave := kkapp.App.DbUser.Slave()
		orderErr := dbSlave.Get(order, sqlkktvstatus["lastorder"], u.Id)

		if orderErr != nil {
			log.Println("[ERROR] order", orderErr)
		}

		if order.ID != "" {
			serviceStatus.LastOrderID = order.ID
		}
	}

	serviceStatus.Service = "kktv"
	serviceStatus.State = u.Role
	serviceStatus.DueDate = u.ExpiredAt
	if u.AutoRenew {
		serviceStatus.InSubscription = true
	}

	response.Data = serviceStatus
}
