package bv

import (
	"bytes"
	"crypto/hmac"
	"crypto/md5"
	"encoding/hex"
	"errors"
	"fmt"
	"log"
	"math"
	"net/url"
	"path/filepath"
	"strconv"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
)

// GetHash2 for random string with timestamp
func GetHash2(timestamp int64) (value string) {
	hmac := hmac.New(md5.New, []byte("I Love KKTV."))
	hmac.Write([]byte(strconv.FormatInt(timestamp, 10)))
	//hexHash :=
	//log.Println(hexHash)
	return fmt.Sprintf("%d%s", timestamp, hex.EncodeToString(hmac.Sum(nil))[:10])
}

// https://github.com/hadesbox/copyS3part/blob/master/copymultipart.go
// constant for number of bits in 1024 megabyte chunk
const max_chunk_size = 1024 * 1024 * 1024

func calculate_limits(lowlimit int64, hilimit int64) string {
	var buffer bytes.Buffer
	buffer.WriteString("bytes=")
	str := strconv.FormatInt(lowlimit, 10)
	buffer.WriteString(str)
	buffer.WriteString("-")
	str = strconv.FormatInt(hilimit, 10)
	buffer.WriteString(str)
	return buffer.String()
}

func copyPart(params *s3.UploadPartCopyInput, partNumber int, client *s3.S3, notify chan<- s3.CompletedPart) {
	log.Println("[INFO] STARTING CHUNK", partNumber, *params.CopySourceRange)
	respUploadPartCopy, err1 := client.UploadPartCopy(params)
	if err1 != nil {
		log.Println("[ERROR] UploadPartCopy", err1)
		panic(err1)
	}
	log.Println("[INFO] SUCCESS CHUNK", partNumber, *respUploadPartCopy.CopyPartResult.ETag)
	notify <- s3.CompletedPart{ETag: aws.String(*respUploadPartCopy.CopyPartResult.ETag), PartNumber: aws.Int64(int64(partNumber))}
}

// MultiPartCopy copy very big s3 file
func MultiPartCopy(sourceBucketName string, sourcePrefix string, destBucketName string, destPrefix string) (err error) {

	// in case sourcePrefix encoded
	sourcePrefix, err = url.QueryUnescape(sourcePrefix)
	client := s3.New(session.New(), &aws.Config{Region: aws.String("ap-northeast-1")})
	result, err := client.ListObjects(&s3.ListObjectsInput{Bucket: &sourceBucketName, Prefix: &sourcePrefix})

	if err != nil {
		log.Println("[ERROR] ListObjects", err)
		return
	}

	log.Println("[INFO] copy", sourceBucketName, sourcePrefix, "to", destBucketName, destPrefix)

	if len(result.Contents) == 0 {
		return errors.New("Not found")
	}

	log.Println("[INFO] TOTAL SIZE", *result.Contents[0].Size, "KEY", *result.Contents[0].Key)

	paramsMultipartUpload := &s3.CreateMultipartUploadInput{
		Bucket: aws.String(destBucketName),
		Key:    aws.String(destPrefix),
	}

	total_size := *result.Contents[0].Size
	log.Println("[INFO] file sizw", total_size)
	total_chunks := int(math.Floor(float64(total_size) / float64(max_chunk_size)))
	log.Println("[INFO] TOTAL CHUNKS", total_chunks)
	remainer_chunk := total_size % max_chunk_size
	log.Println("[INFO] LAST CHUNK Remainder bytes", remainer_chunk)
	resp, err := client.CreateMultipartUpload(paramsMultipartUpload)

	if err != nil {
		log.Println("[ERROR] CreateMultipartUpload", err)
		return
	}

	log.Println("[INFO] S3 Multipart UploadId", *resp.UploadId)

	outputChan := make(chan s3.CompletedPart)

	for i := 0; i <= total_chunks; i++ {
		var res string
		if i == 0 {
			if i == total_chunks {
				res = calculate_limits(int64(i)*max_chunk_size, remainer_chunk-1)
				log.Println("YES", res)
			} else {
				res = calculate_limits(int64(i)*max_chunk_size, (int64(i)+1)*max_chunk_size)
			}
		} else if i == total_chunks {
			res = calculate_limits((int64(i)*max_chunk_size)+1, (int64(i)*max_chunk_size)+remainer_chunk-1)
		} else {
			res = calculate_limits((int64(i)*max_chunk_size)+1, int64(i+1)*max_chunk_size)
		}
		log.Println("[INFO] UPLOAD START CHUNK ", i, res)

		paramsUploadInput := &s3.UploadPartCopyInput{
			Bucket:          aws.String(destBucketName),
			CopySource:      aws.String(url.QueryEscape(filepath.Join(sourceBucketName, sourcePrefix))),
			CopySourceRange: aws.String(res),
			Key:             aws.String(destPrefix),
			PartNumber:      aws.Int64(int64(i + 1)),
			UploadId:        aws.String(*resp.UploadId),
		}

		go copyPart(paramsUploadInput, i, client, outputChan)

	}

	// 5 minutes timeout
	timeout := make(chan bool, 1)
	go func() {
		time.Sleep(300 * time.Second)
		timeout <- true
	}()

	done_chunks := 0

	unord_parts := make([]string, total_chunks+1)

	for done_chunks <= total_chunks {
		select {
		case mensajito := <-outputChan:
			done_chunks++
			//fmt.Println(*mensajito.PartNumber, *mensajito.ETag, done_chunks, total_chunks)
			unord_parts[*mensajito.PartNumber] = *mensajito.ETag
		case <-timeout:
			err = errors.New("time out for copy time, try different chunksize...")
			return err
		}
	}

	ord_parts := make([]*s3.CompletedPart, total_chunks+1)

	for key := range unord_parts {
		ord_parts[key] = &s3.CompletedPart{ETag: aws.String(unord_parts[key]), PartNumber: aws.Int64(int64(key + 1))}
	}

	paramsCompleteMultipartUploadInput := &s3.CompleteMultipartUploadInput{
		Bucket:   aws.String(destBucketName),
		Key:      aws.String(destPrefix),
		UploadId: aws.String(*resp.UploadId),
		MultipartUpload: &s3.CompletedMultipartUpload{
			Parts: ord_parts,
		},
	}
	_, err = client.CompleteMultipartUpload(paramsCompleteMultipartUploadInput)
	if err != nil {
		return err
	}

	log.Println("[INFO] SUCCESS CompleteMultipartUpload")
	return nil
}
