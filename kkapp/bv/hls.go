package bv

import (
	"bytes"
	"errors"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"path"
	"strings"
)

var (
	errHLS = errors.New("wrong hls m3u8 file format")
	// the line break is important
	hlsSubtitleTemplate = `#EXT-X-MEDIA:TYPE=SUBTITLES,GROUP-ID="subs",NAME="%s",DEFAULT=%s,AUTOSELECT=YES,FORCED=NO,LANGUAGE="%s",URI="%s"
`
	m3u8SubtitleTemplate = `#EXTM3U
#EXT-X-TARGETDURATION:%.0f
#EXT-X-VERSION:3
#EXT-X-MEDIA-SEQUENCE:1
#EXT-X-PLAYLIST-TYPE:VOD
#EXTINF:%.1f,
%s
#EXT-X-ENDLIST`
)

// HLS file struct for blendvision
type HLS struct {
	Data      []byte
	Subtitles []string
}

// Parse analyse the HLS file
func (i *HLS) Parse() (err error) {
	return err
}

func (i *HLS) AddSubtitle(relativePath string) (err error) {
	if !strings.HasSuffix(relativePath, ".vtt") {
		return errHLS
	}
	i.Subtitles = append(i.Subtitles, relativePath)
	return nil
}

func (i *HLS) WriteSubtitle() []byte {
	bf := new(bytes.Buffer)
	for _, subtitle := range i.Subtitles {
		// strip ".vtt"
		byDefault := "NO"
		baseName := path.Base(subtitle)
		lang := baseName[:len(baseName)-4]
		translateLang := langTranslate[lang]
		m3u8URI := subtitle[:len(subtitle)-4] + ".m3u8"

		if translateLang == "" {
			translateLang = lang
		}
		if lang == "zh-Hant" {
			byDefault = "YES"
		}
		bf.Write([]byte(fmt.Sprintf(hlsSubtitleTemplate, translateLang, byDefault, lang, m3u8URI)))
	}
	return bf.Bytes()
}

func (i *HLS) WriteSubtitleM3U8(duration float64, baseName string) []byte {
	bf := new(bytes.Buffer)
	bf.Write([]byte(fmt.Sprintf(m3u8SubtitleTemplate, duration, duration, baseName)))
	return bf.Bytes()
}

// WriteFor specific video height mpd file
func (i *HLS) WriteFor(videoHeight string) []byte {
	dataBuffer := new(bytes.Buffer)
	lines := bytes.Split(i.Data, []byte("\n"))
	for idx := 0; idx < len(lines); idx++ {
		line := lines[idx]
		if idx == 0 {
			// always write first line #EXTM3U
			dataBuffer.Write(line)
			dataBuffer.Write([]byte("\n"))
			if len(i.Subtitles) > 0 {
				dataBuffer.Write(i.WriteSubtitle())
			}
			continue
		}

		if bytes.HasPrefix(line, []byte("#EXT-X-STREAM-INF")) {
			if videoHeight == "*" || bytes.Index(line, []byte("x"+videoHeight)) > 0 {
				if len(i.Subtitles) > 0 {
					dataBuffer.Write(append(line, []byte(",SUBTITLES=\"subs\"\n")...))
				} else {
					dataBuffer.Write(append(line, []byte("\n")...))
				}
			} else {
				// skip next line
				idx++
			}
		} else {
			dataBuffer.Write(append(line, []byte("\n")...))
		}

	}
	return dataBuffer.Bytes()
}

// NewHLS get a new instance of HLS from file path
func NewHLS(filePath string) (hls *HLS, err error) {
	hlsFile, err := os.Open(filePath)
	if err != nil {
		log.Println(err)
		return nil, err
	}

	defer hlsFile.Close()

	byteValue, _ := ioutil.ReadAll(hlsFile)
	hls = new(HLS)
	hls.Data = byteValue
	err = hls.Parse()
	if err != nil {
		return nil, err
	}
	return hls, nil
}

// NewHLSbytes get a new instance of HLS from file path
func NewHLSbytes(src []byte) (hls *HLS, err error) {
	hls = new(HLS)
	hls.Data = src
	err = hls.Parse()
	if err != nil {
		return nil, err
	}
	return hls, nil
}
