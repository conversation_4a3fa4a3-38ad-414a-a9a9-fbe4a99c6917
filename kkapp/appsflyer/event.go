package appsflyer

// EV struct for event value of appsflyer
type EV struct {
	AfRevenue  string `json:"af_revenue"`
	AfPrice    string `json:"af_price"`
	AfQuantity string `json:"af_quantity"`
}

// Payload struct for payload of appsflyer
type Payload struct {
	AppsflyerID   string `json:"appsflyer_id"`
	EventName     string `json:"eventName"`
	EventValue    EV     `json:"eventValue"`
	EventTime     string `json:"eventTime"`
	AfEventsAPI   string `json:"af_events_api"`
	AdvertisingID string `json:"advertising_id,omitempty"`
	IDFA          string `json:"idfa,omitempty"`
	BundleID      string `json:"bundle_id,omitempty"`
}

// MediaSource struct for mediasource
type MediaSource struct {
	Facebook  FB    `json:"facebook,omitempty"`
	Appsflyer Flyer `json:"appsflyer"`
}

// FB struct for mediasource facebook
type FB struct {
	ID string `json:"id"`
}

// Flyer struct for appsflyer
type Flyer struct {
	AppsflyerID   string `json:"appsflyerId"`
	IDFA          string `json:"idfa"`
	BundleID      string `json:"bundleId"`
	AdvertisingID string `json:"advertisingId"`
}
