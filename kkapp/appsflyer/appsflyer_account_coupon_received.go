package appsflyer

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"net/http"
	"strconv"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbredeem"
)

// NewAccountCouponReceived preparing payload for Appsflyer
func NewAccountCouponReceived(u model.UserInfo, groups []dbredeem.DBCouponGroup) (p Payload, err error) {

	coupon := groups[0]

	var m MediaSource
	err = json.Unmarshal([]byte(u.MediaSource.String), &m)
	if err != nil {
		log.Println("[ERROR] - Appsflyer - Unmarshal mediasource data failed.", err)
	}

	p.AppsflyerID = m.Appsflyer.AppsflyerID
	p.EventName = "Account Coupon Received"
	p.EventValue.AfRevenue = strconv.Itoa(coupon.Price)
	p.EventValue.AfPrice = strconv.Itoa(coupon.Price)
	p.EventValue.AfQuantity = "1"
	t := time.Now()
	evt := fmt.Sprintf("%04d-%02d-%02d %02d:%02d:%02d.%03d", t.Year(), t.Month(), t.Day(), t.Hour(), t.Minute(), t.Second(), t.Nanosecond())
	p.EventTime = evt
	p.AfEventsAPI = "true"
	p.AdvertisingID = m.Appsflyer.AdvertisingID
	p.IDFA = m.Appsflyer.IDFA
	p.BundleID = m.Appsflyer.BundleID

	return p, err
}

// Send sending Payload to Appsflyer
func (p *Payload) Send() {

	url := appsFlyerURL

	// for Android
	if p.AdvertisingID != "" {
		url += appsFlyerAndroidAppID
	}

	// for iOS
	if p.IDFA != "" && p.BundleID != "" {
		url += appsFlyerIOSAppID
	}

	jsonStr, err := json.Marshal(p)
	if err != nil {
		log.Println("[ERROR] - Appsflyer - Send - json marshal failed.", err)
	}
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
	if err != nil {
		log.Println("[ERROR] - Appsflyer - Send - http.NewRequest failed.", err)
	}

	req.Header.Set("authentication", authentication)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: time.Duration(time.Second * 10)}
	resp, err := client.Do(req)
	if err != nil {
		log.Println("[ERROR] - Appsflyer - Send - http.Client.Do failed.", err)
	}
	defer resp.Body.Close()

	log.Println("[INFO] - Appsflyer - Send - response Status:", resp.Status)
	log.Println("[INFO] - Appsflyer - Send - response Headers:", resp.Header)
	body, _ := ioutil.ReadAll(resp.Body)
	log.Println("[INFO] - Appsflyer - Send - response Body:", string(body))
}
