package kkutil

import (
	"testing"
	"time"
)

func TestParseDuration(t *testing.T) {
	type args struct {
		duration string
	}

	type wants struct {
		unit           string
		durationAmount int
	}

	tests := []struct {
		name  string
		args  args
		wants wants
	}{
		{
			name: "Parse duration 30 days",
			args: args{
				duration: "30 days",
			},
			wants: wants{
				unit:           "day",
				durationAmount: 30,
			},
		},
		{
			name: "Parse duration 3 mons",
			args: args{
				duration: "3 mons",
			},
			wants: wants{
				unit:           "mon",
				durationAmount: 3,
			},
		},
		{
			name: "Parse duration 2 years",
			args: args{
				duration: "2 years",
			},
			wants: wants{
				unit:           "year",
				durationAmount: 2,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if unit, durationAmount := ParseDuration(tt.args.duration); unit != tt.wants.unit || durationAmount != tt.wants.durationAmount {
				t.<PERSON>rrorf("ParseDuration() = %s, %d, wants %v", unit, durationAmount, tt.wants)
			}
		})
	}
}

func TestAddDuration(t *testing.T) {
	type args struct {
		basetime       time.Time
		unit           string
		durationAmount int
	}

	basetime := time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)

	tests := []struct {
		name string
		args args
		want time.Time
	}{
		{
			name: "Add 10 days",
			args: args{
				basetime:       basetime,
				unit:           "day",
				durationAmount: 10,
			},
			want: basetime.AddDate(0, 0, 10),
		},
		{
			name: "Add 3 months",
			args: args{
				basetime:       basetime,
				unit:           "mon",
				durationAmount: 3,
			},
			want: basetime.AddDate(0, 3, 0),
		},
		{
			name: "Add 1 year",
			args: args{
				basetime:       basetime,
				unit:           "year",
				durationAmount: 1,
			},
			want: basetime.AddDate(1, 0, 0),
		},
		{
			name: "Minus 10 days",
			args: args{
				basetime:       basetime,
				unit:           "day",
				durationAmount: -10,
			},
			want: basetime.AddDate(0, 0, -10),
		},
		{
			name: "Minus 3 months",
			args: args{
				basetime:       basetime,
				unit:           "mon",
				durationAmount: -3,
			},
			want: basetime.AddDate(0, -3, 0),
		},
		{
			name: "Minus 1 year",
			args: args{
				basetime:       basetime,
				unit:           "year",
				durationAmount: -1,
			},
			want: basetime.AddDate(-1, 0, 0),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := AddDuration(tt.args.basetime, tt.args.unit, tt.args.durationAmount); got != tt.want {
				t.Errorf("AddDuration() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChtName(t *testing.T) {
	tests := []struct {
		name         string
		durationUnit DurationUnit
		want         string
	}{
		{
			name:         "Should return 天",
			durationUnit: DurationUnitDay,
			want:         "天",
		},
		{
			name:         "Should return 月",
			durationUnit: DurationUnitMonth,
			want:         "月",
		},
		{
			name:         "Should return 年",
			durationUnit: DurationUnitYear,
			want:         "年",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.durationUnit.ChtName(); got != tt.want {
				t.Errorf("durationUnit.ChtName() = %v, want %v", got, tt.want)
			}
		})
	}

}
