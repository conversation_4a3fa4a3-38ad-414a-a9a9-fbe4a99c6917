package kkmiddleware

import (
	"net"
	"net/http"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/geoinfo"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	amplitude "github.com/KKTV/kktv-api-v3/pkg/amplitudelib"
	"github.com/KKTV/kktv-api-v3/pkg/httpreq"
	zlog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/render"
)

// RoamBlocker geo lookup
// block the user who does not come from TAIWAN, not in IP white list or not in review mode (mobile app build)
func RoamBlocker(allowCountry string, ipWhiteList []*net.IPNet) func(next http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		fn := func(w http.ResponseWriter, r *http.Request) {

			// parse geo information from request
			geoinfo := geoinfo.NewGeoInfo(r, "")
			if geoinfo.Country.In(allowCountry) || geoinfo.IP.In(ipWhiteList) {
				next.ServeHTTP(w, r)
				return
			}

			_sInterface := r.Context().Value(ServiceStatusContextKey)
			servicestatus := _sInterface.(*model.ServiceStatus)

			var app model.MobileApp
			var ok bool
			platform := r.Header.Get(httpreq.HeaderPlatform)
			version := r.Header.Get(httpreq.HeaderAppVersion)
			platformMaps := map[string]string{
				"tvOS":       "appletv",
				"iOS":        "ios",
				"iPadOS":     "ios",
				"Android":    "android",
				"Android TV": "androidtv",
			}
			platformRecord := platformMaps[platform]
			app, ok = servicestatus.AppVersion[platformRecord]
			if ok && isVersionMatchReviewVersion(version, app.ReviewVersion) {
				next.ServeHTTP(w, r)
				return
			}

			go func() {
				idtf, _ := amplitude.NewIdentifyFromRequest(r)
				evt := &amplitude.GeoBlockEvent{
					DeviceID:          idtf.DeviceID,
					Country:           geoinfo.IsoCode,
					RegisteredCountry: geoinfo.Country.RegisteredCountry.IsoCode,
					IP:                geoinfo.IPAddress,
				}
				if user, ok := r.Context().Value("user").(model.JwtUser); ok {
					evt.UserID = user.Sub
				}
				if err := kkapp.App.AmplitudeClient.SendEvent(evt); err != nil {
					zlog.Warn("RoamBlocker: send GEO_LOCATION_BLOCKED event fail").Err(err).Send()
				}
			}()
			resp := createGEOBlockedResp()
			render.JSON(w, http.StatusForbidden, resp)
		}
		return http.HandlerFunc(fn)
	}
}

func createGEOBlockedResp() model.KKResp {
	resp := model.KKResp{
		Status: model.KKStatus{
			Type:    "FAIL",
			Subtype: "FORBIDDEN",
			Message: "Forbidden: geo-restriction",
		},
	}

	return resp
}

func isVersionMatchReviewVersion(version, reviewVersion string) bool {
	return version == reviewVersion
}
