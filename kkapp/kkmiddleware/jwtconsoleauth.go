package kkmiddleware

import (
	"context"
	"fmt"
	"net/http"
	"strings"

	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	jwt "gopkg.in/dgrijalva/jwt-go.v3"
)

// JWTConsoleAuth for dashboard jwt
func JWTConsoleAuth(secret, signingMethod, contextKey string) func(next http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		fn := func(w http.ResponseWriter, r *http.Request) {
			var auth, authhead string
			// header we want
			authhead = r.Header.Get("Authorization")

			if strings.HasPrefix(authhead, bearer) {
				auth = strings.Split(authhead, bearer)[1]
			} else {
				w.Header().Set("Content-Type", "application/json")
				w.WriteHeader(http.StatusUnauthorized)
				w.Write(UnauthorizedByte)
				return
			}
			token, err := jwt.Parse(auth, func(t *jwt.Token) (interface{}, error) {
				// Check the signing method
				if t.Method.Alg() != signingMethod {
					return nil, fmt.Errorf("unexpected jwt signing method %s", t.<PERSON><PERSON>["alg"])
				}
				return []byte(secret), nil
			})

			if err != nil {
				w.Header().Set("Content-Type", "application/json")
				w.WriteHeader(http.StatusUnauthorized)
				w.Write(UnauthorizedByte)
				return
			}

			if contextKey != "" {
				claims := token.Claims.(jwt.MapClaims)
				var user dbmeta.ConsoleUser
				user.Exp = int64(claims["exp"].(float64))
				user.Name = claims["name"].(string)
				user.Email = claims["email"].(string)
				user.Avatar = claims["avatar"].(string)
				user.Roles = claims["roles"].(string)

				ctx := context.WithValue(r.Context(), contextKey, user)
				newReq := r.WithContext(ctx)
				// Process request
				next.ServeHTTP(w, newReq)
				return
			}

			next.ServeHTTP(w, r)
		}
		return http.HandlerFunc(fn)
	}
}

// RequireRole middleware for limit dbmeta.ConsoleUser Roles
// only use at dashboard console
func RequireRole(contextKey, role string) func(next http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		fn := func(w http.ResponseWriter, r *http.Request) {
			var found bool
			user, _ := r.Context().Value(contextKey).(dbmeta.ConsoleUser)

			if strings.Contains(user.Roles, "admin") || strings.Contains(user.Roles, role) {
				found = true
			}

			if !found {
				w.Header().Set("Content-Type", "application/json")
				w.WriteHeader(http.StatusForbidden)
				w.Write(UnauthorizedByte)
				return
			}

			next.ServeHTTP(w, r)
		}
		return http.HandlerFunc(fn)
	}
}
