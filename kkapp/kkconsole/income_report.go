package kkconsole

import (
	"errors"
	"log"
	"net/http"
	"strings"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
)

var (
	s3IncomeReportBucket = "kktv-report-income"
)

type fn struct {
	Filename string `json:"filename"`
}

// GetConsoleIncomeReport console api for finance team to
func GetConsoleIncomeReport(w http.ResponseWriter, r *http.Request) {
	var err error
	var filename string
	var files []fn
	response := model.MakeOk()

	defer func() {
		if err != nil {
			log.Println("[ERROR]", err)
			response.Status.Type = "Error"
			response.Status.Subtype = ""
			response.Status.Message = err.Error()
			kkapp.App.Render.JSON(w, http.StatusUnauthorized, response)
			return
		}
		kkapp.App.Render.JSON(w, http.StatusOK, response)
	}()

	data := make(map[string]interface{})
	filename = r.URL.Query().Get("filename")

	// list income report
	if filename == "" {
		var resp *s3.ListObjectsOutput
		svc := s3.New(session.New(), &aws.Config{Region: aws.String("ap-northeast-1")})
		params := &s3.ListObjectsInput{
			Bucket:  aws.String(s3IncomeReportBucket),
			Prefix:  aws.String("income_report"),
			MaxKeys: aws.Int64(7200),
		}
		resp, err = svc.ListObjects(params)

		if err != nil {
			log.Println("[ERROR] list income report", err)
			return
		}

		for _, key := range resp.Contents {
			if strings.HasSuffix(*key.Key, ".xlsx") {
				files = append(files, fn{Filename: *key.Key})
			}
		}

		if len(files) == 0 {
			err = errors.New("not found any income report")
			return
		}
		data["files"] = files
		response.Data = data
		return
	}

	// download income report
	svc := s3.New(session.New(), &aws.Config{Region: aws.String("ap-northeast-1")})
	req, _ := svc.GetObjectRequest(&s3.GetObjectInput{
		Bucket: aws.String(s3IncomeReportBucket),
		Key:    aws.String(filename),
	})
	url, err := req.Presign(5 * time.Minute)
	if err != nil {
		return
	}

	data["url"] = url
	response.Data = data
}
