package kkconsole

import (
	"encoding/json"
	"net/http"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	"github.com/KKTV/kktv-api-v3/pkg/log"
)

// PutConsoleServiceStatus modify or add service status in console
func PutConsoleServiceStatus(w http.ResponseWriter, r *http.Request) {
	var err error
	response := model.MakeOk()

	defer func() {
		kkapp.App.Render.JSON(w, http.StatusOK, response)
	}()

	serviceStatus := new(model.ServiceStatus)

	jsdecoder := json.NewDecoder(r.Body)
	err = jsdecoder.Decode(serviceStatus)

	if err != nil {
		log.Warn("v3PutConsoleServiceStatus: json decode").Err(err).Interface("body", r.Body).Send()
		return
	}

	serviceStatus.Save()

	message := "saved service status"
	dbmeta.ConsoleLog(r, "service", message)
}
