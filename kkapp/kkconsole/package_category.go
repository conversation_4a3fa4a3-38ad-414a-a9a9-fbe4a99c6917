package kkconsole

import (
	"encoding/json"
	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"log"
	"net/http"
)

var (
	sqlPackageCategory = map[string]string{
		"list" : "select id, category from package_category;",
		"insert": "insert into package_category(category) values ($1);",
	}
)

func GetConsolePackageCategory(w http.ResponseWriter, r *http.Request) {
	response := model.MakeOk()
	defer func() {
		kkapp.App.Render.JSON(w, http.StatusOK, response)
	}()
	var categories model.ConsolePackageCategories
	db := kkapp.App.DbUser.Slave()
	err := db.Select(&categories, sqlPackageCategory["list"])

	if err != nil {
		log.Println(err)
		return
	}

	categories.ToJson()
	data := make(map[string]interface{})
	data["category"] = categories
	response.Data = data
}

func PostConsolePackageCategory(w http.ResponseWriter, r *http.Request) {
	var err error
	response := model.MakeOk()
	defer func() {
		if err != nil {
			kkapp.App.Render.JSON(w, http.StatusBadRequest, response)
		} else {
			kkapp.App.Render.JSON(w, http.StatusOK, response)
		}
	}()
	var pc model.ConsolePackageCategory
	jsdecoder := json.NewDecoder(r.Body)
	err = jsdecoder.Decode(&pc)

	if err != nil {
		log.Println("[ERROR]", err)
		response.Status.Message = err.Error()
		return
	}

	db := kkapp.App.DbUser.Master()
	_, err = db.Exec(sqlPackageCategory["insert"], pc.Name)

	if err != nil {
		log.Println("[ERROR]", err)
		response.Status.Message = err.Error()
		return
	}
}
