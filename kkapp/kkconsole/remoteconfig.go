package kkconsole

import (
	"encoding/json"
	"log"
	"net/http"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
)

// PutConsoleRemoteConfig console api
func PutConsoleRemoteConfig(w http.ResponseWriter, r *http.Request) {
	var err error
	response := model.MakeOk()

	defer func() {
		kkapp.App.Render.JSON(w, http.StatusOK, response)
	}()

	remoteConfig := new(model.RemoteConfig)

	jsdecoder := json.NewDecoder(r.Body)
	err = jsdecoder.Decode(remoteConfig)

	if err != nil {
		log.Println("[ERROR]", err)
		return
	}

	// decode success
	remoteConfig.Save()

	message := "saved service status"
	dbmeta.ConsoleLog(r, "service", message)
}

// GetConsoleRemoteConfig console api
func GetConsoleRemoteConfig(w http.ResponseWriter, r *http.Request) {
	var err error
	response := model.MakeOk()

	defer func() {
		if err != nil {
			log.Println("[ERROR]", err)
			kkapp.App.Render.JSON(w, http.StatusBadRequest, response)
		} else {
			kkapp.App.Render.JSON(w, http.StatusOK, response)
		}
	}()

	remoteConfig, err := model.NewRemoteConfig()

	if err != nil {
		return
	}
	response.Data = remoteConfig
}
