package kkconsole

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"net/http"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
)

var (
	imageUploadLimit int64 = 1 * 1024 * 1204 // 1Mb
)

// GetConsoleEvent console api
func GetConsoleEvent(w http.ResponseWriter, r *http.Request) {
	response := model.MakeOk()
	event, err := model.NewAllEvent()
	if err != nil {
		log.Println("[ERROR]", err)
	} else {
		response.Data = event
	}

	kkapp.App.Render.JSON(w, http.StatusOK, response)
}

// PutConsoleEvent console api
func PutConsoleEvent(w http.ResponseWriter, r *http.Request) {
	var err error
	response := model.MakeOk()
	event := new(model.Event)

	jsdecoder := json.NewDecoder(r.Body)
	err = jsdecoder.Decode(event)

	if err != nil {
		log.Println("[ERROR]", err)
	}

	event.Save()
	message := "saved event"
	dbmeta.ConsoleLog(r, "meta", message)
	kkapp.App.Render.JSON(w, http.StatusOK, response)
}

// PostConsoleEventImage console api
func PostConsoleEventImage(w http.ResponseWriter, r *http.Request) {
	response := model.MakeOk()

	eventid := r.URL.Query().Get("id")
	renderError := func(message string) {
		response.Status.Type = "Error"
		response.Status.Message = message
		kkapp.App.Render.JSON(w, http.StatusBadRequest, response)
	}

	if eventid == "" {
		// no eventid
		renderError("No EventID")
		return
	}
	// 1 Mb
	r.Body = http.MaxBytesReader(w, r.Body, imageUploadLimit)
	file, handle, err := r.FormFile("file")
	if err != nil {
		renderError(err.Error())
		return
	}
	// should had file now
	defer file.Close()

	fileBytes, err := ioutil.ReadAll(file)

	if err != nil {
		renderError(err.Error())
		return
	}

	// only allow jpeg image
	mimeType := handle.Header.Get("Content-Type")
	// size := handle.Size

	if mimeType != "image/jpeg" {
		response.Status.Type = "Error"
		response.Status.Message = "The format file is not valid."
		kkapp.App.Render.JSON(w, http.StatusBadRequest, response)
		return
	}

	// ok ready to save to s3
	s3Key := fmt.Sprintf("event/%s.jpeg", eventid)
	s3client := s3.New(session.New(), &aws.Config{Region: aws.String("ap-northeast-1")})
	imagePath := fmt.Sprintf("%s/event/%s.jpeg", kkapp.App.S3ImageHost, eventid)

	params := &s3.PutObjectInput{
		Bucket: aws.String(kkapp.App.S3ImageBucket), // required
		Key:    aws.String(s3Key),                   // required
		ACL:    aws.String("public-read"),
		Body:   bytes.NewReader(fileBytes),
		// ContentLength: aws.Long(size),
		ContentType: aws.String(mimeType),
	}
	_, err = s3client.PutObject(params)
	if err != nil {
		renderError(err.Error())
		return
	}

	// write to event Image
	event, _ := model.NewAllEvent()
	for idx := range event.Event {
		if event.Event[idx].ID == eventid {
			// found EventID
			log.Println("Found")
			event.Event[idx].Image = imagePath
			break
		}
	}
	event.Save()
	kkapp.App.Render.JSON(w, http.StatusOK, response)
}

// DelConsoleEventImage console api
func DelConsoleEventImage(w http.ResponseWriter, r *http.Request) {
	var err error
	response := model.MakeOk()
	eventid := r.URL.Query().Get("id")

	renderError := func(message string) {
		response.Status.Type = "Error"
		response.Status.Message = message
		kkapp.App.Render.JSON(w, http.StatusBadRequest, response)
	}

	defer func() {
	}()

	s3Key := fmt.Sprintf("event/%s.jpeg", eventid)
	s3client := s3.New(session.New(), &aws.Config{Region: aws.String("ap-northeast-1")})

	params := &s3.DeleteObjectInput{
		Bucket: aws.String(kkapp.App.S3ImageBucket), // required
		Key:    aws.String(s3Key),                   // required
	}
	_, err = s3client.DeleteObject(params)
	if err != nil {
		renderError(err.Error())
		return
	}

	// write to event Image
	event, _ := model.NewAllEvent()
	for idx := range event.Event {
		if event.Event[idx].ID == eventid {
			// found EventID
			log.Println("Found")
			event.Event[idx].Image = ""
			break
		}
	}
	event.Save()

	kkapp.App.Render.JSON(w, http.StatusOK, response)
}
