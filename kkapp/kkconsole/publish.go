package kkconsole

import (
	"encoding/json"
	"errors"
	"log"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/kksearch"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	"github.com/lib/pq"
)

var (
	_sqlpublish = map[string]string{
		"get": `SELECT id, name, series_id, meta, pub, unpub from meta_episode WHERE pub BETWEEN $1 AND $2 ORDER BY pub;`,
	}
)

// pubEp helper struct to update meta_episode
type pubEp struct {
	ID  string    `db:"id"`
	Pub time.Time `db:"pub"`
}

// GetConsolePublish console api for query meta_series
func GetConsolePublish(w http.ResponseWriter, r *http.Request) {
	var err error
	var _startInt64 int64
	var start, end time.Time
	var episodes []*dbmeta.Episode

	response := model.MakeOk()
	q := r.URL.Query().Get("q")
	data := make(map[string]interface{})

	defer func() {
		kkapp.App.Render.JSON(w, http.StatusOK, response)
	}()

	_startInt64, err = strconv.ParseInt(q, 10, 64)
	log.Println(_startInt64, err)

	if err == nil {
		start = time.Unix(_startInt64, 0)
		end = start.AddDate(0, 0, 14) // loop up 14 days

		db := kkapp.App.DbMeta.Slave()
		err = db.Select(&episodes, _sqlpublish["get"], start, end)

		if err != nil {
			log.Println("[ERROR]", err)
			return
		}

		for idx := range episodes {
			episodes[idx].Parse()
			episodes[idx].Clean()
		}

		data["episodes"] = episodes
	}
	response.Data = data
}

// PutConsolePublish console api for query meta_series
func PutConsolePublish(w http.ResponseWriter, r *http.Request) {
	var err error

	errMsg := []string{"Not found"}
	payload := []string{}
	existedEpisode := []string{}
	updateEpisode := []string{}
	episodeMap := make(map[string]bool)
	titleMap := make(map[string]bool)
	updateESTitle := []string{}

	response := model.MakeOk()
	data := make(map[string]interface{})

	defer func() {
		if err != nil {
			log.Println("[ERROR]", err)
			response.Status.Type = "Error"
			response.Status.Message = err.Error()
			kkapp.App.Render.JSON(w, http.StatusBadRequest, response)
		} else {
			kkapp.App.Render.JSON(w, http.StatusOK, response)
		}
	}()

	jsdecoder := json.NewDecoder(r.Body)
	err = jsdecoder.Decode(&payload)

	if err == nil {
		episodes := []pubEp{}
		for _, line := range payload {
			if strings.Contains(line, ",") {
				var pubInt64 int64
				lineSlice := strings.SplitN(line, ",", 2)
				log.Println("[INFO]", lineSlice)
				pubInt64, err = strconv.ParseInt(lineSlice[1], 10, 64)
				if err != nil {
					return
				}
				episodes = append(episodes, pubEp{ID: lineSlice[0], Pub: time.Unix(pubInt64, 0).UTC()})
				episodeMap[lineSlice[0]] = false
				updateEpisode = append(updateEpisode, lineSlice[0])

				if len(lineSlice[0]) == 14 {
					// ex: 00000338010001
					// titleID: 00000338
					titleMap[lineSlice[0][:8]] = true
				}
			}
		}

		if len(episodes) > 0 {
			db := kkapp.App.DbMeta.Master()
			// check exist episode
			err = db.Select(&existedEpisode, `SELECT id FROM meta_episode WHERE id = ANY($1)`, pq.Array(updateEpisode))
			if err != nil {
				return
			}

			if len(existedEpisode) != len(updateEpisode) {
				// some episode would like to update but not in meta_episode yet
				log.Println("[INFO] some episode not in meta_episode")
				for _, epID := range existedEpisode {
					episodeMap[epID] = true
				}

				for k, v := range episodeMap {
					if !v {
						errMsg = append(errMsg, k)
					}
				}
				err = errors.New(strings.Join(errMsg, " "))
				return
			}

			// all check passed, collect the update titles
			for titleID, _ := range titleMap {
				updateESTitle = append(updateESTitle, titleID)
			}

			// update meta_episode
			_, err = db.NamedExec(`UPDATE meta_episode AS episode SET pub = v.pub FROM 
 (VALUES (:id, :pub ::::timestamptz)) AS v(id, pub) WHERE v.id = episode.id;`, episodes)
			if err != nil {
				return
			}

			// update elasticsearch titles
			err = kksearch.SyncTitles(updateESTitle)

			// auditlog
			dbmeta.ConsoleLog(r, "publish", strings.Join(payload, "\n"))
		}
	}
	response.Data = data
}
