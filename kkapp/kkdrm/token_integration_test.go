package kkdrm

import (
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/model/playback"
	pmiddleware "github.com/KKTV/kktv-api-v3/kktvapi/pkg/wrapper/middleware"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/go-zoo/bone"
	"github.com/justinas/alice"
	"github.com/rs/cors"
)

func TestGetToken_IntegrationTest(t *testing.T) {
	// create test token
	testDeviceID := "mackbookpro"
	var pb = playback.PlaybackRequest{
		TitleID:   "01000468",
		EpisodeID: "01000468010004",
		Medium:    "SVOD",
	}
	playtoken, err := playback.NewPlayback(kkapp.App.PermissionService, "testuser", dbuser.MembershipPremiumOnly, testDeviceID, pb)

	if err != nil {
		t.Fatal("generate token fail, err:", err)
	}

	mux := bone.New()
	common := alice.New(
		cors.New(cors.Options{
			AllowedHeaders:   []string{"Content-Type", "Authorization", "X-Device-ID"},
			AllowedMethods:   []string{"GET", "OPTIONS", "POST", "PUT", "DELETE"},
			MaxAge:           86400,
			AllowCredentials: true}).Handler,
	)
	// middleware for signed in user client
	guestauthed := common.Append(pmiddleware.GuestAuth())

	mux.Get("/v3/tokens/:tokenid", guestauthed.ThenFunc(GetToken))
	{
		r, err := http.NewRequest("GET", fmt.Sprintf("/v3/tokens/%s", playtoken.PlaybackToken), nil)
		r.Header.Set("X-Device-ID", testDeviceID)

		if err != nil {
			t.Fatal(err)
		}

		w := httptest.NewRecorder()

		mux.ServeHTTP(w, r)

		if status := w.Code; status != http.StatusOK {
			t.Errorf("GetToken handler returned wrong status code: got %v want %v",
				status, http.StatusOK)
		}
	}
}
