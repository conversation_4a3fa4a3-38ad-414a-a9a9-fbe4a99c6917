package kksearch

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/datastore/helper"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kkapp/model/kkcontent"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/wrapper/middleware"
	"github.com/KKTV/kktv-api-v3/pkg/httpreq"
	plog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/go-zoo/bone"
)

var (
	searchHistoryKeyFmt = "search-history:v1:%s:ordered_set"
	collectionCacheKey  = "collections:v3" // cache at redis key "cacher:collections:v3:json"
)

// GetBrowseCollections to get all web platform browses
func GetBrowseCollections(w http.ResponseWriter, r *http.Request) {
	response := model.MakeOk()

	cacher, err := helper.NewCacher(collectionCacheKey)
	if err != nil {
		kkapp.App.Render.JSON(w, http.StatusOK, response)
		return
	}
	data := make(map[string]interface{})
	dataBytes, err := cacher.Get()
	if err != nil {
		// no cache yet
		browse, _ := kkcontent.NewBrowse()
		webCollections := browse.Platform("web")
		data["collections"] = webCollections.GetCollections()
		dataBytes, _ = json.Marshal(data)
		_ = cacher.SetExpire(dataBytes, 300) // 5 minute cache
	} else {
		// get cache
		_ = json.Unmarshal(dataBytes, &data)
	}

	response.Data = data
	kkapp.App.Render.JSON(w, http.StatusOK, response)
}

// GetCollections handler for api
func GetCollections(w http.ResponseWriter, r *http.Request) {
	response := model.MakeOk()

	isHideLustContent, _ := r.Context().Value(middleware.KeyHideLustContent).(bool)

	collectionStr := bone.GetValue(r, "collection")

	notFound := func() {
		response.Status.Type = "NotFound"
		response.Status.Subtype = "CollectionNotFound"
		response.Status.Message = ""
		kkapp.App.Render.JSON(w, http.StatusNotFound, response)
	}

	urlValues, err := httpreq.ParseQuery(r)
	if err != nil {
		plog.Error("get collection: fail to parse query").Err(err).Str("query", r.URL.String()).Send()
		notFound()
		return
	}
	// [BEGIN] workaround for old client's page value starts from 1, but the new one starts from 0
	// https://kktv.atlassian.net/browse/KKTV-11043
	// TODO this v3 GetCollections API should be removed in the future
	if page := urlValues.Get("page"); page != "" {
		if pageInt, err := strconv.Atoi(page); err == nil && pageInt > 0 {
			urlValues.Set("page", strconv.Itoa(pageInt-1))
		}
	}
	// [END]

	collection, err := QueryCollection(collectionStr, urlValues, isHideLustContent)
	if err != nil {
		notFound()
		return
	}
	// [BEGIN] workaround for old client's page value starts from 1, but the new one starts from 0
	// https://kktv.atlassian.net/browse/KKTV-11043
	collection.Page += 1
	// [END]
	response.Data = collection
	kkapp.App.Render.JSON(w, http.StatusOK, response)
}

func QueryCollection(queryTarget string, urlQuery url.Values, isHideLustContent bool) (*Collection, error) {
	var collection *Collection
	var err error
	if strings.Contains(queryTarget, ":") {
		collectionSlice := strings.Split(queryTarget, ":")
		collection, err = NewCollection(collectionSlice[0], collectionSlice[1])
	} else {
		collection, err = NewCollectionViaSHA256(queryTarget)
	}

	if err != nil {
		plog.Warn("get collection: fail to new collection").Err(err).
			Str("query_target", queryTarget).
			Interface("url_query", urlQuery).Send()
		return nil, err
	}

	collection.ParseQueryString(urlQuery)
	if isHideLustContent {
		collection.BlockQueryByPlatform()
	}
	// aggregate genre, country, theme items by collections
	collection.Aggregate()

	err = collection.Query()
	if err != nil {
		plog.Warn("get collection: fail to query").Err(err).
			Str("query_target", queryTarget).
			Interface("url_query", urlQuery).Send()
		collection.Items = []*model.TitleDetail{}
	}
	return collection, nil
}

type searchFigureItem struct {
	ID   string `json:"id,omitempty"`
	Name string `json:"name,omitempty"`
}

// GetSearch search result from elastic search
func GetSearch(w http.ResponseWriter, r *http.Request) {
	response := model.MakeOk()
	defer func() {
		kkapp.App.Render.JSON(w, http.StatusOK, response)
	}()
	user := r.Context().Value("user")

	var userid string
	if user != nil {
		if userModel, ok := user.(model.JwtUser); ok && !userModel.IsGuest() {
			userid = userModel.Sub
		}
	}

	query := bone.GetValue(r, "query")
	if query == "" {
		return
	}

	// fire request to elasticsearch
	var tempArtists []searchFigureItem
	var tempTitles []*model.TitleDetail
	var wg sync.WaitGroup

	hideLustContent, _ := r.Context().Value("hide_lust_content").(bool)

	wg.Add(2)
	go func() {
		artists, err := SearchForFigure(query, hideLustContent)
		if err != nil {
			plog.Error("GetSearch: search for figure").Err(err).Str("query", query).Send()
			artists = make([]searchFigureItem, 0)
		}
		tempArtists = artists
		wg.Done()
	}()

	go func() {
		titles, err := SearchForTitle(query, hideLustContent)
		if err != nil {
			plog.Error("GetSearch: search for title").Err(err).Str("query", query).Send()
			titles = make([]*model.TitleDetail, 0)
		}
		tempTitles = titles
		wg.Done()
	}()
	wg.Wait()

	save := r.URL.Query().Get("save") != "false" // default to save query
	if userid != "" && save {
		SaveSearchHistory(userid, query)
	}
	result := map[string]interface{}{
		"artists": tempArtists,
		"titles":  tempTitles,
	}
	response.Data = result
}

func SearchForTitle(query string, hideLustContent bool) ([]*model.TitleDetail, error) {
	var filterTitles model.CollectionTitles
	titleIDs, err := searchTitle(query, hideLustContent)
	if err != nil {
		return nil, err
	} else {
		filterTitles, err = model.NewTitleDetails(titleIDs)
		titles := filterTitles.FilterNotExpired()
		return titles, nil
	}
}

func SearchForFigure(query string, hideLustContent bool) ([]searchFigureItem, error) {
	figureNames, err := searchFigure(query, hideLustContent)
	if err != nil {
		return nil, err
	} else {
		figures := make([]searchFigureItem, 0)
		for _, row := range figureNames {
			col, _ := NewCollectAddition("figure", row)
			figures = append(figures, searchFigureItem{ID: col.GetID(), Name: row})
		}
		return figures, err
	}
}

// GetSearchSuggestions search result from elastic search
func GetSearchSuggestions(w http.ResponseWriter, r *http.Request) {

	var err error
	var indexQuery *IndexQuery

	indexQuery = new(IndexQuery)
	suggestions := []string{}
	response := model.MakeOk()
	query := bone.GetValue(r, "query")

	defer func() {
		kkapp.App.Render.JSON(w, http.StatusOK, response)
	}()

	// fire request to elasticsearch
	result := make(map[string]interface{})

	if query == "" {
		result["suggestions"] = []string{}
		response.Data = result
		return
	}

	suggestions, err = indexQuery.Suggest(query)
	if err != nil {
		log.Println("[ERROR]", err)
	}

	result["suggestions"] = suggestions

	response.Data = result
}

// GetSearchHistory search history result for user
func GetSearchHistory(w http.ResponseWriter, r *http.Request) {
	result := make(map[string]interface{})
	response := model.MakeOk()
	user := r.Context().Value("user").(model.JwtUser)
	defer func() {
		kkapp.App.Render.JSON(w, http.StatusOK, response)
	}()

	if user.IsGuest() {
		// we do not care about guest user
		response.Data = result
		return
	}

	// get
	searchHistoryKey := fmt.Sprintf(searchHistoryKeyFmt, user.Sub)
	conn := kkapp.App.RedisUser.Slave()
	// history, err := conn.Cmd("ZREVRANGEBYSCORE", searchHistoryKey, "+inf", "-inf").List()
	history, err := conn.Cmd("ZREVRANGE", searchHistoryKey, "0", "-1").List()

	if err != nil {
		log.Println("[ERROR] get serch history error", err)
	}

	historyLen := len(history)

	if historyLen >= 10 {
		result["histories"] = history[0:10]
	} else {
		result["histories"] = history
	}

	// clean up, if this search history getting to long
	if historyLen > 18 {
		go func(clearKey string) {
			conn := kkapp.App.RedisUser.Master()
			resp := conn.Cmd("ZREMRANGEBYRANK", clearKey, "0", "-11")
			if resp.Err != nil {
				log.Println("[ERROR] clear serch history error", clearKey, resp.Err)
			}
		}(searchHistoryKey)
	}

	response.Data = result
}

// DelSearchHistory delsearch history result for user
func DelSearchHistory(w http.ResponseWriter, r *http.Request) {
	result := make(map[string]interface{})
	response := model.MakeOk()
	user := r.Context().Value("user").(model.JwtUser)
	defer func() {
		kkapp.App.Render.JSON(w, http.StatusOK, response)
	}()

	// default empty
	if user.IsGuest() {
		// we do not care about guest user
		return
	}
	// del
	searchHistoryKey := fmt.Sprintf(searchHistoryKeyFmt, user.Sub)
	conn := kkapp.App.RedisUser.Master()
	resp := conn.Cmd("DEL", searchHistoryKey)
	if resp.Err != nil {
		log.Println("[ERROR] delete serch history error", resp.Err)
	}

	response.Data = result
}

func SaveSearchHistory(userID string, q string) {
	searchKey := fmt.Sprintf(searchHistoryKeyFmt, userID)
	if userID == "" || q == "" {
		// no user or query do nothing
		return
	}
	plog.Info("Search: save search history").Str("user_id", userID).Str("query", q).Send()
	conn := kkapp.App.RedisUser.Master()
	// cast to js new Date().getTime()
	// the rankScore in UTC ms
	// rankScore := time.Now().Truncate(time.Millisecond).UnixNano() / int64(time.Millisecond)
	rankScore := time.Now().Unix() * 1000
	r := conn.Cmd("ZADD", searchKey, rankScore, q)
	if r.Err != nil {
		log.Println("[ERROR] user redis save", r.Err)
	}
}
