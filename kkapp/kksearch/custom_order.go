package kksearch

import "sort"

// content team ask for custom order on collection
// please refer
// https://kkvideo.atlassian.net/browse/KKTV-6885
// https://docs.google.com/spreadsheets/d/11cFzSfVdsLrXWKsIq25LZohMTvyk60Y_FiGXzzhpwkY/edit?ts=5db7fa90#gid=1374764195

var (
	orderSlice = []string{
		// country
		"Korea",
		"Japan",
		"JapanAnime",
		"Taiwan",
		"Hongkong",
		"China",
		"Thailand",
		"Singapore",
		"Western",
		"Other",

		// genre
		"戲劇",
		"娛樂",
		"動漫",
		"親子",
		"電影",

		// share
		"浪漫愛情",
		"懸疑推理",
		"職場社會",
		"奇幻冒險",
		"靈異怪誕",
		"恐怖驚悚",
		"青春校園",

		// 戲劇
		"輕鬆喜劇",
		"家庭鄉土",
		"法律政治",
		"時代史劇",
		"經典懷舊",
		"雙字幕",

		// 娛樂
		"旅遊美食",
		"知識生活",
		"實境遊戲",
		"音樂偶像",
		"人物訪談",
		"紀錄片",

		// 動漫
		"校園戀愛",
		"溫馨喜劇",
		"運動競技",
		"歷史軍武",
		"闔家歡",
		"原創動畫",
		"特攝",
		"BL",

		// 親子
		"寓教於樂",

		// 電影
		"劇情",
		"動作武打",
		"家庭親情",
		"歷史鄉土",
		"動畫電影",
		"懷念經典",

		// 999
		"成人",
	}

	orderMapSlice = map[string][]string{
		"genre:戲劇": {
			// country
			"Japan",
			"Korea",
			"Taiwan",
			"Hongkong",
			"China",
			"Thailand",
			"Singapore",
			"Western",
			"Other",

			"浪漫愛情",
			"懸疑推理",
			"職場社會",
			"輕鬆喜劇",
			"靈異怪誕",
			"恐怖驚悚",
			"奇幻冒險",
			"家庭鄉土",
			"青春校園",
			"時代史劇",
			"法律政治",
			"經典懷舊",
			"雙字幕",
			"成人",
		},

		"genre:動畫": {
			// country
			"Japan",
			"Korea",
			"Taiwan",
			"Hongkong",
			"China",
			"Thailand",
			"Singapore",
			"Western",
			"Other",

			"奇幻冒險",
			"戀愛",
			"青春校園",
			"輕鬆喜劇",
			"運動競技",
			"懸疑推理",
			"靈異怪誕",
			"恐怖驚悚",
			"音樂偶像",
			"歷史軍武",
			"職場社會",
			"BL",
			"動畫電影",
			"雙語",
			"闔家歡",
			"無修正動畫",
			"原創動畫",
			"特攝",
			"成人",
			"LGBTQ",
		},

		"genre:娛樂": {
			// country
			"Korea",
			"Japan",
			"Taiwan",
			"Hongkong",
			"China",
			"Thailand",
			"Singapore",
			"Western",
			"Other",

			"旅遊美食",
			"知識生活",
			"實境遊戲",
			"音樂偶像",
			"人物訪談",
		},

		"genre:電影": {
			// country
			"Japan",
			"Korea",
			"Taiwan",
			"Western",
			"Hongkong",
			"China",
			"Thailand",
			"Singapore",
			"Other",

			"劇情",
			"浪漫愛情",
			"輕鬆喜劇",
			"動作武打",
			"奇幻冒險",
			"靈異怪誕",
			"恐怖驚悚",
			"懸疑推理",
			"職場社會",
			"家庭親情",
			"青春校園",
			"歷史鄉土",
			"動畫電影",
			"紀錄片",
			"懷念經典",
			"成人",
			"闔家歡",
			"LGBTQ",
		},

		"genre:親子": {
			// country
			"Taiwan",
			"Western",
			"Japan",
			"Korea",
			"Hongkong",
			"China",
			"Thailand",
			"Singapore",
			"Other",
			"闔家歡",
			"寓教於樂",
			"動畫電影",
			"雙語",
			"尼克兒童頻道",
		},

		"content_agent:采昌": {
			// genre

			"戲劇",
			"電影",
			"動畫",

			// country
			"Japan",
			"Korea",
			"Taiwan",
			"Western",
			"Hongkong",
			"China",
			"Thailand",
			"Singapore",
			"Other",

			"劇情",
			"浪漫愛情",
			"輕鬆喜劇",
			"動作武打",
			"奇幻冒險",
			"靈異怪誕",
			"恐怖驚悚",
			"懸疑推理",
			"職場社會",
			"家庭親情",
			"青春校園",
			"歷史鄉土",
			"動畫電影",
			"紀錄片",
			"懷念經典",
			"成人",
			"闔家歡",
			"LGBTQ",
		},

		"content_agent:TVB": {
			// country
			"Japan",
			"Korea",
			"Taiwan",
			"Western",
			"Hongkong",
			"China",
			"Thailand",
			"Singapore",
			"Other",
			"浪漫愛情",
			"懸疑推理",
			"職場社會",
			"輕鬆喜劇",
			"靈異怪誕",
			"恐怖驚悚",
			"奇幻冒險",
			"家庭鄉土",
			"青春校園",
			"時代史劇",
			"法律政治",
			"經典懷舊",
		},

		"content_agent:Medialink": {
			// country
			"Japan",
			"Korea",
			"Taiwan",
			"Western",
			"Hongkong",
			"China",
			"Thailand",
			"Singapore",
			"Other",
			"奇幻冒險",
			"校園戀愛",
			"溫馨喜劇",
			"運動競技",
			"懸疑推理",
			"靈異怪誕",
			"恐怖驚悚",
			"音樂偶像",
			"歷史軍武",
			"職場社會",
			"BL",
			"動畫電影",
			"闔家歡",
			"原創動畫",
			"特攝",
			"成人",
		},
		"content_agent:華策": {
			// country
			"Japan",
			"Korea",
			"Taiwan",
			"Western",
			"Hongkong",
			"China",
			"Thailand",
			"Singapore",
			"Other",
			"浪漫愛情",
			"懸疑推理",
			"職場社會",
			"輕鬆喜劇",
			"靈異怪誕",
			"恐怖驚悚",
			"奇幻冒險",
			"家庭鄉土",
			"青春校園",
			"時代史劇",
			"法律政治",
			"經典懷舊",
		},
		"content_agent:新傳媒": {
			// country
			"Japan",
			"Korea",
			"Taiwan",
			"Western",
			"Hongkong",
			"China",
			"Thailand",
			"Singapore",
			"Other",
			"浪漫愛情",
			"懸疑推理",
			"職場社會",
			"輕鬆喜劇",
			"靈異怪誕",
			"恐怖驚悚",
			"奇幻冒險",
			"家庭鄉土",
			"青春校園",
			"時代史劇",
			"法律政治",
			"經典懷舊",
		},
		"content_provider:ViuTV": {
			"Japan",
			"Korea",
			"Taiwan",
			"Western",
			"Hongkong",
			"China",
			"Thailand",
			"Singapore",
			"Other",
			"浪漫愛情",
			"懸疑推理",
			"職場社會",
			"輕鬆喜劇",
			"靈異怪誕",
			"恐怖驚悚",
			"奇幻冒險",
			"家庭鄉土",
			"青春校園",
			"時代史劇",
			"法律政治",
			"經典懷舊",
		},
	}

	// for sorting
	rankingMap = slice2Map(orderSlice)

	// custom rankingMap
	rankingOrderMap = map[string]map[string]int{
		"genre:戲劇":                 slice2Map(orderMapSlice["genre:戲劇"]),
		"genre:動畫":                 slice2Map(orderMapSlice["genre:動畫"]),
		"genre:娛樂":                 slice2Map(orderMapSlice["genre:娛樂"]),
		"genre:電影":                 slice2Map(orderMapSlice["genre:電影"]),
		"genre:親子":                 slice2Map(orderMapSlice["genre:親子"]),
		"content_agent:新傳媒":        slice2Map(orderMapSlice["content_agent:新傳媒"]),
		"content_agent:采昌":         slice2Map(orderMapSlice["content_agent:采昌"]),
		"content_agent:TVB":        slice2Map(orderMapSlice["content_agent:TVB"]),
		"content_agent:Medialink":  slice2Map(orderMapSlice["content_agent:Medialink"]),
		"content_agent:華策":         slice2Map(orderMapSlice["content_agent:華策"]),
		"content_provider:ViuTV": slice2Map(orderMapSlice["content_provider:ViuTV"]),
	}

	defaultRanking = 1000
)

// CustomOrderFor category
// ex: genre:電影, genre:親子
// if not found any ranking map, use customStringSlice sort
func CustomOrderFor(category string, names []string) []string {
	tempMap := make(map[string]int)

	// assign orderMap
	if v, ok := rankingOrderMap[category]; ok {
		tempMap = v
	} else {
		tempMap = rankingMap
	}

	sort.Slice(names, func(i, j int) bool {
		var rankL, rankR int
		var ok bool
		if rankL, ok = tempMap[names[i]]; !ok {
			rankL = defaultRanking
		}

		if rankR, ok = tempMap[names[j]]; !ok {
			rankR = defaultRanking
		}
		return rankL < rankR
	})

	return names
}

// helper func turn []string{}map into map[value] => index+1
func slice2Map(s []string) (nm map[string]int) {
	nm = make(map[string]int)
	for idx, v := range s {
		nm[v] = idx + 1
	}
	return
}
