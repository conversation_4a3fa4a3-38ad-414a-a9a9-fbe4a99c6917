package dbuser

import (
	"encoding/json"
	"time"

	"gopkg.in/guregu/null.v3"
)

// ProductPackages console product struct
type ProductPackages struct {
	ID                int             `db:"id" json:"id"`
	Platform          string          `db:"platform" json:"platform"`
	Price             string          `db:"price" json:"price"`
	Duration          string          `db:"duration" json:"duration"`
	Title             string          `db:"title" json:"title"`
	Description       string          `db:"description" json:"description"`
	ButtonText        string          `db:"button_text" json:"button_text"`
	Label             null.String     `db:"label" json:"label"`
	CreatedAt         time.Time       `db:"created_at" json:"created_at"`
	UpdatedAt         time.Time       `db:"updated_at" json:"updated_at"`
	ProductIds        json.RawMessage `db:"product_ids" json:"product_ids"`
	BillingProductIds json.RawMessage `db:"billing_product_ids" json:"billing_product_ids"`
	Active            bool            `db:"active" json:"active"`
	Highlight         string          `db:"highlight" json:"highlight"`
	AutoRenew         bool            `db:"auto_renew" json:"auto_renew"`
	Sort              int64           `db:"sort" json:"sort"`
	Promotion         null.String     `db:"promotion" json:"promotion"`
	Info              null.String     `db:"info" json:"info"`
	Category          null.String     `db:"category" json:"category"`
	PayDuration       null.String     `db:"pay_duration" json:"pay_duration"`
}

type ConsoleProductPackage struct {
	ID                int                         `db:"id" json:"id"`
	Platform          string                      `db:"platform" json:"platform"`
	PayDuration       null.String                 `db:"pay_duration" json:"pay_duration"`
	ProductIds        []int                       `db:"product_ids" json:"product_ids"`
	BillingProductIds []string                    `json:"billing_product_ids"`
	Active            bool                        `db:"active" json:"active"`
	AutoRenew         bool                        `db:"auto_renew" json:"auto_renew"`
	Sort              int64                       `db:"sort" json:"sort"`
	Category          []string                    `db:"category" json:"category"`
	Items             []ConsoleProductPackageItem `json:"item"`
}

type ConsoleProductPackageItem struct {
	Price       int         `db:"price" json:"price"`
	ButtonText  string      `db:"button_text" json:"button_text"`
	Description string      `db:"description" json:"description"`
	DisplayTo   []string    `json:"displayTo"`
	Duration    string      `db:"duration" json:"duration"`
	Highlight   string      `db:"highlight" json:"highlight"`
	Label       null.String `db:"label" json:"label"`
	OriginPrice string      `db:"" json:"originPriceDesc"`
	Title       string      `db:"title" json:"title"`
}
