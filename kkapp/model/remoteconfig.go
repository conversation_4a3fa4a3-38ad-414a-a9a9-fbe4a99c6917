package model

import (
	"encoding/json"
	"log"

	"github.com/KKTV/kktv-api-v3/kkapp"
)

var (
	keyRemoteConfig = "remoteconfig:v1:hash"
)

// RemoteConfig struct for api
type RemoteConfig struct {
	Android   map[string]interface{} `json:"android"`
	AndroidTV map[string]interface{} `json:"androidtv"`
	Ios       map[string]interface{} `json:"ios"`
	AppleTV   map[string]interface{} `json:"appletv"`
	Web       map[string]interface{} `json:"web"`
}

// Save save service status to redis
func (i *RemoteConfig) Save() (err error) {
	androidjson, err := json.Marshal(i.Android)
	androidtvjson, err := json.Marshal(i.AndroidTV)
	iosjson, err := json.Marshal(i.Ios)
	appletvjson, err := json.Marshal(i.AppleTV)
	webjson, err := json.Marshal(i.Web)
	if err != nil {
		log.Println("[ERROR] save service status", err)
	} else {
		pool := kkapp.App.RedisMeta.Master()
		pool.Cmd("HMSET", keyRemoteConfig, "android", androidjson, "ios", iosjson,
			"web", webjson, "androidtv", androidtvjson, "appletv", appletvjson)
	}
	return
}

// NewRemoteConfig for client api v3
func NewRemoteConfig() (realitem *RemoteConfig, err error) {
	realitem = new(RemoteConfig)

	// default empty map
	realitem.Android = make(map[string]interface{})
	realitem.AndroidTV = make(map[string]interface{})
	realitem.Ios = make(map[string]interface{})
	realitem.AppleTV = make(map[string]interface{})
	realitem.Web = make(map[string]interface{})

	// read only
	pool := kkapp.App.RedisMeta.Slave()
	resp, err := pool.Cmd("HGETALL", keyRemoteConfig).Map()

	if err != nil {
		return nil, err
	}

	for k, v := range resp {
		var raw map[string]interface{}
		err = json.Unmarshal([]byte(v), &raw)
		if err != nil {
			return nil, err
		}

		switch k {
		case "android":
			realitem.Android = raw
		case "androidtv":
			realitem.AndroidTV = raw
		case "ios":
			realitem.Ios = raw
		case "appletv":
			realitem.AppleTV = raw
		case "web":
			realitem.Web = raw
		}
	}

	return realitem, err
}
