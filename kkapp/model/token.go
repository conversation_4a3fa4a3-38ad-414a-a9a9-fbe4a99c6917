package model

import (
	"crypto/sha256"
	"database/sql"
	"errors"
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/feature"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/wrapper"
	"github.com/KKTV/kktv-api-v3/pkg/auth"
	plog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	modelmw "github.com/KKTV/kktv-api-v3/pkg/model/middleware"
	"github.com/jmoiron/sqlx"
	"github.com/rs/xid"
	uuid "github.com/satori/go.uuid"

	"gopkg.in/dgrijalva/jwt-go.v3"
	"gopkg.in/guregu/null.v3"
)

const (
	defaultTokenExpireDay        = 30
	defaultRefreshTokenExpireDay = 90
)

var (
	sqltoken = map[string]string{
		"insert": `INSERT INTO tokens (id, user_id, token, token_hash, source_ip, user_agent, expired_at)
 VALUES ($1, $2, $3, $4, $5, $6, $7)`,

		"insert_refresh_token": `INSERT INTO refresh_tokens (token_id, refresh_token, expired_at)
 VALUES ($1, $2, $3) RETURNING id`,

		"get_by_hash": `SELECT t.token,
 date_part('epoch', t.expired_at)::int as expired_at,
 r.refresh_token, date_part('epoch', r.expired_at)::int as refresh_token_expires_date
 FROM tokens t, refresh_tokens r WHERE
 t.user_id = $1 AND t.token_hash = $2 AND t.expired_at > NOW() AND t.id = r.token_id AND r.expired_at > t.expired_at`,

		"expire_token": `UPDATE tokens SET expired_at = NOW(), updated_at = NOW() WHERE token = $1 AND expired_at > NOW() RETURNING id;`,

		"expire_refresh_token_by_token_id": `UPDATE refresh_tokens SET expired_at = NOW() WHERE token_id = $1 AND expired_at > NOW();`,

		"expire_refresh_token_1day": `UPDATE refresh_tokens SET expired_at = NOW() + interval '1 day' WHERE refresh_token = $1 AND expired_at > NOW();`,

		"get_refresh_token": `SELECT t.id, t.user_id, t.token,
 date_part('epoch', t.expired_at)::int as expired_at,
 r.refresh_token, date_part('epoch', r.expired_at)::int as refresh_token_expires_date
 FROM tokens t, refresh_tokens r WHERE t.id = r.token_id AND r.expired_at > NOW() AND r.refresh_token = $1`,

		"verify_token": `SELECT t.id, t.user_id, t.token,
 date_part('epoch', t.expired_at)::int as expired_at, u.role, u.type, u.revoked_at
 FROM tokens t, users u WHERE t.token = $1 AND t.expired_at > NOW() AND u.id = t.user_id AND u.revoked_at IS NULL`,
	}
)

// Token from db
type Token struct {
	Id        string    `db:"id" json:"id"`
	UserId    string    `db:"user_id" json:"user_id"`
	Token     string    `db:"token" json:"token"`
	TokenHash string    `db:"token_hash" json:"token_hash"`
	SourceIp  string    `db:"source_ip" json:"source_ip"`
	UserAgent string    `db:"user_agent" json:"user_agent"`
	ExpiredAt int64     `db:"expired_at" json:"expired_at"`
	CreatedAt int64     `db:"created_at" json:"created_at"`
	UpdatedAt null.Time `db:"updated_at" json:"updated_at"`
}

// RefreshToken from db
type RefreshToken struct {
	Id           int64  `db:"id" json:"id"`
	TokenId      string `db:"token_id" json:"token_id"`
	RefreshToken string `db:"refresh_token" json:"refresh_token"`
	CreatedAt    int64  `db:"created_at" json:"created_at"`
	ExpiredAt    int64  `db:"expired_at" json:"expired_at"`
}

// ProviderToken from db
type ProviderToken struct {
	Provider     string `json:"provider"`
	AppID        string `json:"app_id"`
	Token        string `json:"token"`
	RefreshToken string `json:"refresh_token"`
	ExpiredAt    int64  `json:"expired_at"`
}

// AuthInfo for auth
type AuthInfo struct {
	Id                      string      `db:"id" json:"-"`
	UserId                  string      `db:"user_id" json:"-"`
	Token                   string      `db:"token" json:"token"`
	ExpiredAt               int64       `db:"expired_at" json:"expiredAt"`
	RefreshToken            string      `db:"refresh_token" json:"refreshToken,omitempty"`
	RefreshTokenExpiresDate int64       `db:"refresh_token_expires_date" json:"refreshTokenExpiresDate,omitempty"`
	Provider                null.String `db:"provider" json:"-"`
}

// VerifyToken for kkhandler/verify_token.go
type VerifyToken struct {
	Id        string    `db:"id" json:"-"`
	UserId    string    `db:"user_id" json:"-"`
	Token     string    `db:"token" json:"token"`
	ExpiredAt int64     `db:"expired_at" json:"expiredAt"`
	Role      string    `db:"role"`
	Type      string    `db:"type"`
	RevokedAt null.Time `db:"revoked_at" json:"revoked_at"`
}

type accessTokenClaimsInfo struct {
	UserID     string
	Msno       string
	Role       dbuser.Role
	Type       dbuser.Type
	Membership dbuser.Membership
	Iat        time.Time
	Exp        time.Time
}

func allocateGuestId() string {
	return fmt.Sprintf("guest:KKTV-clients:%s", uuid.Must(uuid.NewV4()).String())
}

func GenTokenHash(SrcToken, SourceIp, UserAgent string) (token_hash string) {
	log.Println("[INFO] GenTokenHash", SrcToken, SourceIp, UserAgent)
	return fmt.Sprintf("%x",
		sha256.Sum256([]byte(
			strings.Join([]string{SrcToken, SourceIp, UserAgent}, ""))))
}

func NewGuestAuthInfo() (authinfo *AuthInfo, err error) {
	var tokenString string
	authinfo = new(AuthInfo)
	now := time.Now().Truncate(time.Second)
	expiredAt := now.Add(time.Hour * time.Duration(24)).Unix()
	userId := allocateGuestId()

	claimsInfo := &accessTokenClaimsInfo{
		UserID:     userId,
		Role:       dbuser.RoleGuest,
		Type:       dbuser.TypeGeneral,
		Iat:        now,
		Exp:        time.Unix(expiredAt, 0),
		Membership: dbuser.NonMember,
	}
	if tokenString, err = genAccessToken(claimsInfo); err != nil {
		plog.Warn("newGuestAuthInfo: failed to genAccessToken").
			Str("user_id", userId).
			Interface("claims_info", claimsInfo).
			Err(err).
			Send()
	}

	authinfo.Token = tokenString
	authinfo.ExpiredAt = expiredAt
	return
}

func NewAuthInfo(userId string, param *AuthParam) (authinfo *AuthInfo, err error) {
	var tokenString string
	var token Token
	var provider ProviderToken
	authinfo = new(AuthInfo)

	now := time.Now().Truncate(time.Second)
	exp := now.Add(time.Hour * time.Duration(defaultTokenExpireDay*24))
	randomStr := xid.New().String()
	timeStr := fmt.Sprintf("%d", now.UnixNano())
	token_id := sha256.Sum256([]byte(strings.Join([]string{"token", param.SrcToken, param.SourceIp, param.UserAgent, timeStr, randomStr}, "")))

	db := kkapp.App.DbUser.Master().Unsafe()
	userSrv := wrapper.NewUserService(db)
	u, err := userSrv.GetActiveByID(userId)
	if err != nil {
		return nil, err
	} else if u == nil {
		return nil, errors.New("user not found")
	}

	featureService := feature.NewService()
	isMembershipEnabled, err := featureService.HasFlagForUser(feature.FlagEnablingMembership, userId)
	if err != nil {
		plog.Warn("NewAuthInfo: failed to check feature FlagEnablingMembership").Err(err).Send()
	}

	claimsInfo := &accessTokenClaimsInfo{
		UserID:     userId,
		Msno:       param.Sub,
		Role:       dbuser.Role(param.Role),
		Type:       dbuser.Type(param.Type),
		Membership: u.GetMembership(isMembershipEnabled),
		Iat:        now,
		Exp:        exp,
	}
	if tokenString, err = genAccessToken(claimsInfo); err != nil {
		plog.Warn("newAuthInfo: failed to genAccessToken").
			Str("user_id", userId).
			Interface("claims_info", claimsInfo).
			Err(err).
			Send()
		return
	}

	token_expiredAt := now.Add(time.Hour * time.Duration(defaultTokenExpireDay*24))

	token.Id = fmt.Sprintf("%x", token_id)
	token.UserId = userId
	token.Token = tokenString
	token.TokenHash = GenTokenHash(param.SrcToken, param.SourceIp, param.UserAgent)
	token.ExpiredAt = token_expiredAt.Unix()

	_, err = db.Exec(sqltoken["insert"], token.Id, token.UserId, token.Token, token.TokenHash, param.SourceIp, param.UserAgent, token_expiredAt)
	if err != nil {
		log.Println("[ERROR] newtoken", err)
		return
	}

	refreshtoken_sha256 := sha256.Sum256([]byte(strings.Join([]string{token.Id, timeStr}, "")))
	refresh_token := fmt.Sprintf("%x", refreshtoken_sha256)
	refreshtoken_expiredAt := now.Add(time.Hour * time.Duration(defaultRefreshTokenExpireDay*24))

	provider.Provider = param.Provider
	provider.Token = param.SrcToken
	provider.RefreshToken = param.RefreshToken
	provider.ExpiredAt = param.ExpiredAt
	provider.AppID = param.AppID

	// providerBytes, err := json.Marshal(provider)
	// log.Println(string(providerBytes), err)

	_, err = db.Exec(sqltoken["insert_refresh_token"], token.Id, refresh_token, refreshtoken_expiredAt)

	if err != nil {
		log.Println("[ERROR] newtoken", err)
		return
	}

	authinfo.Token = tokenString
	authinfo.ExpiredAt = token_expiredAt.Unix()
	authinfo.RefreshToken = refresh_token
	authinfo.RefreshTokenExpiresDate = refreshtoken_expiredAt.Unix()

	return
}

func NewAuthInfoFromTokenHash(userId, token_hash string) (authinfo *AuthInfo, err error) {
	db := kkapp.App.DbUser.Slave()
	authinfo = new(AuthInfo)
	log.Println(userId, token_hash)
	err = db.Get(authinfo, sqltoken["get_by_hash"], userId, token_hash)
	log.Println(err)
	// spew.Dump(authinfo)
	return
}

func NewAuthInfoRefreshToken(param *AuthParam) (*AuthInfo, error) {
	var userinfo UserInfo
	refreshInfo := new(AuthInfo) // to verify exist refresh token
	slaveDb := kkapp.App.DbUser.Slave()

	err := slaveDb.Get(refreshInfo, sqltoken["get_refresh_token"], param.RefreshToken)
	if err != nil {
		return nil, err
	}

	// get the latest userinfo via simpleme NO table join
	err = slaveDb.Get(&userinfo, sqluser["simpleme"], refreshInfo.UserId)
	if err != nil {
		return nil, err
	}

	if userinfo.RevokedAt.Valid {
		return nil, errors.New("user has been revoked")
	}

	db := kkapp.App.DbUser.Master()
	param.Role = userinfo.Role
	param.Type = userinfo.Type
	param.Sub = userinfo.KkboxSub.String
	// IsPrime should always be false since 2024/5/3, all prime users are expired. https://kktv.atlassian.net/browse/KKTV-13167
	param.IsPrime = false
	authInfo, err := NewAuthInfo(userinfo.Id, param)

	// signin edm event
	if userinfo.Email.Valid {
		// go func() {
		// 	err := kkapp.App.Ematic.Signin(userinfo.Email.String)
		// 	if err != nil {
		// 		plog.Warn("NewAuthInfoRefreshToken: failed to call Ematic SignIn").Err(err).Str("email", userinfo.Email.String).Send()
		// 	}
		// }()
	}

	// clean up, set expire the old token and refresh_token
	go func(refreshtoken *AuthInfo, db *sqlx.DB) {
		_, err := db.Exec(sqltoken["expire_refresh_token_1day"], refreshtoken.RefreshToken)
		if err != nil {
			log.Println("[ERROR] set expire refresh token", err)
		}
	}(refreshInfo, db)

	return authInfo, err
}

// NewVerifyToken for verify token kkhandler/kkauth/verify_token.go
func NewVerifyToken(token string) (verifytoken *VerifyToken, err error) {
	verifytoken = new(VerifyToken)
	slaveDb := kkapp.App.DbUser.Slave()

	err = slaveDb.Get(verifytoken, sqltoken["verify_token"], token)
	if err != nil {
		// no validate refresh token
		log.Println("[ERROR]", err)
		return nil, err
	}

	return verifytoken, nil
}

func TokenSetToExpire(token string) (err error) {
	if token != "" {
		db := kkapp.App.DbUser.Master()

		var tokenID string
		if err = db.QueryRow(sqltoken["expire_token"], token).Scan(&tokenID); err != nil {
			if !errors.Is(err, sql.ErrNoRows) {
				return
			}
		}

		if tokenID == "" {
			return nil
		}

		if _, err = db.Exec(sqltoken["expire_refresh_token_by_token_id"], tokenID); err != nil {
			return
		}

	}
	return
}

func hasUserBoughtPrime(kkboxMsno string) (bool, error) {
	if kkboxMsno == "" {
		return false, nil
	}

	dbUser := kkapp.App.DbUser.Slave().Unsafe()
	query := `SELECT EXISTS(SELECT msno_sub FROM kkbox_billing_prime_member WHERE msno_sub = $1)`

	var hasBoughtPrime bool
	if err := dbUser.Get(&hasBoughtPrime, query, kkboxMsno); err != nil {
		return false, fmt.Errorf("hasUserBoughtPrime: failed to dbUser.Get: %w", err)
	}

	return hasBoughtPrime, nil
}

func genAccessToken(info *accessTokenClaimsInfo) (string, error) {
	hasBoughtPrime, err := hasUserBoughtPrime(info.Msno)
	if err != nil {
		return "", fmt.Errorf("genAccessToken: failed to hasUserBoughtPrime: %w", err)
	}

	jwtAuth := auth.NewJWTAuth(jwt.SigningMethodHS256, []byte(config.JWTAuthSignedString))
	claims := modelmw.AccessTokenClaims{
		StandardClaims: jwt.StandardClaims{
			Id:        xid.New().String(),
			IssuedAt:  info.Iat.Unix(),
			ExpiresAt: info.Exp.Unix(),
			Subject:   info.UserID,
			Audience:  "kktv.com",
			Issuer:    "KKTV",
		},
		Memberships:    info.Membership,
		Role:           info.Role,
		Type:           info.Type,
		HasBoughtPrime: hasBoughtPrime,
	}

	tokenString, err := jwtAuth.GenerateToken(claims)
	if err != nil {
		return "", fmt.Errorf("genAccessToken: failed to jwtAuth.GenerateToken: %w", err)
	}

	return tokenString, nil
}
