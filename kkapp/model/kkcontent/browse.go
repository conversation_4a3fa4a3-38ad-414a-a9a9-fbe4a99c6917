package kkcontent

import (
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"net/url"
	"strings"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
)

var (
	sqlbrowse = map[string]string{
		"all": `SELECT * from meta_browse ORDER BY ui_type,meta_browse.order;`,
	}

	browseRedisKey = "browse:v3:json"
)

// RedisCollection meta_browse to redis struct
type RedisCollection struct {
	Title          string `json:"title"`
	Subtitle       string `json:"subtitle,omitempty"`
	ID             string `json:"id"`
	CollectionID   string `json:"collection_id"`
	CollectionName string `json:"collection_name"`
	CollectionType string `json:"collection_type"`
	Image          string `json:"image"`
	Logo           string `json:"logo,omitempty"`
	EntryType      string `json:"entry_type,omitempty"`
}

// BrowseRow strut for meta_browse
type BrowseRow struct {
	ID             int64             `db:"id" json:"id"`
	CollectionType string            `db:"collection_type" json:"collection_type"`
	CollectionName string            `db:"collection_name" json:"collection_name"`
	Title          string            `db:"title" json:"title"`
	Subtitle       string            `db:"subtitle" json:"subtitle"`
	Order          int64             `db:"order" json:"order"`
	ImageSource    string            `db:"image_source" json:"image_source"`
	CreatedAt      time.Time         `db:"created_at" json:"created_at"`
	UpdatedAt      time.Time         `db:"updated_at" json:"updated_at"`
	UIType         string            `db:"ui_type" json:"ui_type"`
	ImagesResized  string            `db:"images_resized" json:"images_resized"`
	Images         map[string]string `db:"images" json:"images,omitempty"`
	Platform       []string          `json:"platform"`
	ImagePreview   string            `json:"image_preview"`
	LogoPreview    string            `json:"logo_preview"`
}

// Browses all meta_browse struct
type Browses []*BrowseRow

// Parse parse json data
func (i *BrowseRow) Parse() {
	i.Images = make(map[string]string)
	// get images from string
	if i.ImagesResized != "" && i.ImagesResized != "{}" {
		err := json.Unmarshal([]byte(i.ImagesResized), &i.Images)
		if err != nil {
			log.Println("[ERROR]", err)
			return
		}
		imgKeys := []string{"xs", "logo"}
		// add image preview path
		for _, key := range imgKeys {

			if image, ok := i.Images[key]; ok {
				// change from s3 path to image URL
				imageURL, err := url.Parse(image)
				if err != nil {
					log.Println("[ERROR]", err)
					return
				}
				var previewURL string

				if strings.Contains(imageURL.Host, "prod") {
					previewURL = fmt.Sprintf("%s%s", "https://images.kktv.com.tw", imageURL.Path)
				} else {
					previewURL = fmt.Sprintf("%s%s", "https://test-images.kktv.com.tw", imageURL.Path)
				}

				if key == "xs" {
					// image_preview
					i.ImagePreview = previewURL

				} else {
					// logo
					i.LogoPreview = previewURL
				}
			}
		}
	}
}

// UpdateCollectionAddition update collection to redis collection additional key
func (i *BrowseRow) UpdateCollectionAddition() (err error) {
	collectionAddition, err := NewCollectAddition(i.CollectionType, i.CollectionName)
	if err != nil {
		return err
	}
	collectionAddition.Title = i.Title
	return collectionAddition.Write()
}

// GetCollections turn meta_browse to redis struct for api
func (browse Browses) GetCollections() (items []*RedisCollection) {
	for _, item := range browse {
		c := new(RedisCollection)
		c.Title = item.Title
		c.Subtitle = item.Subtitle
		c.CollectionType = item.CollectionType
		c.CollectionName = item.CollectionName

		imgKeys := []string{"xs", "logo"}
		// add image preview path
		for _, key := range imgKeys {
			if image, ok := item.Images[key]; ok {
				// change from s3 path to image URL
				imageURL, err := url.Parse(image)
				if err != nil {
					log.Println("[ERROR]", err)
					return
				}
				var previewURL string

				if strings.Contains(imageURL.Host, "prod") {
					previewURL = fmt.Sprintf("%s%s", "https://images.kktv.com.tw", imageURL.Path)
				} else {
					previewURL = fmt.Sprintf("%s%s", "https://test-images.kktv.com.tw", imageURL.Path)
				}

				if key == "xs" {
					c.Image = previewURL
				} else {
					c.Logo = previewURL

				}
			}
		}
		if item.CollectionName == "lust" && item.CollectionType == "plan" {
			c.EntryType = "protected"
		}
		c.CollectionID = dbmeta.GetTitleCollectionID(item.CollectionType, item.CollectionName)
		c.ID = c.CollectionID
		items = append(items, c)
	}
	return
}

// Save write the browse collection to redis
func (browse Browses) Save() (err error) {
	dataBytes, err := json.Marshal(browse)
	if err != nil {
		log.Println("[ERROR]", err)
	}
	log.Println("[INFO] write browse key", browseRedisKey)
	redisPool := kkapp.App.RedisMeta.Master()
	redisPool.Cmd("SET", browseRedisKey, dataBytes)
	return
}

// Upasert write one row the browse collection
func (browse Browses) Upsert(row *BrowseRow) (updatedRow *BrowseRow, err error) {
	var foundExistID bool
	if err != nil {
		log.Println("[ERROR]", err)
	}

	if row.ID == 0 {
		// insert a maybe unique ID
		row.ID = time.Now().Unix()
	}

	for idx, item := range browse {
		if row.ID == item.ID {
			browse[idx] = row
			browse[idx].Parse()
			foundExistID = true
			break
		}
	}

	if !foundExistID {
		// insert
		browse = append(browse, row)
	}
	// update collection addition
	err = row.UpdateCollectionAddition()
	if err != nil {
		return row, err
	}
	// save browse
	err = browse.Save()
	return row, err
}

// Delete delete one row the browse collection
func (browse Browses) Delete(ID int64) (err error) {
	var foundExistID bool
	var index2Del int

	if ID == 0 {
		err = errors.New("Not validate ID")
		return
	}

	for idx, item := range browse {
		if item.ID == ID {
			foundExistID = true
			index2Del = idx
			break
		}
	}

	if !foundExistID {
		err = errors.New("Not validate ID")
		return
	}

	// remove from browse collection
	browse = append(browse[:index2Del], browse[index2Del+1:]...)

	err = browse.Save()
	return err
}

// Get get one from the browse collection
func (browse Browses) Get(ID int64) (row *BrowseRow, err error) {
	var found bool

	if ID == 0 {
		err = errors.New("Empty ID")
		return
	}

	for _, item := range browse {
		if ID == item.ID {
			row = item
			found = true
			break
		}
	}

	if !found {
		// insert
		err = errors.New("NOT found")
	}
	return row, err
}

// Platform filter collection for
// web => web && mobile devices
// tv => tv devices
func (browse Browses) Platform(platform string) (items Browses) {
	for _, item := range browse {
		for _, val := range item.Platform {
			if val == platform {
				items = append(items, item)
				break
			}
		}
	}
	return items
}

// CollectionType filter collection type for
// content_agent => type is content_agent
// genre => type is genre
func (browse Browses) CollectionType(collectionType string) (items Browses) {
	for _, item := range browse {
		if item.CollectionType == collectionType {
			items = append(items, item)
		}
	}
	return items
}

// NewBrowse get the BrowseRow from redis
func NewBrowse() (items Browses, err error) {
	var browseBytes []byte

	redisPool := kkapp.App.RedisMeta.Slave()

	browseBytes, err = redisPool.Cmd("GET", browseRedisKey).Bytes()
	if err != nil {
		log.Println("[ERROR]", err)
		return
	}

	err = json.Unmarshal(browseBytes, &items)
	if err != nil {
		log.Println("[ERROR]", err)
		return
	}

	for idx := range items {
		items[idx].Parse()
	}

	return items, err
}
