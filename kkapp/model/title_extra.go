package model

import (
	"encoding/json"
	"fmt"
	"strings"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	"github.com/KKTV/kktv-api-v3/pkg/log"
)

var (
	// Key store at meta redis
	keyTitleExtraFmt = "meta:v1:title-detail:%s:json"
)

// NewTitleExtra get titles for listing api
func NewTitleExtra(titleid string) (title *dbmeta.MetaExtra, err error) {
	// the titleid should ends with "extra"
	usertitle := new(UserSingleTitleDetail)
	title = new(dbmeta.MetaExtra)
	pool := kkapp.App.RedisMeta.Slave()

	titleID := strings.Replace(titleid, "extra", "", -1)
	hashkey := fmt.Sprintf(keyTitleHashFmt, titleID)

	if resp, err := pool.Cmd("HGET", hashkey, "whole").Bytes(); err != nil {
		log.Warn("get extra error: HGET whole failed").Str("key", hashkey).Err(err).Send()
		return nil, err
	} else {
		if err = json.Unmarshal(resp, usertitle); err != nil {
			log.Warn("get extra error: Unmarshal failed").Str("key", hashkey).
				Err(err).
				Interface("usertitle", usertitle).
				Send()
			return nil, err
		}
	}

	if usertitle.Status == dbmeta.StatusLicenseExpired.String() {
		return nil, nil
	}

	key := fmt.Sprintf(keyTitleExtraFmt, titleid)
	jsonBytes, err := pool.Cmd("GET", key).Bytes()

	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(jsonBytes, title)

	// normalize title.Country to titles api
	// so copy to the correct key then cleanup
	title.Country.CollectionType = "country"
	title.Country.CollectionName = title.Country.Title
	title.Country.Title = ""
	title.Country.CollectionID = ""
	return
}
