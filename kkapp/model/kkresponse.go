package model

import (
	"fmt"
	"net/http"
)

// ErrorResp for error api response
type ErrorResp struct {
	Type    string `json:"type"`
	Message string `json:"message"`
	Subtype string `json:"subtype"`
	Code    int    `json:"-"`
}

// KKStatus for api response
type KKStatus struct {
	Type    string      `json:"type"`
	Subtype interface{} `json:"subtype"`
	Message interface{} `json:"message"`
	Info    interface{} `json:"info,omitempty"` // for extra info
}

// KKResp for api response
type KKResp struct {
	Status KKStatus    `json:"status"`
	Data   interface{} `json:"data"`
}

// KKErrorResp for api response
type KKErrorResp struct {
	Status ErrorResp `json:"status"`
	Code   int       `json:"-"`
}

// MakeOkMap api response helper
func MakeOkMap() map[string]interface{} {
	return map[string]interface{}{
		"status": map[string]interface{}{
			"type":    "OK",
			"subtype": nil,
			"message": nil,
		},
	}

}

// MakeOk api response helper
func MakeOk() (resp KKResp) {
	resp.Status = KKStatus{Type: "OK", Subtype: nil, Message: nil}
	return
}

// ErrorNotFound api response helper
func ErrorNotFound(msg string) (resp KKErrorResp) {
	var err ErrorResp
	err.Message = fmt.Sprintf("Not Found: %s", msg)
	err.Subtype = "NOT FOUND"
	err.Code = http.StatusNotFound
	resp.Status = err
	resp.Code = err.Code
	return
}

// ErrorOauth api response helper
func ErrorOauth(msg string) (resp KKErrorResp) {
	var err ErrorResp
	err.Message = fmt.Sprintf("Unauthorized: %s", msg)
	err.Subtype = "OAUTHERROR"
	err.Code = http.StatusUnauthorized
	resp.Status = err
	resp.Code = err.Code
	return
}

// ErrorBadRequest api response helper
func ErrorBadRequest(msg string) (resp KKErrorResp) {
	var err ErrorResp
	err.Message = fmt.Sprintf("Invalid Request: %s", msg)
	err.Subtype = "BAD REQUEST"
	err.Code = http.StatusBadRequest
	resp.Status = err
	resp.Code = err.Code
	return
}

// ErrorSingup api response helper
func ErrorSignup(msg string) (resp KKErrorResp) {
	var err ErrorResp
	err.Message = fmt.Sprintf("%s", msg)
	err.Subtype = "SERVER MESSAGE"
	err.Code = http.StatusBadRequest
	resp.Status = err
	resp.Code = err.Code
	return
}

// ErrorAuthorized api response helper
func ErrorAuthorized(msg string) (resp KKErrorResp) {
	var err ErrorResp
	err.Message = fmt.Sprintf("Unauthorized: %s", msg)
	err.Subtype = "UNAUTHORIZED"
	err.Code = http.StatusUnauthorized
	resp.Status = err
	resp.Code = err.Code
	return
}

// ErrorServer api response helper
func ErrorServer(msg string) (resp KKErrorResp) {
	var err ErrorResp
	err.Message = fmt.Sprintf("Internal Server Error: %s", msg)
	err.Subtype = "INTERNAL SERVER ERROR"
	err.Code = http.StatusInternalServerError
	resp.Status = err
	resp.Code = err.Code
	return
}

// ErrorTimeout api response helper
func ErrorTimeout(msg string) (resp KKErrorResp) {
	var err ErrorResp
	err.Message = fmt.Sprintf("Request Timeout: %s", msg)
	err.Subtype = "REQUEST TIMEOUT"
	err.Code = http.StatusRequestTimeout
	resp.Status = err
	resp.Code = err.Code
	return
}

// ErrorDeviceNotFound api response helper
func ErrorDeviceNotFound(msg string) (resp KKErrorResp) {
	var err ErrorResp
	err.Message = "Please register a new code"
	err.Subtype = "CodeExpired"
	err.Type = "NotFound"
	err.Code = http.StatusNotFound
	resp.Status = err
	resp.Code = err.Code
	return
}

// ErrorGuestScope api response helper
func ErrorGuestScope(msg string) (resp KKErrorResp) {
	var err ErrorResp
	err.Message = fmt.Sprintf("Unrecognized scope: '%s'", msg)
	err.Subtype = "BAD REQUEST"
	err.Type = "FAIL"
	err.Code = http.StatusBadRequest
	resp.Status = err
	resp.Code = err.Code
	return
}
