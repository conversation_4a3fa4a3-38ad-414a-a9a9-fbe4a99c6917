package model

import (
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	"github.com/KKTV/kktv-api-v3/kkapp/model/kkuser"
	"github.com/KKTV/kktv-api-v3/pkg/datatype"
)

// EpisodePlayed struct for user played episode
type EpisodePlayed struct {
	ID                    string                `json:"id"`
	Title                 string                `json:"title"`
	SeriesTitle           string                `json:"series_title"`
	Duration              datatype.RoundedFloat `json:"duration"`
	PlayZone              bool                  `json:"play_zone"`
	IsAvod                bool                  `json:"is_avod"`
	Still                 string                `json:"still"`
	ContinuedFromPrevious bool                  `json:"continued_from_previous,omitempty"`
	LastPlayed            kkuser.LastPlayed     `json:"last_played,omitempty"`
	Mezzanines            struct {
		Dash *dbmeta.Dash `json:"dash"`
		Hls  *dbmeta.Hls  `json:"hls"`
	} `json:"mezzanines"`
}

func (e *EpisodePlayed) IsAVOD() bool {
	return e.IsAvod
}

// UserTitleDetail for api title titledetail with User Information
type UserTitleDetail struct {
	*TitleDetail
	LastPlayedEpisode EpisodePlayed `json:"last_played_episode,omitempty"`
}

// NewUserTitleDetails get titles for listing api
func NewUserTitleDetails(userid string, titleIds []string) (usertitles []*UserTitleDetail, err error) {
	titles, err := NewTitleDetailsWithSeries(titleIds)
	playedMap, err := kkuser.NewLastPlayedMap(userid, titleIds)
	if err != nil {
		log.Println("[ERROR]", err)
		return
	}
	// dataBytes, err := json.Marshal(titles)
	// log.Println(string(dataBytes))

	for _, title := range titles {
		if title.ID == "" {
			continue
		}
		item := new(UserTitleDetail)
		item.TitleDetail = title

		if lastPlayed, ok := playedMap[title.ID]; ok {
			// to find the last play episode
			var goPlay EpisodePlayed
			var playedEpisodeMeta, nextEpisodeMeta *dbmeta.EpisodeMeta
			var firstEpisodeMeta *dbmeta.EpisodeMeta // save the first episode of one series
			var playedCompleted, rightNext bool

			// get last play episide data for this title
			lastOne := lastPlayed.LastOne
			// if this last play episode is complete
			playedCompleted = lastOne.IsComplete

			// find out this episode
			for _, series := range title.Series {
				for idx, episode := range series.Episodes {
					if idx == 0 {
						firstEpisodeMeta = episode
					}
					if rightNext {
						// continue from previous
						nextEpisodeMeta = episode
						// test if have played this episode
						if lp, ok := lastPlayed.LastPlayedMap[episode.ID]; ok {
							goPlay.LastPlayed = lp
							goPlay.SeriesTitle = series.Title
						} else {
							// not found
							goPlay.LastPlayed = kkuser.LastPlayed{}
						}
						break
					}
					if episode.ID == lastOne.EpisodeID {
						if !playedCompleted {
							playedEpisodeMeta = episode
							goPlay.LastPlayed = lastOne
							goPlay.SeriesTitle = series.Title
							break
						} else {
							// would replace with next episode if have one
							playedEpisodeMeta = episode
							goPlay.LastPlayed = lastOne
							goPlay.SeriesTitle = series.Title
							rightNext = true
						}
					}

				}

				// found last played and IsComplete is false
				if playedEpisodeMeta != nil && rightNext == false {
					break
				}
				// found nextEpisodeMeta
				if nextEpisodeMeta != nil {
					break
				}
			}
			// compose LastPlayedEpisode
			if nextEpisodeMeta != nil {
				// continue from previous
				goPlay.ContinuedFromPrevious = true
				// overwrite playedEpisodeMeta
				playedEpisodeMeta = nextEpisodeMeta
			}

			if playedEpisodeMeta != nil {
				goPlay.ID = playedEpisodeMeta.ID
				goPlay.Title = playedEpisodeMeta.Title
				goPlay.SeriesTitle = playedEpisodeMeta.SeriesTitle
				goPlay.Duration = playedEpisodeMeta.Duration
				goPlay.PlayZone = playedEpisodeMeta.PlayZone
				goPlay.IsAvod = playedEpisodeMeta.IsAvod
				goPlay.Still = playedEpisodeMeta.Still
				goPlay.Mezzanines.Dash = playedEpisodeMeta.Mezzanines.Dash
				goPlay.Mezzanines.Hls = playedEpisodeMeta.Mezzanines.Hls
				item.LastPlayedEpisode = goPlay
			} else if firstEpisodeMeta != nil {
				log.Println("[INFO] do not found any match played episode and firstEpisodeMeta exist,  assign first one")
				goPlay.ID = firstEpisodeMeta.ID
				goPlay.Title = firstEpisodeMeta.Title
				goPlay.SeriesTitle = firstEpisodeMeta.SeriesTitle
				goPlay.Duration = firstEpisodeMeta.Duration
				goPlay.PlayZone = firstEpisodeMeta.PlayZone
				goPlay.IsAvod = firstEpisodeMeta.IsAvod
				goPlay.Still = firstEpisodeMeta.Still
				goPlay.Mezzanines.Dash = firstEpisodeMeta.Mezzanines.Dash
				goPlay.Mezzanines.Hls = firstEpisodeMeta.Mezzanines.Hls
				goPlay.LastPlayed = kkuser.LastPlayed{
					TitleID:          firstEpisodeMeta.TitleID,
					EpisodeID:        firstEpisodeMeta.EpisodeID,
					LastPlayedOffset: 0,
					IsComplete:       false,
				}
				item.LastPlayedEpisode = goPlay
			}

		}

		// clean the series field
		item.Series = nil
		usertitles = append(usertitles, item)
	}
	return
}

// UserAction for title
type UserAction struct {
	IsFavorite bool `json:"is_favorite,omitempty"`
	Rating     *int `json:"rating,omitempty"`
}

// UserSingleTitleDetail for api title titledetail with or without User Information
type UserSingleTitleDetail struct {
	*dbmeta.TitleMeta
	UserActions       UserAction          `json:"user_actions,omitempty"`
	LastPlayedEpisode *dbmeta.EpisodeMeta `json:"last_played_episode,omitempty"`
}

// NewUserSingleTitleDetail get titles for single title detail api
func NewUserSingleTitleDetail(userid string, titleid string) (usertitle *UserSingleTitleDetail, err error) {
	// read only
	pool := kkapp.App.RedisMeta.Slave()
	conn, err := pool.Get()
	if err != nil {
		log.Println(err)
		return nil, err
	}
	defer pool.Put(conn)

	hashkey := fmt.Sprintf(keyTitleHashFmt, titleid)
	// whole is the keyInMetaHash
	resp, err := conn.Cmd("HGET", hashkey, "whole").Bytes()
	if err != nil {
		return nil, err
	}

	usertitle = new(UserSingleTitleDetail)
	err = json.Unmarshal(resp, &usertitle)
	if err != nil {
		return nil, err
	}

	//[BEGIN] workaround for vod2live, https://kktv.atlassian.net/browse/KKTV-10254
	if usertitle.TitleType == "live" {
		log.Println("[DEBUG] getting live for ", titleid)
		onLiveTitleIDs, err := getLiveTitleIDs()
		if err != nil || len(onLiveTitleIDs) == 0 {
			log.Println("fail to get live title IDs or no on air live title", err)
		} else {
			for _, onLiveTitleID := range onLiveTitleIDs {
				isTargetLive := titleid == onLiveTitleID && usertitle.TitleType == "live" &&
					len(usertitle.Series) == 1 && len(usertitle.Series[0].Episodes) == 1 // live should only have 1 vod
				log.Println("[DEBUG] onlive got ", onLiveTitleID, ", isTargetLive:", isTargetLive)
				if !isTargetLive {
					continue
				}
				patchLastPlayedEpisodeForLive(usertitle)
				break
			}
		}
	}
	//[END]

	// don't need the user relative information
	if userid == "" {
		return
	}

	if usertitle.TitleType != "live" {
		var lastPlayed *kkuser.LastPlayedEpisodes

		// if there is a userid also prepare the use relative information
		lastPlayed, err = kkuser.NewLastPlayed(userid, titleid)
		if err != nil {
			log.Println("[ERROR]", err)
			return
		}
		lastPlayed.Load()

		// to find the last play episode
		// var goPlay EpisodePlayed
		var playedEpisodeMeta, nextEpisodeMeta *dbmeta.EpisodeMeta
		var playedCompleted, rightNext bool

		// get last play episide data for this title
		lastOne := lastPlayed.LastOne
		// if this last play episode is complete
		playedCompleted = lastOne.IsComplete

		// compose the lastplayed data with episode
		for seriesIdx, series := range usertitle.TitleMeta.Series {
			for idx, episode := range series.Episodes {
				if lp, ok := lastPlayed.LastPlayedMap[episode.ID]; ok {
					usertitle.TitleMeta.Series[seriesIdx].Episodes[idx].LastPlayed = lp
				}

				// find right lastplayed
				if rightNext && nextEpisodeMeta == nil {
					// continue from previous
					nextEpisodeMeta = episode
				}
				if episode.ID == lastOne.EpisodeID {
					if !playedCompleted {
						playedEpisodeMeta = episode
					} else {
						// would replace with next episode if have one
						playedEpisodeMeta = episode
						rightNext = true
					}
				}
			}
		}
		// compose LastPlayedEpisode
		if nextEpisodeMeta != nil {
			// continue from previous
			playedEpisodeMeta = nextEpisodeMeta
			playedEpisodeMeta.ContinuedFromPrevious = true
		}
		if playedEpisodeMeta != nil {
			usertitle.LastPlayedEpisode = playedEpisodeMeta
		}
	}

	// user action
	// rating && favorite
	var useraction UserAction
	rate, _ := kkuser.NewUserRating(userid)
	rateItem, err := rate.Get(titleid)
	if err != nil {
		log.Println("[ERROR]", err)
	} else {
		useraction.Rating = &rateItem.Rating
	}

	// favorite
	favorite, _ := kkuser.NewFavorite(userid)
	useraction.IsFavorite = favorite.IsFavorite(titleid)

	usertitle.UserActions = useraction
	return
}

func patchLastPlayedEpisodeForLive(usertitle *UserSingleTitleDetail) {
	liveEp := *(usertitle.Series[0].Episodes[0])
	liveStart, liveEnd := liveEp.Pub, liveEp.UnPub
	offsetSecs := int64(0)
	now := time.Now().Unix()
	if now >= liveStart && now <= liveEnd {
		offsetSecs = now - liveStart
	} else if now > liveEnd {
		offsetSecs = liveEnd - liveStart
	}
	if offsetSecs > int64(liveEp.Duration) {
		offsetSecs = int64(liveEp.Duration)
	}

	liveEp.LastPlayed = kkuser.LastPlayed{
		TitleID:          usertitle.ID,
		EpisodeID:        liveEp.ID,
		LastPlayedOffset: offsetSecs,
		IsComplete:       false,
		UpdatedAt:        now,
	}
	usertitle.LastPlayedEpisode = &liveEp
	usertitle.Series[0].Episodes[0].LastPlayed = liveEp.LastPlayed
}

func getLiveTitleIDs() ([]string, error) {
	pool := kkapp.App.RedisMeta.Slave()
	conn, err := pool.Get()
	defer pool.Put(conn)
	if err != nil {
		return nil, err
	}
	cacheKey := "tmp:live:title_ids"
	// whole is the keyInMetaHash
	bytes, err := pool.Cmd("GET", cacheKey).Bytes()
	if err != nil {
		return nil, err
	}
	var ids []string
	if err := json.Unmarshal(bytes, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}
