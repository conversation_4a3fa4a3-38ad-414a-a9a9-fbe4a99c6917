package model

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"log"
	"sort"
	"strings"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/gzip"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	plog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/model/authority"
	"github.com/KKTV/kktv-api-v3/pkg/model/cachemeta"
	"github.com/buger/jsonparser"
	"github.com/mediocregopher/radix.v2/util"
)

var (
	// HASH store at meta redis
	keyTitleHashFmt = "meta:v2:title-detail:%s:hash"
	keyTitleGzipFmt = "meta:v1:title-detail:%s:gzip"
	// TitleDetail need field, not to get too many data
	tdFields = []string{
		"id",
		"title",
		"title_type",
		"status",
		"available",
		"is_ending",
		"is_containing_avod",
		"child_lock",
		"is_validated",
		"release_year",
		"user_rating",
		"user_rating_count",
		"cover",
		"stills",
		"review",
		"release_info",
		"total_episode_counts",
		"total_series_count",
		"latest_update_info",
		"content_labels",
		"content_labels_for_expired_user",
		"content_labels_for_freetrial_user",
		"summary",
		"themes",
		"casts",
		"live_info",
		"free_trial",
		"content_labels_with_full_access",
		"content_labels_without_full_access",
		"accepted_authorities",
		"airing",
		"genres",
		"tags",
		"same_as",
	}

	collectionFields = []string{
		"id",
		"title",
		"title_type",
		"status",
		"available",
		"is_ending",
		"end_year",
		"user_rating",
		"user_rating_count",
		"themes",
	}
)

// luaScript for Get bulk titleDetail from Redis
const luaScript = `
	local results = {}
for i, key in ipairs(KEYS) do
    results[i] = redis.call('HMGET', key, %s)
end
return results
	`

// TitleDetail for api title
type TitleDetail struct {
	ID                            string            `json:"id"`
	Title                         string            `json:"title"`
	TitleType                     string            `json:"title_type"`
	Status                        string            `json:"status"`
	IsEnding                      bool              `json:"is_ending"`
	IsContainingAvod              bool              `json:"is_containing_avod"`
	FreeTrial                     bool              `json:"free_trial"`
	IsValidated                   bool              `json:"is_validated"`
	ChildLock                     bool              `json:"child_lock"`
	ReleaseYear                   int64             `json:"release_year,omitempty"`
	EndYear                       int64             `json:"end_year,omitempty"`
	UserRating                    float64           `json:"user_rating,omitempty"`
	UserRatingCount               int64             `json:"user_rating_count,omitempty"`
	Cover                         string            `json:"cover,omitempty"`
	Stills                        []string          `json:"stills,omitempty"`
	Review                        map[string]string `json:"review,omitempty"`
	ReleaseInfo                   string            `json:"release_info,omitempty"`
	TotalEpisodeCounts            map[string]int    `json:"total_episode_counts,omitempty"`
	TotalSeriesCount              int               `json:"total_series_count,omitempty"`
	LatestUpdateInfo              string            `json:"latest_update_info,omitempty"`
	ContentLabels                 []string          `json:"content_labels,omitempty"`
	ContentLabelsForExpiredUser   []string          `json:"content_labels_for_expired_user,omitempty"`
	ContentLabelsForFreeTrialUser []string          `json:"content_labels_for_freetrial_user"`

	ContentLabelsWithFullAccess    []string              `json:"content_labels_with_full_access,omitempty"`
	ContentLabelsWithoutFullAccess []string              `json:"content_labels_without_full_access,omitempty"`
	AcceptedAuthorities            []authority.Authority `json:"accepted_authorities,omitempty"`
	AiringInfo                     *cachemeta.AiringInfo `json:"airing,omitempty"`

	Available    *bool                    `json:"available,omitempty"`
	Summary      string                   `json:"summary,omitempty"`
	Genres       []*dbmeta.CollectionItem `json:"genres,omitempty"`
	Tags         []*dbmeta.CollectionItem `json:"tags,omitempty"`
	Themes       []*dbmeta.CollectionItem `json:"themes,omitempty"`
	Casts        []*dbmeta.CollectionItem `json:"casts,omitempty"`
	Series       []*dbmeta.Series         `json:"series,omitempty"`
	Live         []*dbmeta.Series         `json:"live,omitempty"`
	LiveInfo     *dbmeta.LiveInfo         `json:"live_info,omitempty"`
	Country      *dbmeta.CollectionItem   `json:"country,omitempty"`
	TitleAliases []string                 `json:"title_alias,omitempty"`
	SameAs       *dbmeta.SameAs           `json:"same_as,omitempty"`
}

func (td *TitleDetail) GetAcceptedAuthorities() []authority.Authority {
	return td.AcceptedAuthorities
}

// CollectionTitles for collections api to sort paging
// the default order is sort by titile publish date
type CollectionTitles []*TitleDetail

// SortByUserRatingCount sort by UserRatingCount
func (col CollectionTitles) SortByUserRatingCount() {
	sort.Slice(col, func(i, j int) bool { return col[i].UserRatingCount > col[j].UserRatingCount })
}

// SortByUserRating sort by UserRating
func (col CollectionTitles) SortByUserRating() {
	sort.Slice(col, func(i, j int) bool { return col[i].UserRating > col[j].UserRating })
}

// SortByEndYearAndID sort by UserRating
func (col CollectionTitles) SortByEndYearAndID() {
	// https://github.com/KKTV/kktv-node-collection/blob/develop/src/index.js
	sort.Slice(col, func(i, j int) bool {
		if col[i].String() > col[j].String() {
			return true
		}
		return false
	})
}

// FilterNotExpired only have title.Available and Status != license_expired
func (col CollectionTitles) FilterNotExpired() (availableTitles []*TitleDetail) {
	for _, item := range col {
		if item.Available != nil && *item.Available && item.Status != "license_expired" {
			item.Available = nil
			availableTitles = append(availableTitles, item)
		}
	}
	return
}

// String for sorting
func (td *TitleDetail) String() (title string) {
	var endingStr = "0"
	var endYear int64
	if td.IsEnding == false {
		endingStr = "1"
	}
	if td.EndYear == 0 {
		// no EndYear value give this year
		endYear = int64(time.Now().Year())
	} else {
		endYear = td.EndYear
	}
	return fmt.Sprintf("%s%d%s", endingStr, endYear, td.ID)
}

// NewTitleDetails get titles for listing api
func NewTitleDetails(titleIds []string) (titles []*TitleDetail, err error) {
	titles, err = NewTitleDetailsViaFields(titleIds, tdFields)
	if err != nil || len(titles) == 0 {
		// redis fail , fall back to database
		plog.Warn("NewTitleDetails, fail to load from redis").Strs("title_ids", titleIds).Err(err).Send()
		return NewTitleDetailsDB(titleIds, tdFieldsExclude)
	}
	return titles, err
}

// NewTitleDetailsWithSeries use jsonparser get title detail and series for listing api
func NewTitleDetailsWithSeries(titleIds []string) (titles []*TitleDetail, err error) {
	hashFields := append(tdFields, "series", "airing")
	titles, err = NewTitleDetailsViaFields(titleIds, hashFields)
	if err != nil || len(titles) == 0 {
		// redis fail , fall back to database
		plog.Warn("NewTitleDetailsWithSeries, fail to load from redis").Strs("title_ids", titleIds).Err(err).Send()
		excludeFields := []string{"genres", "end_year"}
		return NewTitleDetailsDB(titleIds, excludeFields)
	}
	return titles, err
}

func NewBulkTitleDetailsWithGenres(titleIds []string) (titles []*TitleDetail, err error) {
	hashFields := append(tdFields, "genres")
	titles, err = NewBulkTitleDetailsViaFields(titleIds, hashFields)
	if err != nil || len(titles) == 0 {
		// redis fail , fall back to database
		plog.Warn("NewBulkTitleDetailsWithGenres, fail to load from redis").Strs("title_ids", titleIds).Err(err).Send()
		excludeFields := []string{"end_year"}
		return NewTitleDetailsDB(titleIds, excludeFields)
	}
	return titles, err
}

// NewTitleCollection use jsonparser get title detail for collections api
func NewTitleCollection(titleIds []string, cacheKey string) (titles []*TitleDetail, err error) {
	// if the collection item greater than 10, we cache it in redis for 600 seconds
	var collectionCacheNum = 10

	if len(titleIds) > collectionCacheNum && cacheKey != "" {
		pool := kkapp.App.RedisMeta.Slave()
		cacheBytes, err := pool.Cmd("GET", cacheKey).Bytes()
		if err == nil {
			err = json.Unmarshal(cacheBytes, &titles)
			return titles, err
		}
	}

	titles, err = NewTitleDetailsViaFields(titleIds, collectionFields)

	// design should cache this or not
	if len(titleIds) > collectionCacheNum && cacheKey != "" {
		// cache it
		cacheBytes, _ := json.Marshal(titles)
		conn := kkapp.App.RedisMeta.Master()
		// cache 600 seconds (10 minutes)
		conn.Cmd("SETEX", cacheKey, 600, cacheBytes)
	}
	return titles, err
}

// NewTitleDetailsViaFields use jsonparser get title detail and series for listing api
func NewTitleDetailsViaFields(titleIds []string, fields []string) (titles []*TitleDetail, err error) {
	pool := kkapp.App.RedisMeta.Slave()
	conn, err := pool.Get()
	if err != nil {
		log.Println(err)
		return nil, err
	}
	defer pool.Put(conn)
	err = conn.Cmd("MULTI").Err
	for _, titleid := range titleIds {
		hashkey := fmt.Sprintf(keyTitleHashFmt, titleid)
		err := conn.Cmd("HMGET", hashkey, fields).Err
		if err != nil {
			return nil, err
		}
	}
	reply := conn.Cmd("EXEC")
	if reply.Err != nil {
		return nil, reply.Err
	}

	arrReply, err := reply.Array()
	if err != nil {
		return nil, err
	}

	for _, item := range arrReply {
		resp, err := item.ListBytes()
		if err != nil {
			return nil, err
		}
		td := new(TitleDetail)
		// Following conditions must be fulfilled before dealing with the title:
		// 1. resp is not empty
		// 2. resp[0] = id, is not empty
		if len(resp) > 0 && string(resp[0]) != "" {
			var err error
			data := []byte("{}")
			for idx, key := range fields {
				if len(resp[idx]) > 0 {
					data, err = jsonparser.Set(data, resp[idx], key)
					if err != nil {
						log.Println("[ERROR]", err)
					}
				}
			}
			json.Unmarshal([]byte(data), td)
			titles = append(titles, td)
		}
	}

	return titles, nil
}

func NewBulkTitleDetailsViaFields(titleIds []string, fields []string) (titles []*TitleDetail, err error) {
	pool := kkapp.App.RedisMeta.Slave()
	conn, err := pool.Get()
	if err != nil {
		return nil, err
	}
	defer pool.Put(conn)
	fieldsStr := fmt.Sprintf("'%s'", strings.Join(fields, "', '"))
	script := fmt.Sprintf(luaScript, fieldsStr)
	// It must use []interface{}{} because util.LuaEval()
	keys := []interface{}{}
	for _, titleid := range titleIds {
		hashkey := fmt.Sprintf(keyTitleHashFmt, titleid)
		keys = append(keys, hashkey)
	}

	results := util.LuaEval(conn, script, len(keys), keys...)
	if results.Err != nil {
		return
	}
	arrReply, err := results.Array()

	if err != nil {
		return nil, err
	}

	for _, item := range arrReply {
		resp, err := item.ListBytes()
		if err != nil {
			return nil, err
		}
		td := new(TitleDetail)
		// Following conditions must be fulfilled before dealing with the title:
		// 1. resp is not empty
		// 2. resp[0] = id, is not empty
		if len(resp) > 0 && string(resp[0]) != "" {
			var err error
			data := []byte("{}")
			for idx, key := range fields {
				if len(resp[idx]) > 0 {
					data, err = jsonparser.Set(data, resp[idx], key)
					if err != nil {
						plog.Warn("titleDetail: NewBulkTitleDetailsViaFields: jsonparser failed").Err(err).Send()
					}
				}
			}
			json.Unmarshal([]byte(data), td)
			titles = append(titles, td)
		}
	}

	return titles, nil
}

// NewTitleDetailsV2 use jsonparser
func NewTitleDetailsV2(titleIds []string) (titles []*TitleDetail, err error) {
	// read only
	pool := kkapp.App.RedisMeta.Slave()
	conn, err := pool.Get()
	if err != nil {
		log.Println(err)
		return nil, err
	}
	defer pool.Put(conn)

	err = conn.Cmd("MULTI").Err
	for _, titleid := range titleIds {
		hashkey := fmt.Sprintf(keyTitleHashFmt, titleid)
		err := conn.Cmd("HMGET", hashkey, tdFields).Err
		if err != nil {
			return nil, err
		}
	}
	reply := conn.Cmd("EXEC")

	if reply.Err != nil {
		return nil, reply.Err
	}

	arrReply, err := reply.Array()

	if err != nil {
		return nil, err
	}

	for _, item := range arrReply {
		resp, err := item.ListBytes()
		if err != nil {
			return nil, err
		}
		td := new(TitleDetail)
		if len(resp) > 0 && string(resp[0]) != "" {
			// first is id, and must not empty
			var err error
			data := []byte("{}")
			for idx, key := range tdFields {
				data, err = jsonparser.Set(data, resp[idx], key)
				if err != nil {
					log.Println("[ERROR]", err)
				}
			}
			json.Unmarshal([]byte(data), td)

			titles = append(titles, td)
		}
	}
	return titles, nil
}

// NewTitleDetailsV3 get titles for listing api direct from hash key
func NewTitleDetailsV3(titleIds []string) (titles []*TitleDetail, err error) {
	// read only
	pool := kkapp.App.RedisMeta.Slave()
	conn, err := pool.Get()
	if err != nil {
		log.Println(err)
		return nil, err
	}
	defer pool.Put(conn)

	err = conn.Cmd("MULTI").Err
	for _, titleid := range titleIds {
		hashkey := fmt.Sprintf(keyTitleHashFmt, titleid)
		// whole is the keyInMetaHash
		err := conn.Cmd("HGET", hashkey, "whole").Err
		if err != nil {
			return nil, err
		}
	}
	reply := conn.Cmd("EXEC")

	if reply.Err != nil {
		log.Println("[ERROR]", reply.Err)
		return nil, reply.Err
	}

	arrReply, err := reply.Array()
	if err != nil {
		return nil, err
	}

	for _, item := range arrReply {
		resp, err := item.Bytes()
		if err != nil {
			return nil, err
		}
		td := new(TitleDetail)
		err = json.Unmarshal(resp, td)
		if err != nil {
			return nil, err
		}
		titles = append(titles, td)
	}
	return titles, nil
}

// NewTitleDetailsGzip get titles for listing api direct from hash key
// this only leave for reference and benchmark test due to bad performance
func NewTitleDetailsGzip(titleIds []string) (titles []*TitleDetail, err error) {
	// read only
	pool := kkapp.App.RedisMeta.Slave()
	conn, err := pool.Get()
	if err != nil {
		log.Println(err)
		return nil, err
	}
	defer pool.Put(conn)

	err = conn.Cmd("MULTI").Err
	for _, titleid := range titleIds {
		gzipkey := fmt.Sprintf(keyTitleGzipFmt, titleid)
		err := conn.Cmd("GET", gzipkey).Err
		if err != nil {
			return nil, err
		}
	}
	reply := conn.Cmd("EXEC")

	if reply.Err != nil {
		log.Println("[ERROR]", reply.Err)
		return nil, reply.Err
	}

	arrReply, err := reply.Array()
	if err != nil {
		return nil, err
	}

	for _, item := range arrReply {
		resp, err := item.Str()
		if err != nil {
			return nil, err
		}
		// decode base64

		decodeBytes, err := base64.StdEncoding.DecodeString(resp)
		if err != nil {
			log.Println("[ERROR]", err)
			return nil, err
		}

		gunzipBytes, err := gzip.GzipDecode(decodeBytes)
		if err != nil {
			log.Println("[ERROR]", err)
			return nil, err
		}

		td := new(TitleDetail)
		err = json.Unmarshal(gunzipBytes, td)
		if err != nil {
			return nil, err
		}
		titles = append(titles, td)
	}
	return titles, nil
}
