package dbredeem

import (
	"log"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"gopkg.in/guregu/null.v3"
)

var (
	sqlTVEvents = map[string]string{
		"getAll": "SELECT id, name, summary, start_time, end_time, redeem_group_id, vendors, models, devices, created_at, updated_at, active FROM events ORDER BY start_time DESC;",
	}
)

// DBTVEvent struct for mapping TV events
type DBTVEvent struct {
	ID            int         `db:"id"`
	Name          string      `db:"name"`
	Summary       string      `db:"summary"`
	StartTime     time.Time   `db:"start_time"`
	EndTime       time.Time   `db:"end_time"`
	RedeemGroupID string      `db:"redeem_group_id"`
	Vendors       string      `db:"vendors"`
	Models        string      `db:"models"`
	Devices       null.String `db:"devices"`
	CreatedAt     time.Time   `db:"created_at"`
	UpdatedAt     null.Time   `db:"updated_at"`
	Active        bool        `db:"active"`
}

// GetTVEvents get events for console
func GetTVEvents() (tvevents []DBTVEvent, err error) {
	db := kkapp.App.DbRedeem.Slave()

	err = db.Select(&tvevents, sqlTVEvents["getAll"])
	if err != nil {
		log.Println("[ERROR] - Model - GetTVEvents - Get all TV events failed.", err)
		return tvevents, err
	}

	return tvevents, err
}
