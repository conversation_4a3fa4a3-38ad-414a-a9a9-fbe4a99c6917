package dbredeem

import (
	"log"
	"sync"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"gopkg.in/guregu/null.v3"
)

var (
	sqlredeem = map[string]string{
		"reuse": `SELECT C.group_id, count(1)::INTEGER AS used_count
	FROM redemption_logs AS L JOIN coupon_codes AS C ON C.code = L.code GROUP BY C.group_id;`,

		"nonreuse": `SELECT group_id, count(1)::INTEGER AS total_count,
 count(user_id)::INTEGER AS used_count FROM coupon_codes GROUP BY group_id;`,
		"group": `SELECT
	id,
	split_part(description, ';', 1) AS description,
	substring(description FROM 'https://trello.com/c/[a-zA-Z0-9]+') AS trello_link,
	substring(description FROM 'https://kktv.atlassian.net/browse/KKTV-[0-9]+') AS jira_link,
	duration, free_duration,
	valid_since AS start_date,
	expires_at AS end_date,
	NOW() BETWEEN valid_since AND expires_at as is_active,
	allow_reuse,
	usage_limit_per_user,
	price = 0 AS is_free,
	price,
	price_no_tax, 
	fee,
	prefix,
	product_id,
	campaign_group
    FROM coupon_groups
	ORDER BY id DESC;`,

		"detail": `SELECT
	id,
	split_part(description, ';', 1) AS description,
	substring(description FROM 'https://trello.com/c/[a-zA-Z0-9]+') AS trello_link,
	substring(description FROM 'https://kktv.atlassian.net/browse/KKTV-[0-9]+') AS jira_link,
	duration, free_duration,
	valid_since AS start_date,
	expires_at AS end_date,
	NOW() BETWEEN valid_since AND expires_at as is_active,
	allow_reuse,
	usage_limit_per_user,
	price = 0 AS is_free
    FROM coupon_groups WHERE id = $1
    ORDER BY id DESC;`,

		"bycode":                          `SELECT id, code, group_id, user_id, created_at, updated_at, revoked_at FROM coupon_codes WHERE code = $1 LIMIT 1;`,
		"byid":                            `SELECT id, code, group_id, user_id, created_at, updated_at, revoked_at FROM coupon_codes WHERE id = $1 LIMIT 1;`,
		"bygroup":                         `SELECT id, code, group_id, user_id, created_at, updated_at, revoked_at FROM coupon_codes WHERE group_id = $1 LIMIT 1;`,
		"codebygroup":                     `SELECT id, code, group_id, user_id, created_at, updated_at, revoked_at FROM coupon_codes WHERE group_id = $1 ORDER BY id;`,
		"reusecode":                       `SELECT code, user_id, created_at as updated_at FROM redemption_logs WHERE code=$1 ORDER BY id`,
		"getcoupongroups":                 `SELECT id, prefix, price, duration, usage_limit_per_user, allow_reuse, valid_since, expires_at, created_at, updated_at, price_no_tax, description, free_duration, fee, channel, product_id FROM coupon_groups ORDER BY created_at DESC;`,
		"revoke_coupon_code":              `UPDATE coupon_codes SET revoked_at = NOW(), updated_at = NOW() WHERE id = $1;`,
		"active_coupon_code":              `UPDATE coupon_codes SET revoked_at = NULL, updated_at = NOW() WHERE id = $1;`,
		"revoke_coupon_codes_by_group_id": "UPDATE coupon_codes SET revoked_at = NOW(), updated_at = NOW() WHERE user_id IS NULL AND group_id = $1;",
		"active_coupon_codes_by_group_id": "UPDATE coupon_codes SET revoked_at = NULL, updated_at = NOW() WHERE group_id = $1;",
	}
)

// CouponUse console struct
type CouponUse struct {
	GroupID    int64 `db:"group_id" json:"group_id"`
	UsedCount  int64 `db:"used_count" json:"used_count"`
	TotalCount int64 `db:"total_count" json:"total_count"`
}

// CouponCode console struct
type CouponCode struct {
	ID          int64       `db:"id" json:"id"`
	Code        string      `db:"code" json:"code"`
	GroupID     int64       `db:"group_id" json:"group_id"`
	UserID      null.String `db:"user_id" json:"user_id"`
	Price       null.Int    `db:"price" json:"price"`
	PriceNoTax  null.Int    `db:"price_no_tax" json:"price_no_tax"`
	IssueUserID null.String `db:"issue_user_id" json:"issue_user_id"`
	DeviceID    null.String `db:"device_id" json:"device_id"`
	CreatedAt   null.Time   `db:"created_at" json:"created_at"`
	UpdatedAt   null.Time   `db:"updated_at" json:"updated_at"`
	RevokedAt   null.Time   `db:"revoked_at" json:"revoked_at"`
}

// CouponGroup console struct
type CouponGroup struct {
	ID                int64       `db:"id" json:"id,omitempty"`
	Description       string      `db:"description" json:"description,omitempty"`
	TrelloLink        null.String `db:"trello_link" json:"trello_link,omitempty"`
	JiraLink          null.String `db:"jira_link" json:"jira_link,omitempty"`
	Duration          string      `db:"duration" json:"duration,omitempty"`
	StartDate         time.Time   `db:"start_date" json:"start_date,omitempty"`
	EndDate           time.Time   `db:"end_date" json:"end_date,omitempty"`
	IsActive          bool        `db:"is_active" json:"is_active,omitempty"`
	AllowReuse        bool        `db:"allow_reuse" json:"allow_reuse"`
	UsageLimitPerUser null.Int    `db:"usage_limit_per_user" json:"usage_limit_per_user,omitempty"`
	IsFree            bool        `db:"is_free" json:"is_free"`
	UsedCount         int64       `json:"used_count"`
	TotalCount        int64       `json:"total_count"`
	Price             int64       `db:"price" json:"price"`
	PriceNoTax        int64       `db:"price_no_tax" json:"price_no_tax"`
	Fee               int64       `db:"fee" json:"fee"`
	FreeDuration      string      `db:"free_duration" json:"free_duration,omitempty"`
	Prefix            string      `db:"prefix" json:"prefix"`
	ProductID         null.Int    `db:"product_id" json:"product_id"`
	CampaignGroup     null.String `db:"campaign_group" json:"campaign_group"`
}

// DBCouponGroup struct for redeem group data
type DBCouponGroup struct {
	ID                int         `db:"id" json:"id"`
	Prefix            string      `db:"prefix" json:"prefix"`
	Price             int         `db:"price" json:"price"`
	Duration          string      `db:"duration" json:"duration"`
	UsageLimitPerUser null.Int    `db:"usage_limit_per_user" json:"usage_limit_per_user"`
	AllowReuse        bool        `db:"allow_reuse" json:"allow_reuse"`
	ValidSince        time.Time   `db:"valid_since" json:"valid_since"`
	ExpiresAt         time.Time   `db:"expires_at" json:"expires_at"`
	CreatedAt         time.Time   `db:"created_at" json:"created_at"`
	UpdatedAt         time.Time   `db:"updated_at" json:"updated_at"`
	PriceNoTax        int         `db:"price_no_tax" json:"price_no_tax"`
	Description       string      `db:"description" json:"description"`
	FreeDuration      string      `db:"free_duration" json:"free_duration"`
	Fee               int         `db:"fee" json:"fee"`
	Channel           null.String `db:"channel" json:"channel"`
	ProductID         null.Int    `db:"product_id" json:"product_id"`

	// help field for json
	Count         int  `json:"count"`
	IsForFamiPort bool `json:"is_for_fami_port"`
}

// CouponGroupDetail console struct
type CouponGroupDetail struct {
	Coupon CouponGroup   `json:"coupon"`
	Items  []*CouponCode `json:"items"`
}

// NewRedeemBoard get console doshboard for redeem
func NewRedeemBoard() (coupons []*CouponGroup) {

	var wg sync.WaitGroup
	var reuseCoupon []CouponUse
	var nonreuseCoupon []CouponUse
	countMap := make(map[int64]CouponUse)
	db := kkapp.App.DbRedeem.Slave()

	wg.Add(3)
	go func() {
		err := db.Select(&reuseCoupon, sqlredeem["reuse"])
		if err != nil {
			log.Println("[ERROR]", err)
		}
		wg.Done()
	}()

	go func() {
		err := db.Select(&nonreuseCoupon, sqlredeem["nonreuse"])
		if err != nil {
			log.Println("[ERROR]", err)
		}
		wg.Done()
	}()
	go func() {
		err := db.Select(&coupons, sqlredeem["group"])
		if err != nil {
			log.Println("[ERROR]", err)
		}
		wg.Done()
	}()
	wg.Wait()

	// log.Println(reuseCoupon)
	// log.Println(nonreuseCoupon)
	// log.Println(coupons)

	for _, count := range nonreuseCoupon {
		countMap[count.GroupID] = count
	}

	// reuseCoupon will overwrite nonreuseCoupon
	for _, count := range reuseCoupon {
		countMap[count.GroupID] = count
	}

	for _, item := range coupons {
		if count, ok := countMap[item.ID]; ok {
			item.UsedCount = count.UsedCount
			item.TotalCount = count.TotalCount
		}
	}
	return
}

// NewCouponGroup get console doshboard for CouponGroup
func NewCouponGroup(groupID string, code string) (detail CouponGroupDetail) {
	var couponCode CouponCode
	// var items []*CouponCode
	db := kkapp.App.DbRedeem.Slave()
	var err error

	if groupID != "" {
		err = db.Get(&detail.Coupon, sqlredeem["detail"], groupID)
		if err != nil {
			log.Println("[ERROR]", err)
			return
		}

		if detail.Coupon.AllowReuse {
			err = db.Get(&couponCode, sqlredeem["bygroup"], detail.Coupon.ID)
			// spew.Dump(couponCode)
			if err != nil {
				log.Println("[ERROR]", err)
				return
			}
		}

	}

	if code != "" {
		err = db.Get(&couponCode, sqlredeem["bycode"], code)
		if err != nil {
			log.Println("[ERROR]", err)
			return
		}
		err = db.Get(&detail.Coupon, sqlredeem["detail"], couponCode.GroupID)
		if err != nil {
			log.Println("[ERROR]", err)
			return
		}
	}

	if detail.Coupon.AllowReuse && couponCode.ID != 0 {
		// allow reuse, it's global code
		err = db.Select(&detail.Items, sqlredeem["reusecode"], couponCode.Code)
		if err != nil {
			log.Println("[ERROR]", err)
		}

	} else {
		// spew.Dump(couponCode)
		err = db.Select(&detail.Items, sqlredeem["codebygroup"], detail.Coupon.ID)
		if err != nil {
			log.Println("[ERROR]", err)
		}
	}
	return
}

// GetCouponGroups get groups data for console
func GetCouponGroups() (groups []DBCouponGroup, err error) {

	db := kkapp.App.DbRedeem.Slave()
	err = db.Select(&groups, sqlredeem["getcoupongroups"])
	if err != nil {
		log.Println("[ERROR] - GetCouponGroups failed.", err)
		return groups, err
	}

	return groups, err
}

// GetCouponCode get coupon code by id
func GetCouponCode(id string) (couponCode CouponCode, err error) {
	db := kkapp.App.DbRedeem.Slave()
	err = db.Get(&couponCode, sqlredeem["byid"], id)
	if err != nil {
		log.Println("[ERROR] - GetCouponCode failed.", err)
	}
	return couponCode, err
}

// RevokeCouponCode revoke coupon code for console
func RevokeCouponCode(id string) error {
	var err error
	db := kkapp.App.DbRedeem.Master()
	_, err = db.Exec(sqlredeem["revoke_coupon_code"], id)
	if err != nil {
		log.Println("[ERROR] - RevokeCouponCode failed.", err)
	}
	return err
}

// ActiveCouponCode active coupon code for console
func ActiveCouponCode(id string) error {
	var err error
	db := kkapp.App.DbRedeem.Master()
	_, err = db.Exec(sqlredeem["active_coupon_code"], id)
	if err != nil {
		log.Println("[ERROR] - ActiveCouponCode failed.", err)
	}
	return err
}

// RevokeCouponCodesByGroupID revoke all coupon code in coupon group
func RevokeCouponCodesByGroupID(id string) error {
	var err error
	db := kkapp.App.DbRedeem.Master()
	_, err = db.Exec(sqlredeem["revoke_coupon_codes_by_group_id"], id)
	if err != nil {
		log.Println("[ERROR] - RevokeCouponCodesByGroupID failed.", err)
	}
	return err
}

// ActiveCouponCodesByGroupID active all coupon code in coupon group
func ActiveCouponCodesByGroupID(id string) error {
	var err error
	db := kkapp.App.DbRedeem.Master()
	_, err = db.Exec(sqlredeem["active_coupon_codes_by_group_id"], id)
	if err != nil {
		log.Println("[ERROR] - ActiveCouponCodesByGroupID failed.", err)
	}
	return err
}
