package dbmeta

import (
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"math/rand"
	"strconv"
	"strings"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/lib/pq"
	"gopkg.in/guregu/null.v3"
)

var (
	sqlseries = map[string]string{
		"titleid": `SELECT * from meta_series WHERE title_id = $1 ORDER BY id;`,
		"search":  `SELECT id, name, title_id, meta from meta_series WHERE id LIKE ANY($1) ORDER BY id;`,
		// "ids": `SELECT id, name, meta->>'cover' as cover from meta_title WHERE id = ANY($1);`,
	}

	avodFreeSeriesHints = []string{
		"想要更好的畫質？升級VIP享受高畫質",
		"螢幕太小？升級VIP投放到大螢幕",
	}
)

// Series struct represent series in api
type Series struct {
	AvodEpisodeHint    string         `json:"avod_episode_hint"`
	ID                 string         `json:"id"`
	UnitedID           null.String    `json:"united_id"`
	UnitedName         null.String    `json:"united_name"`
	Title              string         `json:"title"`
	Paytype            string         `json:"paytype,omitempty"`
	SubtitleNotsync    bool           `json:"subtitle_notsync,omitempty"`
	Episodes           []*EpisodeMeta `json:"episodes"`
	Summary            string         `json:"summary,omitempty"`
	IsContainingAvod   bool           `json:"is_containing_avod,omitempty"`
	Casts              []string       `json:"casts,omitempty"`
	Directors          []string       `json:"directors,omitempty"`
	Writers            []string       `json:"writers,omitempty"`
	Producers          []string       `json:"producers,omitempty"`
	ContentProvider    string         `json:"content_provider,omitempty"`
	Copyright          string         `json:"copyright,omitempty"`
	AudioTrackLanguage string         `json:"audio_track_lang,omitempty"`

	isWholeSeriesFree bool
	isFreeTrail       bool
	childLock         bool
	avodEpisodeRange  [2]int
	countEpisode      int
	countAvod         int
	episodeTitles     []string
}

// SeriesMeta table meta_series.meta
type SeriesMeta struct {
	Available              string `json:"available"`
	AvodRoyaltyRate        string `json:"avod_royalty_rate"`
	BusinessType           string `json:"business_type"`
	Casts                  string `json:"casts"`
	ContentAgent           string `json:"content_agent"`
	ContentProvider        string `json:"content_provider"`
	Copyright              string `json:"copyright"`
	Country                string `json:"country"`
	Currency               string `json:"currency"`
	Description            string `json:"description"`
	Directors              string `json:"directors"`
	EndYear                string `json:"end_year"`
	EpisodeFormat          string `json:"episode_format"`
	Episodes               string `json:"episodes"`
	FreeTrial              string `json:"free_trial"`
	Genres                 string `json:"genres"`
	HasSubtitles           string `json:"has_subtitles"`
	IsEnding               string `json:"is_ending"`
	ChildLock              string `json:"child_lock"`
	Paytype                string `json:"paytype"`
	SubtitleNotsync        string `json:"subtitle_notsync"`
	LicenseEnd             string `json:"license_end"`
	LicenseStart           string `json:"license_start"`
	LicensedRight          string `json:"licensed_right"`
	MinimumGuarantee       string `json:"minimum_guarantee"`
	OfflineMode            string `json:"offline_mode"`
	PlayZone               string `json:"play_zone"`
	PlayZoneByEpisodes     string `json:"play_zone_by_episodes"`
	RoamingMode            string `json:"roaming_mode"`
	SeriesID               string `json:"series_id"`
	SeriesName             string `json:"series_name"`
	StartYear              string `json:"start_year"`
	SublicensingRecoupment string `json:"sublicensing_recoupment"`
	Tags                   string `json:"tags"`
	Themes                 string `json:"themes"`
	TitleID                string `json:"title_id"`
	TitleName              string `json:"title_name"`
	TitleNameOrig          string `json:"title_name_orig"`
	TitleType              string `json:"title_type"`
	UpdatedAt              string `json:"updated_at"`
	Writers                string `json:"writers"`
	Producers              string `json:"producers"`
}

// SeriesRow strut for meta_series
type SeriesRow struct {
	ID                 string      `db:"id" json:"id"` // serier_id
	Name               string      `db:"name" json:"name"`
	TitleID            string      `db:"title_id" json:"title_id"`
	MetaStr            null.String `db:"meta" json:"meta_str"`
	Meta               SeriesMeta  `db:"null" json:"meta"`
	UnitedID           null.String `db:"united_id" json:"united_id"`
	UnitedName         null.String `db:"united_name" json:"united_name"`
	AudioTrackLanguage null.String `db:"audio_track_lang" json:"audio_track_language"`

	EpisodeCount  int
	Series        *Series  `json:"series,omitempty"`
	EpisodeTitles []string `json:"episode_titles,omitempty"`
}

// Parse parse json data
func (i *SeriesRow) Parse() {
	// get meta from jsonb
	if i.MetaStr.Valid {
		err := json.Unmarshal([]byte(i.MetaStr.String), &i.Meta)

		if err != nil {
			log.Println("[ERROR]", err)
		}
	}

	// count the episode count that avaliable
	if i.Meta.Episodes != "" {
		var episodeFormat string

		if strings.Contains(i.Meta.EpisodeFormat, "%d") {
			episodeFormat = i.Meta.EpisodeFormat
		} else {
			episodeFormat = "%d"
		}

		for _, part := range strings.Split(i.Meta.Episodes, ";") {
			part = strings.TrimSpace(part)

			if strings.Contains(part, "-") {
				partSlice := strings.SplitN(part, "-", 2)
				startStr := partSlice[0]
				endStr := partSlice[1]
				startN, startErr := strconv.Atoi(startStr)
				endN, endErr := strconv.Atoi(endStr)
				if endErr != nil || startErr != nil {
					log.Println("[ERROR]", startErr, endErr)
					return
				}

				for j := startN; j < endN+1; j++ {
					i.EpisodeTitles = append(i.EpisodeTitles, fmt.Sprintf(episodeFormat, j))
				}
			} else if partN, err := strconv.Atoi(part); err == nil {
				i.EpisodeTitles = append(i.EpisodeTitles, fmt.Sprintf(episodeFormat, partN))

			} else {
				i.EpisodeTitles = append(i.EpisodeTitles, strings.TrimSpace(part))
			}
		}
		i.EpisodeCount = len(i.EpisodeTitles)
	}

	// fetch and validate episode

}

// ToSeries get the episode from series_id filter out not validated episodes
func (i *SeriesRow) ToSeries() (series *Series, err error) {
	var validatedEpisodes []*EpisodeMeta
	// prepare all episodes
	i.ToSeriesCMS()

	if i.Series == nil {
		err = errors.New("No series avaliable")
		return
	}

	// remove not validated episode
	// or episode id bigger than expect
	var avodCount int
	for _, ep := range i.Series.Episodes {
		// 00000338010001 length 14
		if ep.IsValidated && len(ep.ID) == 14 {
			countID, _ := strconv.Atoi(ep.ID[10:14])
			// use our own min max range to count avod
			validatedEpisodes = append(validatedEpisodes, ep)

			if ep.PlayZone {
				avodCount++
				if avodCount == 1 {
					// first time got avod episode
					i.Series.avodEpisodeRange[0] = countID
				} else if countID == i.Series.avodEpisodeRange[0]+1 || countID == i.Series.avodEpisodeRange[1]+1 {
					// countID oontinue from last avod episode id;
					// google sheet (series 副本) play_zone_by_episodes , AVOD 集數符合連續的定義 ex: 1-3 或 2-4 等
					i.Series.avodEpisodeRange[1] = countID
				}
				// isContainingAvod
				i.Series.IsContainingAvod = true
			}
		}
	}

	// assign the validatedEpisodes
	i.Series.Episodes = validatedEpisodes
	if avodCount > 0 && avodCount == len(i.Series.Episodes) {
		i.Series.isWholeSeriesFree = true
	}

	rand.Seed(time.Now().UnixNano())
	if i.Series.isWholeSeriesFree {
		i.Series.AvodEpisodeHint = avodFreeSeriesHints[rand.Intn(len(avodFreeSeriesHints))]
	} else if i.Series.avodEpisodeRange[0] != 0 && i.Series.avodEpisodeRange[1] == 0 {
		// one eposide free
		i.Series.AvodEpisodeHint = fmt.Sprintf("第%d集免費看，升級觀看完整集數", i.Series.avodEpisodeRange[0])
	} else if i.Series.avodEpisodeRange[0] != 0 && i.Series.avodEpisodeRange[1] != 0 {
		// one more eposide free
		i.Series.AvodEpisodeHint = fmt.Sprintf("第%d-%d集免費看，升級觀看完整集數",
			i.Series.avodEpisodeRange[0],
			i.Series.avodEpisodeRange[1])
	}

	i.Series.countEpisode = len(i.Series.Episodes)
	i.Series.countAvod = avodCount
	if i.Meta.Casts != "" {
		i.Series.Casts = strings.Split(i.Meta.Casts, ";")
	}
	if i.Meta.Directors != "" {
		i.Series.Directors = strings.Split(i.Meta.Directors, ";")
	}
	if i.Meta.Writers != "" {
		i.Series.Writers = strings.Split(i.Meta.Writers, ";")
	}
	if i.Meta.Producers != "" {
		i.Series.Producers = strings.Split(i.Meta.Producers, ";")
	}
	i.Series.ContentProvider = i.Meta.ContentProvider
	i.Series.Copyright = i.Meta.Copyright
	i.Series.Summary = i.Meta.Description
	i.Series.AudioTrackLanguage = i.AudioTrackLanguage.ValueOrZero()

	return i.Series, nil
}

// ToSeriesCMS get the episode from series_id with all episodes
func (i *SeriesRow) ToSeriesCMS() (series *Series, err error) {
	var episodes []*Episode
	// map for EpisideTitle to EpisodeID
	episodeTitleMap := make(map[string]int)
	// map for EpisideId is for playzone
	playZoneMap := make(map[int]bool)
	episodeFmt := i.Meta.EpisodeFormat
	if !strings.Contains(episodeFmt, "%d") {
		episodeFmt = "%d"
	}

	series = new(Series)
	series.ID = i.ID
	series.Title = i.Name
	series.UnitedID = i.UnitedID
	series.UnitedName = i.UnitedName

	maxEpisodeID := i.EpisodeCount

	episodes, err = NewEpisodeBySeriesID(series.ID)
	if err != nil {
		return
	}
	// refine the play_zone available
	for idx, val := range i.EpisodeTitles {
		// idx for index in slice (start from zero, +1 for mapping episodeID)
		episodeTitleMap[val] = idx + 1
	}

	if i.Meta.FreeTrial == "1" {
		series.isFreeTrail = true
	}

	if i.Meta.ChildLock == "1" {
		series.childLock = true
	}

	if i.Meta.Paytype != "" {
		series.Paytype = i.Meta.Paytype
	}
	if i.Meta.SubtitleNotsync == "1" {
		series.SubtitleNotsync = true
	}

	if i.Meta.PlayZone == "1" {
		if i.Meta.PlayZoneByEpisodes != "" {
			for _, subStr := range strings.Split(i.Meta.PlayZoneByEpisodes, ";") {
				// had predefined avod episodes
				// 1 or 1-3 or 特別篇;1-3 or 上集
				if strings.Contains(subStr, "-") {
					// 1-3
					var err1, err2 error
					var minAvodEpisodeID, maxAvodEpisodeID int
					playzones := strings.Split(subStr, "-")
					minAvodEpisodeID, err1 = strconv.Atoi(playzones[0])
					maxAvodEpisodeID, err2 = strconv.Atoi(playzones[1])
					if err1 == nil && err2 == nil {
						for epID := minAvodEpisodeID; epID <= maxAvodEpisodeID; epID++ {
							epTitle := fmt.Sprintf(episodeFmt, epID)
							if playzoneEpisodeID, ok := episodeTitleMap[epTitle]; ok {
								playZoneMap[playzoneEpisodeID] = true
							}
						}
					} else {
						log.Println("[ERROR] playzone_by_episodes", i.ID, i.Meta.PlayZoneByEpisodes)
					}

				} else {
					// 1 or 特別篇
					epID, errAtoi := strconv.Atoi(subStr)
					if errAtoi == nil {
						// 1
						playZoneMap[epID] = true
					} else {
						// maybe 特別篇，let's give it a try
						if playzoneEpisodeID, ok := episodeTitleMap[subStr]; ok {
							playZoneMap[playzoneEpisodeID] = true
						} else {
							log.Println("[ERROR] playzone_by_episodes not found", i.ID, i.Meta.PlayZoneByEpisodes)
						}
					}
				}
			}
		} else {
			// empty pre define, all set to playzone episode
			for _, val := range episodeTitleMap {
				playZoneMap[val] = true
			}
		}
	}
	// remove episode id bigger than expect
	for _, i := range episodes {
		// 00000338010001 length 14
		// validate ID length
		if len(i.Meta.ID) == 14 {
			countID, _ := strconv.Atoi(i.Meta.ID[10:14])
			if countID <= maxEpisodeID {
				// use our own min max range to count avod
				if _, ok := playZoneMap[countID]; ok && i.Meta.FreeTrial {
					i.Meta.PlayZone = true
					i.Meta.IsAvod = true
				}
				series.Episodes = append(series.Episodes, &i.Meta)
			}
		}
	}

	// copy to series for title last update info
	series.episodeTitles = i.EpisodeTitles

	i.Series = series
	return
}

// NewSeriesByTitleID get the api series for titleID
func NewSeriesByTitleID(titleID string) (series []*Series, licenseDates []string, err error) {
	var items []SeriesRow
	db := kkapp.App.DbMeta.Slave().Unsafe()

	err = db.Select(&items, sqlseries["titleid"], titleID)
	if err != nil {
		log.Println("[ERROR]", err)
	}

	for idx := range items {
		items[idx].Parse()
		seriesitem, err := items[idx].ToSeries()
		if err != nil {
			log.Println("[ERROR]", err)
			continue
		}
		licenseDates = append(licenseDates, items[idx].Meta.LicenseStart, items[idx].Meta.LicenseEnd)

		if seriesitem.Episodes != nil && len(seriesitem.Episodes) > 0 {
			series = append(series, seriesitem)
		}
	}
	return
}

// NewSeriesByQuery get the api series for dashboard console
func NewSeriesByQuery(q string) (series []*SeriesRow, err error) {
	db := kkapp.App.DbMeta.Slave()

	likeID := []string{}
	for _, w := range strings.Split(q, " ") {
		if _, err = strconv.Atoi(w); err == nil {
			likeID = append(likeID, strings.TrimSpace(w)+"%")
		}
	}

	log.Println(likeID)
	err = db.Select(&series, sqlseries["search"], pq.Array(likeID))
	if err != nil {
		log.Println("[ERROR]", err)
	}

	for idx := range series {
		series[idx].Parse()
		_, err = series[idx].ToSeriesCMS()
		if err != nil {
			log.Println("[ERROR]", err)
		}
	}
	return
}
