package dbmeta

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"log"
	"math/rand"
	"strings"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"gopkg.in/guregu/null.v3"
)

var (
	featuredHashKey = "pages:v1:featured:sharon:hash"

	CacheHighlightKey = "publish:hl"       // meta redis cache key "cacher:publish:hl:json"
	CacheChoiceKey    = "publish:choice"   // meta redis cache key "cacher:publish:choice:json"
	CacheHeadlineKey  = "publish:headline" // meta redis cache key "cacher:publish:headline:json"

	sqltl = map[string]string{

		// to fetch titlelists with list_type `choice`
		"published_choice_titlelist": `SELECT mt.*
			FROM meta_titlelist mt
			WHERE
			mt.enabled = true AND mt.visible_since <= now() AND mt.visible_until > now() AND
			mt.meta #> '{collections}' ? 'genre:featured' AND
			(mt.meta -> 'pinned' IS NULL OR mt.meta ->> 'pinned' = ANY(ARRAY['airing', 'new_finale'])) AND
			mt.list_type = 'choice' ORDER BY "order";`,

		//For the legacy client app featured page, only query the headline added to genre:featured collections
		"published_headline_titlelist": `SELECT mt.*, ml.name as title_name, ml.meta ->> 'title_type' as title_type from meta_titlelist mt
			 LEFT JOIN meta_title ml ON mt.title_id = ml.id WHERE
			 mt.enabled = true AND mt.visible_since <= now() AND mt.visible_until > now() AND
			 mt.meta #> '{collections}' ? 'genre:featured' AND
			 mt.list_type = ANY(ARRAY['link', 'title']) ORDER BY "order";`,

		//Only the genre:featured page has highlight titlelist
		"published_highlight_titlelist": `
				SELECT mt.*
				from meta_titlelist mt
				 WHERE
				 mt.enabled = true AND mt.visible_since <= now() AND mt.visible_until > now() AND
				 mt.meta #> '{collections}' ? 'genre:featured' AND
				 mt.list_type = 'highlight' ORDER BY "order";`,
	}
)

// internal use for redis
type editorRecommend struct {
	ShareId string   `json:"share_id"`
	Type    string   `json:"type"`
	Title   string   `json:"title"`
	Items   []string `json:"items"`
}

// TitleList struct for meta_titlelist.meta
type TitleListMeta struct {
	TitleID            []string      `json:"title_id,omitempty"`
	ShareId            string        `json:"share_id,omitempty"`
	Description        string        `json:"description,omitempty"`
	BackgroundImageUrl string        `json:"background_image_url,omitempty"`
	Copyright          string        `json:"copyright,omitempty"`
	OGDescription      string        `json:"og_description,omitempty"`
	OGImage            string        `json:"og_image,omitempty"`
	Collections        []string      `json:"collections,omitempty"`
	Pinned             string        `json:"pinned,omitempty"`
	Roles              []string      `json:"roles,omitempty"`
	Platforms          []string      `json:"platforms,omitempty"` // web|app|tv
	Comments           []MetaComment `json:"comments,omitempty"`
	Editor             *Editor       `json:"editor,omitempty"`
	VideoUrl           string        `json:"video_url,omitempty"`
}

type MetaComment struct {
	TitleID string `json:"title_id"`
	Comment string `json:"comment"`
}

type Editor struct {
	Name        string `json:"name"`
	Avatar      string `json:"avatar"`
	Description string `json:"description"`
}

// Scan interface for db
func (t *TitleListMeta) Scan(src interface{}) error {
	byteValue, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("metas field must be a []byte, got %T instead", src)
	}
	return json.Unmarshal(byteValue, t)

}

// Value interface for db
func (t TitleListMeta) Value() (driver.Value, error) {
	return json.Marshal(t)
}

// TitleList struct for table meta_titlelist
// would supoort ListType link, title, choice, headline, highlight
type TitleList struct {
	ID                     int64          `db:"id" json:"id,omitempty"`
	Caption                string         `db:"caption" json:"title,omitempty"`
	Summary                string         `db:"summary" json:"summary,omitempty"`
	SourceImage            string         `db:"source_image" json:"source_image,omitempty"`
	ResizedImages          null.String    `db:"resized_images" json:"-"`
	Enabled                *bool          `db:"enabled" json:"enabled,omitempty"`
	Order                  int64          `db:"order" json:"order,omitempty"`
	CreatedAt              time.Time      `db:"created_at" json:"-"`
	UpdatedAt              time.Time      `db:"updated_at" json:"-"`
	TitleID                null.String    `db:"title_id" json:"title_id,omitempty"`
	TitleName              null.String    `db:"title_name" json:"title_name,omitempty"`
	VisibleSince           *time.Time     `db:"visible_since" json:"visible_since,omitempty"`
	VisibleUntil           *time.Time     `db:"visible_until" json:"visible_until,omitempty"`
	Topic                  string         `db:"topic" json:"topic,omitempty"`
	URI                    string         `db:"uri" json:"uri,omitempty"`
	URL                    string         `db:"url" json:"url,omitempty"`
	ListType               string         `db:"list_type" json:"list_type,omitempty"`
	TrailerAutoplayEnabled bool           `db:"trailer_autoplay_enabled" json:"trailer_autoplay_enabled"`
	TrailerEpisodeID       null.String    `db:"trailer_episode_id" json:"trailer_episode_id,omitempty"`
	DominantColor          null.String    `db:"dominant_color" json:"dominant_color,omitempty"`
	Meta                   *TitleListMeta `db:"meta" json:"meta,omitempty"`

	// attribute for processing
	TitleType null.String       `db:"title_type" json:"-"`
	Type      string            `json:"type,omitempty"`
	Trailer   *redisEpisode     `json:"trailer,omitempty"`
	Image     string            `json:"image,omitempty"`
	ImageMap  map[string]string `json:"-"`
}

// Parse parse
func (i *TitleList) Parse() {
	// image
	if i.ResizedImages.Valid {
		err := json.Unmarshal([]byte(i.ResizedImages.String), &i.ImageMap)
		if err != nil {
			log.Println("[ERROR]", i.ID, err)
		}

		// handle highlight origibal aspect ratio image
		var imageURL string
		if i.ListType == "highlight" {
			imageURL = i.ImageMap["hl"]
		} else {
			imageURL = i.ImageMap["xs"]
		}

		// sometime tests enviroment could have prod image url
		if imageURL != "" {
			// do have image
			if strings.Contains(imageURL, "kktv-prod-images") {
				// prod image bucket
				i.Image = strings.Replace(imageURL, "s3://kktv-prod-images", "https://images.kktv.com.tw", 1)
			} else {
				// test image bucket
				i.Image = strings.Replace(imageURL, "s3://kktv-test-images", "https://test-images.kktv.com.tw", 1)
			}
		}
	}

	// trailer
	if i.TitleID.Valid {
		extraID := fmt.Sprintf("%sextra", i.TitleID.String)
		extraTitle := new(MetaExtra)
		key := fmt.Sprintf("meta:v1:title-detail:%s:json", extraID)
		pool := kkapp.App.RedisMeta.Slave()
		jsonBytes, err := pool.Cmd("GET", key).Bytes()
		if err != nil {
			log.Println("[ERROR]", err)
			return
		}
		err = json.Unmarshal(jsonBytes, extraTitle)
		log.Println(string(jsonBytes))
		if err != nil {
			log.Println("[ERROR]", err)
			return
		}

		// only return trailer when trailer_autoplay_enabled is true and there're trailers in this title
		if i.TrailerAutoplayEnabled && len(extraTitle.Series) > 0 {
			series := extraTitle.Series[0]
			if i.TrailerEpisodeID.Valid {
				// editor choice trailer episode ID
				for _, trailer := range series.Episodes {
					if trailer.ID == i.TrailerEpisodeID.String {
						i.Trailer = &trailer
						break
					}
				}
			} else {
				trailerIdx := rand.Intn(len(series.Episodes))
				i.Trailer = &series.Episodes[trailerIdx]
			}
		}
	}

	//
}

// Clean export to api for headline
// the headline use type instead of list_type
// and remove attrubute not needed
func (i *TitleList) Clean() {
	i.Type = i.ListType
	i.ListType = ""
	i.ID = 0
	i.Order = 0
	i.SourceImage = ""
	i.Enabled = nil
	i.VisibleSince = nil
	i.VisibleUntil = nil
	// i.Meta = nil
}

func (i *TitleList) GetRoles() []string {
	if i.Meta == nil {
		return nil
	}
	return i.Meta.Roles
}

func (i *TitleList) GetListType() string {
	if i.ListType == "" {
		return i.Type
	}
	return i.ListType
}

func GetPublishHighlight() (titlelist []*TitleList, err error) {
	db := kkapp.App.DbMeta.Slave()
	err = db.Select(&titlelist, sqltl["published_highlight_titlelist"])
	if err != nil {
		return
	}
	for i := range titlelist {
		titlelist[i].Parse()
	}
	return
}

func GetPublishHeadlines() (titlelist []*TitleList, err error) {
	db := kkapp.App.DbMeta.Slave()
	err = db.Select(&titlelist, sqltl["published_headline_titlelist"])
	if err != nil {
		return
	}
	for i := range titlelist {
		titlelist[i].Parse()
	}
	return
}

// PublishEditorRecommendation write to redis key
func PublishEditorRecommendation() (err error) {
	var titlelist []TitleList
	var mainTitles, newFinale editorRecommend
	var subTitles []editorRecommend

	db := kkapp.App.DbMeta.Slave()

	err = db.Select(&titlelist, sqltl["published_choice_titlelist"])
	if err != nil {
		return
	}

	for i := range titlelist {
		titlelist[i].Parse()
	}

	// compose
	for _, item := range titlelist {
		if item.Meta.Pinned == "airing" {
			// pick the airing pinned title list for main titlelist
			mainTitles.Type = "api.list.big_view"
			mainTitles.Items = item.Meta.TitleID
			mainTitles.Title = item.Caption
			mainTitles.ShareId = item.Meta.ShareId
		} else if item.Meta.Pinned == "new_finale" {
			newFinale.Type = "api.list.cover_view"
			newFinale.Items = item.Meta.TitleID
			newFinale.Title = item.Caption
			newFinale.ShareId = item.Meta.ShareId
		} else {
			// the others
			var subItem editorRecommend
			subItem.Type = "api.list.cover_view"
			subItem.Items = item.Meta.TitleID
			subItem.Title = item.Caption
			subItem.ShareId = item.Meta.ShareId
			subTitles = append(subTitles, subItem)
		}
	}

	// "editor_main_recommendation",
	if mainTitles.Type != "" {
		log.Println("[INFO] write to hash pages:v1:featured:sharon:hash editor_main_recommendation")
		redisPool := kkapp.App.RedisMeta.Master()
		jsonBytes, _ := json.Marshal(mainTitles)
		if err = redisPool.Cmd("HSET", featuredHashKey, "editor_main_recommendation", jsonBytes).Err; err != nil {
			return err
		}
		if err != nil {
			log.Println("[ERROR]", err)
			return err
		}
	}

	// "new_finale", for 全集新上架片單
	if newFinale.Type != "" {
		log.Println("[INFO] write to hash pages:v1:featured:sharon:hash new_finale")
		redisPool := kkapp.App.RedisMeta.Master()
		jsonBytes, _ := json.Marshal(newFinale)
		if err = redisPool.Cmd("HSET", featuredHashKey, "new_finale", jsonBytes).Err; err != nil {
			return err
		}
		if err != nil {
			log.Println("[ERROR]", err)
			return err
		}
	}

	// "editor_sub_recommendations"
	if len(subTitles) > 0 {
		log.Println("[INFO] write to hash pages:v1:featured:sharon:hash editor_sub_recommendations")
		redisPool := kkapp.App.RedisMeta.Master()
		jsonBytes, _ := json.Marshal(subTitles)
		if err = redisPool.Cmd("HSET", featuredHashKey, "editor_sub_recommendations", jsonBytes).Err; err != nil {
			return err
		}
	}
	return err
}
