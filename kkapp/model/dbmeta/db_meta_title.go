package dbmeta

import (
	"bytes"
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"reflect"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/meta"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/wrapper/paidplan"
	"github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/model/authority"
	"github.com/KKTV/kktv-api-v3/pkg/model/cachemeta"
	"github.com/buger/jsonparser"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/lib/pq"
	"gopkg.in/guregu/null.v3"
)

var (
	sqlmetatitle = map[string]string{
		"search":  `SELECT id, name, editor_comments, meta, release_info from meta_title WHERE id LIKE ANY($1) OR name LIKE ANY($2);`,
		"ids":     `SELECT id, name, meta->>'cover' as cover from meta_title WHERE id = ANY($1);`,
		"all_ids": `SELECT id, name, meta ->> 'cover' AS cover, CONCAT(id, ' ', name) AS label FROM meta_title ORDER BY id DESC`,
		"idlike":  `SELECT id, name, meta->>'cover' as cover from meta_title WHERE id LIKE ANY($1);`,
		"byid":    `SELECT id, name, editor_comments, meta, release_info, united_name, same_as FROM meta_title WHERE id = $1;`,
		"inid":    `SELECT id, name, editor_comments, meta FROM meta_title WHERE id = ANY($1) ORDER BY array_position($1::varchar[], id);`,
		"all":     `SELECT id, name, editor_comments, meta FROM meta_title ORDER BY id;`,
	}

	// 30*24*60*60
	day30InSecond int64 = 2592000
	// 72*60*60
	hour72InSecond int64 = 259200

	// regular expression for 第?話
	reCartoonEpisode = regexp.MustCompile(`第([ |\d]{1,})話`)

	keyTitleFmt     = "meta:v1:title-detail:%s:json"
	keyTitleHashFmt = "meta:v2:title-detail:%s:hash"
	keyTitleGzipFmt = "meta:v1:title-detail:%s:gzip"

	// we store whole meta in "meta:v2:title-detail:%s:hash" whole
	wholeKeyInMetaHash = "whole"

	// title detail field to redis hash
	// Currently have in hash KKTV/kktv-service-meta/meta_redis/api.py
	tdFields = []string{
		"id",
		"title",
		"title_type",
		"united_name",
		"available",
		"status",
		"is_ending",
		"is_containing_avod",
		"is_validated",
		"child_lock",
		"release_year",
		"end_year",
		"user_rating",
		"user_rating_count",
		"cover",
		"stills",
		"review",
		"release_info",
		"total_episode_counts",
		"total_series_count",
		"latest_update_info",
		"content_labels",
		"content_labels_for_expired_user",
		"content_labels_for_freetrial_user",
		"summary",
		"genres",
		"themes",
		"tags",
		"directors",
		"casts",
		"country",
		"series",
		"live_info",
		"free_trial",
		"content_labels_with_full_access",
		"content_labels_without_full_access",
		"accepted_authorities",
		"airing",
		"same_as",
	}
)

// TitleMetaStatus define different kind of title meta status in constant values
type TitleMetaStatus string

const (
	StatusComingSoon     TitleMetaStatus = "coming_soon"
	StatusLicenseValid   TitleMetaStatus = "license_valid"
	StatusLicenseExpired TitleMetaStatus = "license_expired"
)

// Val return the string value of title meta status
func (t TitleMetaStatus) String() string {
	return string(t)
}

// TitleMeta struct for title api struct
type TitleMeta struct {
	ID                            string   `json:"id"`
	UnitedName                    string   `json:"united_name,omitempty"`
	Title                         string   `json:"title"`
	TitleType                     string   `json:"title_type"`
	Copyright                     string   `json:"copyright"`
	Cover                         string   `json:"cover"`
	Stills                        []string `json:"stills"`
	ContentLabels                 []string `json:"content_labels"`
	ContentLabelsForExpiredUser   []string `json:"content_labels_for_expired_user"`
	ContentLabelsForFreeTrialUser []string `json:"content_labels_for_freetrial_user"`

	ContentLabelsWithFullAccess    []string              `json:"content_labels_with_full_access"`
	ContentLabelsWithoutFullAccess []string              `json:"content_labels_without_full_access"`
	AcceptedAuthorities            []authority.Authority `json:"accepted_authorities"`
	AiringInfo                     *cachemeta.AiringInfo `json:"airing"`

	TitleAliases        []string          `json:"title_aliases"`
	Available           bool              `json:"available"`
	IsEnding            bool              `json:"is_ending"`
	IsContainingAvod    bool              `json:"is_containing_avod"`
	ReverseDisplayOrder bool              `json:"reverse_display_order"`
	IsValidated         bool              `json:"is_validated"`
	FreeTrial           bool              `json:"free_trial"`
	ChildLock           bool              `json:"child_lock"`
	EndYear             int64             `json:"end_year"`
	ReleaseYear         int64             `json:"release_year"`
	TitleExtra          map[string]string `json:"title_extra"`
	Review              map[string]string `json:"review"`
	ReleaseInfo         string            `json:"release_info"`
	Status              string            `json:"status"`
	Summary             string            `json:"summary"`
	LatestUpdateInfo    string            `json:"latest_update_info"`
	TotalEpisodeCounts  map[string]int    `json:"total_episode_counts"`
	TotalSeriesCount    int               `json:"total_series_count"`
	Rating              int64             `json:"rating"`
	UserRatingCount     int64             `json:"user_rating_count"`
	UserRating          float64           `json:"user_rating"`
	WikiOrig            string            `json:"wiki_orig"`
	WikiZh              string            `json:"wiki_zh"`
	Ost                 *Ost              `json:"ost"`
	SameAs              *SameAs           `json:"same_as"`

	// license start for available title but have no series
	TotalLicenseStart int64 `json:"total_license_start"`
	TotalLicenseEnd   int64 `json:"total_license_end"`

	// collection for api
	Country          *CollectionItem   `json:"country"`
	ContentAgents    []*CollectionItem `json:"content_agents"`
	ContentProviders []*CollectionItem `json:"content_providers"`
	Themes           []*CollectionItem `json:"themes"`
	Genres           []*CollectionItem `json:"genres"`
	Tags             []*CollectionItem `json:"tags"`
	Directors        []*CollectionItem `json:"directors"`
	Writers          []*CollectionItem `json:"writers"`
	Producers        []*CollectionItem `json:"producers"`
	Casts            []*CollectionItem `json:"casts"`
	Series           []*Series         `json:"series,omitempty"`
	Live             []*Series         `json:"live,omitempty"`
	LiveInfo         *LiveInfo         `json:"live_info,omitempty"`
}

type LiveInfo struct {
	IsStreaming bool `json:"is_streaming"`
}

// Ost struct
type Ost struct {
	ArtistName string `json:"artist_name"`
	Image      string `json:"image"`
	Title      string `json:"title"`
	URL        string `json:"url"`
}

// SameAs struct
type SameAs struct {
	Imdb  null.String `json:"imdb"`
	Wiki  null.String `json:"wiki"`
	Other null.String `json:"other"`
}

func (s *SameAs) Scan(src any) error {
	var data []byte
	switch v := src.(type) {
	case nil:
		return nil
	case string:
		data = []byte(v)
	case []byte:
		data = v
	}
	return json.Unmarshal(data, s)
}

func (s SameAs) Value() (driver.Value, error) {
	if reflect.DeepEqual(s, SameAs{}) {
		return nil, nil
	}
	if jsonValue, err := json.Marshal(s); err != nil {
		return nil, err
	} else {
		return driver.Value(jsonValue), nil
	}
}

// LoadSeries load series data
// then you could Validate the title and count count the tag for user
func (i *TitleMeta) LoadSeries() {
	series, licenseDates, err := NewSeriesByTitleID(i.ID)
	if err == nil {
		// find if this title contain avod, automatically add 免費 theme
		// if there is no one.
		for _, item := range series {
			if item.childLock {
				i.ChildLock = true
			}
			if item.IsContainingAvod {
				var hadFree bool
				var yearForSorting int64
				for _, theme := range i.Themes {
					yearForSorting = theme.yearForSorting
					if theme.CollectionName == "免費" {
						// make sure free first
						// ci, themes := theme, append(i.Themes[0:idx], i.Themes[idx+1:]...)
						// i.Themes = append([]*CollectionItem{ci}, themes...)
						hadFree = true
						break
					}
				}
				if !hadFree {
					ci := NewCollection("theme", "免費")
					ci.titleID = i.ID
					ci.yearForSorting = yearForSorting
					ci.isEnding = i.IsEnding
					// i.Themes = append([]*CollectionItem{ci}, i.Themes...)
					i.Themes = append(i.Themes, ci)
				}
				break
			}
		}
		i.Series = series
	} else {
		log.Warn("db_meta_title: loadSeries: NewSeriesByTitleID").
			Err(err).
			Str("titleID", i.ID).
			Send()
	}

	// total license start end
	if len(licenseDates) > 0 {
		var licenseStart, licenseEnd string
		var start, end time.Time

		// put license dates in order, start from smaller date string
		sort.Strings(licenseDates)

		licenseStart = licenseDates[0]
		licenseEnd = licenseDates[len(licenseDates)-1]

		// convert license date string from taipei time zone to utc unix time
		// refer https://github.com/KKTV/kktv-lib-pymeta/blob/develop/pymeta/__init__.py#L94
		start, err = time.Parse("2006-01-02 -07", fmt.Sprintf("%s +08", licenseStart))
		if err == nil {
			i.TotalLicenseStart = start.Unix()
		}
		end, err = time.Parse("2006-01-02 15:04:05 -07", fmt.Sprintf("%s 23:59:59 +08", licenseEnd))
		if err == nil {
			i.TotalLicenseEnd = end.Unix()
		}
	}
}

// Validate title
func (i *TitleMeta) Validate() {

	// default set to true
	i.IsValidated = true

	////////////////////////////////////////
	// basic value check
	if i.Title == "" || i.Summary == "" || i.TitleType == "" || i.Cover == "" {
		i.IsValidated = false
	}
	if i.EndYear < i.ReleaseYear {
		i.IsValidated = false
	}

	if len(i.Stills) == 0 {
		i.IsValidated = false
	}

	if !i.IsValidated {
		return
	}

	////////////////////////////////////////
	// inject extra property for title meta
	// content labels and there's an display order of labels must be obey:
	// 0: coming_soon, broadcasting, expire_soon
	// 1: new_arrival
	// 2: vip
	// 3: partial_episodes_free
	// 4: dual_subtitle
	contentLabel := []string{}
	contentLabelForExpire := []string{}
	contentLabelForFreetrial := []string{}
	contentLabelWithoutFullAccess := make([]string, 0)

	// check license status
	currentTime := time.Now().Unix()
	var IsComingSoon, IsLicenseValidated bool

	// the publish time is coming OR
	// already pass the publish time but no available episode yet
	// note: TotalLicenseStart , TotalLicenseEnd populate at i.LoadSeries()
	IsComingSoon = (i.TotalLicenseStart > currentTime) ||
		(i.TotalLicenseStart <= currentTime && currentTime <= i.TotalLicenseEnd && len(i.Series) == 0)

	IsLicenseValidated = i.TotalLicenseStart <= currentTime && currentTime <= i.TotalLicenseEnd

	if IsComingSoon {
		i.Status = "coming_soon"
	} else if IsLicenseValidated {
		i.Status = "license_valid"
	} else {
		i.Status = "license_expired"
	}

	// IsValidated
	if i.Available && (i.Status == "coming_soon" || i.Status == "license_valid") {
		// not care about i.Series
		i.IsValidated = true
	} else {
		i.IsValidated = false
	}

	// 0: coming_soon
	if i.Status == "coming_soon" {
		contentLabel = append(contentLabel, StatusComingSoon.String())
		contentLabelForExpire = append(contentLabelForExpire, StatusComingSoon.String())
		contentLabelForFreetrial = append(contentLabelForFreetrial, StatusComingSoon.String())

		if i.TitleExtra != nil {
			i.LatestUpdateInfo = "預告搶先看"
		} else {
			i.LatestUpdateInfo = "即將上架"
		}
	}
	if i.TitleType == "live" {
		i.LiveInfo = &LiveInfo{}
	}

	// if this title has series data, and last series has episodes
	if len(i.Series) != 0 && (i.Series[len(i.Series)-1]).Episodes != nil {
		var firstEpisode, lastEpisode *EpisodeMeta
		now := time.Now().Unix()

		firstEpisode = i.Series[0].Episodes[0]
		sI := len(i.Series) - 1
		epI := len(i.Series[sI].Episodes) - 1
		lastEpisode = i.Series[sI].Episodes[epI]

		// 0: expire_soon
		if (firstEpisode.LicenseEnd - now) < day30InSecond {
			contentLabel = append(contentLabel, "expire_soon")
			contentLabelForExpire = append(contentLabelForExpire, "expire_soon")
			contentLabelForFreetrial = append(contentLabelForFreetrial, "expire_soon")
		}

		// 1: new_arrival
		// last series index && last eposide index
		if (now - lastEpisode.PublishTime) < hour72InSecond {
			contentLabel = append(contentLabel, "new_arrival")
			contentLabelForExpire = append(contentLabelForExpire, "new_arrival")
			contentLabelForFreetrial = append(contentLabelForFreetrial, "new_arrival")
		}

		// iterate through each series to get totalEpisodeCounts and avod counts
		var totalCount, avodCount int
		totalEpisodeCounts := make(map[string]int)
		i.FreeTrial = true
		for _, item := range i.Series {
			totalCount += item.countEpisode
			avodCount += item.countAvod
			totalEpisodeCounts[item.ID] = len(item.Episodes)
			if !item.isFreeTrail {
				i.FreeTrial = false
			}
		}
		i.TotalEpisodeCounts = totalEpisodeCounts

		if avodCount > 0 {
			i.IsContainingAvod = true
		}

		// generate content labels for 2: vip and 3: partial_episodes_free
		if !i.FreeTrial {
			contentLabelForFreetrial = append(contentLabelForFreetrial, "vip")
		}
		if avodCount < totalCount {
			if avodCount > 0 {
				contentLabelForExpire = append(contentLabelForExpire, "partial_episodes_free")
				contentLabelWithoutFullAccess = append(contentLabelWithoutFullAccess, "partial_episodes_free")
			} else {
				// avodCount == 0
				contentLabelForExpire = append(contentLabelForExpire, "vip")
				contentLabelWithoutFullAccess = append(contentLabelWithoutFullAccess, "vip")
			}
		}

		// 4: dual_subtitle
		if len(firstEpisode.SubtitleMap) > 1 {
			contentLabel = append(contentLabel, "dual_subtitle")
			contentLabelForExpire = append(contentLabelForExpire, "dual_subtitle")
			contentLabelForFreetrial = append(contentLabelForFreetrial, "dual_subtitle")
		}

		// Dealing with last_update_info
		// 取得目前最後一季最後一集的 Episode Title
		var lastEpisodeTitle string
		lastSeriesCount, _ := strconv.Atoi(lastEpisode.ID[8:10])
		lastEpisodeCount, _ := strconv.Atoi(lastEpisode.ID[10:14])
		i.TotalSeriesCount = lastSeriesCount

		if len(i.Series[sI].episodeTitles) > 0 {
			lastTitleIndex := len(i.Series[sI].episodeTitles) - 1
			lastEpisodeTitle = i.Series[sI].episodeTitles[lastTitleIndex]
		}

		if i.TitleType == "film" {
			// count the minute"
			i.LatestUpdateInfo = fmt.Sprintf("%d分鐘", int(firstEpisode.Duration)/60)
		}

		if i.IsEnding {
			switch i.TitleType {
			case "series":
				// get the lastEpisode series
				i.LatestUpdateInfo = fmt.Sprintf("共%d季", i.TotalSeriesCount)
			case "miniseries":
				i.LatestUpdateInfo = fmt.Sprintf("共%d集", totalCount)
			}
		} else {
			switch i.TitleType {
			case "series":
				// cook different update info for cartoon,
				if len(reCartoonEpisode.FindStringSubmatch(lastEpisodeTitle)) == 2 {
					// found 第?話
					cartoonCountStr := reCartoonEpisode.FindStringSubmatch(lastEpisodeTitle)[1]
					i.LatestUpdateInfo = fmt.Sprintf("更新至第%d季第%s話", i.TotalSeriesCount, cartoonCountStr)
				} else {
					i.LatestUpdateInfo = fmt.Sprintf("更新至第%d季第%d集", i.TotalSeriesCount, lastEpisodeCount)
				}
			case "miniseries":
				if len(reCartoonEpisode.FindStringSubmatch(lastEpisodeTitle)) == 2 {
					// found 第?話
					cartoonCountStr := reCartoonEpisode.FindStringSubmatch(lastEpisodeTitle)[1]
					i.LatestUpdateInfo = fmt.Sprintf("更新至第%s話", cartoonCountStr)
				} else {
					i.LatestUpdateInfo = fmt.Sprintf("更新至第%d集", totalCount)
				}
			case "live":
				i.LatestUpdateInfo = "現正直播中"
				i.LiveInfo.IsStreaming = true
			}
		}
	}
	i.ContentLabels = contentLabel
	i.ContentLabelsForExpiredUser = contentLabelForExpire
	i.ContentLabelsForFreeTrialUser = contentLabelForFreetrial

	i.ContentLabelsWithFullAccess = contentLabel
	i.ContentLabelsWithoutFullAccess = append(contentLabelWithoutFullAccess, contentLabel...)
	// [BEGIN] TODO required authorities should comes from the belong plans
	acceptedAuthorities := []authority.Authority{
		authority.PremiumPlay,
	}
	if i.IsContainingAvod {
		acceptedAuthorities = append(acceptedAuthorities, authority.FreemiumPlay)
	}
	if i.FreeTrial {
		acceptedAuthorities = append(acceptedAuthorities, authority.FreeTrialPlay)
	}
	// [END]
	i.AcceptedAuthorities = acceptedAuthorities
}

func patchPaidPlanAuthorities(i *TitleMeta, repo paidplan.PlanAuthMapRepository) {
	titleAcceptedAuthorities := make([]authority.Authority, 0)

	for _, genre := range i.Genres {
		if genre == nil {
			continue
		}
		planAuthorities, err := repo.ListAuthoritiesByGenre(genre.CollectionName)
		if err != nil {
			log.Warn("db_meta_title: title2Redis: ListAuthoritiesByGenre").Err(err).
				Str("title_id", i.ID).Str("genre", genre.CollectionName).Send()
		}
		if len(planAuthorities) == 0 {
			continue
		}
		titleAcceptedAuthorities = append(titleAcceptedAuthorities, planAuthorities...)
	}
	if len(titleAcceptedAuthorities) > 0 {
		i.AcceptedAuthorities = slice.Union(i.AcceptedAuthorities, titleAcceptedAuthorities)
	}
}

// titleMETA internal use struct for meta_title table field meta
type titleMETA struct {
	Ost *Ost `json:"ost"`
	// Casts            map[string][]string `json:"casts"`
	ContentAgents    []string `json:"content_agents"`
	ContentProviders []string `json:"content_providers"`
	Country          string   `json:"country"`
	Copyright        string   `json:"copyright"`
	Cover            string   `json:"cover"`
	Description      string   `json:"description"`
	Directors        []string `json:"directors"`
	EndYear          int64    `json:"end_year"`
	ExtraTitleID     string   `json:"extra_title_id"`
	Genres           []string `json:"genres"`
	Available        bool     `json:"available"`
	HasSeries        bool     `json:"has_series"`
	IsEnding         bool     `json:"is_ending"`
	ChildLock        bool     `json:"child_lock"`
	OrderedCasts     []struct {
		Name  string   `json:"name"`
		Roles []string `json:"roles"`
	} `json:"ordered_casts"`
	Producers           []string       `json:"producers"`
	TotalLicenseStart   int64          `json:"total_license_start"`
	TotalLicenseEnd     int64          `json:"total_license_end"`
	Rating              int64          `json:"rating"`
	ReverseDisplayOrder bool           `json:"reverse_display_order"`
	StartYear           int64          `json:"start_year"`
	Stills              []string       `json:"stills"`
	Tags                []string       `json:"tags"`
	Themes              []string       `json:"themes"`
	TitleAliases        []string       `json:"title_aliases"`
	TitleID             string         `json:"title_id"`
	TitleName           string         `json:"title_name"`
	TitleType           string         `json:"title_type"`
	TotalEpisodeCounts  map[string]int `json:"total_episode_counts"`
	// UpdatedAt from DynomaDB some is int64, some string
	// UpdatedAt       interface{} `json:"updated_at"`
	UserRating      float64  `json:"user_rating"`
	UserRatingCount int64    `json:"user_rating_count"`
	WikiOrig        string   `json:"wiki_orig"`
	WikiZh          string   `json:"wiki_zh"`
	Writers         []string `json:"writers"`
}

// Title struct
type Title struct {
	ID             string      `db:"id" json:"id"`
	Name           string      `db:"name" json:"name"`
	EditorComments string      `db:"editor_comments" json:"editor_comments"`
	ReleaseInfo    string      `db:"release_info" json:"release_info"`
	MetaStr        null.String `db:"meta" json:"meta_str"`
	Meta           titleMETA   `db:"null" json:"meta"` // null not exist, just forece the sql to lookup different column than meta store the Unmarshal json of meta_extra.meta
	SameAs         *SameAs     `db:"same_as" json:"same_as"`
	UnitedID       null.String `db:"united_id" json:"united_id"`
	UnitedName     null.String `db:"united_name" json:"united_name"`
}

// TitleHint help struct
type TitleHint struct {
	ID    string      `db:"id" json:"id"`
	Name  string      `db:"name" json:"name"`
	Cover null.String `db:"cover" json:"cover"`
	Label string      `db:"label" json:"label,omitempty"`
}

// Parse json.Unmarshal from meta field
// **IMPORTANT** Parse will return the struct for api use
// the instance itself represent the Title at db
func (i *Title) Parse() (item *TitleMeta, err error) {
	if i.MetaStr.Valid {
		err = json.Unmarshal([]byte(i.MetaStr.String), &i.Meta)
		if err != nil {
			return
		}
	}
	item = new(TitleMeta)

	item.ID = i.ID
	item.Title = strings.TrimSpace(i.Name)
	item.UnitedName = i.UnitedName.String
	review := make(map[string]string)
	if i.EditorComments != "" {
		review["content"] = i.EditorComments
	}
	item.Review = review
	if i.Meta.IsEnding {
		item.ReleaseInfo = ""
	} else {
		item.ReleaseInfo = i.ReleaseInfo
	}
	item.Copyright = i.Meta.Copyright
	item.TitleType = strings.TrimSpace(i.Meta.TitleType)
	item.Cover = strings.TrimSpace(i.Meta.Cover)
	item.Stills = i.Meta.Stills
	item.TitleAliases = i.Meta.TitleAliases
	item.Available = i.Meta.Available
	item.IsEnding = i.Meta.IsEnding
	item.ChildLock = i.Meta.ChildLock
	item.ReverseDisplayOrder = i.Meta.ReverseDisplayOrder
	item.EndYear = i.Meta.EndYear
	item.SameAs = i.SameAs

	item.TotalLicenseStart = i.Meta.TotalLicenseStart

	item.ReleaseYear = i.Meta.StartYear
	if i.Meta.ExtraTitleID != "" {
		titleExtra := make(map[string]string)
		titleExtra["id"] = i.Meta.ExtraTitleID
		item.TitleExtra = titleExtra
	}
	item.Summary = strings.TrimSpace(i.Meta.Description)
	item.TotalEpisodeCounts = i.Meta.TotalEpisodeCounts
	item.TotalSeriesCount = len(i.Meta.TotalEpisodeCounts)
	item.Rating = i.Meta.Rating
	item.UserRatingCount = i.Meta.UserRatingCount
	item.UserRating = i.Meta.UserRating
	item.WikiOrig = i.Meta.WikiOrig
	item.WikiZh = i.Meta.WikiZh
	item.Ost = i.Meta.Ost
	var yearForSorting int64

	if item.EndYear >= item.ReleaseYear {
		yearForSorting = item.EndYear
	} else {
		yearForSorting = item.ReleaseYear
	}

	if i.Meta.Country != "" {
		ci := NewCollection("country", i.Meta.Country)
		ci.Title = ci.CollectionName
		ci.titleID = item.ID
		ci.yearForSorting = yearForSorting
		ci.isEnding = item.IsEnding
		item.Country = ci
	}

	if len(i.Meta.ContentAgents) > 0 {
		cis := []*CollectionItem{}
		for _, j := range i.Meta.ContentAgents {
			ci := NewCollection("content_agent", j)
			ci.titleID = item.ID
			ci.yearForSorting = yearForSorting
			ci.isEnding = item.IsEnding

			cis = append(cis, ci)
		}
		item.ContentAgents = cis
	}
	if len(i.Meta.ContentProviders) > 0 {
		cis := []*CollectionItem{}
		for _, j := range i.Meta.ContentProviders {
			ci := NewCollection("content_provider", j)
			ci.titleID = item.ID
			ci.yearForSorting = yearForSorting
			ci.isEnding = item.IsEnding

			cis = append(cis, ci)
		}
		item.ContentProviders = cis
	}

	if len(i.Meta.Themes) > 0 {
		cis := []*CollectionItem{}
		for _, j := range i.Meta.Themes {
			ci := NewCollection("theme", j)
			ci.titleID = item.ID
			ci.yearForSorting = yearForSorting
			ci.isEnding = item.IsEnding

			cis = append(cis, ci)
		}
		item.Themes = cis
	}

	if len(i.Meta.Genres) > 0 {
		cis := []*CollectionItem{}
		for _, j := range i.Meta.Genres {
			ci := NewCollection("genre", j)
			ci.Title = ci.CollectionName
			ci.titleID = item.ID
			ci.yearForSorting = yearForSorting
			ci.isEnding = item.IsEnding

			cis = append(cis, ci)
		}
		item.Genres = cis
	}

	if len(i.Meta.Tags) > 0 {
		cis := []*CollectionItem{}
		for _, j := range i.Meta.Tags {
			cis = append(cis, NewCollection("tag", j))
		}
		item.Tags = cis
	}

	if len(i.Meta.Directors) > 0 {
		cis := []*CollectionItem{}
		for _, j := range i.Meta.Directors {
			ci := NewCollection("figure", j)
			ci.titleID = item.ID
			ci.yearForSorting = yearForSorting
			ci.isEnding = item.IsEnding

			cis = append(cis, ci)
		}
		item.Directors = cis
	}

	if len(i.Meta.Writers) > 0 {
		cis := []*CollectionItem{}
		for _, j := range i.Meta.Writers {
			ci := NewCollection("figure", j)
			ci.titleID = item.ID
			ci.yearForSorting = yearForSorting
			ci.isEnding = item.IsEnding

			cis = append(cis, ci)
		}
		item.Writers = cis
	}

	if len(i.Meta.Producers) > 0 {
		cis := []*CollectionItem{}
		for _, j := range i.Meta.Producers {
			ci := NewCollection("figure", j)
			ci.titleID = item.ID
			ci.yearForSorting = yearForSorting
			ci.isEnding = item.IsEnding

			cis = append(cis, ci)
		}
		item.Producers = cis
	}

	if len(i.Meta.OrderedCasts) > 0 {
		cis := []*CollectionItem{}
		for _, j := range i.Meta.OrderedCasts {
			castC := NewCollection("figure", j.Name)
			castC.Roles = j.Roles
			castC.titleID = item.ID
			castC.yearForSorting = yearForSorting
			castC.isEnding = item.IsEnding

			cis = append(cis, castC)

		}
		item.Casts = cis
	}

	// FIXME if image host get right
	if i.Meta.Cover != "" {
		i.Meta.Cover = strings.Replace(i.Meta.Cover, "image.kktv.com", "images.kktv.com", 1)
		item.Cover = i.Meta.Cover
	}

	if len(i.Meta.Stills) > 0 {
		for idx, _ := range i.Meta.Stills {
			i.Meta.Stills[idx] = strings.Replace(i.Meta.Stills[idx], "image.kktv.com", "images.kktv.com", 1)
		}
		item.Stills = i.Meta.Stills
	}

	isAiringSeries := !item.IsEnding && meta.IsAiringTitleType(item.TitleType) && item.ReleaseInfo != ""
	if isAiringSeries {
		if airingInfo, err := meta.InterpretAiringInfo(item.ReleaseInfo); err == nil {
			item.AiringInfo = airingInfo
		}
	}

	return
}

// NewTitle new a title from db
func NewTitle(titleID string) (item *Title, err error) {
	item = new(Title)
	db := kkapp.App.DbMeta.Slave()
	err = db.Get(item, sqlmetatitle["byid"], titleID)
	if err != nil {
		return
	}
	if item.MetaStr.Valid {
		item.Parse()
	}
	return
}

// NewTitle4Api new a title for api struct from db
func NewTitle4Api(titleID string) (apiitem *TitleMeta, err error) {
	item := new(Title)
	db := kkapp.App.DbMeta.Slave()
	err = db.Get(item, sqlmetatitle["byid"], titleID)
	if err != nil {
		return
	}
	if item.MetaStr.Valid {
		apiitem, err = item.Parse()
	}
	return
}

// NewTitleMeta new titles for api struct from db
func NewTitleMeta(titles []string) (items []*TitleMeta, err error) {
	// item := new(Title)
	preItems := []*Title{}
	db := kkapp.App.DbMeta.Slave()
	err = db.Select(&preItems, sqlmetatitle["inid"], pq.Array(titles))
	if err != nil {
		return
	}
	for idx := range preItems {
		if preItems[idx].MetaStr.Valid {
			item, err := preItems[idx].Parse()
			if err == nil {
				items = append(items, item)
			}
		}
	}
	return
}

// NewAllTitleMeta new all titles for api struct from db
func NewAllTitleMeta() (items []*TitleMeta, err error) {
	// item := new(Title)
	preItems := []*Title{}
	db := kkapp.App.DbMeta.Slave()
	err = db.Select(&preItems, sqlmetatitle["all"])

	if err != nil {
		return
	}

	for idx := range preItems {
		if preItems[idx].MetaStr.Valid {
			item, err := preItems[idx].Parse()
			if err == nil {
				items = append(items, item)
			}
		}
	}
	return
}

// NewTitleByQuery from query titleID ann titleName beginsWith
func NewTitleByQuery(q string) (items []*Title, err error) {
	db := kkapp.App.DbMeta.Slave()
	titleID := []string{}
	titleName := []string{}

	for _, w := range strings.Split(q, " ") {

		if _, err := strconv.Atoi(w); err == nil {
			titleID = append(titleID, strings.TrimSpace(w)+"%")
		} else {
			titleName = append(titleName, strings.TrimSpace(w)+"%")
		}
	}

	if q != "" && (len(titleID) > 0 || len(titleName) > 0) {
		err = db.Select(&items, sqlmetatitle["search"], pq.Array(titleID), pq.Array(titleName))
		if err != nil {
			return
		}

	}

	for _, item := range items {
		item.Parse()
	}
	return
}

// NewTitleHint help func for api query titleID and titleName cover only
func NewTitleHint(ids string) (items []*TitleHint, err error) {
	db := kkapp.App.DbMeta.Slave()
	titleID := []string{}

	for _, w := range strings.Split(ids, " ") {
		titleID = append(titleID, strings.TrimSpace(w))
	}
	err = db.Select(&items, sqlmetatitle["ids"], pq.Array(titleID))

	return
}

// NewAllTitleHint help func for api query titleID and titleName cover only
func NewAllTitleHint() (items []*TitleHint, err error) {
	db := kkapp.App.DbMeta.Slave()

	err = db.Select(&items, sqlmetatitle["all_ids"])
	return
}

// NewTitleLikeID help func for api query titleID and titleName cover only
func NewTitleLikeID(q string) (items []*TitleHint, err error) {
	db := kkapp.App.DbMeta.Slave()
	titleID := []string{}

	for _, w := range strings.Split(q, " ") {
		if _, err := strconv.Atoi(w); err == nil {
			titleID = append(titleID, strings.TrimSpace(w)+"%")
		}
	}

	if q != "" && len(titleID) > 0 {
		err = db.Select(&items, sqlmetatitle["idlike"], pq.Array(titleID))
	}
	return
}

// LoadAllTitles fetch all titles from rds and validated
func LoadAllTitles() (alltitles []*TitleMeta, err error) {

	// prepare all titles
	alltitles, err = NewAllTitleMeta()

	var wg sync.WaitGroup
	// number of worker
	numWorker := 6
	// use buffer chan as worker limit
	workerQueue := make(chan *TitleMeta, numWorker)
	worker := func(idx int, w *sync.WaitGroup) {
		for {
			job, ok := <-workerQueue
			if !ok {
				break
			}
			job.LoadSeries()
			job.Validate()
			wg.Done()
		}
	}
	// start workers
	for i := 0; i < numWorker; i++ {
		go worker(i, &wg)
	}

	// inject all titles
	for _, title := range alltitles {
		wg.Add(1)
		workerQueue <- title
	}
	close(workerQueue)
	wg.Wait()

	return
}

// JSONMarshalNoEscape not escape when JSONMarshal
func JSONMarshalNoEscape(t interface{}) ([]byte, error) {
	buffer := &bytes.Buffer{}
	encoder := json.NewEncoder(buffer)
	encoder.SetEscapeHTML(false)
	err := encoder.Encode(t)
	return buffer.Bytes(), err
}

// Title2Redis generate title data write to redis
func Title2Redis(titleID string) {

	title, err := NewTitle4Api(titleID)
	if err != nil {
		log.Error("db_meta_title: title2Redis: newTitle4Api").
			Err(err).Send()
		return
	}

	// load series and episodes into title
	title.LoadSeries()
	title.Validate()

	planAuthoritiesRepo := paidplan.NewPlanAuthMapRepository(kkapp.App.DbUser.Slave())
	patchPaidPlanAuthorities(title, planAuthoritiesRepo)

	// series
	if title.Series == nil {
		title.Series = []*Series{}
	}

	// currently write
	data, err := JSONMarshalNoEscape(title)
	if err != nil {
		log.Error("db_meta_title: title2Redis: json marshal").
			Err(err).Send()
		return
	}

	redisKey := fmt.Sprintf(keyTitleHashFmt, title.ID)

	hashCmd := []interface{}{}
	for _, key := range tdFields {
		valueStr, dt, _, err := jsonparser.Get(data, key)
		if err != nil {
			log.Warn("db_meta_title: title2Redis: json parser").
				Str("key", key).Str("titleID", title.ID).
				Err(err).Send()
		} else {
			if dt.String() == "string" {
				valueStr = strconv.AppendQuote([]byte(""), string(valueStr))
			}
			hashCmd = append(hashCmd, key, valueStr)
		}
	}

	if len(hashCmd) > 0 {
		pool := kkapp.App.RedisMeta.Master()
		conn, err := pool.Get()
		if err != nil {
			log.Warn("db_meta_title: title2Redis: get redis pool").
				Err(err).Send()
		}
		defer pool.Put(conn)

		err = conn.Cmd("MULTI").Err
		if err != nil {
			log.Warn("db_meta_title: title2Redis: redis command multi").
				Err(err).Send()
		}

		conn.Cmd("HMSET", redisKey, hashCmd)
		conn.Cmd("HSET", redisKey, wholeKeyInMetaHash, data)

		////////////////////////////////////////////////////////////////////////
		// we change the collection_id in struct, not to write back yet
		// after all title api porting to V3, wait the mobile client upgrade to
		// new version, we could stop the old version lambda
		//---------------------------------------------------------------------
		// redisJSONKey := fmt.Sprintf(keyTitleFmt, title.ID)
		// redisJSONGzipKey := fmt.Sprintf(keyTitleGzipFmt, title.ID)

		// gzipBytes, err := gzip.GzipEncode(data)
		// if err == nil {
		// 	base64Str := base64.StdEncoding.EncodeToString(gzipBytes)
		// 	conn.Cmd("SET", redisJSONGzipKey, base64Str)
		// }
		// conn.Cmd("SET", redisJSONKey, data)
		//---------------------------------------------------------------------
		////////////////////////////////////////////////////////////////////////

		reply := conn.Cmd("EXEC")

		if err := reply.Err; err != nil {
			log.Warn("db_meta_title: title2Redis: redis command exec").
				Err(err).Send()
		} else {
			log.Info("db_meta_title: title2Redis: write title to redis success").
				Str("titleID", title.ID).Send()
		}
	}
}
