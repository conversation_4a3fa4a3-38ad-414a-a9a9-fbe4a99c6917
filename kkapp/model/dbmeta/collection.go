package dbmeta

import (
	"crypto/hmac"
	"crypto/sha256"
	"fmt"
	"strings"
)

var (
	// CollectionCountryMap work around for the country Collection in api
	// /v3/browse
	CollectionCountryMap = map[string]string{
		"韓國":  "Korea",
		"日本":  "Japan",
		"中國":  "China",
		"台灣":  "Taiwan",
		"泰國":  "Thailand",
		"新加坡": "Singapore",
		"香港":  "Hongkong",
		"美國":  "America",
		"西班牙": "Spain",
		"法國":  "France",
		"加拿大": "Canada",
		"瑞士":  "Switzerland",
		"阿根廷": "Argentina",
		"德國":  "Germany",
		"澳洲":  "Australia",
		"巴西":  "Brazil",
		"智利":  "Chile",
		"英格蘭": "England",
		"芬蘭":  "Finland",
		"伊朗":  "Iran",
		"以色列": "Israel",
		"義大利": "Italy",
		"秘魯":  "Peru",
		"菲律賓": "Philippines",
		"歐美":  "Western",
		"其它":  "Other",
	}
)

// Collection https://github.com/KKTV/kktv-node-collection/blob/develop/src/index.js
// Collection for redis collection info
type Collection struct {
	ID                          string            `json:"id"`
	Title                       string            `json:"title"`
	TitleType                   string            `json:"title_type"`
	IsEnding                    bool              `json:"is_ending"`
	IsContainingAvod            bool              `json:"is_containing_avod"`
	UserRating                  float64           `json:"user_rating"`
	Cover                       string            `json:"cover"`
	Stills                      []string          `json:"stills"`
	Review                      map[string]string `json:"review"`
	TotalEpisodeCounts          map[string]int    `json:"total_episode_counts"`
	LatestUpdateInfo            string            `json:"latest_update_info"`
	ContentLabels               []string          `json:"content_labels"`
	ContentLabelsForExpiredUser []string          `json:"content_labels_for_expired_user"`
	Summary                     string            `json:"summary"`
}

// CollectionItem struct in api
type CollectionItem struct {
	ID string `json:"id"`
	// CollectionID   string   `json:"collection_id"`
	// FIXME remove Title after iOS client fix KKTV-7120
	Title          string   `json:"title,omitempty"`
	CollectionType string   `json:"collection_type"`
	CollectionName string   `json:"collection_name"`
	Roles          []string `json:"roles,omitempty"`
	titleID        string
	yearForSorting int64
	isEnding       bool
}

// CollectionItems group of CollentionItem
type CollectionItems []*CollectionItem

func (c CollectionItems) Len() int { return len(c) }

func (c CollectionItems) Swap(i, j int) {
	c[i], c[j] = c[j], c[i]
}

func (c CollectionItems) Less(i, j int) bool {
	if c[i].isEnding == false {
		// isEnding false first
		return true
	}
	if c[i].yearForSorting > c[j].yearForSorting {
		// bigger yearForSorting first
		return true
	}
	if c[i].titleID > c[j].titleID {
		// greater titleID first
		return true
	}
	return true
}

// GetCollectionID convert to sha256 ID
func GetCollectionID(ctype, cname string) (collectionid string) {
	var byteSlice []byte
	// work around
	if ctype == "genre" && strings.HasSuffix(cname, "專區") {
		ctype = "content_agent"
		cname = strings.TrimRight(cname, "專區")
	}

	fullName := fmt.Sprintf("%s_%s", ctype, cname)

	for _, s := range []rune(fullName) {
		// log.Println(s, string(s), byte(s), byte(s&0xff))
		byteSlice = append(byteSlice, byte(s))
	}

	h := hmac.New(sha256.New, []byte("collection_name"))
	h.Write(byteSlice)
	// return hex.EncodeToString(h.Sum(nil))
	return fmt.Sprintf("%x", h.Sum(nil))
}

// GetTitleCollectionID convert to sha256 ID
func GetTitleCollectionID(ctype, cname string) (collectionid string) {
	////////////////////////////////////////////////////////////////////////////
	// work around
	if ctype == "genre" && strings.HasSuffix(cname, "專區") {
		ctype = "content_agent"
		cname = strings.TrimRight(cname, "專區")
	}
	if country, ok := CollectionCountryMap[cname]; ok && ctype == "country" {
		// change the country from Chinese to English
		cname = country
	}
	// end of work around
	////////////////////////////////////////////////////////////////////////////

	fullName := fmt.Sprintf("%s_%s", ctype, cname)
	h := hmac.New(sha256.New, []byte("collection_name"))
	h.Write([]byte(fullName))
	// return hex.EncodeToString(h.Sum(nil))
	return fmt.Sprintf("%x", h.Sum(nil))
}

// NewCollection get a collection
func NewCollection(ctype, cname string) (item *CollectionItem) {
	item = new(CollectionItem)
	item.ID = GetTitleCollectionID(ctype, cname)
	// item.CollectionID = GetTitleCollectionID(ctype, cname)
	item.CollectionType = ctype
	item.CollectionName = cname
	return
}
