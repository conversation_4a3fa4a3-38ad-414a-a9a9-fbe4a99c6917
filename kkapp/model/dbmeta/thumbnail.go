package dbmeta

// generate thumbnail for an episode, inject to manifest folder
// the each thumbnail compose 8x8 photo, each phone token from episode in 5 second interval

import (
	"bytes"
	"errors"
	"fmt"
	"image"
	"image/color"
	"io/ioutil"
	"log"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/aws/aws-sdk-go/service/s3/s3manager"
	"github.com/disintegration/imaging"
)

var (
	thumbnailTmpDir = "/tmp" // should work on general unix, lambda runtime
	batchSize       = 64     // 8x8 image for one thumbnail
	hostBucketMap   = map[string]string{
		"https://theater.kktv.com.tw":      "kktv-prod-theater",
		"https://test-theater.kktv.com.tw": "kktv-test-theater",
		"kktv-prod-theater":                "kktv-prod-images",
		"kktv-test-theater":                "kktv-test-images",
	}
)

// Thumbnail for episode
type Thumbnail struct {
	EpisodeID         string
	Path              string
	Files             []string
	Batches           [][]string
	ThumbnailFiles    []string
	LocalThumbnailDir string
	S3FolderKey       string
	S3TheaterBucket   string
	S3ImageBucket     string
}

// Download all thumbnails for this EpisodeID
func (t *Thumbnail) Download() (err error) {
	downloader := s3manager.NewDownloader(session.New(&aws.Config{
		Region: aws.String("ap-northeast-1"),
	}))

	objects := []s3manager.BatchDownloadObject{}
	for _, f := range t.Files {
		fp, err := os.Create(filepath.Join(thumbnailTmpDir, f))

		if err != nil {
			log.Println("[ERROR]", err)
			return err
		}
		objects = append(objects,
			s3manager.BatchDownloadObject{
				Object: &s3.GetObjectInput{
					Bucket: aws.String(t.S3ImageBucket),
					Key:    aws.String(f),
				},
				Writer: fp,
			})
	}
	log.Println("[INFO] Download thumbnail")
	iter := &s3manager.DownloadObjectsIterator{Objects: objects}
	if err = downloader.DownloadWithIterator(aws.BackgroundContext(), iter); err != nil {
		log.Println("[ERROR]", err)
	}
	return err
}

// Upload all thumbnails for this EpisodeID
func (t *Thumbnail) Upload() (err error) {
	uploader := s3manager.NewUploader(session.New(&aws.Config{
		Region: aws.String("ap-northeast-1"),
	}))
	objects := []s3manager.BatchUploadObject{}
	for _, f := range t.ThumbnailFiles {
		fn := fmt.Sprintf("%s/%s", t.S3FolderKey, filepath.Base(f))
		log.Println(fn)

		fp, err := os.Open(f)
		if err != nil {
			log.Println("[ERROR]", err)
			return err
		}
		fileInfo, _ := fp.Stat()
		size := fileInfo.Size()
		buffer := make([]byte, size)
		fp.Read(buffer)
		objects = append(objects,
			s3manager.BatchUploadObject{
				Object: &s3manager.UploadInput{
					Bucket:      aws.String(t.S3TheaterBucket),
					Key:         aws.String(fn),
					ACL:         aws.String("public-read"),
					ContentType: aws.String(http.DetectContentType(buffer)),
					Body:        bytes.NewReader(buffer),
				},
			})
	}
	log.Println("[INFO] Upload thumbnail", objects)
	iter := &s3manager.UploadObjectsIterator{Objects: objects}
	if err = uploader.UploadWithIterator(aws.BackgroundContext(), iter); err != nil {
		log.Println("[ERROR]", err)
	}
	return err
}

// FindPath on s3 for thumbnail
func (t *Thumbnail) FindPath() (err error) {
	var resp *s3.ListObjectsOutput
	folderParent := t.EpisodeID[6:8]
	// thumbnails/38/00000338010001
	path := fmt.Sprintf("thumbnails/%s/%s", folderParent, t.EpisodeID)
	svc := s3.New(session.New(), &aws.Config{Region: aws.String("ap-northeast-1")})
	params := &s3.ListObjectsInput{
		Bucket:  aws.String(t.S3ImageBucket),
		Prefix:  aws.String(path),
		MaxKeys: aws.Int64(7200),
	}
	log.Println(path)
	resp, err = svc.ListObjects(params)

	if err != nil {
		log.Println("[ERROR] list thumbnails", t.EpisodeID)
		return err
	}

	for _, key := range resp.Contents {
		if strings.HasSuffix(*key.Key, ".jpg") {
			t.Files = append(t.Files, *key.Key)
		}
	}
	sort.Strings(t.Files)

	if len(t.Files) == 0 {
		return errors.New("not found any thumbnail photos")
	}

	t.LocalThumbnailDir = filepath.Dir(filepath.Join(thumbnailTmpDir, t.Files[0]))
	log.Println("[INFO] local process dir", t.LocalThumbnailDir)
	os.MkdirAll(t.LocalThumbnailDir, os.ModePerm)

	// batching
	files := t.Files[:] // clone
	batches := [][]string{}
	for batchSize < len(files) {
		files, batches = files[batchSize:], append(batches, files[0:batchSize:batchSize])
	}
	batches = append(batches, files)
	t.Batches = batches
	return err
}

// Cleanup on local folder
func (t *Thumbnail) Cleanup() {
	if t.LocalThumbnailDir != "" && strings.HasPrefix(t.LocalThumbnailDir, "/tmp/thumbnails/") {
		log.Println("[INFO] cleanup local folder", t.LocalThumbnailDir)
		os.RemoveAll(t.LocalThumbnailDir)
	}
}

// Compose on s3 for thumbnail
func (t *Thumbnail) Compose() (err error) {
	// general thumbnail height 90, 160, 240
	// we implementation height 120, 240
	// vtt text format
	// 00:00:00.000 --> 00:00:05.000
	// thumb_sprite-0.jpg#xywh=0,0,284,160
	var height, width int
	var errSmall error
	t.ThumbnailFiles = []string{}
	thumbnailVtt := filepath.Join(t.LocalThumbnailDir, "thumbnail.vtt")
	thumbnailSmallVtt := filepath.Join(t.LocalThumbnailDir, "thumbnailsmall.vtt")
	vtt := []string{}      // big general with 240
	vttSmall := []string{} // small half size of 240
	height = 240
	// get the aspect ration width from first image
	if len(t.Batches) > 0 && len(t.Batches[0]) > 0 {
		fPath := filepath.Join(thumbnailTmpDir, t.Batches[0][0])
		src, err := imaging.Open(fPath)

		if err != nil {
			return err
		}
		// resize to height 240
		src = imaging.Resize(src, 0, 240, imaging.Lanczos)
		width = src.Bounds().Dx()
	}
	log.Println("[INFO] Compose thumbnail", width, height)
	for idx, batch := range t.Batches {
		thumbnailBaseFn := fmt.Sprintf("thumb_sprite-%05d.jpg", idx)                          // big
		thumbnailSmallBaseFn := fmt.Sprintf("thumb_spritesmall-%05d.jpg", idx)                // small
		thumbnailFile := fmt.Sprintf("%s/%s", t.LocalThumbnailDir, thumbnailBaseFn)           // big
		thumbnailSmallFile := fmt.Sprintf("%s/%s", t.LocalThumbnailDir, thumbnailSmallBaseFn) // small
		t.ThumbnailFiles = append(t.ThumbnailFiles, thumbnailFile)                            // big
		t.ThumbnailFiles = append(t.ThumbnailFiles, thumbnailSmallFile)                       // small

		dst := imaging.New(width*8, height*8, color.NRGBA{0, 0, 0, 0})
		for count, f := range batch {

			fn := filepath.Join(thumbnailTmpDir, f)
			src, err := imaging.Open(fn)

			if err != nil {
				return err
			}
			src = imaging.Resize(src, 0, 240, imaging.Lanczos)
			// count paste point, delta x && delta y
			yd := count / 8 * height
			xd := count % 8 * width
			dst = imaging.Paste(dst, src, image.Pt(xd, yd))

			// count second
			baseFnSecond := idx*batchSize*5 + count*5 // each photo with 5 seconds duration
			start := time.Unix(int64(baseFnSecond), 0)
			end := time.Unix(int64(baseFnSecond+5), 0)
			// 00:00:00.000 --> 00:00:05.000
			// thumb_sprite-0.jpg#xywh=0,0,284,160
			// big
			{
				timeLine := fmt.Sprintf("%s --> %s", start.UTC().Format("15:04:05.000"), end.UTC().Format("15:04:05.000"))
				imageLine := fmt.Sprintf("%s#xywh=%d,%d,%d,%d", thumbnailBaseFn, xd, yd, width, height)
				line := fmt.Sprintf("%s\n%s", timeLine, imageLine)
				vtt = append(vtt, line)
			}

			// small
			{
				timeLine := fmt.Sprintf("%s --> %s", start.UTC().Format("15:04:05.000"), end.UTC().Format("15:04:05.000"))
				imageLine := fmt.Sprintf("%s#xywh=%d,%d,%d,%d", thumbnailSmallBaseFn, xd/2, yd/2, width/2, height/2)
				line := fmt.Sprintf("%s\n%s", timeLine, imageLine)
				vttSmall = append(vttSmall, line)
			}
		}

		err = imaging.Save(dst, thumbnailFile)
		errSmall = imaging.Save(dst, thumbnailSmallFile)
		if err != nil || errSmall != nil {
			log.Fatalf("[ERROR] failed to save thumbnail image: %v", err)
			return err
		}

		// convert thumbnailSmall to haft size
		thumbResize, err := imaging.Open(thumbnailSmallFile)
		if err != nil {
			log.Fatalf("[ERROR] failed to open thumbnail small image: %v", err)
			return err
		}
		thumbResize = imaging.Resize(thumbResize, width*4, height*4, imaging.Lanczos)
		imaging.Save(thumbResize, thumbnailSmallFile)
	}

	// write to vtt file
	if len(vtt) > 0 {
		vtt = append([]string{"WEBVTT"}, vtt...)
		vttBytes := []byte(strings.Join(vtt, "\n\n"))
		ioutil.WriteFile(thumbnailVtt, vttBytes, 0644)
		t.ThumbnailFiles = append(t.ThumbnailFiles, thumbnailVtt)
	}

	if len(vttSmall) > 0 {
		vttSmall = append([]string{"WEBVTT"}, vttSmall...)
		vttBytes := []byte(strings.Join(vttSmall, "\n\n"))
		ioutil.WriteFile(thumbnailSmallVtt, vttBytes, 0644)
		t.ThumbnailFiles = append(t.ThumbnailFiles, thumbnailSmallVtt)
	}
	log.Println(t.ThumbnailFiles)
	return err
}

// hadExisted had existed on s3 for thumbnail already
func (t *Thumbnail) hadExisted() (yesno bool) {
	var resp *s3.ListObjectsOutput
	var err error

	// only check general size thumbnail sprite
	thumbnailVtt := filepath.Join(t.S3FolderKey, "thumbnail.vtt")
	log.Println("[INFO] check existing for", t.S3TheaterBucket, thumbnailVtt)
	svc := s3.New(session.New(), &aws.Config{Region: aws.String("ap-northeast-1")})
	params := &s3.ListObjectsInput{
		Bucket: aws.String(kkapp.App.S3TheaterBucket),
		Prefix: aws.String(thumbnailVtt),
	}
	resp, err = svc.ListObjects(params)
	if err != nil {
		log.Println("[ERROR] list thumbnails", err)
		return false
	}

	if len(resp.Contents) == 0 {
		return false
	}

	// should found the thumbnail file, check if expire longer than 6 hours
	if time.Now().Unix()-resp.Contents[0].LastModified.UTC().Unix() > 21600 {
		// 60*60*6 = 21600 seconds
		return false
	}

	return true
}

// Cook start to build thumbnails
func (t *Thumbnail) Cook() {
	// var err error
	episode, err := NewEpisodeByEpisodeID(t.EpisodeID)

	if err != nil {
		log.Println("[ERROR]", err)
		return
	}

	if episode.Meta.Mezzanines.Hls == nil {
		log.Println("[INFO] the manefest file not ready yet", t.EpisodeID)
		return
	}

	u, err := url.Parse(episode.Meta.Mezzanines.Hls.URI)
	if err != nil {
		log.Println("[ERROR] the manefest file path", err)
		return
	}

	// pick S3 bucket via data source
	for host, bucket := range hostBucketMap {
		if strings.HasPrefix(episode.Meta.Mezzanines.Hls.URI, host) {
			t.S3TheaterBucket = bucket
			t.S3ImageBucket = hostBucketMap[bucket]
			break
		}
	}

	if t.S3TheaterBucket == "" || t.S3ImageBucket == "" {
		log.Println("[ERROR] the manefest file path wrong", episode.Meta.Mezzanines.Hls.URI)
		return
	}

	// S3ThumbnailFolder
	// baseName format 00000338010001_8ef36f0220208dc5abefcadc57d0eda7 episodeid_hashID
	baseName := filepath.Base(filepath.Dir(u.Path))
	if strings.Contains(baseName, "_") {
		hashID := strings.Split(baseName, "_")[1]
		t.S3FolderKey = fmt.Sprintf("%s/%s", filepath.Dir(u.Path), hashID)
		if strings.HasPrefix(t.S3FolderKey, "/") {
			// remove slash
			t.S3FolderKey = t.S3FolderKey[1:]
		}
		log.Println("[INFO] thumbnail would store at", t.S3FolderKey)
	} else {
		log.Println("[INFO] the manefest file not ready yet", t.EpisodeID)
		return
	}

	if t.S3FolderKey == "" {
		log.Println("[ERROR] not found a validate S3 manifest folder key")
		return
	}

	if t.hadExisted() {
		log.Println("[INFO] the thumbnail sprite had existed")
		return
	}

	err = t.FindPath()
	if err != nil {
		log.Println("[ERROR]", err.Error(), t.EpisodeID)
		return
	}

	t.Download()
	t.Compose()
	t.Upload()
	t.Cleanup()
}

// NewThumbnail handle the thumbnail for episode
func NewThumbnail(EpisodeID string) {
	if len(EpisodeID) != 14 {
		// not validate ID
		log.Println("[ERROR] episodeID", EpisodeID)
		return
	}

	thumbnail := new(Thumbnail)
	thumbnail.EpisodeID = EpisodeID
	thumbnail.Cook()
	return
}
