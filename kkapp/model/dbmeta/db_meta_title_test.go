package dbmeta

import (
	"testing"

	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/wrapper/paidplan"
	"github.com/KKTV/kktv-api-v3/pkg/model/authority"
	"github.com/KKTV/kktv-api-v3/pkg/model/cachemeta"
	metamodel "github.com/KKTV/kktv-api-v3/pkg/model/dbmeta"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
)

func TestPatchPaidPlanAuthorities(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockAuthorityRepo := paidplan.NewMockPlanAuthMapRepository(ctrl)

	testcases := []struct {
		name                        string
		title                       *TitleMeta
		given                       func()
		expectedAcceptedAuthorities []authority.Authority
	}{
		{
			name: "one genre is specified",
			title: &TitleMeta{
				Genres:              []*CollectionItem{{CollectionName: "動漫"}},
				AcceptedAuthorities: []authority.Authority{authority.PremiumPlay},
			},
			given: func() {
				mockAuthorityRepo.EXPECT().ListAuthoritiesByGenre("動漫").Return([]authority.Authority{authority.PlanAnimePlay}, nil)
			},
			expectedAcceptedAuthorities: []authority.Authority{authority.PremiumPlay, authority.PlanAnimePlay},
		},
		{
			name: "only distinct authorities are added when multiple genres are specified",
			title: &TitleMeta{
				Genres:              []*CollectionItem{{CollectionName: "動漫"}, {CollectionName: "親子"}},
				AcceptedAuthorities: []authority.Authority{authority.PremiumPlay},
			},
			given: func() {
				auths := []authority.Authority{authority.PlanAnimePlay, authority.PlanAnimePlay}
				for i, s := range []string{"動漫", "親子"} {
					mockAuthorityRepo.EXPECT().ListAuthoritiesByGenre(s).
						Return([]authority.Authority{auths[i]}, nil)
				}
			},
			expectedAcceptedAuthorities: []authority.Authority{authority.PremiumPlay, authority.PlanAnimePlay},
		},
	}
	for _, tc := range testcases {
		defer ctrl.Finish()
		t.Run(tc.name, func(t *testing.T) {
			tc.given()
			patchPaidPlanAuthorities(tc.title, mockAuthorityRepo)
			assert.ElementsMatch(t, tc.expectedAcceptedAuthorities, tc.title.AcceptedAuthorities)
		})
	}
}

func TestTitle_Parse(t *testing.T) {
	const (
		titleID = "04290106"
	)

	testcases := []struct {
		name       string
		title      *Title
		assertFunc func(*TitleMeta, error)
	}{
		{
			name: "IsEnding=false, TitleType=series SHOULD got airing info",
			title: &Title{
				ID: titleID,
				Meta: titleMETA{
					IsEnding:  false,
					TitleType: metamodel.TitleTypeSeries.String(),
				},
				ReleaseInfo: "每{{wd:3|t:1200}}更新",
			},
			assertFunc: func(m *TitleMeta, err error) {
				assert.NoError(t, err)
				assert.Equal(t, titleID, m.ID)
				assert.False(t, m.IsEnding)
				assert.Equal(t, metamodel.TitleTypeSeries.String(), m.TitleType)
				assert.Len(t, m.AiringInfo.Schedule, 1)
				assert.Equal(t, "每週三12:00更新", m.AiringInfo.RenderedReleaseInfo)
				assert.Equal(t, "1200", m.AiringInfo.Schedule[0].Time)
				assert.Equal(t, cachemeta.WeekdayWednesday, m.AiringInfo.Schedule[0].Weekday)
			},
		},
	}
	for _, tc := range testcases {
		t.Run(tc.name, func(t *testing.T) {
			m, err := tc.title.Parse()
			tc.assertFunc(m, err)
		})
	}
}
