package kkfamily

import (
	"database/sql"
	"encoding/json"
	"errors"
	"log"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbuser"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/wrapper"
	usermodel "github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/jmoiron/sqlx"
	"github.com/lib/pq"
	"gopkg.in/guregu/null.v3"
)

var (
	familyPaymentType = "family"
	sqlfamily         = map[string]string{
		"isFamilyPlanOwner": `SELECT
					COUNT(1) AS cnt
					FROM users u JOIN payment_info p ON u.id = p.user_id
					WHERE p.family_id = u.id AND u.id=$1;`,
		// "getfamily"
		// order by p.payment_type make sure 'credit_card' always in front of 'family'
		// user_id, payment_type
		// 002, credit_card -> this one we take as family owner
		// 001, family -> member
		// 005, family -> member
		"getfamily": `SELECT
 u.id, u.email, u.phone, u."name", date_part('epoch', u.expired_at)::int as expired_at,
 u."role", u."type", p.payment_type, p.family_id, fo.next_order_date, fo.item_name , fo.family_limit, fo.product_name
 FROM payment_info p
 JOIN users u ON u.id = p.user_id
 LEFT JOIN
 (SELECT o.id, o.user_id, o.start_date, o.end_date,date_part('epoch', o.end_date)::int AS next_order_date, o.status,
 p.item_name, p.bundle->'family' AS family_limit, p.name AS product_name
 FROM orders o, products p WHERE p.bundle->'family' IS NOT NULL AND o.user_id = $1
 AND o.status = 'ok' AND NOW() BETWEEN o.start_date - interval '1 day' AND o.end_date AND o.product_id = p.id ORDER
 BY o.end_date DESC LIMIT 1 ) as fo
 ON u.id = fo.user_id
 WHERE (p.user_id = $1 OR p.family_id = $1) ORDER BY fo.family_limit,p.payment_type, p.updated_at DESC;`,

		"familyWithoutStatus": `SELECT
			u.id,
			u.email,
			u.phone,
			u."name",
			date_part('epoch', u.expired_at)::int as expired_at,
			u."role",
			u."type",
			p.payment_type,
			p.family_id,
			fo.next_order_date,
			fo.item_name,
			fo.family_limit,
			fo.product_name,
			fo.status
		FROM payment_info p
		JOIN users u ON u.id = p.user_id
		LEFT JOIN
			(
				SELECT
					o.id,
					o.user_id,
					o.start_date,
					o.end_date,
					date_part('epoch', o.end_date)::int AS next_order_date,
					o.status,
					p.item_name,
					p.bundle->'family' AS family_limit,
					p.name AS product_name
				FROM orders o, products p
				WHERE p.bundle->'family' IS NOT NULL
				AND o.user_id = $1
				AND ( NOW() BETWEEN o.start_date - interval '1 day' AND o.end_date )
				AND o.product_id = p.id
				AND o.status = 'ok'
				ORDER BY o.end_date DESC LIMIT 1
			) AS fo
		ON u.id = fo.user_id
		WHERE (p.user_id = $1 OR p.family_id = $1)
		ORDER BY fo.family_limit,p.payment_type, p.updated_at DESC;`,

		"user": `SELECT id, email, phone, avatar_url, name, birthday, gender, date_part('epoch',
 expired_at)::int as expired_at, created_at,role, media_source, auto_renew, type, created_by,
 media_source #>> '{kkbox,sub}' as kkboxsub,
 media_source #>> '{childlock}' as childlock
 FROM users WHERE id = $1`,

		"payinfo": `SELECT credit_card_6no, credit_card_4no, payment_type, telecom_mp_id, family_id FROM payment_info WHERE user_id = $1`,

		"updateuser": `UPDATE users SET role = $1, expired_at = $2, auto_renew = $3, updated_at = NOW() WHERE id = $4;`,

		"updateowner": `UPDATE users SET media_source = COALESCE(media_source, '{}'::jsonb) || $1::jsonb,  updated_at = NOW() WHERE id = $2;`,

		"upsertpayment": `INSERT INTO payment_info (
 user_id,
 email,
 phone,
 payment_type,
 family_id) VALUES (
 :user_id,
 :email,
 :phone,
 :payment_type,
 :family_id) ON CONFLICT (user_id) DO UPDATE SET
 email = :email,
 phone = :phone,
 payment_type = :payment_type ,
 family_id = :family_id,
 updated_at = NOW();`,

		"dismiss":        `UPDATE users SET auto_renew = False, media_source = media_source - 'family',  updated_at = NOW() WHERE id = ANY($1);`,
		"dismisspayment": `UPDATE payment_info SET family_id = NULL, updated_at = NOW() WHERE user_id = ANY($1);`,
	}
)

// FamilyInfo map json response for family information
type FamilyInfo struct {
	Id            string      `db:"id" json:"id"`
	Email         null.String `db:"email" json:"email"`
	Phone         null.String `db:"phone" json:"phone"`
	Name          null.String `db:"name" json:"name"`
	ExpiredAt     int64       `db:"expired_at" json:"expiredAt"`
	Role          string      `db:"role" json:"-"`
	Type          string      `db:"type" json:"-"`
	PaymentType   string      `db:"payment_type" json:"payment_type"`
	FamilyID      null.String `db:"family_id" json:"-"`
	ItemName      null.String `db:"item_name" json:"-"`
	ProductName   null.String `db:"product_name" json:"-"`
	NextOrderDate null.Int    `db:"next_order_date" json:"-"`
	FamilyLimit   null.Int    `db:"family_limit" json:"-"`
	Status        null.String `db:"status" json:"-"`
}

// NewFamilyInfo get family infomation
func NewFamilyInfo(userid string, db *sqlx.DB) (familyinfo []FamilyInfo, err error) {

	err = db.Select(&familyinfo, sqlfamily["getfamily"], userid)
	if err != nil {
		log.Println("[ERROR] - FamilyInfo ", err)
	}

	return
}

func NewFamilyWithoutStatus(userid string, db *sqlx.DB) (familyinfo []FamilyInfo, err error) {
	err = db.Select(&familyinfo, sqlfamily["familyWithoutStatus"], userid)

	if err != nil {
		log.Println("[ERROR] - FamilyInfo ", err)
	}

	return
}

// MediaSourceFamily for user.media_source
type MediaSourceFamily struct {
	Family []string `json:"family"`
}

// Family family struct
type Family struct {
	FamilyID      string       `db:"family_id" json:"family_id"`
	ItemName      null.String  `db:"item_name" json:"item_name,omitempty"`
	ProductName   null.String  `db:"product_name" json:"product_name,omitempty"`
	NextOrderDate null.Int     `db:"next_order_date" json:"next_order_date,omitempty"`
	FamilyLimit   null.Int     `db:"family_limit" json:"family_limit,omitempty"`
	Owner         FamilyInfo   `json:"owner"`
	Family        []FamilyInfo `json:"family,omitempty"`
	Status        null.String  `json:"status"`
}

// GetUser get one userinfo
func (f *Family) GetUser(userid string) (userinfo model.UserInfo, err error) {
	var payinfo model.PaymentInfo
	db := kkapp.App.DbUser.Slave()
	err = db.Get(&userinfo, sqlfamily["user"], userid)
	if err != nil {
		return
	}

	err = db.Get(&payinfo, sqlfamily["payinfo"], userid)

	if err != nil && err != sql.ErrNoRows {
		// not notFound error
		return
	}

	if payinfo.PaymentType != "" {
		userinfo.PaymentType = payinfo.PaymentType
	}

	if payinfo.FamilyId.Valid {
		userinfo.FamilyID = payinfo.FamilyId.String
	}

	return userinfo, nil
}

// Add a new user to the same famiy
func (f *Family) Add(userid string) (err error) {
	familyLimit := int(f.FamilyLimit.Int64)

	// family limit check
	if len(f.Family) >= familyLimit {
		return errFamilyLimit
	}

	// no family ok order
	if !f.Owner.NextOrderDate.Valid {
		return errInFamily
	}

	userinfo, err := f.GetUser(userid)

	if err != nil {
		return err
	}

	// check user current role
	if userinfo.Role == model.UserRolePremium {
		return errVipUser
	}

	// check if in family plan already
	//if userinfo.PaymentType == "family" && userinfo.FamilyID != "" {
	//return errInFamily
	//}

	////////////////////////////////////////////////////////////////////////////////
	// user had sure not in premium and not in any family plan
	// 1. update user role, expiredAt
	// 2. upsert user payment_info
	// 3. update owner media_souce attribute family
	// 4. TODO maybe amplitude event

	db := kkapp.App.DbUser.Master().Unsafe()
	var tx *sqlx.Tx
	tx, err = db.Beginx()
	userService := wrapper.NewUserService(db)

	defer func() {
		if err != nil {
			log.Println("[ERROR]", err)
			tx.Rollback()
		}
		commitErr := tx.Commit()
		log.Println("[INFO] family add commit", commitErr)
	}()

	// 1. update user role, expiredAt
	// f.Owner.NextOrderDate come from family ok order.
	expiredDate := time.Unix(f.Owner.NextOrderDate.Int64, 0).UTC()
	log.Println("[INFO] update user for family plan", expiredDate, userid)
	_, err = userService.WithTx(tx).UpdateByFields(userid, map[usermodel.UsersField]interface{}{
		usermodel.UserFieldRole:       model.UserRolePremium,
		usermodel.UserFieldExpiredAt:  expiredDate,
		usermodel.UserFieldAutoRenew:  true,
		usermodel.UserFieldMembership: usermodel.MembershipPremiumOnly,
	})
	if err != nil {
		return
	}

	// 2. upsert user payment_info
	paymentInfo := new(dbuser.PaymentInfo)
	paymentInfo.UserID = userid
	paymentInfo.Email = userinfo.Email
	paymentInfo.Phone = userinfo.Phone
	paymentInfo.PaymentType = null.String{NullString: sql.NullString{String: familyPaymentType, Valid: true}}
	paymentInfo.FamilyID = null.String{NullString: sql.NullString{String: f.FamilyID, Valid: true}}
	log.Println("[INFO] upsert payment_info", paymentInfo)
	_, err = tx.NamedExec(sqlfamily["upsertpayment"], paymentInfo)
	if err != nil {
		return
	}

	// 3. update owner media_souce attribute family
	var members MediaSourceFamily
	for _, member := range f.Family {
		members.Family = append(members.Family, member.Id)
	}
	members.Family = append(members.Family, userid)
	jsBytes, _ := json.Marshal(members)

	log.Println("[INFO] update owner media_source", f.FamilyID, string(jsBytes))
	_, err = tx.Exec(sqlfamily["updateowner"], jsBytes, f.FamilyID)

	if err != nil {
		return
	}

	return
}

// Remove one user from this family
func (f *Family) Remove(userid string) (err error) {
	var found bool
	for _, member := range f.Family {
		if member.Id == userid {
			found = true
			break
		}
	}

	if !found {
		return errFamilyPlan
	}

	////////////////////////////////////////////////////////////////////////////////
	// user had sure currently in family plan
	// 1. update user role, expiredAt
	// 2. upsert user payment_info
	// 3. update owner media_souce attribute family
	// 4. TODO maybe amplitude event

	db := kkapp.App.DbUser.Master()
	var tx *sqlx.Tx
	tx, err = db.Beginx()
	userService := wrapper.NewUserService(db)
	defer func() {
		if err != nil {
			log.Println("[ERROR]", err)
			tx.Rollback()
		}
		commitErr := tx.Commit()
		log.Println("[INFO] family remove commit", commitErr)
	}()

	// 1. update user role, expiredAt

	//loc, _ := time.LoadLocation("Asia/Taipei")
	//now := time.Now().Truncate(time.Second).In(loc)
	//expiredDate := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, loc).AddDate(0, 0, 1).UTC()
	expiredDate := time.Now().Truncate(time.Second).UTC()

	log.Println("[INFO] update user for family plan", expiredDate, userid)

	_, err = userService.WithTx(tx).UpdateByFields(userid, map[usermodel.UsersField]interface{}{
		usermodel.UserFieldRole:       model.UserRoleExpired,
		usermodel.UserFieldExpiredAt:  expiredDate,
		usermodel.UserFieldAutoRenew:  false,
		usermodel.UserFieldMembership: usermodel.MembershipExpired,
	})
	if err != nil {
		return
	}

	// 2. upsert user payment_info
	paymentInfo := new(dbuser.PaymentInfo)
	paymentInfo.UserID = userid
	paymentInfo.PaymentType = null.String{NullString: sql.NullString{String: familyPaymentType, Valid: true}}
	paymentInfo.FamilyID = null.String{NullString: sql.NullString{String: "", Valid: false}}

	log.Println("[INFO] upsert payment_info", paymentInfo)
	_, err = tx.NamedExec(sqlfamily["upsertpayment"], paymentInfo)
	if err != nil {
		return
	}

	// 3. update owner media_souce attribute family
	var members MediaSourceFamily
	for _, member := range f.Family {
		if member.Id != userid {
			members.Family = append(members.Family, member.Id)
		}
	}
	jsBytes, _ := json.Marshal(members)
	log.Println("[INFO] update owner media_source", f.FamilyID, string(jsBytes))
	_, err = tx.Exec(sqlfamily["updateowner"], jsBytes, f.FamilyID)

	if err != nil {
		return
	}
	return
}

// Dismiss the whold family plan, the owner cancel the family plan
// the cancel order should handle at credit_card.go
func (f *Family) Dismiss() (err error) {
	////////////////////////////////////////////////////////////////////////////////
	// user had sure currently in family plan
	// 1. update user auto_renew, remove media_source->>'family'
	// 2. update user payment_info

	db := kkapp.App.DbUser.Master()
	var tx *sqlx.Tx
	tx, err = db.Beginx()

	defer func() {
		if err != nil {
			log.Println("[ERROR]", err)
			tx.Rollback()
		}
		commitErr := tx.Commit()
		log.Println("[INFO] family dismiss commit", commitErr)
	}()

	var members MediaSourceFamily
	for _, member := range f.Family {
		members.Family = append(members.Family, member.Id)
	}

	// 1. update user auto_renew
	log.Println("[INFO] dismiss family plan", f.FamilyID)
	_, err = tx.Exec(sqlfamily["dismiss"], pq.Array(members.Family))
	if err != nil {
		return
	}

	// 2. update user payment_info
	log.Println("[INFO] dismiss payment_info", f.FamilyID)
	_, err = tx.Exec(sqlfamily["dismisspayment"], pq.Array(members.Family))
	if err != nil {
		return
	}

	return
}

// NewFamily new a Family instance
func NewFamily(userid string) (f *Family, err error) {
	f = new(Family)
	f.FamilyID = userid

	slaveDB := kkapp.App.DbUser.Slave()
	family, err := NewFamilyInfo(userid, slaveDB)

	if err != nil {
		return nil, err
	}
	err = f.setFamily(family)
	if err != nil {
		return nil, err
	}

	return
}

func NewFamilyWithOrderStatus(userid string) (f *Family, err error) {
	f = new(Family)
	f.FamilyID = userid

	slaveDB := kkapp.App.DbUser.Slave()
	family, err := NewFamilyWithoutStatus(userid, slaveDB)

	if err != nil {
		return nil, err
	}
	err = f.setFamily(family)
	if err != nil {
		return nil, err
	}

	return
}

func (f *Family) setFamily(family []FamilyInfo) error {
	if len(family) > 0 && family[0].FamilyLimit.Valid {
		f.Family = family
		f.Owner = family[0]
		f.Status = family[0].Status

		if !f.Owner.FamilyLimit.Valid || f.Owner.FamilyLimit.Int64 < 1 {
			return errors.New("not a family")
		}

		f.FamilyLimit = f.Owner.FamilyLimit

		if f.Owner.NextOrderDate.Valid {
			f.NextOrderDate = f.Owner.NextOrderDate
		}

		if f.Owner.ItemName.Valid {
			f.ItemName = f.Owner.ItemName
		}

		if f.Owner.ProductName.Valid {
			f.ProductName = f.Owner.ProductName
		}
	}
	return nil
}
