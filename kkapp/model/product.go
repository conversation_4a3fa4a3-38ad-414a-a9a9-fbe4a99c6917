package model

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"log"

	plog "github.com/KKTV/kktv-api-v3/pkg/log"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"gopkg.in/guregu/null.v3"
)

// Package
// kktv_users packages

var (
	// products product_packages
	sqlpp = map[string]string{
		"list": `SELECT
			pkg.id,
			pkg.price::money::numeric::float8,
			pkg.duration,
			pkg.title,
			pkg.description,
			pkg.button_text,
			pkg.label,
			pkg.highlight,
			pkg.promotion,
			pd.name,
			pd.payment_type,
			pd.auto_renew,
			pd.as_subscribe,
			pd.bundle,
			pkg.targets,
			pkg.category,
			pkg.pay_duration,
			pkg.billing_product_ids,
			pkg.sort
		FROM
			products pd,
			product_packages pkg
		WHERE
			pkg.active = TRUE
			AND pkg.platform = $1
			AND (pkg.product_ids)::jsonb @> (pd.id::text)::jsonb
		ORDER BY
			pkg.sort,
			pkg.id,
			pd.id;`,

		"list_billing_package": `SELECT
			pkg.id,
			pkg.price::money::numeric::float8,
			pkg.duration,
			pkg.title,
			pkg.description,
			pkg.button_text,
			pkg.label,
			pkg.highlight,
			pkg.promotion,
			pkg.targets,
			pkg.category,
			pkg.pay_duration,
			pkg.billing_product_ids,
			pkg.sort
		FROM
			product_packages pkg
		WHERE
			pkg.active = TRUE
			AND pkg.platform = $1
			AND jsonb_array_length((pkg.billing_product_ids)::jsonb) > 0
			AND jsonb_array_length((pkg.product_ids)::jsonb) = 0
		ORDER BY
			pkg.sort,
			pkg.id;`,

		"get": `SELECT
			pkg.id,
			pkg.price::money::numeric::float8,
			pkg.duration,
			pkg.title,
			pkg.description,
			pkg.button_text,
			pkg.label,
			pkg.highlight,
			pkg.promotion,
			pd.name,
			pd.payment_type,
			pd.auto_renew,
			pd.as_subscribe,
			pd.bundle,
			pkg.billing_product_ids
		FROM
			product_packages AS pkg,
			products AS pd
		WHERE
			pkg.active = TRUE
			AND pkg.id = $1
			AND(pkg.product_ids)::jsonb @> (pd.id::text)::jsonb
		ORDER BY
			pkg.id,
			pd.id;`,

		"get_billing_pkg_by_id": `SELECT
			pkg.id,
			pkg.price::money::numeric::float8,
			pkg.duration,
			pkg.title,
			pkg.description,
			pkg.button_text,
			pkg.label,
			pkg.highlight,
			pkg.promotion,
			pkg.billing_product_ids
		FROM
			product_packages AS pkg
		WHERE
			pkg.active = TRUE
			AND pkg.id = $1
			AND jsonb_array_length((pkg.billing_product_ids)::jsonb) > 0
		ORDER BY
			pkg.id;`,
	}
)

type ProductBundle struct {
	Prefix                   []string `json:"prefix,omitempty"`
	Qualification            string   `json:"qualification,omitempty"`
	EmailDomains             []string `json:"email_domains,omitempty"`
	QualificationDescription string   `json:"qualification_description,omitempty"`
	NeedRedeemCode           bool     `json:"needRedeemCode,omitempty"`
	Title                    string   `json:"title,omitempty"`
	Subtitle                 string   `json:"subtitle,omitempty"`
	Description              string   `json:"description,omitempty"`
	PackageDescription       string   `json:"package_description,omitempty"`
	UrlName                  string   `json:"url_name,omitempty"`
	TextColor                string   `json:"text_color,omitempty"`
	CampaignImage            string   `json:"campaign_image,omitempty"`
	BackgroundImage          string   `json:"background_image,omitempty"`
	BackgroundColor          string   `json:"background_color,omitempty"`
}

type PackageLayout struct {
	Title              null.String `db:"title" json:"title,omitempty"`
	Subtitle           null.String `db:"subtitle" json:"subtitle,omitempty"`
	Description        null.String `db:"description" json:"description,omitempty"`
	PackageDescription null.String `db:"package_description" json:"package_description,omitempty"`
	CTAText            null.String `db:"cta_text" json:"cta_text,omitempty"`
	TextColor          null.String `db:"text_color" json:"text_color,omitempty"`
	CampaignImage      null.String `db:"image" json:"campaign_image,omitempty"`
	BackgroundImage    null.String `db:"background_image" json:"background_image,omitempty"`
	BackgroundColor    null.String `db:"background_color" json:"background_color,omitempty"`
	Terms              null.String `db:"terms" json:"terms,omitempty"`
	DisplayMoreBtn     bool        `db:"display_more_btn" json:"display_more_btn"`
}

// Scan interface for db
func (t *ProductBundle) Scan(src interface{}) error {
	byteValue, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("metas field must be a []byte, got %T instead", src)
	}
	return json.Unmarshal(byteValue, t)

}

// Value interface for db
func (t ProductBundle) Value() (driver.Value, error) {
	return json.Marshal(t)
}

// ProductPackage struct for table product_package
type ProductPackage struct {
	ID          string         `db:"id" json:"id"`
	Price       float64        `db:"price" json:"price"`
	Duration    string         `db:"duration" json:"duration"`
	Title       string         `db:"title" json:"title"`
	Description string         `db:"description" json:"description"`
	ButtonText  string         `db:"button_text" json:"button_text"`
	Highlight   string         `db:"highlight" json:"highlight"`
	Label       null.String    `db:"label" json:"label"`
	Promotion   null.String    `db:"promotion" json:"promotion"`
	Products    []Product      `json:"products"`
	Name        string         `db:"name" json:"-"`
	PaymentType string         `db:"payment_type" json:"-"`
	AutoRenew   bool           `db:"auto_renew" json:"-"`
	AsSubscribe bool           `db:"as_subscribe" json:"-"`
	Bundle      *ProductBundle `db:"bundle" json:"-"`
	Sort        int64          `db:"sort" json:"sort"`
	Targets     null.String    `db:"targets" json:"-"`
	Category    null.String    `db:"category" json:"-"`

	PayDuration       null.String     `db:"pay_duration" json:"pay_duration"`
	DisplayCategory   []string        `json:"category"`
	OriginalPrice     string          `json:"original_price"`
	BillingProductIds json.RawMessage `db:"billing_product_ids" json:"-"`
	Layout            PackageLayout   `json:"layout"`
}

// Product struct
type Product struct {
	Source      string         `json:"source,omitempty"`
	Name        string         `db:"name" json:"name"`
	PaymentType string         `db:"payment_type" json:"payment_type"`
	AutoRenew   bool           `db:"auto_renew" json:"auto_renew"`
	AsSubscribe bool           `db:"as_subscribe" json:"as_subscribe"`
	Prefix      []string       `json:"prefix,omitempty"`
	Bundle      *ProductBundle `json:"bundle,omitempty"`
}

// Pkgs group of ProductPackage
type Pkgs []*ProductPackage

func (pkgs Pkgs) GetDisplayCategories() map[string]bool {
	var categoriesExists map[string]bool = make(map[string]bool)
	for _, pkg := range pkgs {
		for _, category := range pkg.DisplayCategory {
			if categoriesExists[category] {
				continue
			}
			categoriesExists[category] = true
		}
	}
	return categoriesExists
}

// ListByPlatform group product via platform
func (pp *ProductPackage) ListByPlatform(platform string) (pkgs Pkgs) {
	var err error
	db := kkapp.App.DbUser.Slave()

	err = db.Select(&pkgs, sqlpp["list"], platform)
	if err != nil {
		log.Println("[ERROR] query product_packages", err)
		return
	}
	return pkgs
}

func (pp *ProductPackage) ListBillingPkgByPlatform(platform string) (pkgs Pkgs) {
	var err error
	db := kkapp.App.DbUser.Slave()

	err = db.Select(&pkgs, sqlpp["list_billing_package"], platform)
	if err != nil {
		log.Println("[ERROR] query billing packages", err)
		return
	}
	return pkgs
}

// ListByID get product via Id
func (pp *ProductPackage) ListByID(id string) (pkgs Pkgs) {
	var err error
	db := kkapp.App.DbUser.Slave()

	err = db.Select(&pkgs, sqlpp["get"], id)

	if err != nil {
		log.Println("[ERROR] query product_packages", err)
		return
	}
	return pkgs
}

func (pp *ProductPackage) ListBillingPkgByID(id string) (pkgs Pkgs) {
	var err error
	db := kkapp.App.DbUser.Slave()

	err = db.Select(&pkgs, sqlpp["get_billing_pkg_by_id"], id)

	if err != nil {
		log.Println("[ERROR] query billing package", err)
		return
	}
	return pkgs
}

// Regroup join same product id in same group
func (pkgs Pkgs) Regroup() (newpkgs Pkgs) {
	groupMap := make(map[string]*ProductPackage)
	sortedKey := []string{}
	newpkgs = []*ProductPackage{}

	for _, pkg := range pkgs {
		var product = Product{
			Source:      "origin",
			Name:        pkg.Name,
			PaymentType: pkg.PaymentType,
			AutoRenew:   pkg.AutoRenew,
			AsSubscribe: pkg.AsSubscribe,
			Bundle:      pkg.Bundle,
		}

		if pkg.Bundle != nil && len(pkg.Bundle.Prefix) > 0 {
			product.Prefix = pkg.Bundle.Prefix
		}

		if pkg.Bundle != nil {
			product.Bundle = pkg.Bundle

			imageHost := "https://images.kktv.com.tw"
			if kkapp.App.Debug {
				imageHost = "https://test-images.kktv.com.tw"
			}

			if product.Bundle.CampaignImage != "" {
				product.Bundle.CampaignImage = fmt.Sprintf("%s/%s", imageHost, product.Bundle.CampaignImage)
			}

			if product.Bundle.BackgroundImage != "" {
				product.Bundle.BackgroundImage = fmt.Sprintf("%s/%s", imageHost, product.Bundle.BackgroundImage)
			}
		}

		if _, exist := groupMap[pkg.ID]; !exist {
			groupMap[pkg.ID] = pkg
			sortedKey = append(sortedKey, pkg.ID)
		}
		if product.Name != "" {
			groupMap[pkg.ID].Products = append(groupMap[pkg.ID].Products, product)
		}
	}

	for _, key := range sortedKey {
		newpkgs = append(newpkgs, groupMap[key])
	}
	return
}

func (pkgs Pkgs) MapBillingProducts() {
	// update cache for billing products
	billingProducts, err := kkapp.App.BillingClient.ListProducts()
	if err != nil {
		plog.Warn("GetConsoleBillingProduct: billingClient: list products error").Err(err).Send()
	}

	for _, pkg := range pkgs {
		var billingProductIds []string
		json.Unmarshal(pkg.BillingProductIds, &billingProductIds)

		for _, identifier := range billingProductIds {
			billingProduct := billingProducts.Map[identifier]

			product := Product{
				Source:      "billing",
				Name:        billingProduct.Identifier,
				PaymentType: billingProduct.PaymentType,
				AutoRenew:   billingProduct.Recurring,
				AsSubscribe: billingProduct.IntroductoryOfferable,
				Prefix:      []string{},
				Bundle:      &ProductBundle{},
			}

			if pkg.Bundle != nil && len(pkg.Bundle.Prefix) > 0 {
				product.Prefix = pkg.Bundle.Prefix
			}

			if pkg.Bundle != nil {
				product.Bundle = pkg.Bundle
				// default prod image
				imageHost := "https://images.kktv.com.tw"

				if kkapp.App.Debug {
					// debug env use test image
					imageHost = "https://test-images.kktv.com.tw"
				}

				if product.Bundle.CampaignImage != "" {
					product.Bundle.CampaignImage = fmt.Sprintf("%s/%s", imageHost, product.Bundle.CampaignImage)
				}

				if product.Bundle.BackgroundImage != "" {
					product.Bundle.BackgroundImage = fmt.Sprintf("%s/%s", imageHost, product.Bundle.BackgroundImage)
				}
			}

			pkg.Products = append(pkg.Products, product)
		}
	}
}

// NewProductPackage return a *ProductPackage
func NewProductPackage() (pp *ProductPackage) {
	return pp
}
