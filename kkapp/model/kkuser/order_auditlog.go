package kkuser

import (
	"fmt"

	"github.com/KKTV/kktv-api-v3/kkapp"
)

var (
	orderAuditSQLs = map[string]string{
		"get_audit_logs_by_order": `SELECT * FROM auditlog 
			WHERE action = 'update_order' 
			AND before->>'id' = $1 
			AND created_at >= (NOW() - INTERVAL '1 year') 
			ORDER BY created_at DESC`,
	}
)

type OrderAuditLog struct {
	OrderID string                   `json:"user_id,omitempty"`
	Logs    []map[string]interface{} `json:"logs,omitempty"`
}

func (a *OrderAuditLog) Load() {
	db := kkapp.App.DbMeta.Slave()
	rows, err := db.Queryx(orderAuditSQLs["get_audit_logs_by_order"], a.OrderID)
	if err != nil {
		fmt.Println(err)
	}
	defer rows.Close()

	var auditlogs []map[string]interface{}
	for rows.Next() {
		a := map[string]interface{}{}
		err = rows.MapScan(a)
		if err != nil {
			fmt.Println(err)
		}
		for k, encoded := range a {
			switch encoded.(type) {
			case []byte:
				a[k] = string(encoded.([]byte))
			}
		}
		auditlogs = append(auditlogs, a)
	}
	a.Logs = auditlogs
}
