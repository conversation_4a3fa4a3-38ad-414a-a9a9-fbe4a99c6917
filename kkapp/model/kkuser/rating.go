package kkuser

import (
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"strconv"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
)

var (
	// hash
	userRatingFmt = "user-rating:v1:%s:hash"
)

// RateItem struct for user rating
type RateItem struct {
	Rating    int   `json:"rating"`
	UpdatedAt int64 `json:"updated_at"`
}

// UserRating user all rating hash
type UserRating struct {
	Rating map[string]RateItem `json:"rating"`
	UserID string              `json:"-"`
}

func (i *UserRating) key() (key string) {
	return fmt.Sprintf(userRatingFmt, i.UserID)
}

// Load from redis hash
func (i *UserRating) Load() {

	key := i.key()
	pool := kkapp.App.RedisUser.Slave()
	hashMap, err := pool.Cmd("HGETALL", key).Map()

	if err != nil {
		log.Println("[ERROR]", err)
		return
	}
	i.Parse(hashMap)
}

// Parse Unmarshal from redis hash
func (i *UserRating) Parse(hashMap map[string]string) {

	itemMap := make(map[string]RateItem)
	for k, v := range hashMap {
		var item RateItem
		err := json.Unmarshal([]byte(v), &item)

		if err != nil {
			log.Println(err)
		} else {
			itemMap[k] = item
		}
	}

	i.Rating = itemMap
}

// Rate user rate
func (i *UserRating) Rate(titleid string, rating int) (err error) {

	key := i.key()

	if _, err = strconv.Atoi(titleid); err != nil {
		return
	}
	if rating >= 5 {
		rating = 5
	}

	if rating <= 0 {
		rating = 0
	}

	pool := kkapp.App.RedisUser.Master()
	if rating == 0 {
		// delete it
		pool.Cmd("HDEL", key, titleid)
	} else {
		rateitem := make(map[string]interface{})
		rateitem["rating"] = rating
		rateitem["updated_at"] = time.Now().Unix()
		jsonBytes, _ := json.Marshal(rateitem)
		pool.Cmd("HSET", key, titleid, jsonBytes)
	}

	return
}

// Get user rate for single title
func (i *UserRating) Get(titleid string) (item RateItem, err error) {
	key := i.key()
	pool := kkapp.App.RedisUser.Master()
	rateBytes, err := pool.Cmd("HGET", key, titleid).Bytes()
	if err != nil {
		// no user rating for this title
		return item, err
	}
	json.Unmarshal(rateBytes, &item)
	return
}

// NewUserRating user rate titleid
func NewUserRating(userid string) (item *UserRating, err error) {
	item = new(UserRating)

	if userid == "" {
		err = errors.New("wrong userid")
		return
	}

	item.UserID = userid
	return
}
