package recommend

import "sort"

// KeyVal key value sortr for map[string]int
type KeyVal struct {
	Key   string
	Value int
}

// KeyValSorter key value sortr for map[string]int
type KeyValSorter []KeyVal

// Sort a map[string]int
func (kvs KeyValSorter) Sort() {
	sort.Slice(kvs, func(i, j int) bool {
		return kvs[i].Value > kvs[j].Value // desc
	})
}

// NewKeyValSorter get a sorted KeyValue slice
func NewKeyValSorter(kvmap map[string]int) (result KeyValSorter) {
	for key, value := range kvmap {
		result = append(result, KeyVal{Key: key, Value: value})
	}
	result.Sort()
	return result
}
