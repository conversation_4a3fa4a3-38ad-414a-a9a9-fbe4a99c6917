package recommend

import (
	"encoding/json"
	"fmt"
	"log"
	"math/rand"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/model"
	"github.com/KKTV/kktv-api-v3/kkapp/model/kkuser"
)

var (
	userRSFmt             = "rs:v2:%s:json"
	watchedSeconds  int64 = 600
	watchedTitleNum       = 4
	watchedOK             = 2      // exceed this count of an spisode consider watched
	rsTimeOut       int64 = 21600  // 6*60*60 seconds, 6 hours
	rsTTL           int64 = 259200 //  86400 * 3,  3 days
	// genre "浪漫愛情", "職場社會", "懸疑推理", "科幻想像", "家庭鄉土", "音樂綜藝", "古裝史劇", "經典改編"
	// 家庭鄉土 經典改編 目前都下架了
	// 音樂綜藝 改成 綜藝 ，已經沒有要出現在 featured page 了
	paddingGenres   = []string{"浪漫愛情", "職場社會", "懸疑推理", "科幻想像", "古裝史劇", "動漫"}
	recommendFields = []string{"id", "title", "title_type", "is_ending", "end_year", "user_rating", "user_rating_count", "genres"}
)

// Recommend store user action for recommendation
// refer https://s2lufz895e.execute-api.ap-northeast-1.amazonaws.com/00/people/:userID/recommended_lists
// spec https://iqkual.axshare.com/#g=1&p=featured
type Recommend struct {
	UserID             string   `json:"user_id"`
	Watched            []string `json:"watched"`
	Liked              []string `json:"liked"`
	Hated              []string `json:"hated"`
	Favorited          []string `json:"favorited"`
	Genre              []string `json:"genre"`
	RecommendWatched   []string `json:"recommend_watched"`
	RecommendLiked     []string `json:"recommend_liked"`
	RecommendFavorited []string `json:"recommend_favorited"`
	ExpiredAt          int64    `json:"expiredAt"`
}

// AmplitudeEventContext for api recomendation
type AmplitudeEventContext struct {
	ShowedMechanism string `json:"showed_mechanism"`
	Source          string `json:"source"`
}

// "amplitude_event_context": {
// 	"showed_mechanism": "fixed",
// 	"source": "human"
// 	},

func (i *Recommend) key() (key string) {
	return fmt.Sprintf(userRSFmt, i.UserID)
}

// Load user favorite titleID
func (i *Recommend) Load() {
	// type of key => zset
	key := i.key()
	pool := kkapp.App.RedisRs.Slave()
	rsBytes, err := pool.Cmd("GET", key).Bytes()
	if err != nil {
		log.Println("[ERROR] recommend", err)
		return
	}

	err = json.Unmarshal(rsBytes, i)

	if err != nil {
		log.Println("[ERROR] recommend format", key)
	}
	return
}

// Save user recommend to redis
func (i *Recommend) Save() {
	i.ExpiredAt = time.Now().Truncate(time.Second).Unix() + rsTimeOut
	rsBytes, _ := json.Marshal(i)
	key := i.key()
	pool := kkapp.App.RedisRs.Master()
	err := pool.Cmd("SETEX", key, rsTTL, rsBytes).Err

	if err != nil {
		log.Println("[ERROR] recommend set", key)
	}
}

func watched(userID string, hatedMap map[string]bool) (watchedTitleID []string) {
	// get the validated latest 5 watched title
	// what means watched a title
	// wtached 2 or 2 more episode
	// each episode at least longer than 600 seconds ( 10 minutes, avoid the trailer 預告)
	// or if the title type is film and user watch half episode

	log.Println("Watch History", userID)
	ws, err := kkuser.NewWatchHistory(userID)
	ws.Load()
	if err != nil {
		log.Println("[ERROR]", err)
		return
	}
	for _, titleID := range ws.Titles {
		if _, ok := hatedMap[titleID]; ok {
			// make sure not in hatedMap
			continue
		}
		title, _ := model.NewUserSingleTitleDetail(userID, titleID)
		for _, series := range title.Series {
			okCount := len(series.Episodes) / 2
			watchedCount := 0
			for _, episode := range series.Episodes {
				if lp, ok := episode.LastPlayed.(kkuser.LastPlayed); ok {
					if lp.LastPlayedOffset > watchedSeconds {
						watchedCount++
					}
					// log.Println(lp, watchedCount, okCount)
					// watched 2 episode, or watched more than half episodes
					if watchedCount >= watchedOK || watchedCount > okCount {
						watchedTitleID = append(watchedTitleID, title.ID)
						break
					}
				}
			}
			// found one series already
			if watchedCount >= watchedOK || watchedCount > okCount {
				break
			}
		}

		// got enough watched Titles
		if len(watchedTitleID) >= watchedTitleNum {
			break
		}
	}
	log.Println("WATCHED TITLE", watchedTitleID)
	return
}

func rated(userID string) (likedTitleID []string, hatedTitleID []string) {
	// get the rate score great than 4 (0~5)
	log.Println("Rated Titles", userID)
	ur, err := kkuser.NewUserRating(userID)
	ur.Load()
	if err != nil {
		log.Println("[ERROR]", err)
		return
	}
	for key, item := range ur.Rating {
		if item.Rating >= 4 {
			likedTitleID = append(likedTitleID, key)
		}

		if item.Rating < 3 {
			hatedTitleID = append(hatedTitleID, key)
		}
	}
	log.Println("LIKE TITLE", likedTitleID)
	log.Println("HATE TITLE", hatedTitleID)
	return
}

func favorited(userID string) (favoritedTitleID []string) {
	log.Println("Favorited Titles", userID)
	fav, err := kkuser.NewFavorite(userID)
	fav.Load()
	if err != nil {
		log.Println("[ERROR]", err)
		return
	}
	return fav.Titles
}

// Worker to process user recommend
func (i *Recommend) Worker() {
	userID := i.UserID
	goodMap := make(map[string]bool)
	badMap := make(map[string]bool)
	preferredGenres := []string{}

	// user data process
	likedID, hatedID := rated(userID)

	for _, key := range likedID {
		goodMap[key] = false
	}

	// bad title
	for _, key := range hatedID {
		badMap[key] = false
	}

	watchedID := watched(userID, badMap)
	favoritedID := favorited(userID)

	for _, key := range watchedID {
		goodMap[key] = false
	}

	for _, key := range favoritedID {
		goodMap[key] = false
	}

	i.Watched = watchedID[:]
	i.Favorited = favoritedID[:]
	i.Liked = likedID[:]
	i.Hated = hatedID
	i.RecommendFavorited = []string{}
	i.RecommendWatched = []string{}
	i.RecommendLiked = []string{}

	// preferrd genres
	titleIDs := []string{}
	for key := range goodMap {
		titleIDs = append(titleIDs, key)
	}
	log.Println(titleIDs)

	titles, _ := model.NewTitleDetailsViaFields(titleIDs, recommendFields)
	genreMap := make(map[string]int)

	for _, title := range titles {
		for _, item := range title.Genres {
			genreMap[item.CollectionName] = genreMap[item.CollectionName] + 1
		}
	}
	sortedGenre := NewKeyValSorter(genreMap)
	minGenreCount := 4
	preferredGenreMap := make(map[string]bool)

	for idx, item := range sortedGenre {
		if idx < minGenreCount {
			preferredGenres = append(preferredGenres, item.Key)
			preferredGenreMap[item.Key] = true
		}
	}
	// if no enough preferred genres, fill it
	if len(preferredGenres) < minGenreCount {
		for _, key := range paddingGenres {
			if _, ok := preferredGenreMap[key]; !ok {
				preferredGenres = append(preferredGenres, key)
			}
			if len(preferredGenres) >= minGenreCount {
				break
			}
		}
	}
	i.Genre = preferredGenres

	// recommend start to pick
	// watched
	rand.Shuffle(len(watchedID), func(i, j int) {
		watchedID[i], watchedID[j] = watchedID[j], watchedID[i]
	})
	// favorited
	rand.Shuffle(len(favoritedID), func(i, j int) {
		favoritedID[i], favoritedID[j] = favoritedID[j], favoritedID[i]
	})
	// liked
	rand.Shuffle(len(likedID), func(i, j int) {
		likedID[i], likedID[j] = likedID[j], likedID[i]
	})

	// map avoid duplicate recommendation
	recommendMap := make(map[string]bool)

	// pick up loop, picked the related_titles more than 8 items
	for _, titleID := range watchedID {
		if _, ok := recommendMap[titleID]; !ok {
			relatedTitles, err := model.NewTitleRelatedID(titleID)
			if err == nil && len(relatedTitles) > 8 {
				i.RecommendWatched = append(i.RecommendWatched, titleID)
				recommendMap[titleID] = true
			}
		}
		// take no more than 2
		if len(i.RecommendWatched) >= 2 {
			break
		}
	}
	for _, titleID := range favoritedID {
		if _, ok := recommendMap[titleID]; !ok {
			relatedTitles, err := model.NewTitleRelatedID(titleID)
			if err == nil && len(relatedTitles) > 8 {
				i.RecommendFavorited = append(i.RecommendFavorited, titleID)
				recommendMap[titleID] = true
			}
		}
		// take no more than 2
		if len(i.RecommendFavorited) >= 2 {
			break
		}
	}

	for _, titleID := range likedID {
		if _, ok := recommendMap[titleID]; !ok {
			relatedTitles, err := model.NewTitleRelatedID(titleID)
			if err == nil && len(relatedTitles) > 8 {
				i.RecommendLiked = append(i.RecommendLiked, titleID)
				recommendMap[titleID] = true
			}
		}
		// take no more than 2
		if len(i.RecommendLiked) >= 2 {
			break
		}
	}

	// save it
	i.ExpiredAt = time.Now().Unix() + rsTimeOut
	i.Save()
}

// NewRecommend new user recommend
func NewRecommend(userID string) (item *Recommend) {
	item = new(Recommend)

	if userID == "" {
		log.Println("[ERROR] not userID, nothing we can do here.")
		return
	}

	item.UserID = userID
	item.Load()

	now := time.Now().Unix()
	// item.Worker()
	if now > item.ExpiredAt {
		// this user recommendation had expired, fire a new worker to
		// process the latest user action
		// get the new recommendation
		go item.Worker()
	}
	return
}
