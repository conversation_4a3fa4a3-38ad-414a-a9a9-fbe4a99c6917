package model

import "testing"

func TestUseLegacyProductName(t *testing.T) {
	testCases := []struct {
		name        string
		paymentType string
		want        bool
	}{
		{
			name:        "credit_card should be TRUE",
			paymentType: "credit_card",
			want:        true,
		},
		{
			name:        "iab should be FALSE",
			paymentType: "iab",
			want:        false,
		},
		{
			name:        "pxpayplus should be TRUE",
			paymentType: "pxpayplus",
			want:        true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			got := useLegacyProductName(tc.paymentType)
			if got != tc.want {
				t.<PERSON>("useLegacyProductName() = %v, want %v", got, tc.want)
			}
		})
	}
}
