package model

import (
	"crypto/md5"
	"encoding/json"
	"fmt"
	"sort"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
)

var (
	announceKey = "meta:v1:system-announcements:json"
)

// AnnounceItem for api
type AnnounceItem struct {
	URL       string `json:"url"`
	Message   string `json:"message"`
	ID        string `json:"id,omitempty"`
	StartTime int64  `json:"start_time,omitempty"`
	EndTime   int64  `json:"end_time,omitempty"`
}

// AnnounceItems for api
type AnnounceItems []AnnounceItem

func (a AnnounceItems) Len() int {
	return len(a)
}
func (a AnnounceItems) Swap(i, j int) {
	a[i], a[j] = a[j], a[i]
}
func (a AnnounceItems) Less(i, j int) bool {
	return a[i].StartTime < a[j].StartTime
}

// Announcement struct for api
type Announcement struct {
	Announcement AnnounceItems `json:"announcements"`
}

// Save this Annoucement to redis
func (i *Announcement) Save() (err error) {
	dataBytes, err := json.Marshal(i)
	if err != nil {
		return
	}
	// redis write
	pool := kkapp.App.RedisMeta.Master()
	err = pool.Cmd("SET", announceKey, dataBytes).Err
	return
}

// NewAnnouncement get announcements
func NewAnnouncement() (item *AnnounceItem, err error) {

	announce := new(Announcement)
	// item := new(wrapServiceStatus)
	// read only
	pool := kkapp.App.RedisMeta.Slave()
	resp, err := pool.Cmd("GET", announceKey).Bytes()

	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(resp, announce)

	if err != nil {
		return nil, err
	}

	sort.Sort(announce.Announcement)
	if len(announce.Announcement) > 0 {
		now := time.Now().Unix()
		for _, obj := range announce.Announcement {
			if now > obj.StartTime && now < obj.EndTime {
				// found retrun
				item = new(AnnounceItem)
				item.URL = obj.URL
				item.Message = obj.Message
				item.ID = fmt.Sprintf("%x", md5.Sum([]byte(item.Message+item.URL)))
				return item, nil
			}
		}
	}
	return item, err
}

// NewAllAnnouncement get all announcements for console dashboard
func NewAllAnnouncement() (announce *Announcement, err error) {
	announce = new(Announcement)
	// read only
	pool := kkapp.App.RedisMeta.Slave()
	resp, err := pool.Cmd("GET", announceKey).Bytes()

	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(resp, announce)

	if err != nil {
		return nil, err
	}
	sort.Sort(announce.Announcement)
	return
}
