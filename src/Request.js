const supertest = require('supertest');
const uuid = require("uuid"); //require supertest

// this JWT token will be expired at 2072-07-21 3:46:45PM
const GUEST_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJfcmFuZG9tIjoiY2JsMmZwYTg3ZDVsMWVtNGpoaGciLCJhdWQiOiJra3R2LmNvbSIsImV4cCI6MzIzNjMxMjgwNSwiaWF0IjoxNjU5NTEyODA1LCJpc3MiOiJLS1RWIiwicm9sZSI6Imd1ZXN0Iiwic3ViIjoiZ3Vlc3Q6S0tUVi1jbGllbnRzOjMzOTBlZDA0LWVhMTEtNDc4MC1iZDRmLTQ4Y2QxZTU3MTUzYSIsInR5cGUiOiJnZW5lcmFsIn0.cUX6pHrFXN-3iMY34_TM_z-jGa8JH6OveZA1XuoQOdc';

const deviceID = uuid.v4();
const hook = (method = 'post') => (args) => {
    return supertest('https://api.kktv.me')[method](args)
        .set('User-Agent', 'kktv-api-test agent/1.0')
        .set('Authorization', `Bearer ${GUEST_TOKEN}`)
        .set('X-Device-Id', deviceID);
}

const request = {
    post: hook('post'),
    get: hook('get'),
    put: hook('put'),
    delete: hook('delete'),
};

module.exports = {
    request
};
