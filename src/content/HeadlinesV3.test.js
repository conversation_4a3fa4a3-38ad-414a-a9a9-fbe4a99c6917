const {request} = require("../Server");

describe("HeadlineV3", () => {
    let response;
    beforeAll(async () => {
        // Wait for the above test to run and populate 'value'.
        response = await request.get("/v3/headlines");
    });

    it("should return 200 OK and have content in headlines", async () => {
        expect(response.status).toBe(200);
        expect(response.body.data.headlines).toBeDefined();
        expect(response.body.data.headlines.length).toBeGreaterThan(3)

        response.body.data.headlines.forEach(item => {
            expect(item.title).not.toBe('');
            expect(item.summary).not.toBe('');
            expect(item.topic).not.toBe('');
            expect(item.type).not.toBe('');
            expect(item.image).not.toBe('');
        });
    })
})
