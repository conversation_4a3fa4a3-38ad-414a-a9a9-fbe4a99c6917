const {request} = require("../Request");

describe("CollectionV4", () => {
    const collectionBase = "/v4/a/collections";
    beforeAll(async () => {

    });

    function expectResponseHasContent(response, titleAmount = 5) {
        expect(response.status).toBe(200);
        expect(response.body.data.items.length).toBeGreaterThan(titleAmount);
        expect(response.body.data.pagination.total).toBeGreaterThan(titleAmount);
    }

    it("should have content for main key `genre:戲劇`", async () => {
        let response = await request.get(encodeURI(collectionBase + "/genre:戲劇"));
        expectResponseHasContent(response, 5);

        expect(response.body.data.filter.map(item => item.display_name)).toEqual(["地區", "類型"]);
    })

    it("should have content for main key `genre:戲劇` + country:Taiwan + theme:浪漫愛情", async () => {
        let response = await request.get(encodeURI(collectionBase + "/genre:戲劇?country=Taiwan&theme=浪漫愛情"));
        expectResponseHasContent(response, 5);
        expect(response.body.data.filter.map(item => item.display_name)).toEqual(["地區", "類型"]);
    })

    it("should have same title content with v3 collection API", async () => {
        const queryString = "/genre:動漫?country=Japan&theme=奇幻冒險";
        let v3Resp = await request.get(encodeURI("/v3/collections" + queryString));
        let v4Resp = await request.get(encodeURI("/v4/a/collections" + queryString));

        expect(v4Resp.body.data.pagination.total).toEqual(v3Resp.body.data.total);
        let v4titleAmount = v4Resp.body.data.items.length;
        let v3titleAmount = v3Resp.body.data.items.length;
        expect(v4titleAmount).toEqual(v3titleAmount);
        expect(v4titleAmount).toBeGreaterThan(5);
        expect(v4Resp.body.data.items[0].name).toEqual(v3Resp.body.data.items[0].title);
        expect(v4Resp.body.data.items[v4titleAmount-1].name).toEqual(v3Resp.body.data.items[v3titleAmount-1].title);

    })
});
