{"name": "kktv-api-v3", "description": "KKTV V3 API", "role": "arn:aws:iam::************:role/kktv-test-lambda-vpc-basic", "vpc": {"securityGroups": ["sg-30aa0754"], "subnets": ["subnet-21f3a478", "subnet-241b2553"]}, "environment": {"ENV": "test", "DEBUG": "true", "SSO_ALLOWED_DOMAIN": "kkculture.com,choco.media", "API_DOC_OAUTH_CALLBACK": "https://test-api.kktv.me/auth/doc/callback?provider=google", "API_DOC_SSO_ALLOWED_ACCOUNTS": "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>", "REDISMETA": "test-meta-redis.r3iujk.ng.0001.apne1.cache.amazonaws.com:6379,test-meta-redis-ro.r3iujk.ng.0001.apne1.cache.amazonaws.com:6379", "REDISUSER": "test-meta-redis.r3iujk.ng.0001.apne1.cache.amazonaws.com:6379,test-meta-redis-ro.r3iujk.ng.0001.apne1.cache.amazonaws.com:6379", "REDISRS": "test-meta-redis.r3iujk.ng.0001.apne1.cache.amazonaws.com:6379,test-meta-redis-ro.r3iujk.ng.0001.apne1.cache.amazonaws.com:6379", "REDISPLAYBACK": "test-meta-redis.r3iujk.ng.0001.apne1.cache.amazonaws.com:6379,test-meta-redis-ro.r3iujk.ng.0001.apne1.cache.amazonaws.com:6379", "DBUSER": "postgres://martian:<EMAIL>:5432/kktv_users,postgres://martian:<EMAIL>:5432/kktv_users", "DBMETA": "postgres://editor:<EMAIL>:5432/meta", "DBREDEEM": "postgres://sinner:<EMAIL>:5432/kktv_redeem", "SEARCHHOST": "http://search-kktv-test-elasticsearch-uhzg75sj7xa7zggyj7sff2yche.ap-northeast-1.es.amazonaws.com", "BILLINGHOST": "https://bapi-stag.kktv.me", "NEWEBPAY_PAYMENT_URL": "https://ccore.newebpay.com/MPG/mpg_gateway", "NEXMO_API_KEY": "********", "NEXMO_API_SECRET": "3lJGuzEZDvzxN2Ci", "NEXMO_PHONE_NUMBER": "KKTV", "MAILGUN_DOMAIN": "kktv.me", "MAILGUN_API_KEY": "************************************", "CLIENT_WEB_HOST": "https://test-web.kktv.com.tw", "JWT_AUTH_SIGNED_STRING": "cffyrt3VQWPAUujwU5xvldKq9Wu6X7TKxMaHXVDnexwoXcZGOtg3sUAsTzAMfXCT", "LICENSE_URL_WIDEVINE": "https://test-license.kktv.com.tw", "LICENSE_URL_PLAYREADY": "https://test-license.kktv.com.tw", "LICENSE_URL_FAIRPLAY": "https://test-license.kktv.com.tw", "KKS_BV_LICENSE_URL": "https://drm.platform.blendvision.com/api/v3/drm/license", "KKS_BV_TENANT_ID": "59094e78-aa39-4a26-b076-c365a27cd134", "KKS_BV_TENANT_ID_FOR_LINETV": "00037e2b-b3fd-4d44-a3cb-4f227a518142", "KKBOX_APP_ID": "KKTV", "KKBOX_APP_SECRET": "2042c77fb3b3d7df0f1d341b3084ff4a19693ea5", "KKBOX_BASE_URL": "https://api-member.kkbox-testing.com.tw", "THEATER_CDN_SIGN_KEY": "6aWbx7JfdDDcRwa1", "EZPAY_API_HOST": "https://cinv.ezpay.com.tw", "EZPAY_MERCHANT_ID": "31576568", "EZPAY_HASH_KEY": "6vIvMoUM73VcNRt4vcHlN9bAtXyEt7vz", "EZPAY_HASH_IV": "Dd5qBZcJ27VQZoVu", "MOD_API_KEY": "eF897R3n8T8S3SZEWmtlwan4jAGDmBfoTgFFRxjb", "MOD_CLIENT_USERNAME": "kktv", "MOD_CLIENT_PASSWORD": "0igQS3FP+DiqNc&OYeqVY1w_GIRaDwHoP5ZRaDnGE^dH%Qpefl", "MOD_PROXY_URL": "http://**************/v2", "SLACK_TOKEN": "*********************************************************", "GENAI_GEMINI_API_KEY": "AIzaSyANoPSvew5SoFSkHaONYETnMmaCcy_f9FU", "AWS_S3_BUCKET_KKTV_API": "kktv-test-api", "GOOGLE_APP_ID": "**********-rec4u6f2j1t6pv6m690cqlfhc2q2jur2.apps.googleusercontent.com", "GOOGLE_APP_SECRET": "wUGfIt3oLdj1YPYWLmrV7LWW"}, "timeout": 30, "memory": 1600}