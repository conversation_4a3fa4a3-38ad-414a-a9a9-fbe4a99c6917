#!/bin/bash

# the AWS container Environment url encode
s3path=`python3 -c "import urllib.parse as ul;print(ul.unquote_plus('$@'))"`
baseName=$(basename $s3path)
uploadDir=$(dirname $s3path)
s3UploadPath="$uploadDir/translator.mp4"

echo "Downloading $s3path"
aws s3 cp $s3path ./$baseName

echo "Convert to 240p for translator"
ffmpeg -i $baseName -i watermark.png -filter_complex "scale=trunc(oh*a/2)*2:240,overlay=(main_w-overlay_w)/2:(main_h-overlay_h)/2" -preset ultrafast translator.mp4

echo "Upload to $s3UploadPath"
aws s3 cp ./translator.mp4 $s3UploadPath
