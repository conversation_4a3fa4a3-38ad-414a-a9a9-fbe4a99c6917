package main

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/kksearch"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	"github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/avast/retry-go"
	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-sdk-go/service/dynamodb"
	"github.com/aws/aws-sdk-go/service/dynamodb/dynamodbattribute"
	"gopkg.in/guregu/null.v3"
)

var (
	sqlsync = map[string]string{
		"title":   `INSERT INTO meta_title (id, name, editor_comments, meta, same_as, united_id, united_name) VALUES ($1, $2, $3, $4, $5, $6, $7) ON CONFLICT (id) DO UPDATE SET name=$2, meta=$4, same_as=$5, united_id=$6, united_name=$7`,
		"episode": `INSERT INTO meta_episode (id, name, series_id, meta) VALUES ($1, $2, $3, $4) ON CONFLICT (id) DO UPDATE SET name=$2, meta=meta_episode.meta || $4`,
		"extra":   `INSERT INTO meta_extra (id, name, title_id, series_id, meta) VALUES ($1, $2, $3, $4, $5) ON CONFLICT (id) DO UPDATE SET meta=$5`,
		"key": `INSERT INTO meta_key (id, content_id, content_type, key_value, meta) VALUES ($1, $2, $3, $4, $5) ON CONFLICT (id) DO UPDATE SET
 content_id=$2, content_type=$3, key_value=$4, meta=$5;`,
		"series": `INSERT INTO meta_series (id, name, title_id, meta, united_title_id, united_id, united_name, audio_track_lang) VALUES ($1, $2, $3, $4, $5, $6, $7, $8) ON CONFLICT (id) DO UPDATE SET name=$2, meta=$4, united_title_id=$5, united_id=$6, united_name=$7, audio_track_lang=$8`,
	}
)

type sameAs struct {
	Wiki  string `json:"wiki,omitempty"`
	Imdb  string `json:"imdb,omitempty"`
	Other string `json:"other,omitempty"`
}

func init() {
	log.Info("Lambda MetaToRds init").Send()
	if err := config.Init(); err != nil {
		log.Fatal("main: init config fail").Err(err).Send()
	}
	kkapp.ContextInit()
}

// refer https://stackoverflow.com/questions/49129534/unmarshal-mapstringdynamodbattributevalue-into-a-struct
// UnmarshalStreamImage converts events.DynamoDBAttributeValue to struct
func UnmarshalStreamImage(attribute map[string]events.DynamoDBAttributeValue, out interface{}) error {

	dbAttrMap := make(map[string]*dynamodb.AttributeValue)

	for k, v := range attribute {

		var dbAttr dynamodb.AttributeValue

		bytes, marshalErr := v.MarshalJSON()
		if marshalErr != nil {
			return marshalErr
		}

		json.Unmarshal(bytes, &dbAttr)
		dbAttrMap[k] = &dbAttr
	}

	return dynamodbattribute.UnmarshalMap(dbAttrMap, out)
	// return dynamo.UnmarshalItem(dbAttrMap, out)

}

// data-keys
func HandlerKey(record events.DynamoDBEventRecord) {
	//  meta_key
	// id character varying(14) NOT NULL,
	var err error
	var jsonBytes []byte
	var keyID, contentID, contentType, keyValue string
	newOne := record.Change.NewImage
	log.Info("handling key").Interface("new_image", newOne).Send()

	if _, ok := newOne["key_id"]; ok {
		keyID = newOne["key_id"].String()
	}

	if _, ok := newOne["key_value"]; ok {
		keyValue = newOne["key_value"].String()
	}

	if _, ok := newOne["content_id"]; ok {
		contentID = newOne["content_id"].String()
	}

	if _, ok := newOne["content_type"]; ok {
		contentType = newOne["content_type"].String()
	}

	if keyID == "" {
		log.Error("empty key_id").Send()
		return
	}

	item := make(map[string]interface{})
	if err := UnmarshalStreamImage(newOne, &item); err != nil {
		log.Error("fail to dynamodb convert").Err(err).Send()
		return
	}

	jsonBytes, err = json.Marshal(item)
	if err != nil {
		log.Error("fail to marshal").Err(err).Send()
		return
	}

	log.Info("ready to insert into meta_key").
		RawJSON("meta", jsonBytes).Str("key_id", keyID).Str("content_id", contentID).Send()

	db := kkapp.App.DbMeta.Master()

	if _, err := db.Exec(sqlsync["key"], keyID, contentID, contentType, keyValue, jsonBytes); err != nil {
		log.Error("fail to insert into key db").Err(err).RawJSON("meta", jsonBytes).Send()
	}
}

// data-extras
func HandlerExtra(record events.DynamoDBEventRecord) {
	//  meta_extra
	// id        | character varying(14) |           | not null |
	var err error
	var jsonBytes []byte
	var episodeID, seriesID, titleID, episodeName string

	newOne := record.Change.NewImage
	log.Info("handling extra").Interface("new_image", newOne).Send()

	if _, ok := newOne["episode_id"]; ok {
		episodeID = newOne["episode_id"].String()
	}

	if _, ok := newOne["series_id"]; ok {
		seriesID = newOne["series_id"].String()
	}

	if _, ok := newOne["title_id"]; ok {
		titleID = newOne["title_id"].String()
	}

	if _, ok := newOne["episode_name"]; ok {
		episodeName = newOne["episode_name"].String()
	}

	if episodeID == "" || seriesID == "" {
		log.Error("empty episode_id or series_id").Send()
		return
	}

	item := make(map[string]interface{})
	err = UnmarshalStreamImage(newOne, &item)

	if err != nil {
		log.Error("fail to dynamodb convert").Err(err).Send()
		return
	}

	jsonBytes, err = json.Marshal(item)
	if err != nil {
		log.Error("fail to marshal").Err(err).Send()
		return
	}

	db := kkapp.App.DbMeta.Master()
	if _, err := db.Exec(sqlsync["extra"], episodeID, episodeName, titleID, seriesID, jsonBytes); err != nil {
		log.Error("fail to insert into extra db").Err(err).RawJSON("meta", jsonBytes).Send()
	}

	// write to redis
	if titleID != "" {
		// sync to meta database meta_extra
		dbmeta.Extra2Redis(titleID)
		// sync to manifet redis
		dbmeta.ManifestBytes2Redis(episodeID, jsonBytes)
	}
}

func HandlerTitle(record events.DynamoDBEventRecord) {
	//  meta_title
	// id              | character varying(8)  |           | not null |
	var err error
	var jsonBytes []byte
	var titleID, titleName string
	var unitedTitleID, unitedTitleName null.String

	newOne := record.Change.NewImage
	log.Info("handling title").Interface("new_image", newOne).Send()

	if _, ok := newOne["title_id"]; ok {
		titleID = newOne["title_id"].String()
	}
	if _, ok := newOne["title_name"]; ok {
		titleName = newOne["title_name"].String()
	}
	if titleID == "" || titleName == "" {
		log.Error("empty title_id or title_name").Send()
		return
	}

	if s, ok := newOne["united_title_id"]; ok {
		unitedTitleID = null.StringFrom(s.String())
	}
	if s, ok := newOne["united_title_name"]; ok {
		unitedTitleName = null.StringFrom(s.String())
	}

	item := make(map[string]interface{})
	if err := UnmarshalStreamImage(newOne, &item); err != nil {
		log.Error("fail to dynamodb convert").Err(err).Send()
		return
	}
	item, sameAs := processSameAs(item)

	jsonBytes, err = json.Marshal(item)
	if err != nil {
		log.Error("fail to marshal").Err(err).Send()
		return
	}

	log.Info("ready to insert into meta_title").
		Str("title_id", titleID).Str("title_name", titleName).Bytes("meta", jsonBytes).Interface("same_as", sameAs).
		Send()

	db := kkapp.App.DbMeta.Master()

	if _, err := db.Exec(sqlsync["title"], titleID, titleName, "", jsonBytes, sameAs, unitedTitleID, unitedTitleName); err != nil {
		log.Error("fail to insert into meta_title db").Err(err).
			Str("title_id", titleID).Str("title_name", titleName).Send()
	}

	// write to redis
	dbmeta.Title2Redis(titleID)
	// update elasticsearch
	if err := kksearch.SyncTitles([]string{titleID}); err != nil {
		log.Warn("fail to sync title to elasticsearch").Str("title_id", titleID).Err(err).Send()
	}
}

func processSameAs(item map[string]interface{}) (map[string]interface{}, sql.NullString) {
	if sameAsStr, ok := item["same_as"].(string); ok {
		var (
			sameAsRaw    string
			sameAsStruct sameAs
		)
		sameAsRaw = sameAsStr
		urls := strings.Split(sameAsRaw, ";")
		for _, url := range urls {
			switch {
			case strings.Contains(url, "wiki"):
				sameAsStruct.Wiki = url
			case strings.Contains(url, "imdb"):
				sameAsStruct.Imdb = url
			default:
				sameAsStruct.Other = url
			}
		}

		delete(item, "same_as")

		if jsonByte, err := json.Marshal(sameAsStruct); err != nil {
			log.Error("fail to processSameAs: marshal failed").Err(err).Send()
			return item, sql.NullString{Valid: false}
		} else {
			return item, sql.NullString{Valid: true, String: string(jsonByte)}
		}
	}
	return item, sql.NullString{Valid: false}
}

func HandlerEpisode(record events.DynamoDBEventRecord) {
	//  meta_episode
	// id        | character varying(14) |           | not null |
	var err error
	var jsonBytes []byte
	var episodeID, seriesID, episodeName string

	newOne := record.Change.NewImage
	log.Info("handling episode").Interface("new_image", newOne).Send()

	if _, ok := newOne["episode_id"]; ok {
		episodeID = newOne["episode_id"].String()
	}

	if _, ok := newOne["series_id"]; ok {
		seriesID = newOne["series_id"].String()
	}

	if _, ok := newOne["episode_name"]; ok {
		episodeName = newOne["episode_name"].String()
	}

	if episodeID == "" || seriesID == "" {
		log.Error("empty episode_id or series_id").Send()
		return
	}

	item := make(map[string]interface{})
	err = UnmarshalStreamImage(newOne, &item)
	if err != nil {
		log.Error("fail to dynamodb convert").Err(err).Send()
		return
	}

	jsonBytes, err = json.Marshal(item)
	if err != nil {
		log.Error("fail to marshal").Err(err).Send()
		return
	}

	log.Info("ready to insert into meta_episode").
		RawJSON("meta", jsonBytes).Str("episode_id", episodeID).Str("episode_name", episodeName).Str("series_id", seriesID).
		Send()

	db := kkapp.App.DbMeta.Master()

	// sync to meta database meta_episode
	if err := dbViolatesForeignKeyRetryProxy(
		func() error {
			_, err := db.Exec(sqlsync["episode"], episodeID, episodeName, seriesID, jsonBytes)
			return err
		},
		func(n uint, err error) {
			log.Warn("HandlerEpisode: retrying insert episode into db").Uint("times", n).Err(err).Send()
		},
	); err != nil {
		log.Error("fail to insert episode into db").Str("episode_id", episodeID).Err(err).Send()
	}

	// sync to manifest redis
	if _, _, err := dbmeta.ManifestBytes2Redis(episodeID, jsonBytes); err != nil {
		log.Warn("fail to dump manifest into redis").Str("episode_id", episodeID).Err(err).Send()
	}

	// sync to meta redis genarate the new title data
	if len(seriesID) >= 10 {
		title_id := seriesID[0:8]
		dbmeta.Title2Redis(title_id)

		// update elasticsearch
		kksearch.SyncTitles([]string{title_id})
	}
}

func HandlerSeries(record events.DynamoDBEventRecord) {
	//  meta_series
	// id     | name  | title_id | meta
	var err error
	var jsonBytes []byte
	newOne := record.Change.NewImage

	log.Info("handling series").Interface("new_image", newOne).Send()

	seriesNameFmt := "第%d季"

	var titleID, seriesID, seriesName string

	if _, ok := newOne["title_id"]; ok {

		titleID = newOne["title_id"].String()
	}

	if _, ok := newOne["series_id"]; ok {

		seriesID = newOne["series_id"].String()
	}

	if titleID == "" || seriesID == "" {
		log.Error("empty episode_id or series_id").Send()
		return
	}
	seriesNumber, err := strconv.Atoi(seriesID)
	if err != nil {
		log.Error("invalid series_id: not number").Send()
		return

	}

	item := make(map[string]interface{})
	if err = UnmarshalStreamImage(newOne, &item); err != nil {
		log.Warn("fail to dynamodb convert").Err(err).Send()
	}

	jsonBytes, err = json.Marshal(item)
	if err != nil {
		log.Error("fail to marshal").Err(err).Send()
		return
	}
	dbSeriesID := titleID + seriesID
	seriesName = fmt.Sprintf(seriesNameFmt, seriesNumber)

	var unitedTitleID, unitedSeriesID, unitedSeriesName, audioTrackLang null.String
	if s, ok := newOne["united_title_id"]; ok {
		unitedTitleID = null.StringFrom(s.String())
	}
	if s, ok := newOne["united_series_id"]; ok {
		str := s.String()
		if v, err := strconv.Atoi(str); err == nil {
			unitedSeriesID = null.StringFrom(fmt.Sprintf("%s%02d", unitedTitleID.String, v))
		}
	}
	if s, ok := newOne["united_series_name"]; ok {
		unitedSeriesName = null.StringFrom(s.String())
	}
	if s, ok := newOne["lang"]; ok {
		audioTrackLang = null.StringFrom(s.String())
	}

	db := kkapp.App.DbMeta.Master()

	if err := dbViolatesForeignKeyRetryProxy(
		func() error {
			_, err := db.Exec(sqlsync["series"], dbSeriesID, seriesName, titleID, jsonBytes, unitedTitleID, unitedSeriesID, unitedSeriesName, audioTrackLang)
			return err
		},
		func(n uint, err error) {
			log.Warn("HandlerSeries: retrying insert series into db").Uint("times", n).Err(err).Send()
		},
	); err != nil {
		log.Error("fail to insert meta_series into db").Err(err).
			Str("dbSeriesID", dbSeriesID).Str("seriesName", seriesName).Str("title_id", titleID).Send()
	}
	// write to redis
	if titleID != "" {
		dbmeta.Title2Redis(titleID)

		// update elasticsearch
		kksearch.SyncTitles([]string{titleID})
	}

}

// dbViolatesForeignKeyRetryProxy gives the exec function to retry to fix the https://kktv.atlassian.net/browse/KKTV-10611
// due to the DynamoDBEventRecord events are not following fixed order, sometimes the Handler receives Episode event then the Titles events,
// it causes violation of Postgres foreign key constraint.
// This retry proxy do retry 3 times in order to make sure the meta inserted into db successfully
func dbViolatesForeignKeyRetryProxy(exec func() error, onRetry func(n uint, err error)) error {
	return retry.Do(
		exec,
		retry.RetryIf(func(err error) bool {
			// the violation exception looks like "pq: insert or update on table "meta_episode" violates foreign key constraint "meta_episode_series_id_xxxx_fk_meta_series_id"
			return strings.Contains(err.Error(), "violates foreign key constraint")
		}),
		retry.OnRetry(onRetry),
		retry.Delay(1*time.Minute),
		retry.Attempts(3),
	)
}

func Handler(ctx context.Context, e events.DynamoDBEvent) {
	// "eventSourceARN": "arn:aws:dynamodb:us-west-2:account-id:table/ExampleTableWithStream/stream/2015-06-27T00:48:05.899",
	// EventName
	//    * INSERT - a new item was added to the table.
	//    * MODIFY - one or more of an existing item's attributes were modified.
	//    * REMOVE - the item was deleted from the table
	for _, record := range e.Records {
		fmt.Printf("Processing request data for event ID %s, type %s.\n", record.EventID, record.EventName)

		// Print new values for attributes name and age
		// name := record.Change.NewImage["name"].String()
		// age, _ := record.Change.NewImage["age"].Integer()
		// FIXME current not implement DELETE, because, that control via publish-data table
		if record.EventName == "REMOVE" {
			continue
		}

		table := strings.Split(record.EventSourceArn, "/")[1]

		log.Info("begin to handle event").Str("table", table).Interface("record", record).Send()

		switch {

		case strings.Contains(table, "data-episodes"):
			HandlerEpisode(record)

		case strings.Contains(table, "data-titles"):
			HandlerTitle(record)

		case strings.Contains(table, "data-extras"):
			HandlerExtra(record)

		case strings.Contains(table, "data-keys"):
			HandlerKey(record)

		case strings.Contains(table, "meta-series"):
			HandlerSeries(record)
		}

		log.Info("finish").Str("table", table).Str("event", record.EventName).Send()
	}
}

func main() {
	// AWS lambda environment, run and exit
	if os.Getenv("AWS_EXECUTION_ENV") != "" {
		lambda.Start(Handler)
		return
	}

	/* Local test*/
	var evt events.DynamoDBEvent
	// load test data from file ./testdata/modify_series.json
	f, err := os.Open("./functions/meta2rds/testdata/modify_series.json")
	if err != nil {
		log.Fatal("fail to open test data").Err(err).Send()
	}
	if err := json.NewDecoder(f).Decode(&evt); err != nil {
		log.Fatal("fail to decode test data").Err(err).Send()
	}

	Handler(context.Background(), evt)
}
