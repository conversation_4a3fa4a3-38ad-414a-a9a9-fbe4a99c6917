(function(t){function e(e){for(var i,s,o=e[0],l=e[1],c=e[2],d=0,p=[];d<o.length;d++)s=o[d],n[s]&&p.push(n[s][0]),n[s]=0;for(i in l)Object.prototype.hasOwnProperty.call(l,i)&&(t[i]=l[i]);u&&u(e);while(p.length)p.shift()();return r.push.apply(r,c||[]),a()}function a(){for(var t,e=0;e<r.length;e++){for(var a=r[e],i=!0,o=1;o<a.length;o++){var l=a[o];0!==n[l]&&(i=!1)}i&&(r.splice(e--,1),t=s(s.s=a[0]))}return t}var i={},n={app:0},r=[];function s(e){if(i[e])return i[e].exports;var a=i[e]={i:e,l:!1,exports:{}};return t[e].call(a.exports,a,a.exports,s),a.l=!0,a.exports}s.m=t,s.c=i,s.d=function(t,e,a){s.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:a})},s.r=function(t){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},s.t=function(t,e){if(1&e&&(t=s(t)),8&e)return t;if(4&e&&"object"===typeof t&&t&&t.__esModule)return t;var a=Object.create(null);if(s.r(a),Object.defineProperty(a,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var i in t)s.d(a,i,function(e){return t[e]}.bind(null,i));return a},s.n=function(t){var e=t&&t.__esModule?function(){return t["default"]}:function(){return t};return s.d(e,"a",e),e},s.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},s.p="/v3/console/";var o=window["webpackJsonp"]=window["webpackJsonp"]||[],l=o.push.bind(o);o.push=e,o=o.slice();for(var c=0;c<o.length;c++)e(o[c]);var u=l;r.push([0,"chunk-vendors"]),a()})({0:function(t,e,a){t.exports=a("56d7")},"0328":function(t,e,a){},"0671":function(t,e,a){"use strict";var i=a("6c80"),n=a.n(i);n.a},"0729":function(t,e,a){},"0d4e":function(t,e,a){"use strict";var i=a("f151"),n=a.n(i);n.a},"186c":function(t,e,a){},1914:function(t,e,a){},"1a9a":function(t,e,a){},"1e75":function(t,e,a){},"26c3":function(t,e,a){},"26da":function(t,e,a){},"2a16":function(t,e,a){"use strict";var i=a("f2b4"),n=a.n(i);n.a},"2a8f":function(t,e,a){"use strict";var i=a("8cfb"),n=a.n(i);n.a},"2b56":function(t,e,a){"use strict";var i=a("8069"),n=a.n(i);n.a},"2bd9":function(t,e,a){"use strict";var i=a("b878"),n=a.n(i);n.a},"2cb7":function(t,e,a){},"2f63":function(t,e,a){"use strict";var i=a("0729"),n=a.n(i);n.a},"3dd4":function(t,e,a){"use strict";var i=a("1914"),n=a.n(i);n.a},"45a5":function(t,e,a){},4678:function(t,e,a){var i={"./af":"2bfb","./af.js":"2bfb","./ar":"8e73","./ar-dz":"a356","./ar-dz.js":"a356","./ar-kw":"423e","./ar-kw.js":"423e","./ar-ly":"1cfd","./ar-ly.js":"1cfd","./ar-ma":"0a84","./ar-ma.js":"0a84","./ar-sa":"8230","./ar-sa.js":"8230","./ar-tn":"6d83","./ar-tn.js":"6d83","./ar.js":"8e73","./az":"485c","./az.js":"485c","./be":"1fc1","./be.js":"1fc1","./bg":"84aa","./bg.js":"84aa","./bm":"a7fa","./bm.js":"a7fa","./bn":"9043","./bn.js":"9043","./bo":"d26a","./bo.js":"d26a","./br":"6887","./br.js":"6887","./bs":"2554","./bs.js":"2554","./ca":"d716","./ca.js":"d716","./cs":"3c0d","./cs.js":"3c0d","./cv":"03ec","./cv.js":"03ec","./cy":"9797","./cy.js":"9797","./da":"0f14","./da.js":"0f14","./de":"b469","./de-at":"b3eb","./de-at.js":"b3eb","./de-ch":"bb71","./de-ch.js":"bb71","./de.js":"b469","./dv":"598a","./dv.js":"598a","./el":"8d47","./el.js":"8d47","./en-SG":"cdab","./en-SG.js":"cdab","./en-au":"0e6b","./en-au.js":"0e6b","./en-ca":"3886","./en-ca.js":"3886","./en-gb":"39a6","./en-gb.js":"39a6","./en-ie":"e1d3","./en-ie.js":"e1d3","./en-il":"7333","./en-il.js":"7333","./en-nz":"6f50","./en-nz.js":"6f50","./eo":"65db","./eo.js":"65db","./es":"898b","./es-do":"0a3c","./es-do.js":"0a3c","./es-us":"55c9","./es-us.js":"55c9","./es.js":"898b","./et":"ec18","./et.js":"ec18","./eu":"0ff2","./eu.js":"0ff2","./fa":"8df4","./fa.js":"8df4","./fi":"81e9","./fi.js":"81e9","./fo":"0721","./fo.js":"0721","./fr":"9f26","./fr-ca":"d9f8","./fr-ca.js":"d9f8","./fr-ch":"0e49","./fr-ch.js":"0e49","./fr.js":"9f26","./fy":"7118","./fy.js":"7118","./ga":"5120","./ga.js":"5120","./gd":"f6b4","./gd.js":"f6b4","./gl":"8840","./gl.js":"8840","./gom-latn":"0caa","./gom-latn.js":"0caa","./gu":"e0c5","./gu.js":"e0c5","./he":"c7aa","./he.js":"c7aa","./hi":"dc4d","./hi.js":"dc4d","./hr":"4ba9","./hr.js":"4ba9","./hu":"5b14","./hu.js":"5b14","./hy-am":"d6b6","./hy-am.js":"d6b6","./id":"5038","./id.js":"5038","./is":"0558","./is.js":"0558","./it":"6e98","./it-ch":"6f12","./it-ch.js":"6f12","./it.js":"6e98","./ja":"079e","./ja.js":"079e","./jv":"b540","./jv.js":"b540","./ka":"201b","./ka.js":"201b","./kk":"6d79","./kk.js":"6d79","./km":"e81d","./km.js":"e81d","./kn":"3e92","./kn.js":"3e92","./ko":"22f8","./ko.js":"22f8","./ku":"2421","./ku.js":"2421","./ky":"9609","./ky.js":"9609","./lb":"440c","./lb.js":"440c","./lo":"b29d","./lo.js":"b29d","./lt":"26f9","./lt.js":"26f9","./lv":"b97c","./lv.js":"b97c","./me":"293c","./me.js":"293c","./mi":"688b","./mi.js":"688b","./mk":"6909","./mk.js":"6909","./ml":"02fb","./ml.js":"02fb","./mn":"958b","./mn.js":"958b","./mr":"39bd","./mr.js":"39bd","./ms":"ebe4","./ms-my":"6403","./ms-my.js":"6403","./ms.js":"ebe4","./mt":"1b45","./mt.js":"1b45","./my":"8689","./my.js":"8689","./nb":"6ce3","./nb.js":"6ce3","./ne":"3a39","./ne.js":"3a39","./nl":"facd","./nl-be":"db29","./nl-be.js":"db29","./nl.js":"facd","./nn":"b84c","./nn.js":"b84c","./pa-in":"f3ff","./pa-in.js":"f3ff","./pl":"8d57","./pl.js":"8d57","./pt":"f260","./pt-br":"d2d4","./pt-br.js":"d2d4","./pt.js":"f260","./ro":"972c","./ro.js":"972c","./ru":"957c","./ru.js":"957c","./sd":"6784","./sd.js":"6784","./se":"ffff","./se.js":"ffff","./si":"eda5","./si.js":"eda5","./sk":"7be6","./sk.js":"7be6","./sl":"8155","./sl.js":"8155","./sq":"c8f3","./sq.js":"c8f3","./sr":"cf1e","./sr-cyrl":"13e9","./sr-cyrl.js":"13e9","./sr.js":"cf1e","./ss":"52bd","./ss.js":"52bd","./sv":"5fbd","./sv.js":"5fbd","./sw":"74dc","./sw.js":"74dc","./ta":"3de5","./ta.js":"3de5","./te":"5cbb","./te.js":"5cbb","./tet":"576c","./tet.js":"576c","./tg":"3b1b","./tg.js":"3b1b","./th":"10e8","./th.js":"10e8","./tl-ph":"0f38","./tl-ph.js":"0f38","./tlh":"cf75","./tlh.js":"cf75","./tr":"0e81","./tr.js":"0e81","./tzl":"cf51","./tzl.js":"cf51","./tzm":"c109","./tzm-latn":"b53d","./tzm-latn.js":"b53d","./tzm.js":"c109","./ug-cn":"6117","./ug-cn.js":"6117","./uk":"ada2","./uk.js":"ada2","./ur":"5294","./ur.js":"5294","./uz":"2e8c","./uz-latn":"010e","./uz-latn.js":"010e","./uz.js":"2e8c","./vi":"2921","./vi.js":"2921","./x-pseudo":"fd7e","./x-pseudo.js":"fd7e","./yo":"7f33","./yo.js":"7f33","./zh-cn":"5c3a","./zh-cn.js":"5c3a","./zh-hk":"49ab","./zh-hk.js":"49ab","./zh-tw":"90ea","./zh-tw.js":"90ea"};function n(t){var e=r(t);return a(e)}function r(t){var e=i[t];if(!(e+1)){var a=new Error("Cannot find module '"+t+"'");throw a.code="MODULE_NOT_FOUND",a}return e}n.keys=function(){return Object.keys(i)},n.resolve=r,t.exports=n,n.id="4678"},"558c":function(t,e,a){},"56d7":function(t,e,a){"use strict";a.r(e);a("a481"),a("14c6"),a("08c1"),a("4842"),a("d9fc"),a("f466"),a("579f"),a("587a");var i=a("bc3a"),n=a.n(i),r=a("a026"),s=a("5f5b"),o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("router-view")},l=[],c=(a("28a5"),a("386d"),a("6762"),a("2fdb"),"token"),u="user",d="/v3/console/user/me",p="/login",m=function(){var t=localStorage.getItem(c);return!!(t&&t.length>10)},b=function(){return JSON.parse(localStorage.getItem(u))},f=function(t){var e=b();return!!(e&&e.roles&&e.roles.indexOf(t)>=0)},h={bus:new r["default"],user:{authenticated:!1,profile:{},isAdmin:!1},checkAuth:function(){return m()},logout:function(t){localStorage.removeItem(c),localStorage.removeItem(u)},login:function(t,e){return n.a.post(d,{accountid:t,password:e})},me:function(t){return n.a.get(d,{headers:this.getAuthHeader()})},profile:function(){return b()},setToken:function(t){localStorage.setItem(c,t)},setProfile:function(t){localStorage.setItem(u,JSON.stringify(t))},getAuthHeader:function(){return{Authorization:"Bearer "+localStorage.getItem(c)}},hasRole:function(t,e){return!(!t||!t.roles||!t.roles.includes("admin")&&!t.roles.includes(e))},requireAuth:function(t,e,a){m()?a():(console.log("auth failed ..."),a({path:p}))},requireAdmin:function(t,e,a){m()?f("admin")?a():(console.log("role failed ..."),a({path:p})):(console.log("auth failed ..."),a({path:p}))}},g={name:"app",created:function(){var t=this;if(window.location.search.indexOf("token")>-1){var e=window.location.search.split("token=")[1];h.setToken(e),t.$store.commit("SET_TOKEN",e),window.location="/v3/console/#/dashboard"}h.checkAuth()&&(console.log("App authed"),h.me().then(function(e){var a=e.data.data;h.setProfile(a),t.$store.commit("SET_USER",e.data.data)}).catch(function(t){console.log("get profile fail",t)}))}},v=g,_=(a("5c0b"),a("2877")),y=Object(_["a"])(v,o,l,!1,null,null,null),w=y.exports,O=a("8c4f"),k=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app"},[a("AppHeader",{attrs:{fixed:""}},[a("SidebarToggler",{staticClass:"d-lg-none",attrs:{display:"md",mobile:""}}),a("b-link",{staticClass:"navbar-brand",attrs:{to:"#"}},[a("img",{staticClass:"navbar-brand-full",attrs:{src:"img/logo.png",width:"100",height:"35",alt:"KKTV Logo"}}),a("img",{staticClass:"navbar-brand-minimized",attrs:{src:"img/logo-symbol.png",width:"35",height:"35",alt:"KKTV Logo"}})]),a("SidebarToggler",{staticClass:"d-md-down-none",attrs:{display:"lg"}}),a("b-navbar-nav",{staticClass:"d-md-down-none"},[a("b-nav-item",{staticClass:"px-3",attrs:{to:"/dashboard"}},[t._v("Dashboard")])],1),a("b-navbar-nav",{staticClass:"ml-auto"},[a("DefaultHeaderDropdownAccnt")],1),a("AsideToggler",{staticClass:"d-none d-lg-block"})],1),a("div",{staticClass:"app-body"},[a("AppSidebar",{attrs:{fixed:""}},[a("SidebarHeader"),a("SidebarForm"),a("SidebarNav",{attrs:{navItems:t.nav}}),a("SidebarFooter"),a("SidebarMinimizer")],1),a("main",{staticClass:"main"},[a("Breadcrumb",{attrs:{list:t.list}}),a("div",{staticClass:"container-fluid"},[a("router-view")],1)],1),a("AppAside",{attrs:{fixed:""}},[a("DefaultAside")],1)],1),a("TheFooter",[a("div",[a("a",{attrs:{href:"https://www.kktv.me"}},[t._v("KKTV")]),a("span",{staticClass:"ml-1"},[t._v("© 2018-"+t._s((new Date).getFullYear()))])]),a("div",{staticClass:"ml-auto"},[t._v("\n      Made with "),a("i",{staticClass:"fa fa-heart",staticStyle:{color:"red"},attrs:{"aria-hidden":"true"}})])])],1)},j=[],x=(a("7f7f"),{items:[{name:"Dashboard",url:"/dashboard",icon:"icon-speedometer"},{name:"Customer",url:"/user",icon:"icon-people",children:[{name:"User",url:"/user",icon:"icon-user"},{name:"User 修改紀錄查詢",url:"/user/userchangelog",icon:"icon-user"},{name:"Order",url:"/user/order",icon:"icon-handbag"},{name:"Token",url:"/user/token",icon:"icon-badge"},{name:"Family",url:"/user/family",icon:"fa fa-users"},{name:"MOD(查中華電信)",url:"/user/mod",icon:"icon-badge"}]},{name:"Redeem",url:"/redeem",icon:"icon-tag"},{name:"ServiceStatus",url:"/servicestatus",icon:"fa fa-cog"},{name:"Product",url:"/product",icon:"icon-present"},{name:"Package",url:"/package",icon:"fa fa-gift"},{name:"Content",url:"/content",icon:"icon-doc",children:[{name:"Title",url:"/content/title",icon:"icon-docs"},{name:"Series",url:"/content/series",icon:"icon-layers"},{name:"Extra",url:"/content/extra",icon:"fa fa-file-video-o"},{name:"Publish",url:"/content/publish",icon:"fa fa-calendar-check-o"},{name:"Browse 選單",url:"/content/browse",icon:"icon-list",badge:{variant:"primary",text:"NEW"}},{name:"Browse 片單",url:"/content/titlelist",icon:"icon-list"},{name:"HotKeyWord",url:"/content/hotkeyword",icon:"icon-magnifier"},{name:"Announce",url:"/content/announce",icon:"icon-feed"},{name:"Event",url:"/content/event",icon:"icon-info"},{name:"Headline 片單",url:"/content/metatitlelist/link,title",icon:"icon-graph"},{name:"Choice 片單",url:"/content/metatitlelist/choice",icon:"icon-list"},{name:"Highlight 片單",url:"/content/metatitlelist/highlight",icon:"icon-list"},{name:"Encoder",url:"/content/encoder",icon:"icon-camrecorder"},{name:"Encoder BV",url:"/content/encoderbv",icon:"icon-camrecorder"},{name:"Ads",url:"/content/ads",icon:"icon-basket"}]},{name:"RemoteConfig",url:"/remoteconfig",icon:"icon-present"},{name:"TVEvents",url:"/tvevents",icon:"fa fa-tv"},{name:"Income Report",url:"/finance/income",icon:"fa fa-table"},{name:"Android Devices",url:"/android/devices",icon:"fa fa-android"}]}),S=a("f1fb"),D=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("b-tabs",[a("b-tab",[a("template",{slot:"title"},[a("i",{staticClass:"icon-list"})]),t._l(t.items,function(e){return a("div",{key:e.id,staticClass:"p-3"},[a("div",{staticClass:"message"},[a("div",{staticClass:"py-3 pb-5 mr-3 float-left"},[a("div",{staticClass:"avatar"},[a("img",{staticClass:"img-avatar",attrs:{src:e.avatar,alt:e.email}})])]),a("div",[a("small",{staticClass:"text-muted"},[t._v(t._s(e.name))]),a("small",{staticClass:"text-muted"},[t._v(t._s(new Date(e.created_at).toLocaleString()))])]),a("div",{staticClass:"text-truncate font-weight-bold"},[t._v(t._s(e.action))]),a("small",{staticClass:"text-muted"},[t._v(t._s(e.message))]),a("hr")])])})],2)],1)},C=[],P={request:function(t,e){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};if(t){if(e){var r=e,s=Object.assign({Authorization:"Bearer "+localStorage.getItem("token")},i);return n()({method:t,url:r,data:a,headers:s,timeout:25e3})}console.error("API function call requires uri argument")}else console.error("API function call requires method argument")}},q={name:"DefaultAside",data:function(){return{items:[]}},mounted:function(){var t=this;console.log("Aside Mounted"),P.request("get","/v3/console/auditlog").then(function(e){e.data&&e.data.data&&e.data.data.logs&&(t.items=e.data.data.logs)}).catch(function(t){console.log("get auditlog fail")})}},M=q,A=Object(_["a"])(M,D,C,!1,null,null,null),T=A.exports,I=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("AppHeaderDropdown",{attrs:{right:"","no-caret":""}},[a("template",{slot:"header"},[a("img",{staticClass:"img-avatar",attrs:{src:t.user&&t.user.avatar,alt:t.user&&t.user.name}})]),t._v("\\\n  "),a("template",{slot:"dropdown"},[t.user&&t.user.name&&t.user.email?a("b-dropdown-item",[a("i",{staticClass:"fa fa-shield"}),t._v(t._s(t.user.name)+" "+t._s(t.user.email))]):t._e(),a("b-dropdown-item",{on:{click:function(e){return e.preventDefault(),t.onLogout()}}},[a("i",{staticClass:"fa fa-lock"}),t._v("Logout")])],1)],2)},$=[],B=(a("8e6e"),a("ac6a"),a("456d"),a("bd86")),F=a("2f62");function E(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),a.push.apply(a,i)}return a}function L(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?E(a,!0).forEach(function(e){Object(B["a"])(t,e,a[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):E(a).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))})}return t}var U,R,N,z,H,K,J,V,W={name:"DefaultHeaderDropdownAccnt",components:{AppHeaderDropdown:S["g"]},data:function(){return{itemsCount:42}},methods:{onLogout:function(){h.logout(),this.$router.push("/login")}},computed:L({},Object(F["b"])(["user"]))},G=W,Y=Object(_["a"])(G,I,$,!1,null,null,null),X=Y.exports,Z={name:"full",components:{AsideToggler:S["b"],AppHeader:S["f"],AppSidebar:S["h"],AppAside:S["a"],TheFooter:S["e"],Breadcrumb:S["c"],DefaultAside:T,DefaultHeaderDropdownAccnt:X,SidebarForm:S["j"],SidebarFooter:S["i"],SidebarToggler:S["n"],SidebarHeader:S["k"],SidebarNav:S["m"],SidebarMinimizer:S["l"]},data:function(){return{nav:x.items}},computed:{name:function(){return this.$route.name},list:function(){return this.$route.matched.filter(function(t){return t.name||t.meta.label})}}},Q=Z,tt=Object(_["a"])(Q,k,j,!1,null,null,null),et=tt.exports,at=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"animated fadeIn"},[a("b-row")],1)},it=[],nt=a("1fca"),rt=a("1fef"),st=a("36e4"),ot={extends:nt["b"],props:["height","width"],mounted:function(){var t=Object(st["getStyle"])("--primary")||"#20a8d8",e=[{label:"My First dataset",backgroundColor:t,borderColor:"rgba(255,255,255,.55)",data:[65,59,84,84,51,55,40]}];this.renderChart({labels:["January","February","March","April","May","June","July"],datasets:e},{tooltips:{enabled:!1,custom:rt["CustomTooltips"]},maintainAspectRatio:!1,legend:{display:!1},scales:{xAxes:[{gridLines:{color:"transparent",zeroLineColor:"transparent"},ticks:{fontSize:2,fontColor:"transparent"}}],yAxes:[{display:!1,ticks:{display:!1,min:Math.min.apply(Math,e[0].data)-5,max:Math.max.apply(Math,e[0].data)+5}}]},elements:{line:{borderWidth:1},point:{radius:4,hitRadius:10,hoverRadius:4}}})}},lt=ot,ct=Object(_["a"])(lt,U,R,!1,null,null,null),ut=ct.exports,dt={extends:nt["b"],props:["height","width"],mounted:function(){var t=Object(st["getStyle"])("--light-blue")||"#63c2de",e=[{label:"My First dataset",backgroundColor:t,borderColor:"rgba(255,255,255,.55)",data:[1,18,9,17,34,22,11]}];this.renderChart({labels:["January","February","March","April","May","June","July"],datasets:e},{tooltips:{enabled:!1,custom:rt["CustomTooltips"]},maintainAspectRatio:!1,legend:{display:!1},scales:{xAxes:[{gridLines:{color:"transparent",zeroLineColor:"transparent"},ticks:{fontSize:2,fontColor:"transparent"}}],yAxes:[{display:!1,ticks:{display:!1,min:Math.min.apply(Math,e[0].data)-5,max:Math.max.apply(Math,e[0].data)+5}}]},elements:{line:{tension:1e-5,borderWidth:1},point:{radius:4,hitRadius:10,hoverRadius:4}}})}},pt=dt,mt=Object(_["a"])(pt,N,z,!1,null,null,null),bt=mt.exports,ft={extends:nt["b"],props:["height","width"],mounted:function(){var t=[{label:"My First dataset",backgroundColor:"rgba(255,255,255,.2)",borderColor:"rgba(255,255,255,.55)",data:[78,81,80,45,34,12,40]}];this.renderChart({labels:["January","February","March","April","May","June","July"],datasets:t},{tooltips:{enabled:!1,custom:rt["CustomTooltips"]},maintainAspectRatio:!1,legend:{display:!1},scales:{xAxes:[{display:!1}],yAxes:[{display:!1}]},elements:{line:{borderWidth:2},point:{radius:0,hitRadius:10,hoverRadius:4}}})}},ht=ft,gt=Object(_["a"])(ht,H,K,!1,null,null,null),vt=gt.exports,_t={extends:nt["a"],props:["height"],mounted:function(){var t=[{label:"My First dataset",backgroundColor:"rgba(255,255,255,.3)",borderColor:"transparent",data:[78,81,80,45,34,12,40,75,34,89,32,68,54,72,18,98]}];this.renderChart({labels:["","","","","","","","","","","","","","","",""],datasets:t},{tooltips:{enabled:!1,custom:rt["CustomTooltips"]},maintainAspectRatio:!1,legend:{display:!1},scales:{xAxes:[{display:!1,categoryPercentage:1,barPercentage:.5}],yAxes:[{display:!1}]}})}},yt=_t,wt=Object(_["a"])(yt,J,V,!1,null,null,null),Ot=wt.exports;function kt(t,e){return Math.floor(Math.random()*(e-t+1)+t)}var jt,xt,St,Dt,Ct,Pt,qt={extends:nt["b"],props:["height"],mounted:function(){for(var t=Object(st["getStyle"])("--success")||"#4dbd74",e=Object(st["getStyle"])("--info")||"#20a8d8",a=Object(st["getStyle"])("--danger")||"#f86c6b",i=27,n=[],r=[],s=[],o=0;o<=i;o++)n.push(kt(50,200)),r.push(kt(80,100)),s.push(65);this.renderChart({labels:["Mo","Tu","We","Th","Fr","Sa","Su","Mo","Tu","We","Th","Fr","Sa","Su","Mo","Tu","We","Th","Fr","Sa","Su","Mo","Tu","We","Th","Fr","Sa","Su"],datasets:[{label:"My First dataset",backgroundColor:Object(st["hexToRgba"])(e,10),borderColor:e,pointHoverBackgroundColor:"#fff",borderWidth:2,data:n},{label:"My Second dataset",backgroundColor:"transparent",borderColor:t,pointHoverBackgroundColor:"#fff",borderWidth:2,data:r},{label:"My Third dataset",backgroundColor:"transparent",borderColor:a,pointHoverBackgroundColor:"#fff",borderWidth:1,borderDash:[8,5],data:s}]},{tooltips:{enabled:!1,custom:rt["CustomTooltips"],intersect:!0,mode:"index",position:"nearest",callbacks:{labelColor:function(t,e){return{backgroundColor:e.data.datasets[t.datasetIndex].borderColor}}}},maintainAspectRatio:!1,legend:{display:!1},scales:{xAxes:[{gridLines:{drawOnChartArea:!1}}],yAxes:[{ticks:{beginAtZero:!0,maxTicksLimit:5,stepSize:Math.ceil(50),max:250},gridLines:{display:!0}}]},elements:{point:{radius:0,hitRadius:10,hoverRadius:4,hoverBorderWidth:3}}})}},Mt=qt,At=Object(_["a"])(Mt,jt,xt,!1,null,null,null),Tt=At.exports,It={extends:nt["b"],props:{data:{type:Array,default:function(){return[0,22,34,46,58,70,46]}},height:{type:String,default:"100"}},mounted:function(){this.renderChart({labels:["January","February","March","April","May","June","July"],datasets:[{backgroundColor:"rgba(255,255,255,.1)",borderColor:"rgba(255,255,255,.55)",pointHoverBackgroundColor:"#fff",borderWidth:2,data:this.data}]},{tooltips:{enabled:!1,custom:rt["CustomTooltips"]},responsive:!0,maintainAspectRatio:!1,legend:{display:!1},scales:{xAxes:[{display:!1}],yAxes:[{display:!1}]},elements:{point:{radius:0,hitRadius:10,hoverRadius:4,hoverBorderWidth:3}}})}},$t=It,Bt=Object(_["a"])($t,St,Dt,!1,null,null,null),Ft=Bt.exports,Et={components:{CustomTooltips:rt["CustomTooltips"]},extends:nt["b"],props:["data","height","width","variant"],mounted:function(){this.renderChart({labels:["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],datasets:[{backgroundColor:"transparent",borderColor:this.getVariant(this.variant)||"#c2cfd6",data:this.data}]},{responsive:!0,tooltips:{enabled:!1,custom:rt["CustomTooltips"],intersect:!0,mode:"index",position:"nearest",callbacks:{labelColor:function(t,e){return{backgroundColor:e.data.datasets[t.datasetIndex].borderColor}}}},maintainAspectRatio:!0,scales:{xAxes:[{display:!1}],yAxes:[{display:!1}]},elements:{line:{borderWidth:2},point:{radius:0,hitRadius:10,hoverRadius:4,hoverBorderWidth:3}},legend:{display:!1}})},methods:{getVariant:function(t,e){return"#"===t[0]?t:Object(st["getStyle"])("--".concat(t),e)}}},Lt=Et,Ut=Object(_["a"])(Lt,Ct,Pt,!1,null,null,null),Rt=Ut.exports,Nt={name:"dashboard",components:{Callout:S["d"],CardLine1ChartExample:ut,CardLine2ChartExample:bt,CardLine3ChartExample:vt,CardBarChartExample:Ot,MainChartExample:Tt,SocialBoxChartExample:Ft,CalloutChartExample:Rt},data:function(){return{selected:"Month",tableItems:[{avatar:{url:"img/avatars/1.jpg",status:"success"},user:{name:"Yiorgos Avraamu",new:!0,registered:"Jan 1, 2015"},country:{name:"USA",flag:"us"},usage:{value:50,period:"Jun 11, 2015 - Jul 10, 2015"},payment:{name:"Mastercard",icon:"fa fa-cc-mastercard"},activity:"10 sec ago"},{avatar:{url:"img/avatars/2.jpg",status:"danger"},user:{name:"Avram Tarasios",new:!1,registered:"Jan 1, 2015"},country:{name:"Brazil",flag:"br"},usage:{value:22,period:"Jun 11, 2015 - Jul 10, 2015"},payment:{name:"Visa",icon:"fa fa-cc-visa"},activity:"5 minutes ago"},{avatar:{url:"img/avatars/3.jpg",status:"warning"},user:{name:"Quintin Ed",new:!0,registered:"Jan 1, 2015"},country:{name:"India",flag:"in"},usage:{value:74,period:"Jun 11, 2015 - Jul 10, 2015"},payment:{name:"Stripe",icon:"fa fa-cc-stripe"},activity:"1 hour ago"},{avatar:{url:"img/avatars/4.jpg",status:""},user:{name:"Enéas Kwadwo",new:!0,registered:"Jan 1, 2015"},country:{name:"France",flag:"fr"},usage:{value:98,period:"Jun 11, 2015 - Jul 10, 2015"},payment:{name:"PayPal",icon:"fa fa-paypal"},activity:"Last month"},{avatar:{url:"img/avatars/5.jpg",status:"success"},user:{name:"Agapetus Tadeáš",new:!0,registered:"Jan 1, 2015"},country:{name:"Spain",flag:"es"},usage:{value:22,period:"Jun 11, 2015 - Jul 10, 2015"},payment:{name:"Google Wallet",icon:"fa fa-google-wallet"},activity:"Last week"},{avatar:{url:"img/avatars/6.jpg",status:"danger"},user:{name:"Friderik Dávid",new:!0,registered:"Jan 1, 2015"},country:{name:"Poland",flag:"pl"},usage:{value:43,period:"Jun 11, 2015 - Jul 10, 2015"},payment:{name:"Amex",icon:"fa fa-cc-amex"},activity:"Last week"}],tableFields:{avatar:{label:'<i class="icon-people"></i>',class:"text-center"},user:{label:"User"},country:{label:"Country",class:"text-center"},usage:{label:"Usage"},payment:{label:"Payment method",class:"text-center"},activity:{label:"Activity"}}}},methods:{variant:function(t){var e;return t<=25?e="info":t>25&&t<=50?e="success":t>50&&t<=75?e="warning":t>75&&t<=100&&(e="danger"),e},flag:function(t){return"flag-icon flag-icon-"+t}}},zt=Nt,Ht=(a("2b56"),Object(_["a"])(zt,at,it,!1,null,null,null)),Kt=Ht.exports,Jt=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{attrs:{id:"login"}},[a("div",{staticClass:"container"},[a("b-row",{staticClass:"justify-content-center"},[a("b-col",{attrs:{md:"8"}},[a("b-card-group",[a("b-card",{staticClass:"p-4",attrs:{"no-body":""}},[a("b-card-body",[a("b-form",[a("h1",{staticStyle:{color:"white"}},[t._v("KKTV CONSOLE")]),a("b-row",[a("b-col",{attrs:{cols:"6"}},[a("p",{staticClass:"text-muted"},[t._v("\n                      請使用您 KKTV Google Account 登入， 對應您的部門，有不同權限，如權限不足請洽 "),a("i",[t._v("Product Team")])]),a("a",[a("button",{staticClass:"btn btn-lg btn-google-plus",on:{click:t.onLogin}},[t._v("Google 帳號登入 "),a("i",{staticClass:"fa fa-google-plus-square",attrs:{"aria-hidden":"true"}})])])])],1)],1)],1)],1)],1)],1)],1)],1)])},Vt=[],Wt={name:"Login",data:function(){return{authgoogle:"/v3/auth/console/login?provider=google"}},methods:{onLogin:function(t){t.preventDefault(),window.location=this.authgoogle}}},Gt=Wt,Yt=(a("b90f"),Object(_["a"])(Gt,Jt,Vt,!1,null,"a0454246",null)),Xt=Yt.exports,Zt=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("b-row",[a("b-col",{attrs:{cols:"12",xl:"12"}},[a("transition",{attrs:{name:"slide"}},[a("b-card",{attrs:{header:t.caption}},[a("b-row",[a("b-col",{attrs:{sm:"12"}},[a("form",{on:{submit:function(e){return e.preventDefault(),t.onFetch(e)}}},[a("b-form-group",[a("b-input-group",[a("b-form-input",{attrs:{type:"text",id:"name"},model:{value:t.q,callback:function(e){t.q=e},expression:"q"}}),a("b-input-group-append",[a("b-button",{directives:[{name:"b-tooltip",rawName:"v-b-tooltip.hover",modifiers:{hover:!0}}],attrs:{variant:"primary",title:"開始搜尋後，這個轉轉轉，停了，就是查完了喔"},on:{click:t.onFetch}},[a("i",{staticClass:"fa",class:{"fa-refresh":t.loading,"fa-spin":t.loading}}),t._v("\n                  Submit\n                  ")])],1)],1),a("b-alert",{attrs:{show:t.dismissCountDown,dismissible:"",variant:"warning"},on:{dismissed:function(e){t.dismissCountDown=0},"dismiss-count-down":t.countDownChanged}},[t._v("\n                    "+t._s(t.alertMsg)+"\n              ")])],1)],1),a("small",{domProps:{innerHTML:t._s("輸入要查詢的 user_id, email, 電話號碼 或 <span style='font-weight: bold;color: #e67300;'>中華電信 MOD ID</span> ， 您可以用空格，隔開多個查詢條件，電話及 MOD ID 查詢是用模糊比對，時間較慢")}})])],1),t.items?a("b-table",{attrs:{hover:t.hover,striped:t.striped,bordered:t.bordered,small:t.small,fixed:t.fixed,responsive:"sm",items:t.items,fields:t.fields,"current-page":t.currentPage,"per-page":t.perPage},on:{"row-clicked":t.rowClicked},scopedSlots:t._u([{key:"id",fn:function(e){return[a("strong",[t._v(t._s(e.item.id))]),t._v("\n          "+t._s(e.item.email||e.item.phone)),a("br"),e.item.mod_subscriber_id?a("strong",[t._v(t._s(e.item.mod_subscriber_id)+" "+t._s(e.item.mod_subscriber_area)+" ")]):t._e(),e.item.mediaSource?a("textarea",{directives:[{name:"model",rawName:"v-model",value:e.item.mediaSource,expression:"data.item.mediaSource"}],attrs:{cols:"50",rows:"3",readonly:""},domProps:{value:e.item.mediaSource},on:{input:function(a){a.target.composing||t.$set(e.item,"mediaSource",a.target.value)}}}):t._e()]}},{key:"role",fn:function(e){return[a("b-badge",{attrs:{variant:t.getBadge(e.item.role)}},[t._v(t._s(e.item.role))])]}},{key:"type",fn:function(e){return[a("b-badge",{attrs:{variant:t.getBadge(e.item.type)}},[t._v(t._s(e.item.type))])]}}],null,!1,2764934181)}):t._e()],1)],1)],1)],1)},Qt=[];a("6b54");function te(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),a.push.apply(a,i)}return a}function ee(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?te(a,!0).forEach(function(e){Object(B["a"])(t,e,a[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):te(a).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))})}return t}var ae={name:"Users",props:{caption:{type:String,default:"Users"},hover:{type:Boolean,default:!0},striped:{type:Boolean,default:!0},bordered:{type:Boolean,default:!1},small:{type:Boolean,default:!1},fixed:{type:Boolean,default:!1}},data:function(){return{alertMsg:"",dismissCountDown:0,items:[],q:"",fields:[{key:"id"},{key:"role"},{key:"type"}],currentPage:1,perPage:0,totalRows:0}},computed:ee({},Object(F["b"])(["loading"])),mounted:function(){this.$route.query.q&&(this.q=this.$route.query.q,this.onFetch())},methods:{countDownChanged:function(t){this.dismissCountDown=t},showAlert:function(){this.dismissCountDown=5},getBadge:function(t){var e;switch(t){case"premium":e="success";break;case"freetrial":e="primary";break;case"expired":e="warning";break;case"general":e="success";break;case"test":e="warning";break;case"pr":e="secondary";break;case"classmate":e="secondary";break;case"prime":e="success";break}return e},getRowCount:function(t){return t.length},userLink:function(t){return"user/".concat(t.toString())},rowClicked:function(t){var e=this.userLink(t.id);this.$router.push({path:e})},onFetch:function(){var t=this;this.$router.push({path:"/user",query:{q:this.q}});var e=encodeURIComponent(this.q);P.request("get","/v3/console/user?q="+e).then(function(e){e.data&&e.data.data&&e.data.data.users?t.items=e.data.data.users:(t.alertMsg="沒搜尋到任何結果",t.showAlert())})}}},ie=ae,ne=(a("bb1a"),Object(_["a"])(ie,Zt,Qt,!1,null,"4c21fea8",null)),re=ne.exports,se=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("b-row",[a("b-col",{attrs:{cols:"12",xl:"12"}},[a("b-card",{attrs:{"no-header":""}},[a("template",{slot:"header"},[a("b-button",{staticClass:"mr-1",on:{click:t.goBack}},[t._v("<")]),a("b-button",{staticClass:"mr-1",on:{click:function(e){return t.onOrder(t.$route.params.id)}}},[t._v("Order")]),t.familyID&&""!=t.familyID?a("b-button",{directives:[{name:"b-tooltip",rawName:"v-b-tooltip.hover",modifiers:{hover:!0}}],staticClass:"mr-1",attrs:{title:"有發現 family 資訊，查詢是否有 family 方案及成員"},on:{click:function(e){return t.onFamily(t.familyID)}}},[t._v("Family")]):t._e(),a("b-button",{staticClass:"mr-1",on:{click:function(e){return t.onToken(t.$route.params.id)}}},[t._v("Token")]),a("b-button",{staticClass:"mr-1",on:{click:function(e){return t.onFavorite(t.$route.params.id)}}},[t._v("Favorite Title")]),a("b-button",{staticClass:"mr-1",on:{click:function(e){return t.onWatchHistory(t.$route.params.id)}}},[t._v("Watch History")]),a("b-button",{staticClass:"mr-1",on:{click:function(e){return t.onUserInfo(t.$route.params.id)}}},[t._v("UserInfo")]),a("b-button",{staticClass:"mr-1",attrs:{variant:"primary"},on:{click:function(e){return t.onAmplitude(t.$route.params.id)}}},[t._v("Amplitude")]),a("b-button",{staticClass:"mr-1",on:{click:function(e){return t.onAuditLog(t.$route.params.id)}}},[t._v("Audit Log")]),"iab"==t.item.payment_type||"iap"==t.item.payment_type?a("b-button",{directives:[{name:"b-tooltip",rawName:"v-b-tooltip.hover",modifiers:{hover:!0}}],attrs:{title:"會即時查詢 Google(iab) 或是 Apple(iap) 目前收據狀態，is_in_grace_period 如果是 true 表示使用者在寬限期中 "},on:{click:function(e){return t.onUserReceipt(t.$route.params.id)}}},[t._v("查詢 "+t._s(t.item.payment_type)+" 收據")]):t._e(),a("br"),t._v("\n        User id:  "+t._s(t.$route.params.id)+"\n        "),a("br"),t._v("\n        expired at "),a("strong",[t._v(t._s(new Date(1e3*t.item.expiredAt).toLocaleString()))]),t.receiptMsg?a("strong"):t._e()],1),a("b-table",{attrs:{striped:"",small:"",fixed:"",responsive:"sm",items:t.items,fields:t.fields},scopedSlots:t._u([{key:"value",fn:function(e){return[a("div",["mediaSource"==e.item.key&&""!=t.kkbox?a("b-button",{attrs:{variant:"primary"},on:{click:function(e){return t.onKKBOX(t.kkbox)}}},[t._v("\n              KKBOX 帳號狀態\n            ")]):t._e(),t._v(" \n            "),"mediaSource"==e.item.key&&"kkbox"!=t.item.origin_provider&&""!=t.kkbox?a("b-button",{directives:[{name:"b-tooltip",rawName:"v-b-tooltip.hover",modifiers:{hover:!0}}],attrs:{variant:"warning",title:"刪除帳號中對應 kkbox 的資訊，用戶可以重新用 kkbox 登入重新綁定"},on:{click:function(e){return t.onUpdate(t.item.id,e,"unbind")}}},[a("i",{staticClass:"fa"}),t._v("\n              解除 KKBOX 帳號綁定\n            ")]):t._e()],1),"mod_subscriber_id"==e.item.key&&e.item.value?a("b-button",{attrs:{variant:"primary"},on:{click:function(a){return t.onMOD(e.item.value)}}},[t._v("HINET MOD 狀態")]):t._e(),a("strong",[t._v(t._s(e.item.value))])]}}])}),a("template",{slot:"footer"})],2),a("b-card-group",[a("b-card",[a("div",{staticClass:"mt-2"},[t._v("Username:")]),a("b-form-input",{attrs:{type:"text"},model:{value:t.tmpUser.name,callback:function(e){t.$set(t.tmpUser,"name",e)},expression:"tmpUser.name"}}),a("div",{staticClass:"mt-2"},[t._v("Email:")]),a("b-form-input",{attrs:{type:"text"},model:{value:t.tmpUser.email,callback:function(e){t.$set(t.tmpUser,"email",e)},expression:"tmpUser.email"}}),a("div",{staticClass:"mt-2"},[t._v("Phone:")]),a("b-form-input",{attrs:{type:"text"},model:{value:t.tmpUser.phone,callback:function(e){t.$set(t.tmpUser,"phone",e)},expression:"tmpUser.phone"}}),a("span",{staticClass:"m-md-1 icon-pencil"}),t._v("請選擇要變更的 Role: "),a("b-form-select",{staticClass:"m-md-2",attrs:{options:t.roleOptions},model:{value:t.tmpUser.role,callback:function(e){t.$set(t.tmpUser,"role",e)},expression:"tmpUser.role"}}),a("span",{staticClass:"m-md-1 icon-pencil"}),t._v("請選擇要變更的 Type: "),a("b-form-select",{staticClass:"m-md-2",attrs:{options:t.typeOptions},model:{value:t.tmpUser.type,callback:function(e){t.$set(t.tmpUser,"type",e)},expression:"tmpUser.type"}}),a("span",{staticClass:"m-md-1 icon-pencil"}),t._v("請選擇要變更的 Auto Renew: "),a("b-form-select",{staticClass:"m-md-2",attrs:{options:t.autorenewOptions},model:{value:t.tmpUser.autoRenew,callback:function(e){t.$set(t.tmpUser,"autoRenew",e)},expression:"tmpUser.autoRenew"}}),a("span",{staticClass:"m-md-1 icon-calendar"}),t._v("請選擇要變更的到期日 expiredAt:"),a("flat-pickr",{attrs:{config:t.config},on:{"on-close":function(e){return t.onTimeChange()}},model:{value:t.expired_at,callback:function(e){t.expired_at=e},expression:"expired_at"}}),a("div",{staticClass:"mt-2"},[t._v("異動原因:")]),a("form",{on:{submit:function(t){t.stopPropagation(),t.preventDefault()}}},[a("b-form-group",{attrs:{state:t.reasonInfoState,"invalid-feedback":"*必填欄位"}},[a("b-form-input",{attrs:{type:"text",state:t.reasonInfoState,required:""},model:{value:t.reason_info,callback:function(e){t.reason_info=e},expression:"reason_info"}})],1)],1),a("div",{staticStyle:{width:"270px"}},[a("b-button",{directives:[{name:"b-tooltip",rawName:"v-b-tooltip.hover",modifiers:{hover:!0}}],attrs:{variant:"warning",title:"修改／儲存使用者，您必須有 客服管理 的權限"},on:{click:function(e){return t.onUpdate(t.item.id,e)}}},[a("i",{staticClass:"fa"}),t._v("\n              更新使用者資料\n          ")])],1)],1),a("b-card",[this.item.payment_type?a("div",{staticClass:"mt-2"},[t._v("\n          Recipient Adderss:\n          "),a("b-form-input",{attrs:{type:"text"},model:{value:t.tmpUser.recipient_address,callback:function(e){t.$set(t.tmpUser,"recipient_address",e)},expression:"tmpUser.recipient_address"}}),t._v("\n          載具類別(0=手機條碼載具 1=自然人憑證條碼載具 2=智付寶載具):\n          "),a("b-form-group",{attrs:{state:t.reasonCarrierTypeState,"invalid-feedback":"*必須是 0, 1 或 2 ，且須填寫手機條碼載具"}},[a("b-form-input",{attrs:{type:"text"},model:{value:t.tmpUser.carrier_type,callback:function(e){t.$set(t.tmpUser,"carrier_type",e)},expression:"tmpUser.carrier_type"}})],1),t._v("\n\n          載具編號(一般為手機條碼載具):\n          "),a("b-form-input",{attrs:{type:"text"},model:{value:t.tmpUser.carrier_value,callback:function(e){t.$set(t.tmpUser,"carrier_value",e)},expression:"tmpUser.carrier_value"}}),t._v("\n          異動原因:\n          "),a("form",{on:{submit:function(t){t.stopPropagation(),t.preventDefault()}}},[a("b-form-group",{attrs:{state:t.reasonPaymentState,"invalid-feedback":"*必填欄位"}},[a("b-form-input",{attrs:{type:"text",state:t.reasonPaymentState,required:""},model:{value:t.reason_payment,callback:function(e){t.reason_payment=e},expression:"reason_payment"}})],1)],1),a("b-button",{directives:[{name:"b-tooltip",rawName:"v-b-tooltip.hover",modifiers:{hover:!0}}],attrs:{variant:"warning",title:"修改／儲存使用者，您必須有 客服管理 的權限"},on:{click:function(e){return t.onUpdate(t.item.id,e,"payment")}}},[a("i",{staticClass:"fa"}),t._v("\n            更新使用者付款資料\n          ")]),a("hr"),t.item.email?a("b-button",{directives:[{name:"b-tooltip",rawName:"v-b-tooltip.hover",modifiers:{hover:!0}}],attrs:{variant:"warning",title:"User 拒收EDM"},on:{click:function(e){return t.onUpdate(t.item.id,e,"edmUnsubscribe")}}},[a("i",{staticClass:"fa"}),t._v("User EDM 取消\n          ")]):t._e(),a("hr")],1):t._e(),t.item.mod_subscriber_id?a("div",{staticClass:"mt-2"},[a("b-button",{directives:[{name:"b-tooltip",rawName:"v-b-tooltip.hover",modifiers:{hover:!0}}],attrs:{variant:"warning",title:"清除這個使用者目前綁定的 MOD ID 資訊，讓這位使用者可以再使用別的帳號做綁定，請先將接續訂單取消，並無重複訂閱的情形"},on:{click:function(e){return t.onUpdate(t.item.id,e,"unbindMOD")}}},[a("i",{staticClass:"fa"}),t._v("解除 MOD ID 綁定 ")]),a("hr")],1):t._e(),a("b-button",{directives:[{name:"b-tooltip",rawName:"v-b-tooltip.hover",modifiers:{hover:!0}}],attrs:{variant:"danger",title:"刪除帳號 refresh_tokens tokens 及所有訂單，限管理者權限"},on:{click:function(e){return t.onUserReset(t.$route.params.id)}}},[t._v("\n          UserReset")]),a("div",{staticStyle:{width:"270px","padding-top":"30px"}},[a("b-button",{directives:[{name:"b-tooltip",rawName:"v-b-tooltip.hover",modifiers:{hover:!0}}],attrs:{variant:"danger",title:"刪除使用者"},on:{click:function(e){return t.onUpdate(t.item.id,e,"disableAccount")}}},[a("i",{staticClass:"fa"}),t._v("刪除使用者")])],1)],1)],1),1==t.isAdmin?a("b-card",[a("div",{staticClass:"mt-2"},[t._v("修改CMS權限，勾選即增加，取消勾選即取消權限")]),t._l(t.consoleRoles,function(e){return a("b-form-checkbox",{on:{change:function(a){return t.setRole(a,e.value)}},model:{value:e.checked,callback:function(a){t.$set(e,"checked",a)},expression:"value.checked"}},[t._v("\n            "+t._s(e.value)+"\n          ")])})],2):t._e(),a("b-alert",{attrs:{show:t.dismissCountDown,dismissible:"",variant:"warning"},on:{dismissed:function(e){t.dismissCountDown=0},"dismiss-count-down":t.countDownChanged}},[t._v("\n      "+t._s(t.alertMsg)+"\n    ")])],1),a("b-modal",{ref:"formModal",attrs:{title:"user 資訊",size:"xl","ok-only":""},on:{ok:t.onModalOK},model:{value:t.showModal,callback:function(e){t.showModal=e},expression:"showModal"}},[a("b-form-textarea",{attrs:{id:"textarea",placeholder:"the userinfo response api data",rows:"25"},model:{value:t.userinfoStr,callback:function(e){t.userinfoStr=e},expression:"userinfoStr"}})],1)],1)},oe=[],le=(a("5df3"),a("96cf"),a("3b8d")),ce=a("768b"),ue=(a("ffc1"),a("673e"),a("7514"),{name:"User",props:{caption:{type:String,default:"User id"}},data:function(){return{items:[],item:{mediaSource:{}},dismissCountDown:0,alertMsg:"",receiptMsg:"",reasonInfoState:null,reasonPaymentState:null,reasonCarrierTypeState:null,expired_at:null,reason_info:"",reason_payment:"",showModal:!1,userinfoStr:"",tmpUser:{new_role:null,new_type:null,email:null,phone:null,recipient_address:null,name:null,carrier_type:null,carrier_value:null},autorenewOptions:[{value:!0,text:"True"},{value:!1,text:"False"}],roleOptions:[{value:"",text:"請選擇..."},{value:"expired",text:"expired"},{value:"freetrial",text:"freetrial"},{value:"premium",text:"premium"}],typeOptions:[{value:"",text:"請選擇..."},{value:"classmate",text:"classmate"},{value:"general",text:"general"},{value:"prime",text:"prime"},{value:"kkbox",text:"kkbox"},{value:"pr",text:"pr"},{value:"test",text:"test"}],config:{enableTime:!0,altFormat:"Y-m-d H:i",altInput:!0,dateFormat:"Y-m-d H:i"},kkbox:"",familyID:"",fields:[{key:"key"},{key:"value"}],isAdmin:!1,consoleRoles:[],userEmail:""}},mounted:function(){this.onFetch()},methods:{goBack:function(){this.$router.go(-1)},onKKBOX:function(t){""!==t&&window.open("/v3/primestatus?sub="+encodeURIComponent(t))},onMOD:function(t){""!==t&&this.$router.push({path:"/user/mod",query:{q:t}})},onOrder:function(t){this.$router.push({path:"/user/order",query:{q:t}})},onFamily:function(t){this.$router.push({path:"/user/family",query:{q:t}})},onToken:function(t){this.$router.push({path:"/user/token",query:{q:t}})},onFavorite:function(t){this.$router.push({path:"/user/favorite/"+t})},onWatchHistory:function(t){this.$router.push({path:"/user/watch_history/"+t})},onAmplitude:function(t){window.open("https://analytics.amplitude.com/kktv/project/148242/search/"+t)},onAuditLog:function(t){this.$router.push({path:"/user/audit_log/"+t})},countDownChanged:function(t){this.dismissCountDown=t},onUserInfo:function(){var t=this,e=this.$route.params.id;P.request("get","/v3/console/userinfo?q="+e).then(function(e){e.data&&(console.log(e.data),t.userinfoStr=JSON.stringify(e.data,null,2))}),this.showModal=!0},onUserReceipt:function(){var t=this,e=this.$route.params.id,a="/v3/console/user_receipt?id=".concat(e,"&payment_type=").concat(this.item.payment_type);console.log(a),P.request("get",a).then(function(e){e.data&&(console.log(e.data),t.userinfoStr=JSON.stringify(e.data,null,2))}),this.showModal=!0},onModalOK:function(){this.userinfoStr=""},onUserReset:function(){var t=this,e=this.$route.params.id;P.request("put","/v3/console/userreset?q="+e).then(function(e){t.alertMsg="重置成功",t.showAlert()}).catch(function(e){t.alertMsg="重置失敗",t.showAlert()})},showAlert:function(){this.dismissCountDown=5},onTimeChange:function(){this.tmpUser.expiredAt;this.$forceUpdate()},onFetch:function(){var t=this;console.log("onFetch");var e=this.$route.params.id;P.request("get","/v3/console/user?q="+e).then(function(a){if(a.data&&a.data.data&&a.data.data.users){var i=a.data.data.users,n=i.find(function(t){return t.id.toString()===e});t.item=n;var r=JSON.parse(n.mediaSource);r&&r.kkbox&&r.kkbox.sub?t.kkbox=r.kkbox.sub:t.kkbox="",r&&r.family?t.familyID=n.id:n.family_id&&(t.familyID=n.family_id);var s=n?Object.entries(n):[["id","Not found"]];t.items=s.map(function(t){var e=Object(ce["a"])(t,2),a=e[0],i=e[1];return"expiredAt"===a&&(i=new Date(1e3*i).toLocaleString()),"createdAt"===a&&(i=new Date(i).toLocaleString()),{key:a,value:i}}),t.expired_at=1e3*n.expiredAt,t.tmpUser.role=n.role,t.tmpUser.type=n.type,t.tmpUser.autoRenew=n.autoRenew,t.tmpUser.email=n.email,t.tmpUser.phone=n.phone,t.tmpUser.name=n.name,t.tmpUser.recipient_address=n.recipient_address,t.tmpUser.carrier_type=n.carrier_type,t.tmpUser.carrier_value=n.carrier_value,t.reason_info="",t.reason_payment="",t.userEmail=n.email}t.onConsoleAdjust()})},resetFormStates:function(){this.reasonInfoState=null,this.reasonPaymentState=null,this.reasonCarrierTypeState=null},onUpdate:function(t,e){var a=this,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;this.resetFormStates();var n;e.target,e.target.firstChild;switch(this.setBtnLoadingStyle(e),i=i||"info",i){case"info":if(!this.reason_info)return this.reasonInfoState="invalid",void this.setBtnErrorStyle(e);this.tmpUser.reason=this.reason_info,n="/v3/console/user/".concat(t),this.tmpUser.old_role=this.item.role,this.tmpUser.old_type=this.item.type,this.tmpUser.email||(this.tmpUser.email=null),this.tmpUser.phone||(this.tmpUser.phone=null);var r=new Date(this.expired_at),s=r.toISOString().split("T"),o=s[0]+" "+s[1].substring(0,5);this.tmpUser.expiredAt=o;break;case"payment":if(this.tmpUser.carrier_type||this.tmpUser.carrier_value){var l=this.tmpUser.carrier_type;if(!["0","1","2"].includes(l))return this.reasonCarrierTypeState="invalid",void this.setBtnErrorStyle(e);if(!this.tmpUser.carrier_value)return this.reasonCarrierTypeState="invalid",void this.setBtnErrorStyle(e)}if(this.tmpUser.carrier_type||(this.tmpUser.carrier_type=null),this.tmpUser.carrier_value||(this.tmpUser.carrier_value=null),!this.reason_payment)return this.reasonPaymentState="invalid",void this.setBtnErrorStyle(e);this.tmpUser.reason=this.reason_payment,n="/v3/console/user/".concat(t,"?type=payment"),this.tmpUser.recipient_address||(this.tmpUser.recipient_address=null);break;case"unbind":n="/v3/console/user/".concat(t,"?type=unbind");break;case"unbindMOD":n="/v3/console/user/".concat(t,"?type=unbindMOD");break;case"edmUnsubscribe":n="/v3/console/user/".concat(t,"?type=edmUnsubscribe");break;case"disableAccount":var c=new Date,u=Math.floor(c/1e3);if(!this.reason_info)return this.reasonInfoState="invalid",void this.setBtnErrorStyle(e);if("prime"===this.item.type)return alert('使用者身份為 "prime" 不能刪除 !'),void this.setBtnErrorStyle(e);if("expired"!=this.item.role)return alert('使用者身份必需是 "expired" !'),void this.setBtnErrorStyle(e);if(!(this.item.expiredAt<u))return alert('"到期日" 需小於今天才能刪除 !'),void this.setBtnErrorStyle(e);this.tmpUser.name=null,this.tmpUser.email=null,this.tmpUser.phone=null,this.tmpUser.role="expired",this.tmpUser.type="general",this.tmpUser.mediaSource=null,this.tmpUser.autoRenew=!1;var d=c.toISOString().split("T"),p=d[0]+" "+d[1].substring(0,5);this.tmpUser.expiredAt=p,this.tmpUser.reason=this.reason_info,n="/v3/console/user/".concat(t,"?type=disableAccount");break}console.log(n),P.request("put",n,this.tmpUser).then(function(t){a.setBtnSuccessStyle(e),a.alertMsg="更新成功",a.showAlert(),a.$forceUpdate()}).catch(function(t){a.setBtnErrorStyle(e),t.response&&t.response.data&&t.response.data.status&&t.response.data.status.message?a.alertMsg="更新失敗: "+t.response.data.status.message:a.alertMsg="您的權限不足",a.showAlert()}).finally(function(){setTimeout(function(){a.setBtnWarningStyle(e),a.onFetch()},2e3)})},resetBtnStyle:function(t){var e=t.target,a=t.target.firstChild;e.classList.remove("btn-warning","btn-danger","btn-success"),a.classList.remove("fa-warning","fa-check"),a.classList.remove("fa-refresh","fa-spin")},setBtnLoadingStyle:function(t){this.resetBtnStyle(t);t.target;var e=t.target.firstChild;e.classList.add("fa-refresh"),e.classList.add("fa-spin")},setBtnWarningStyle:function(t){this.resetBtnStyle(t);var e=t.target,a=t.target.firstChild;e.classList.add("btn-warning"),a.classList.remove("fa-warning","fa-check"),a.classList.remove("fa-refresh","fa-spin")},setBtnErrorStyle:function(t){this.resetBtnStyle(t);var e=t.target,a=t.target.firstChild;e.classList.add("btn-danger"),a.classList.add("fa-warning"),a.classList.remove("fa-refresh","fa-spin")},setBtnSuccessStyle:function(t){this.resetBtnStyle(t);var e=t.target,a=t.target.firstChild;e.classList.add("btn-success"),a.classList.add("fa-check"),a.classList.remove("fa-refresh","fa-spin")},onConsoleAdjust:function(){var t=Object(le["a"])(regeneratorRuntime.mark(function t(){var e,a,i,n,r,s,o=this;return regeneratorRuntime.wrap(function(t){while(1)switch(t.prev=t.next){case 0:if(e=this.userEmail,this.isAdmin="admin"===JSON.parse(localStorage.user).roles,!1!==this.isAdmin){t.next=4;break}return t.abrupt("return");case 4:return t.next=6,Promise.all([P.request("get","/v3/console/roles"),P.request("get","/v3/console/user/roles?email="+e)]);case 6:a=t.sent,i=Object(ce["a"])(a,2),n=i[0],r=i[1],s=r.data.data,n.data.data.map(function(t){o.consoleRoles.push({value:t.name,key:t.name,checked:s&&s.includes(t.name)})});case 12:case"end":return t.stop()}},t,this)}));function e(){return t.apply(this,arguments)}return e}(),setRole:function(t,e){var a=this,i=this.userEmail,n={roles:[e],email:i},r="/v3/console/user/roles",s=t?"post":"delete";P.request(s,r,n).then(function(){a.alertMsg="更新成功",a.showAlert()}).catch(function(t){t.response&&t.response.data&&t.response.data.status&&t.response.data.status.message?a.alertMsg="更新失敗: "+t.response.data.status.message:a.alertMsg="您的權限不足",a.showAlert()})}}}),de=ue,pe=Object(_["a"])(de,se,oe,!1,null,null,null),me=pe.exports,be=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("b-row",[a("b-col",{attrs:{cols:"12",xl:"12"}},[a("transition",{attrs:{name:"slide"}},[a("b-card",{attrs:{header:t.caption}},[a("form",{on:{submit:function(e){return e.preventDefault(),t.onFetch(e)}}},[a("b-col",{attrs:{sm:"12"}},[a("b-form-group",[a("b-input-group",[a("b-col",{attrs:{sm:"3"}},[a("flat-pickr",{attrs:{placeholder:"請選擇時間區間",config:t.flatpickrConfig},model:{value:t.ttime,callback:function(e){t.ttime=e},expression:"ttime"}})],1),a("b-col",{attrs:{sm:"7"}},[a("b-form-input",{attrs:{type:"text",id:"name"},model:{value:t.q,callback:function(e){t.q=e},expression:"q"}})],1),a("b-col",{attrs:{sm:"2"}},[a("b-input-group-append",[a("b-button",{directives:[{name:"b-tooltip",rawName:"v-b-tooltip.hover",modifiers:{hover:!0}}],attrs:{variant:"primary",title:"查詢"},on:{click:t.onFetch}},[a("i",{staticClass:"fa",class:{"fa-refresh":t.loading,"fa-spin":t.loading}}),t._v("查詢")])],1)],1)],1)],1),a("medium",{domProps:{innerHTML:t._s("請輸入要查詢的 user_id, email, 關鍵字; 多個查詢條件時，請使用「空格」分隔，例如：「update_user 公關」")}}),a("b-alert",{attrs:{show:t.dismissCountDown,dismissible:"",variant:"warning"},on:{dismissed:function(e){t.dismissCountDown=0},"dismiss-count-down":t.countDownChanged}},[t._v("\n                   "+t._s(t.alertMsg)+"\n          ")])],1)],1),t.items?a("b-table",{attrs:{hover:t.hover,striped:t.striped,bordered:t.bordered,small:t.small,fixed:t.fixed,responsive:"sm",items:t.items,fields:t.fields,"current-page":t.currentPage,"per-page":t.perPage},scopedSlots:t._u([{key:"created_at",fn:function(e){return[t._v("\n          "+t._s(t.convertdate(e.item.created_at))+"\n        ")]}},{key:"before",fn:function(e){return[e.item.before&&"{}"!=JSON.stringify(e.item.before)?a("textarea",{attrs:{cols:"40",rows:"3",readonly:""}},[t._v(t._s(e.item.before))]):t._e()]}},{key:"after",fn:function(e){return[e.item.after&&"{}"!=JSON.stringify(e.item.before)?a("textarea",{attrs:{cols:"40",rows:"3",readonly:""}},[t._v(t._s(e.item.after))]):t._e()]}}],null,!1,1927086902)}):t._e()],1)],1)],1)],1)},fe=[];a("f576");function he(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),a.push.apply(a,i)}return a}function ge(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?he(a,!0).forEach(function(e){Object(B["a"])(t,e,a[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):he(a).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))})}return t}var ve={name:"UserChangeLog",props:{caption:{type:String,default:"User 修改紀錄查詢"},hover:{type:Boolean,default:!0},striped:{type:Boolean,default:!0},bordered:{type:Boolean,default:!1},small:{type:Boolean,default:!1},fixed:{type:Boolean,default:!1}},data:function(){return{alertMsg:"",dismissCountDown:0,items:[],q:"",fields:[{key:"id"},{key:"username"},{key:"email"},{key:"action"},{key:"reason"},{key:"created_at"},{key:"before"},{key:"after"}],currentPage:1,perPage:0,totalRows:0,flatpickrConfig:{mode:"range",altInput:!0,dateFormat:"Y-m-d",altFormat:"Y-m-d",allowInput:!1},ttime:null}},computed:ge({},Object(F["b"])(["loading"])),mounted:function(){null===this.ttime&&(this.ttime=this.d4date()),this.$route.query.q&&(this.q=this.$route.query.q,this.onFetch())},methods:{countDownChanged:function(t){this.dismissCountDown=t},showAlert:function(){this.dismissCountDown=5},getRowCount:function(t){return t.length},convertdate:function(t){var e=new Date(t),a=e.getFullYear(),i=String(e.getMonth()+1).padStart(2,"0"),n=String(e.getDate()).padStart(2,"0"),r=String(e.getHours()).padStart(2,"0"),s=String(e.getMinutes()).padStart(2,"0"),o=String(e.getSeconds()).padStart(2,"0"),l=a+"-"+i+"-"+n+" "+r+":"+s+":"+o;return l},d4date:function(){var t=6048e5,e=new Date,a=new Date((new Date).getTime()-t),i=e.getFullYear()+"-"+(e.getMonth()+1)+"-"+e.getDate(),n=a.getFullYear()+"-"+(a.getMonth()+1)+"-"+a.getDate(),r=[i,n];return r},onFetch:function(){var t=this;this.$router.push({path:"userchangelog",query:{q:this.q,t:this.ttime}});var e=encodeURIComponent(this.q);P.request("get","/v3/console/userchangelog?t="+this.ttime+"&q="+e).then(function(e){console.log("response.data",e.data),e.data&&e.data.data&&e.data.data.auditlogs?t.items=e.data.data.auditlogs:(t.alertMsg="沒搜尋到任何結果",t.showAlert())})}}},_e=ve,ye=(a("da97"),Object(_["a"])(_e,be,fe,!1,null,"44c6653a",null)),we=ye.exports,Oe=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("b-row",[a("b-col",{attrs:{cols:"12",xl:"12"}},[a("transition",{attrs:{name:"slide"}},[a("b-card",{attrs:{"no-header":""}},[a("template",{slot:"header"},[""!=t.q?a("b-button",{on:{click:function(e){return t.$router.go(-1)}}},[t._v("<")]):t._e(),t._v("\n        Order:  "+t._s(t.q)+"\n      ")],1),a("b-row",[a("b-col",{attrs:{sm:"12"}},[a("form",{on:{submit:function(e){return e.preventDefault(),t.onFetch(e)}}},[a("b-form-group",{attrs:{description:""}},[a("b-input-group",[a("b-form-input",{attrs:{type:"text",id:"name"},model:{value:t.q,callback:function(e){t.q=e},expression:"q"}}),a("b-input-group-append",[a("b-button",{attrs:{variant:"primary"},on:{click:t.onFetch}},[a("i",{staticClass:"fa",class:{"fa-refresh":t.loading,"fa-spin":t.loading}}),t._v("\n                  Submit\n                  ")])],1)],1)],1)],1),a("b-alert",{attrs:{show:t.dismissCountDown,dismissible:"",variant:t.alertCss},on:{dismissed:function(e){t.dismissCountDown=0},"dismiss-count-down":t.countDownChanged}},[t._v("\n                 "+t._s(t.alertMsg)+"\n          ")]),a("small",{domProps:{innerHTML:t._s("輸入 user_id, order_id, <span style='font-weight: bold;color: #e67300;'>超商繳費代碼 或是 IAB GPA 訂單號 </span>, 單筆查詢，點擊可看 Order 詳細資料；或您也可以由 User 的細節頁面點擊進入該 User 所有 Order 列表")}})],1)],1),t.payment.user_id?a("b-row",[a("b-col",{attrs:{sm:"12"}},[t.unSubscribeAvaliable?a("form",{on:{submit:function(e){return e.preventDefault(),t.onUnSubscribe(e)}}},[a("b-input-group",[t._v("\n            付款方式 "),a("b",[t._v(t._s(t.paymentHint[t.payment.payment_type]||"未知"))]),t._v(" "),"telecom"==t.payment.payment_type?a("b",[t._v(t._s(t.mpIdHint[t.payment.telecom_mp_id]||"未知 "+t.payment.telecom_mp_id))]):t._e(),a("b-form-input",{attrs:{type:"text",id:"reason",placeholder:"如要退租，請輸入退租理由，謝謝"},model:{value:t.reason,callback:function(e){t.reason=e},expression:"reason"}}),a("b-input-group-append",[a("b-button",{attrs:{variant:"primary"},on:{click:t.onUnSubscribe}},[a("i",{staticClass:"fa",class:{"fa-refresh":t.loading,"fa-spin":t.loading}}),t._v("\n                  取消訂閱\n                  ")])],1)],1)],1):t._e()])],1):t._e(),a("b-form-checkbox",{attrs:{value:"checked","unchecked-value":""},model:{value:t.showDetail,callback:function(e){t.showDetail=e},expression:"showDetail"}},[t._v("顯示詳細資料")]),t.items?a("b-table",{directives:[{name:"b-tooltip",rawName:"v-b-tooltip.hover",modifiers:{hover:!0}}],attrs:{hover:t.hover,striped:t.striped,bordered:t.bordered,small:t.small,fixed:t.fixed,responsive:"sm",items:t.items,fields:t.fields,"current-page":t.currentPage,"per-page":t.perPage,title:"點擊訂單編號 ID 可以看細節喔"},scopedSlots:t._u([{key:"id",fn:function(e){return[a("b-button",{attrs:{variant:"primary"},on:{click:function(a){return t.rowClicked(e.item)}}},[t._v(t._s(e.item.id))]),t._v(" \n          "),e.item.invoice?a("b-button",{attrs:{variant:"primary"},on:{click:function(a){return t.onInvoice(e.item.id)}}},[t._v("發票")]):t._e(),e.item.invoiceItem?a("b-card",[a("b",[t._v("KKTV, Inc.")]),a("li",[t._v("發票號碼: "+t._s(e.item.invoiceItem.invoice_number))]),a("li",[t._v("隨機碼: "+t._s(e.item.invoiceItem.random_number))]),a("li",[t._v("開立時間: "+t._s(new Date(e.item.invoiceItem.created_at).toLocaleString()))]),a("li",[t._v("金額: "+t._s(e.item.invoiceItem.total_amount))])]):t._e(),t.showDetail?a("span",[a("br"),t._v("\n          "+t._s(e.item.user_id)+" "),a("br"),t.showDetail&&e.item.info?a("textarea",{directives:[{name:"model",rawName:"v-model",value:e.item.info,expression:"data.item.info"}],attrs:{cols:"45",rows:"5",readonly:""},domProps:{value:e.item.info},on:{input:function(a){a.target.composing||t.$set(e.item,"info",a.target.value)}}}):t._e()]):t._e()]}},{key:"payment_type",fn:function(e){return[t._v("\n          "+t._s(t.paymentHint[e.item.payment_type]||"未知")+"\n          "+t._s(e.item.external_order_id)+"\n          "),a("br"),t._v("\n          "+t._s(e.item.item_name)+"\n        ")]}},{key:"order_date",fn:function(e){return[t._v("\n          "+t._s(new Date(e.item.order_date).toLocaleString())+"\n        ")]}},{key:"duration",fn:function(e){return[t._v("\n          "+t._s(new Date(e.item.start_date).toLocaleDateString())+" 至"),a("br"),t._v("\n          "+t._s(new Date(e.item.end_date).toLocaleDateString())+"\n        ")]}},{key:"created_at",fn:function(e){return[t._v("\n          "+t._s(new Date(e.item.created_at).toLocaleString())+" (created_at)\n          "),e.item.canceled_at?a("span",[a("br"),t._v(t._s(new Date(e.item.canceled_at).toLocaleString())+" (canceled_at)")]):t._e(),e.item.realized_at?a("span",[a("br"),t._v(t._s(new Date(e.item.realized_at).toLocaleString())+" (realized_at)")]):t._e()]}},{key:"price",fn:function(e){return[t._v("\n          "+t._s(e.item.price)+" "),a("br"),t._v("\n          "+t._s(e.item.price_no_tax)+" (未稅) "),a("br"),t._v("\n          "+t._s(e.item.tax_rate)+"%(稅率)\n        ")]}},{key:"status",fn:function(e){return[a("h4",[a("b-badge",{attrs:{variant:t.getBadge(e.item.status)}},[t._v(t._s(e.item.status?e.item.status:"未實現"))]),a("br"),a("b-badge",{attrs:{variant:t.getBadge("error")}},[t._v(t._s(e.item.in_grace>0?"寬限期":""))])],1)]}}],null,!1,2699526108)}):t._e()],2)],1)],1)],1)},ke=[];function je(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),a.push.apply(a,i)}return a}function xe(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?je(a,!0).forEach(function(e){Object(B["a"])(t,e,a[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):je(a).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))})}return t}var Se={name:"Order",props:{caption:{type:String,default:"Order"},hover:{type:Boolean,default:!0},striped:{type:Boolean,default:!0},bordered:{type:Boolean,default:!1},small:{type:Boolean,default:!0},fixed:{type:Boolean,default:!1}},data:function(){return{items:[],payment:{payment_type:"",user_id:""},unSubscribeAvaliable:!1,reason:"",showDetail:!1,alertCss:"",alertMsg:"",dismissCountDown:0,q:"",fields:[{key:"id",label:"訂單編號"},{key:"payment_type",label:"付款方式"},{key:"order_date",label:"訂單時間"},{key:"duration",label:"付費週期"},{key:"created_at",label:"時間"},{key:"price",label:"價格"},{key:"status",label:"狀態"}],unSubDict:{telecom:!0,credit_card:!0},paymentHint:{iap:"IAP (iOS)",iab:"IAB (Android)",credit_card:"信用卡",cvs_code:"超商代碼繳費",coupon:"COUPON (兌換序號)",telecom:"電信付款",mod:"MOD (中華電信)",bandott:"BANDOTT (便當機上盒)",tstar:"TSTAR (台灣之星)"},mpIdHint:{CYC_TCC:"台哥大（月租）",CYC_FET:"遠傳（月租）",CYC_OTP839:"中華 839（月租）",CYC_OTPCHT:"中華市話（月租）",CYC_HINET:"HiNet（月租）",CYC_TSTAR:"台灣之星（月租）",TCC:"台哥大",FET:"遠傳",FETCSP:"遠傳預付卡",OTP839:"中華839",OTPCHT:"中華市話",APT:"亞太",VIBO:"威寶",HINET:"HiNet",SONET:"net",ESUN:"支付寶",BILL99:"快錢"},currentPage:1,perPage:0,totalRows:0}},computed:xe({},Object(F["b"])(["loading"])),mounted:function(){this.$route.query.q&&(this.q=this.$route.query.q,this.onFetch())},methods:{getBadge:function(t){var e;switch(t){case"ok":e="success";break;case"fail":e="danger";break;case"error":e="warning";break;case"cancel":e="warning";break;case"in_process":e="outline-success";break;case"refund":e="secondary";break}return e},getRowCount:function(t){return t.length},orderLink:function(t){return"order/".concat(t.toString())},rowClicked:function(t){var e=this.orderLink(t.id);this.$router.push({path:e})},countDownChanged:function(t){this.dismissCountDown=t},showAlert:function(){this.dismissCountDown=5},onInvoice:function(t){var e=this,a="/v3/console/invoice?q="+t;P.request("get",a).then(function(t){if(t.data&&t.data.data&&t.data.data.invoice)for(var a=t.data.data.invoice,i=0;i<e.items.length;i++)if(e.items[i].id==a.order_id){e.items[i].invoiceItem=a,e.$set(e.items,i,e.items[i]);break}})},onUnSubscribe:function(){var t=this;if(!this.reason)return this.alertCss="warning",this.alertMsg="請填寫退租理由",void this.showAlert();console.log("unsubscribe");var e=this.payment.user_id,a={userID:e,reason:this.reason},i=Object.keys(a).map(function(t){return t+"="+a[t]}).join("&");console.log(e,i),P.request("post","/v3/console/order/unsubscribe?"+i).then(function(){t.alertCss="success",t.alertMsg="取消訂閱成功",t.showAlert(),t.onFetch()}).catch(function(e){t.alertCss="danger",t.alertMsg="取消訂閱失敗",e.response.data.status.message&&(t.alertMsg="取消訂閱失敗 "+e.response.data.status.message),t.showAlert()})},onFetch:function(){var t=this;this.$router.push({path:"/user/order",query:{q:this.q}}),P.request("get","/v3/console/order?q="+this.q).then(function(e){if(e.data&&e.data.data&&e.data.data.orders){if(t.items=e.data.data.orders,e.data.data.payment){t.payment=e.data.data.payment;var a=e.data.data.orders.length>0?null==e.data.data.orders[0].status:null;t.unSubDict[t.payment.payment_type]&&a?t.unSubscribeAvaliable=!0:t.unSubscribeAvaliable=!1}}else t.items=[]})}}},De=Se,Ce=(a("6c09"),Object(_["a"])(De,Oe,ke,!1,null,"6c3094dd",null)),Pe=Ce.exports,qe=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("b-row",[a("b-col",{attrs:{cols:"12",xl:"12"}},[a("b-card",{attrs:{"no-header":""}},[a("template",{slot:"header"},[a("b-button",{on:{click:function(e){return t.$router.go(-1)}}},[t._v("<")]),t._v("\n        Order: "+t._s(t.item.id)+"\n        "),a("b-button",{on:{click:function(e){return t.onUser(t.item.user_id)}}},[t._v("User")]),t._v(" \n        "),a("b-button",{on:{click:function(e){return t.onProduct(t.product)}}},[t._v("Product")]),t._v(" \n        "),a("b-button",{on:{click:function(e){return t.onAuditLog(t.$route.params.order_id)}}},[t._v("Audit Log")])],1),a("b-row",[a("b-col",{attrs:{sm:"12"}},[a("b-form",{attrs:{inline:""},on:{submit:function(t){t.stopPropagation(),t.preventDefault()}}},[a("label",{staticClass:"mr-sm-2",attrs:{for:"inline-form-custom-select-pref"}},[t._v("變更狀態為")]),a("b-form-group",{staticStyle:{width:"10%"},attrs:{state:t.statusState,"invalid-feedback":"*必選欄位",tooltip:!0}},[a("b-form-select",{staticStyle:{width:"100%"},attrs:{state:t.statusState,options:["ok","cancel","refund"]},model:{value:t.newStatus,callback:function(e){t.newStatus=e},expression:"newStatus"}})],1),t._v("\n             \n            "),a("label",{staticClass:"mr-sm-2",attrs:{for:"inline-form-custom-select-pref"}},[t._v("異動原因")]),a("b-form-group",{staticStyle:{width:"20%"},attrs:{state:t.reasonState,"invalid-feedback":"*必填欄位",tooltip:!0}},[a("b-form-input",{staticStyle:{width:"100%"},attrs:{type:"text",state:t.reasonState,required:""},model:{value:t.reason,callback:function(e){t.reason=e},expression:"reason"}})],1),t._v("\n             \n            "),a("b-button",{directives:[{name:"b-tooltip",rawName:"v-b-tooltip.hover",modifiers:{hover:!0}}],staticClass:"fa",attrs:{variant:"warning",title:"異動訂單狀態為您新選取的選項，您必須有 user 的權限"},on:{click:function(e){return t.onUpdate()}}},[a("i",{staticClass:"fa"}),t._v("\n              更改訂單狀態\n            ")])],1),a("div",{staticStyle:{color:"red","padding-top":"10px"}},[t._v(" * 如果要取消 IAB/IAP 訂單，請先確認用戶已退訂，謝謝！")]),a("b-alert",{attrs:{show:t.dismissCountDown,dismissible:"",variant:t.alertCss},on:{dismissed:function(e){t.dismissCountDown=0},"dismiss-count-down":t.countDownChanged}},[t._v("\n            "+t._s(t.alertMsg)+"\n          ")])],1)],1),a("br"),a("b-table",{attrs:{striped:"",small:"",fixed:"",responsive:"sm",items:t.items,fields:t.fields},scopedSlots:t._u([{key:"value",fn:function(e){return["status"==e.item.key?a("b-badge",{attrs:{variant:t.getBadge(e.item.value)}},[t._v(t._s(e.item.value?e.item.value:"未實現"))]):a("strong",[t._v(t._s(e.item.value))])]}}])})],2)],1)],1)},Me=[];function Ae(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),a.push.apply(a,i)}return a}function Te(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?Ae(a,!0).forEach(function(e){Object(B["a"])(t,e,a[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):Ae(a).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))})}return t}var Ie={name:"OrderDetail",props:{caption:{type:String,default:"Order Detail"},hover:{type:Boolean,default:!0},striped:{type:Boolean,default:!0},bordered:{type:Boolean,default:!1},small:{type:Boolean,default:!1},fixed:{type:Boolean,default:!1}},data:function(){return{alertCss:"",alertMsg:"",dismissCountDown:0,items:[],item:{},newStatus:null,reasonState:null,statusState:null,reason:"",fields:[{key:"key"},{key:"value"}],product:""}},computed:Te({},Object(F["b"])(["loading"])),mounted:function(){console.log("OrderDetail Mounted"),this.onFetch()},methods:{countDownChanged:function(t){this.dismissCountDown=t},showAlert:function(){this.dismissCountDown=5},getBadge:function(t){var e;switch(t){case"ok":e="success";break;case"fail":e="danger";break;case"error":e="warning";break;case"cancel":e="warning";break;case"in_process":e="outline-success";break;case"refund":e="secondary";break}return e},paymentHint:function(t){var e;switch(t){case"iab":e="Android";break;case"iap":e="Apple";break;case"credit_card":e="信用卡";break;case"cvs_code":e="超商代碼繳費";break;case"coupon":e="Coupon";break;case"mod":e="Mod";break;case"bandott":e="Bandott 便當";break;case"tstar":e="台灣之星";break;case"telecom":e="電信付款";break;default:e="未知"}return e},onUser:function(t){this.$router.push({path:"/user/"+t})},onProduct:function(t){this.$router.push({path:"/product/",query:{q:t}})},onAuditLog:function(t){this.$router.push({path:"/user/order/audit_log/"+this.$route.params.order_id})},onUpdate:function(){var t=this;if(this.resetFormStates(),this.item.id)if(this.newStatus)if(this.reason){var e="/v3/console/order",a={id:this.item.id,status:this.newStatus,reason:this.reason};P.request("put",e,a).then(function(e){console.log("OK"),t.alertCss="success",t.alertMsg="儲存成功",t.showAlert(),t.onFetch()}).catch(function(e){e.response&&e.response.data&&e.response.data.status&&e.response.data.status.message&&(t.alertMsg=e.response.data.status.message),t.alertCss="warning",t.showAlert()})}else this.reasonState="invalid";else this.statusState="invalid"},onFetch:function(){var t=this,e=this.$route.params.order_id;this.$router.push({path:"/user/order/"+e}),P.request("get","/v3/console/order?q="+e).then(function(a){if(a.data&&a.data.data&&a.data.data.orders){var i=a.data.data.orders,n=i.find(function(t){return t.id.toString()===e});t.item=n;var r=n?Object.entries(n):[["id","Not fount"]],s=["start_date","end_date","order_date","created_at","realized_at","canceled_at"];t.items=r.map(function(e){var a=Object(ce["a"])(e,2),i=a[0],n=a[1];return"product_id"==i?P.request("get","/v3/console/product").then(function(e){if(e.data&&e.data.data&&e.data.data.products){var a=e.data.data.products,i=a.find(function(t){return t.id===n.toString()});t.product=i.name}}):"payment_type"===i?n=n+" "+t.paymentHint(n):"status"===i&&(n=n||"未實現"),s.includes(i)&&null!=n&&(n=new Date(n).toLocaleString()),{key:i,value:n}})}else t.alertMsg="沒搜尋到任何結果",t.showAlert()})},resetFormStates:function(){this.reasonState=null,this.statusState=null}}},$e=Ie,Be=(a("2bd9"),Object(_["a"])($e,qe,Me,!1,null,"7240954e",null)),Fe=Be.exports,Ee=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("b-row",[a("b-col",{attrs:{cols:"12",xl:"12"}},[a("transition",{attrs:{name:"slide"}},[a("b-card",{attrs:{"no-header":""}},[a("template",{slot:"header"},[a("b-button",{on:{click:t.goBack}},[t._v("<")]),t._v(" \n          Order:  "+t._s(t.$route.params.order_id)+"\n        ")],1),a("b-row",[a("b-col",{attrs:{sm:"12"}},[a("form",{on:{submit:function(e){return e.preventDefault(),t.onFetch(e)}}},[a("b-form-group",[a("b-alert",{attrs:{show:t.dismissCountDown,dismissible:"",variant:"warning"},on:{dismissed:function(e){t.dismissCountDown=0},"dismiss-count-down":t.countDownChanged}},[t._v("\n                您的權限不足\n                ")])],1)],1)])],1),t.items?a("b-table",{attrs:{hover:t.hover,striped:t.striped,bordered:t.bordered,small:t.small,fixed:t.fixed,responsive:"sm",items:t.items,fields:t.fields,"current-page":t.currentPage,"per-page":t.perPage},scopedSlots:t._u([{key:"compare",fn:function(e){return[a("span",{domProps:{innerHTML:t._s(e.item.compare)}})]}}],null,!1,855556663)}):t._e()],2)],1)],1)],1)},Le=[],Ue=a("952c");function Re(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),a.push.apply(a,i)}return a}function Ne(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?Re(a,!0).forEach(function(e){Object(B["a"])(t,e,a[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):Re(a).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))})}return t}var ze={name:"OrderAuditLog",props:{hover:{type:Boolean,default:!0},striped:{type:Boolean,default:!0},bordered:{type:Boolean,default:!1},small:{type:Boolean,default:!1},fixed:{type:Boolean,default:!0}},data:function(){return{items:[],titleMap:{},dismissCountDown:0,q:"",fields:[{key:"action",label:"操作"},{key:"compare",label:"異動",thStyle:{width:"30%"}},{key:"reason",label:"異動原因"},{key:"username",label:"操作人員"},{key:"created_at",label:"異動時間",sortable:!0}],currentPage:1,perPage:20,totalRows:0}},computed:Ne({},Object(F["b"])(["loading"])),mounted:function(){this.onFetch()},methods:{goBack:function(){this.$router.go(-1)},countDownChanged:function(t){this.dismissCountDown=t},showAlert:function(){this.dismissCountDown=5},onFetch:function(){var t=this,e=this.$route.params.order_id;P.request("get","/v3/console/order/"+e+"/audit_log").then(function(e){console.log(e),e.data&&e.data.data&&(t.items=e.data.data,t.items.map(function(t){var e=JSON.parse(t.before),a=JSON.parse(t.after),i=Object(Ue["diffString"])(e,a).replace(/\[31m/g,'<span style="color: red;">').replace(/\[32m/g,'<span style="color: green;">').replace(/\[39m/g,"</span>");t.compare="<pre>"+i+"</pre>"}))})}}},He=ze,Ke=(a("d2f7"),a("c389"),Object(_["a"])(He,Ee,Le,!1,null,"1669d388",null)),Je=Ke.exports,Ve=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("b-row",[a("b-col",{attrs:{cols:"12",xl:"12"}},[a("transition",{attrs:{name:"slide"}},[a("b-card",{attrs:{"no-header":""}},[a("template",{slot:"header"},[""!=t.q?a("b-button",{on:{click:function(e){return t.$router.go(-1)}}},[t._v("<")]):t._e(),t._v("\n        Token:  "+t._s(t.q)+"\n      ")],1),a("b-row",[a("b-col",{attrs:{sm:"12"}},[a("form",{on:{submit:function(e){return e.preventDefault(),t.onFetch(e)}}},[a("b-form-group",{attrs:{description:"輸入 user_id ，您也可以由 user 的細節頁面點擊進入該 user Token 列表"}},[a("b-input-group",[a("b-form-input",{attrs:{type:"text",id:"name"},model:{value:t.q,callback:function(e){t.q=e},expression:"q"}}),a("b-input-group-append",[a("b-button",{attrs:{variant:"primary"},on:{click:t.onFetch}},[a("i",{staticClass:"fa",class:{"fa-refresh":t.loading,"fa-spin":t.loading}}),t._v("\n                  Submit\n                  ")])],1),a("b-alert",{attrs:{show:t.dismissCountDown,dismissible:"",variant:"warning"},on:{dismissed:function(e){t.dismissCountDown=0},"dismiss-count-down":t.countDownChanged}},[t._v("\n          這位大大，您的權限不足\n          ")])],1)],1)],1)])],1),a("p",[a("b-button",{directives:[{name:"b-tooltip",rawName:"v-b-tooltip.hover",modifiers:{hover:!0}}],attrs:{variant:"primary",title:"把這個user所有的 token 及 refresh 設定成 Expired，會讓播放行為失效"},on:{click:function(e){return t.onAllExpired()}}},[t._v("Set All Expired")])],1),t.items?a("b-table",{attrs:{hover:t.hover,striped:t.striped,bordered:t.bordered,small:t.small,fixed:t.fixed,responsive:"sm",items:t.items,fields:t.fields,"current-page":t.currentPage,"per-page":t.perPage},scopedSlots:t._u([{key:"token_id",fn:function(e){return[a("strong",[t._v(t._s(e.item.token_id))]),t._v(" \n          "),new Date(e.item.token_expired)>new Date?a("b-button",{directives:[{name:"b-tooltip",rawName:"v-b-tooltip.hover",modifiers:{hover:!0}}],attrs:{variant:"primary",title:"把這一組 token 及 refresh 設定成 Expired，會讓播放行為失效"},on:{click:function(a){return t.onExpired(e.item.token_id,e.item.refresh_token)}}},[t._v("Set Expired")]):t._e(),a("br"),t._v("\n          "+t._s(new Date(e.item.token_created).toLocaleString())+" (created)"),a("br"),t._v("\n          "+t._s(new Date(e.item.token_expired).toLocaleString())+" (expired)"),a("br"),t._v("\n          "+t._s(e.item.source_ip)),a("br"),t._v("\n          "+t._s(e.item.user_agent)+"\n          "),a("i",{staticClass:"fa fa-lg",class:t.userAgentHint(e.item.user_agent)}),a("br"),a("textarea",{directives:[{name:"model",rawName:"v-model",value:e.item.token,expression:"data.item.token"}],attrs:{cols:"50",rows:"3",readonly:""},domProps:{value:e.item.token},on:{input:function(a){a.target.composing||t.$set(e.item,"token",a.target.value)}}}),a("textarea",{attrs:{cols:"50",rows:"3",readonly:""},domProps:{innerHTML:t._s(t.tokenString(e.item.token))}})]}},{key:"refresh_token",fn:function(e){return[a("strong",[t._v(t._s(e.item.refresh_token))]),a("br"),t._v("\n          "+t._s(new Date(e.item.created_at).toLocaleString())+" (created)"),a("br"),t._v("\n          "+t._s(new Date(e.item.expired_at).toLocaleString())+" (expired)"),a("br"),e.item.provider?a("textarea",{directives:[{name:"model",rawName:"v-model",value:e.item.provider,expression:"data.item.provider"}],attrs:{cols:"50",rows:"3",readonly:""},domProps:{value:e.item.provider},on:{input:function(a){a.target.composing||t.$set(e.item,"provider",a.target.value)}}}):t._e(),e.item.provider?a("span",[a("br"),e.item.provider.includes("kkbox")?a("b-button",{directives:[{name:"b-tooltip",rawName:"v-b-tooltip.hover",modifiers:{hover:!0}}],attrs:{variant:"primary",title:"如要查詢狀態，請到 User 資訊頁面(簡稱上一頁)"}},[t._v("KKBOX")]):t._e(),e.item.provider.includes("facebook")?a("b-button",{attrs:{variant:"secondary"}},[t._v("Facebook")]):t._e(),e.item.provider.includes("accountkit")?a("b-button",{attrs:{variant:"danger"}},[t._v("AccountKit")]):t._e()],1):t._e()]}}],null,!1,*********)}):t._e()],2)],1)],1)],1)},We=[];a("f559");function Ge(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),a.push.apply(a,i)}return a}function Ye(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?Ge(a,!0).forEach(function(e){Object(B["a"])(t,e,a[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):Ge(a).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))})}return t}var Xe={name:"Token",props:{caption:{type:String,default:"Tokens"},hover:{type:Boolean,default:!0},striped:{type:Boolean,default:!0},bordered:{type:Boolean,default:!1},small:{type:Boolean,default:!1},fixed:{type:Boolean,default:!1}},data:function(){return{items:[],dismissCountDown:0,q:"",fields:[{key:"token_id"},{key:"refresh_token"}],currentPage:1,perPage:0,totalRows:0}},computed:Ye({},Object(F["b"])(["loading"])),mounted:function(){this.$route.query.q&&(this.q=this.$route.query.q,this.onFetch())},methods:{getBadge:function(t){return"Active"===t?"success":"Inactive"===t?"secondary":"Pending"===t?"warning":"Banned"===t?"danger":"primary"},countDownChanged:function(t){this.dismissCountDown=t},showAlert:function(){this.dismissCountDown=5},userAgentHint:function(t){var e="";return e=t.startsWith("com.kktv.ios.kktv")||t.includes("iPhone")?"fa-apple":t.startsWith("com.kktv.kktv")||t.includes("Adnroid")?"fa-android":t.startsWith("axios")?"fa-desktop":t.includes("Macintosh")?"fa-desktop":t.includes("Windows")?"fa-desktop":t.includes("Linux")?"fa-linux":"fa-question",e},getRowCount:function(t){return t.length},tokenString:function(t){return 3===t.split(".").length?window.atob(t.split(".")[1]):"{}"},openKKBOX:function(t){var e=JSON.parse(t);e&&e.provider&&e.token&&""!==e.token&&"kkbox"===e.provider&&window.open("https://account.kkbox.com/oauth2/tokeninfo?access_token="+encodeURIComponent(e.token))},onExpired:function(t,e){var a=this;console.log(t,e);var i="/v3/console/token?tokenid=".concat(t,"&refreshtoken=").concat(e);P.request("put",i).then(function(t){a.onFetch()}).catch(function(t){a.showAlert()})},onAllExpired:function(){var t=this;P.request("put","/v3/console/device/token?userID="+this.q).then(function(e){t.onFetch()}).catch(function(e){t.showAlert()})},onFetch:function(){var t=this;this.$router.push({path:"/user/token",query:{q:this.q}}),P.request("get","/v3/console/token?q="+this.q).then(function(e){e.data&&e.data.data&&e.data.data.tokens&&(t.items=e.data.data.tokens)})}}},Ze=Xe,Qe=(a("eb76"),Object(_["a"])(Ze,Ve,We,!1,null,"5cdea644",null)),ta=Qe.exports,ea=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("b-row",[a("b-col",{attrs:{cols:"12",xl:"12"}},[a("transition",{attrs:{name:"slide"}},[a("b-card",{attrs:{"no-header":""}},[a("template",{slot:"header"},[""!=t.q?a("b-button",{on:{click:function(e){return t.$router.go(-1)}}},[t._v("<")]):t._e(),t._v("\n        Family:  "+t._s(t.q)+"\n\n      "),""!=t.q?a("b-button",{on:{click:function(e){return t.onOrder(t.q)}}},[a("i",{staticClass:"fa",class:{"fa-refresh":t.loading,"fa-spin":t.loading}}),t._v("\n        查訂單\n      ")]):t._e()],1),a("b-row",[a("b-col",{attrs:{sm:"12"}},[a("form",{on:{submit:function(e){return e.preventDefault(),t.onFetch(e)}}},[a("b-form-group",{attrs:{description:""}},[a("b-input-group",[a("b-form-input",{attrs:{type:"text",id:"name"},model:{value:t.q,callback:function(e){t.q=e},expression:"q"}}),a("b-input-group-append",[a("b-button",{attrs:{variant:"primary"},on:{click:t.onFetch}},[a("i",{staticClass:"fa",class:{"fa-refresh":t.loading,"fa-spin":t.loading}}),t._v("\n                  Submit\n                  ")])],1)],1)],1)],1),a("b-alert",{attrs:{show:t.dismissCountDown,dismissible:"",variant:t.alertCss},on:{dismissed:function(e){t.dismissCountDown=0},"dismiss-count-down":t.countDownChanged}},[t._v("\n                 "+t._s(t.alertMsg)+"\n          ")]),a("small",{domProps:{innerHTML:t._s("輸入 family_id 也就是家庭方案付款人的 user_id")}})],1)],1),t.item.family?a("b-table",{attrs:{striped:"",small:"",fixed:"",responsive:"sm",items:t.items,fields:t.fields},scopedSlots:t._u([{key:"value",fn:function(e){return["next_order_date"==e.item.key?a("strong",[t._v("目前合約到期日 "+t._s(new Date(1e3*e.item.value).toLocaleString()))]):"family"==e.item.key?a("strong",[t._v("\n            共享人員列表 (第一位為付款人)\n            "),t._l(e.item.value,function(e){return a("li",[a("b-button",{on:{click:function(a){return t.onUser(e.id)}}},[t._v(t._s(e.id))]),a("br"),a("small",[t._v(t._s(e))])],1)})],2):a("strong",[t._v(t._s(e.item.value))])]}}],null,!1,3716932101)}):a("h3",[t._v("目前此 family ID 無任何有效合約，想了解更多，請查看此 family ID 訂單")])],2)],1)],1)],1)},aa=[];function ia(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),a.push.apply(a,i)}return a}function na(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?ia(a,!0).forEach(function(e){Object(B["a"])(t,e,a[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):ia(a).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))})}return t}var ra={name:"Family",props:{caption:{type:String,default:"Order"},hover:{type:Boolean,default:!0},striped:{type:Boolean,default:!0},bordered:{type:Boolean,default:!1},small:{type:Boolean,default:!0},fixed:{type:Boolean,default:!1}},data:function(){return{items:[],item:{},hadFamily:!1,payment:{payment_type:"",user_id:""},reason:"",showDetail:!1,alertCss:"",alertMsg:"",dismissCountDown:0,q:"",fields:[{key:"key"},{key:"value"}],paymentHint:{iap:"IAP (iOS)",iab:"IAB (Android)",credit_card:"信用卡",cvs_code:"超商代碼繳費",coupon:"COUPON (兌換序號)",telecom:"電信付款",mod:"MOD (中華電信)",bandott:"BANDOTT (便當機上盒)",tstar:"TSTAR (台灣之星)"},currentPage:1,perPage:0,totalRows:0}},computed:na({},Object(F["b"])(["loading"])),mounted:function(){this.$route.query.q&&(this.q=this.$route.query.q,this.onFetch())},methods:{getBadge:function(t){var e;switch(t){case"ok":e="success";break;case"fail":e="danger";break;case"error":e="warning";break;case"cancel":e="warning";break;case"in_process":e="outline-success";break;case"refund":e="secondary";break}return e},getRowCount:function(t){return t.length},orderLink:function(t){return"order/".concat(t.toString())},rowClicked:function(t){var e=this.orderLink(t.id);this.$router.push({path:e})},countDownChanged:function(t){this.dismissCountDown=t},showAlert:function(){this.dismissCountDown=5},onUser:function(t){this.$router.push({path:"/user/"+t})},onOrder:function(t){this.$router.push({path:"/user/order",query:{q:t}})},onFetch:function(){var t=this;this.$router.push({path:"/user/family",query:{q:this.q}}),P.request("get","/v3/console/family?q="+this.q).then(function(e){if(e.data&&e.data.data&&e.data.data.family){t.item=e.data.data.family;var a=t.item?Object.entries(t.item):[["id","Not fount"]];t.items=a.map(function(t){var e=Object(ce["a"])(t,2),a=e[0],i=e[1];return console.log(a,i),{key:a,value:i}}),e.data.data.family.family_limit?t.hadFamily=!0:t.hadFamily=!1}})}}},sa=ra,oa=(a("6400"),Object(_["a"])(sa,ea,aa,!1,null,"09f973df",null)),la=oa.exports,ca=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("b-row",[a("b-col",{attrs:{cols:"12",xl:"12"}},[a("transition",{attrs:{name:"slide"}},[a("b-card",{attrs:{"no-header":""}},[a("template",{slot:"header"},[t._v("\n        MOD subscriber ID:  "+t._s(t.q)+"\n      ")]),a("b-row",[a("b-col",{attrs:{sm:"12"}},[a("form",{on:{submit:function(e){return e.preventDefault(),t.onFetch(e)}}},[a("b-form-group",{attrs:{description:""}},[a("b-input-group",[a("b-form-input",{attrs:{type:"text",id:"name"},model:{value:t.q,callback:function(e){t.q=e},expression:"q"}}),a("b-input-group-append",[a("b-button",{attrs:{variant:"primary"},on:{click:t.onFetch}},[a("i",{staticClass:"fa",class:{"fa-refresh":t.loading,"fa-spin":t.loading}}),t._v("\n                  Submit\n                  ")])],1),a("b-alert",{attrs:{show:t.dismissCountDown,dismissible:"",variant:"warning"},on:{dismissed:function(e){t.dismissCountDown=0},"dismiss-count-down":t.countDownChanged}},[t._v("\n          這位大大，您的權限不足\n          ")])],1)],1),a("small",{domProps:{innerHTML:t._s("請輸入 MOD ID 查詢 <span style='font-weight: bold; color: #e67300;'>※ 這裡的資料是透過 API 去「中華電信的 SMS 系統」查詢，呈現的資料是用戶目前在中華電信的訂閱狀況，供同仁排除問題時對照使用。</span>")}})],1)])],1),a("table",{staticClass:"table b-table table-striped",attrs:{"aria-colcount":"1"}},[a("thead",{},[a("tr",[a("th",{attrs:{"aria-colindex":"1"}},[t._v("MOD ID")]),a("th",[t._v("區域 (北/中/南)")]),a("th",[t._v("訂購方案 (KKTV)")]),a("th",[t._v("方案代碼")]),a("th",[t._v("服務代碼")]),a("th",[t._v("價格")]),a("th",[t._v("起訂日")]),a("th",[t._v("結束日")]),a("th",[t._v("綁定的 userID (KKTV)")])])]),a("tbody",{},[a("tr",[a("td",[t._v(" "+t._s(t.subscriber_id)+" ")]),a("td",[t._v(" "+t._s(t.area)+" ")]),a("td",[t._v(" "+t._s(t.subscriber.ProductName)+" ")]),a("td",[t._v(" "+t._s(t.subscriber.ItemID)+" ")]),a("td",[t._v(" "+t._s(t.subscriber.MType)+" ")]),a("td",[t._v(" "+t._s(t.subscriber.Price)+" ")]),a("td",[t._v(" "+t._s(t.subscriber.StartTime)+" ")]),a("td",[t._v(" "+t._s(t.subscriber.EndTime)+" ")]),a("td",{on:{click:function(e){return t.click2User(t.subscriber.UserID)}}},[t._v(" "+t._s(t.subscriber.UserID)+" ")])])])])],2)],1)],1)],1)},ua=[];function da(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),a.push.apply(a,i)}return a}function pa(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?da(a,!0).forEach(function(e){Object(B["a"])(t,e,a[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):da(a).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))})}return t}var ma={name:"MOD",props:{caption:{type:String,default:"MOD"},hover:{type:Boolean,default:!0},striped:{type:Boolean,default:!0},bordered:{type:Boolean,default:!1},small:{type:Boolean,default:!1},fixed:{type:Boolean,default:!1}},data:function(){return{area:"",subscriber_id:"",subscriber:{},alertMsg:"",dismissCountDown:0,q:"",fields:[{key:"subscriber_id"},{key:"subscriber_area"}],currentPage:1,perPage:0,totalRows:0}},computed:pa({},Object(F["b"])(["loading"])),mounted:function(){this.$route.query.q&&(this.q=this.$route.query.q,this.onFetch())},methods:{getBadge:function(t){return"Active"===t?"success":"Inactive"===t?"secondary":"Pending"===t?"warning":"Banned"===t?"danger":"primary"},countDownChanged:function(t){this.dismissCountDown=t},showAlert:function(){this.dismissCountDown=5},userAgentHint:function(t){var e="";return e=t.startsWith("com.kktv.ios.kktv")||t.includes("iPhone")?"fa-apple":t.startsWith("com.kktv.kktv")||t.includes("Adnroid")?"fa-android":t.startsWith("axios")?"fa-desktop":t.includes("Macintosh")?"fa-desktop":t.includes("Windows")?"fa-desktop":t.includes("Linux")?"fa-linux":"fa-question",e},getRowCount:function(t){return t.length},click2User:function(t){this.$router.push({path:"/user/".concat(t.toString())})},onFetch:function(){var t=this;this.$router.push({path:"/user/mod",query:{q:this.q}}),P.request("get","/v3/console/modorderstate?q="+this.q).then(function(e){e.data&&e.data.data&&(t.subscriber_id=e.data.data.subsriberID,"None"===e.data.data.subsriberArea?t.area="帳號不存在，非訂戶":t.area=e.data.data.subsriberArea,t.subscriber=e.data.data.subsriberinfo,""===e.data.data.subsriberinfo.ItemID&&(t.subscriber.ItemID="未訂閱方案"),""===e.data.data.subsriberinfo.UserID&&(t.subscriber.UserID="未綁定"))})}}},ba=ma,fa=(a("b27a"),Object(_["a"])(ba,ca,ua,!1,null,"0df01e17",null)),ha=fa.exports,ga=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("b-row",[a("b-col",{attrs:{cols:"12",xl:"12"}},[a("transition",{attrs:{name:"slide"}},[a("b-card",{attrs:{"no-header":""}},[a("template",{slot:"header"},[a("b-button",{on:{click:t.goBack}},[t._v("<")]),t._v(" \n        User id:  "+t._s(t.$route.params.id)+"\n      ")],1),a("b-row",[a("b-col",{attrs:{sm:"12"}},[a("form",{on:{submit:function(e){return e.preventDefault(),t.onFetch(e)}}},[a("b-form-group",[a("b-input-group"),a("b-alert",{attrs:{show:t.dismissCountDown,dismissible:"",variant:"warning"},on:{dismissed:function(e){t.dismissCountDown=0},"dismiss-count-down":t.countDownChanged}},[t._v("\n            這位大大，您的權限不足\n            ")])],1)],1)])],1),t.items?a("b-table",{attrs:{hover:t.hover,striped:t.striped,bordered:t.bordered,small:t.small,fixed:t.fixed,responsive:"sm",items:t.items,fields:t.fields,"current-page":t.currentPage,"per-page":t.perPage},scopedSlots:t._u([{key:"title_id",fn:function(e){return[t._v("\n          "+t._s(("00"+(e.index+1)).slice(-2))+"\n          "),a("b-button",{on:{click:function(a){return t.onDelete(e.item)}}},[a("i",{staticClass:"fa fa-trash"}),t._v(" Delete")]),t._v("\n           \n          "),a("strong",[t._v(t._s(e.item))]),t._v("\n           \n          "),t.titleMap[e.item]?a("span",[t.titleMap[e.item].cover?a("img",{attrs:{src:t.titleMap[e.item].cover,width:"50"}}):t._e(),t._v("\n            "+t._s(t.titleMap[e.item].name)+"\n          ")]):t._e()]}}],null,!1,31946609)}):t._e()],2)],1)],1)],1)},va=[];function _a(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),a.push.apply(a,i)}return a}function ya(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?_a(a,!0).forEach(function(e){Object(B["a"])(t,e,a[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):_a(a).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))})}return t}var wa={name:"Favirite",props:{caption:{type:String,default:"Favorite Tiles"},hover:{type:Boolean,default:!0},striped:{type:Boolean,default:!0},bordered:{type:Boolean,default:!1},small:{type:Boolean,default:!1},fixed:{type:Boolean,default:!1}},data:function(){return{items:[],titleMap:{},dismissCountDown:0,q:"",fields:[{key:"title_id"}],currentPage:1,perPage:0,totalRows:0}},computed:ya({},Object(F["b"])(["loading"])),mounted:function(){this.onFetch()},methods:{goBack:function(){this.$router.go(-1)},getBadge:function(t){return"Active"===t?"success":"Inactive"===t?"secondary":"Pending"===t?"warning":"Banned"===t?"danger":"primary"},getRowCount:function(t){return t.length},countDownChanged:function(t){this.dismissCountDown=t},showAlert:function(){this.dismissCountDown=5},onDelete:function(t){var e=this,a=this.$route.params.id,i="/v3/console/user/favorite/"+a+"/"+t;console.log(i),P.request("delete",i).then(function(t){t.data&&t.data.data&&t.data.data.titles?e.items=t.data.data.titles:e.items=[]}).catch(function(t){e.showAlert()})},onTitleMap:function(){var t=this,e="";e=this.items.join(" "),P.request("get","/v3/console/titlehint?q="+e).then(function(e){if(e.data&&e.data.data&&e.data.data.titles)for(var a=e.data.data.titles,i=0;i<a.length;i++){var n=a[i];t.$set(t.titleMap,n.id,n)}})},onFetch:function(){var t=this,e=this.$route.params.id;P.request("get","/v3/console/user/favorite/"+e).then(function(e){e.data&&e.data.data&&e.data.data.titles&&(t.items=e.data.data.titles,t.onTitleMap())})}}},Oa=wa,ka=(a("9fff"),Object(_["a"])(Oa,ga,va,!1,null,"754e3e24",null)),ja=ka.exports,xa=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("b-row",[a("b-col",{attrs:{cols:"12",xl:"12"}},[a("transition",{attrs:{name:"slide"}},[a("b-card",{attrs:{"no-header":""}},[a("template",{slot:"header"},[a("b-button",{on:{click:t.goBack}},[t._v("<")]),t._v(" \n        User id:  "+t._s(t.$route.params.id)+"\n      ")],1),a("b-row",[a("b-col",{attrs:{sm:"12"}},[a("form",{on:{submit:function(e){return e.preventDefault(),t.onFetch(e)}}},[a("b-form-group",[a("b-input-group"),a("b-alert",{attrs:{show:t.dismissCountDown,dismissible:"",variant:"warning"},on:{dismissed:function(e){t.dismissCountDown=0},"dismiss-count-down":t.countDownChanged}},[t._v("\n            這位大大，您的權限不足\n            ")])],1)],1)])],1),t.items?a("b-table",{attrs:{hover:t.hover,striped:t.striped,bordered:t.bordered,small:t.small,fixed:t.fixed,responsive:"sm",items:t.items,fields:t.fields,"current-page":t.currentPage,"per-page":t.perPage},scopedSlots:t._u([{key:"title_id",fn:function(e){return[t._v("\n          "+t._s(("00"+(e.index+1)).slice(-2))+"\n          "),a("b-button",{on:{click:function(a){return t.onDelete(e.item)}}},[a("i",{staticClass:"fa fa-trash"}),t._v(" Delete")]),t._v("\n           \n          "),a("strong",[t._v(t._s(e.item))]),t._v("\n           \n          "),t.titleMap[e.item]?a("span",[t.titleMap[e.item].cover?a("img",{attrs:{src:t.titleMap[e.item].cover,width:"50"}}):t._e(),t._v("\n            "+t._s(t.titleMap[e.item].name)+"\n          ")]):t._e()]}}],null,!1,31946609)}):t._e()],2)],1)],1)],1)},Sa=[];function Da(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),a.push.apply(a,i)}return a}function Ca(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?Da(a,!0).forEach(function(e){Object(B["a"])(t,e,a[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):Da(a).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))})}return t}var Pa={name:"WatchHistory",props:{caption:{type:String,default:"Watch History"},hover:{type:Boolean,default:!0},striped:{type:Boolean,default:!0},bordered:{type:Boolean,default:!1},small:{type:Boolean,default:!1},fixed:{type:Boolean,default:!1}},data:function(){return{items:[],titleMap:{},dismissCountDown:0,q:"",fields:[{key:"title_id"}],currentPage:1,perPage:0,totalRows:0}},computed:Ca({},Object(F["b"])(["loading"])),mounted:function(){this.onFetch()},methods:{goBack:function(){this.$router.go(-1)},getBadge:function(t){return"Active"===t?"success":"Inactive"===t?"secondary":"Pending"===t?"warning":"Banned"===t?"danger":"primary"},getRowCount:function(t){return t.length},countDownChanged:function(t){this.dismissCountDown=t},showAlert:function(){this.dismissCountDown=5},onDelete:function(t){var e=this,a=this.$route.params.id,i="/v3/console/user/watch_history/"+a+"/"+t;console.log(i),P.request("delete",i).then(function(t){t.data&&t.data.data&&t.data.data.titles?e.items=t.data.data.titles:e.items=[]}).catch(function(t){e.showAlert()})},onTitleMap:function(){var t=this,e="";e=this.items.join(" "),P.request("get","/v3/console/titlehint?q="+e).then(function(e){if(e.data&&e.data.data&&e.data.data.titles)for(var a=e.data.data.titles,i=0;i<a.length;i++){var n=a[i];t.$set(t.titleMap,n.id,n)}})},onFetch:function(){var t=this,e=this.$route.params.id;P.request("get","/v3/console/user/watch_history/"+e).then(function(e){e.data&&e.data.data&&e.data.data.titles&&(t.items=e.data.data.titles,t.onTitleMap())})}}},qa=Pa,Ma=(a("b45c"),Object(_["a"])(qa,xa,Sa,!1,null,"202e726e",null)),Aa=Ma.exports,Ta=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("b-row",[a("b-col",{attrs:{cols:"12",xl:"12"}},[a("transition",{attrs:{name:"slide"}},[a("b-card",{attrs:{"no-header":""}},[a("template",{slot:"header"},[a("b-button",{on:{click:t.goBack}},[t._v("<")]),t._v(" \n          User id:  "+t._s(t.$route.params.id)+"\n        ")],1),a("b-row",[a("b-col",{attrs:{sm:"12"}},[a("form",{on:{submit:function(e){return e.preventDefault(),t.onFetch(e)}}},[a("b-form-group",[a("b-alert",{attrs:{show:t.dismissCountDown,dismissible:"",variant:"warning"},on:{dismissed:function(e){t.dismissCountDown=0},"dismiss-count-down":t.countDownChanged}},[t._v("\n                您的權限不足\n                ")])],1)],1)])],1),t.items?a("b-table",{attrs:{hover:t.hover,striped:t.striped,bordered:t.bordered,small:t.small,fixed:t.fixed,responsive:"sm",items:t.items,fields:t.fields,"current-page":t.currentPage,"per-page":t.perPage},scopedSlots:t._u([{key:"compare",fn:function(e){return[a("span",{domProps:{innerHTML:t._s(e.item.compare)}})]}}],null,!1,855556663)}):t._e()],2)],1)],1)],1)},Ia=[];function $a(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),a.push.apply(a,i)}return a}function Ba(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?$a(a,!0).forEach(function(e){Object(B["a"])(t,e,a[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):$a(a).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))})}return t}var Fa,Ea={name:"UserAuditLog",props:{hover:{type:Boolean,default:!0},striped:{type:Boolean,default:!0},bordered:{type:Boolean,default:!1},small:{type:Boolean,default:!1},fixed:{type:Boolean,default:!0}},data:function(){return{items:[],titleMap:{},dismissCountDown:0,q:"",fields:[{key:"action",label:"操作"},{key:"compare",label:"異動",thStyle:{width:"30%"}},{key:"reason",label:"異動原因"},{key:"username",label:"操作人員"},{key:"created_at",label:"異動時間",sortable:!0}],currentPage:1,perPage:20,totalRows:0}},computed:Ba({},Object(F["b"])(["loading"])),mounted:function(){this.onFetch()},methods:{goBack:function(){this.$router.go(-1)},countDownChanged:function(t){this.dismissCountDown=t},showAlert:function(){this.dismissCountDown=5},onFetch:function(){var t=this,e=this.$route.params.id;P.request("get","/v3/console/user/"+e+"/audit_log").then(function(e){console.log(e),e.data&&e.data.data&&(t.items=e.data.data,t.items.map(function(t){var e=JSON.parse(t.before),a=JSON.parse(t.after),i=Object(Ue["diffString"])(e,a).replace(/\[31m/g,'<span style="color: red;">').replace(/\[32m/g,'<span style="color: green;">').replace(/\[39m/g,"</span>");t.compare="<pre>"+i+"</pre>"}))})}}},La=Ea,Ua=(a("a0ce"),a("6798"),Object(_["a"])(La,Ta,Ia,!1,null,"1aac218b",null)),Ra=Ua.exports,Na=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("b-row",[a("b-col",{attrs:{cols:"12",xl:"12"}},[a("transition",{attrs:{name:"slide"}},[a("b-card",{attrs:{header:t.caption}},[a("b-row",[a("b-col",{attrs:{sm:"12"}},[a("form",{on:{submit:function(e){return e.preventDefault(),t.onFetch(e)}}},[a("b-form-group",{attrs:{description:""}},[a("b-input-group",[a("b-form-input",{attrs:{type:"text",id:"name",placeholder:"輸入名稱或是代碼以在介面上過濾"},model:{value:t.q,callback:function(e){t.q=e},expression:"q"}}),a("b-input-group-append",[a("b-button",{attrs:{variant:"primary"},on:{click:function(e){return t.onEdit({})}}},[t._v("新增")])],1)],1)],1)],1),a("small",{domProps:{innerHTML:t._s("輸入 name or payment type ，預設只顯示啟用中的產品，<span style='font-weight: bold;color: #e67300;'>要顯示全部，請選顯示全部</span>")}}),a("br"),a("b-form-checkbox",{attrs:{value:"checked","unchecked-value":"false",inline:""},model:{value:t.showAll,callback:function(e){t.showAll=e},expression:"showAll"}},[t._v("顯示全部(包含未啟用)")]),a("b-form-checkbox",{attrs:{value:"checked","unchecked-value":"false",inline:""},model:{value:t.isCampaign,callback:function(e){t.isCampaign=e},expression:"isCampaign"}},[t._v("只顯示campaign")]),a("b-alert",{attrs:{show:t.dismissCountDown,dismissible:"",variant:t.alertCss},on:{dismissed:function(e){t.dismissCountDown=0},"dismiss-count-down":t.countDownChanged}},[t._v("\n                 "+t._s(t.alertMsg)+"\n          ")])],1)],1),t.items?a("b-table",{attrs:{hover:t.hover,striped:t.striped,bordered:t.bordered,small:t.small,fixed:t.fixed,responsive:"sm",items:t.filterItems,fields:t.fields,"current-page":t.currentPage,"per-page":t.perPage},scopedSlots:t._u([{key:"action",fn:function(e){return[a("b-button",{on:{click:function(a){return t.onEdit(e.item)}}},[a("i",{staticClass:"fa fa-edit"})])]}},{key:"item_name",fn:function(e){return[t._v("\n          "+t._s(e.item.item_name)+" "),a("br"),t._v("\n          "+t._s(e.item.name)),a("br"),t._v("\n          id: "+t._s(e.item.id)+"\n          "),e.item.external_product_id?a("br"):t._e(),t._v(t._s(e.item.external_product_id)+"\n        ")]}},{key:"price",fn:function(e){return[t._v("\n          "+t._s(e.item.price)+" "),a("br"),t._v("\n          (未稅價格 "+t._s(e.item.price_no_tax)+")\n        ")]}},{key:"tax_rate",fn:function(e){return[t._v("\n          "+t._s(e.item.tax_rate)+"%\n        ")]}},{key:"duration",fn:function(e){return[t._v("\n          "+t._s(e.item.duration)+"\n          "),e.item.free_duration?a("br"):t._e(),t._v("免費 "+t._s(e.item.free_duration)+"\n        ")]}},{key:"auto_renew",fn:function(e){return[a("b-badge",{attrs:{variant:t.getBadge(e.item.auto_renew)}},[t._v(t._s(e.item.auto_renew))])]}},{key:"as_subscribe",fn:function(e){return[a("b-badge",{attrs:{variant:t.getBadge(e.item.as_subscribe)}},[t._v(t._s(e.item.as_subscribe))])]}},{key:"active",fn:function(e){return[a("b-badge",{attrs:{variant:t.getBadge(e.item.active)}},[t._v(t._s(e.item.active))])]}}],null,!1,3622495089)}):t._e()],1)],1)],1),a("b-modal",{ref:"formModal",attrs:{size:"xl",title:t.detailTitle},on:{ok:t.onOK,cancel:t.onCancel},model:{value:t.showDetail,callback:function(e){t.showDetail=e},expression:"showDetail"}},[a("b-form-group",[a("b-form-group",{attrs:{label:"付款方式",description:"請先選取付款方式，才會自動計算手續費"}},[a("b-form-select",{attrs:{options:t.paymentOptions,state:t.formState.payment_type},on:{change:t.onChangePaymentType},model:{value:t.detailObj.payment_type,callback:function(e){t.$set(t.detailObj,"payment_type",e)},expression:"detailObj.payment_type"}}),a("b-form-invalid-feedback",{attrs:{state:t.formState.payment_type}},[t._v("\n            請選擇payment_type\n          ")])],1),a("b-form-group",{attrs:{label:"產品代號",description:"必須是獨一無二的代碼，不可以有特殊字元，請用因英文數字半形，不可以和其他產品代號重複"}},[a("b-form-input",{directives:[{name:"b-tooltip",rawName:"v-b-tooltip.hover",modifiers:{hover:!0}}],attrs:{type:"text",state:t.formState.name,title:"必須是獨一無二的，不可以重複"},model:{value:t.detailObj.name,callback:function(e){t.$set(t.detailObj,"name",e)},expression:"detailObj.name"}}),a("b-form-invalid-feedback",{attrs:{state:t.formState.name}},[t._v("\n            必填欄位\n          ")])],1),a("b-row",[a("b-col",[a("b-form-group",{attrs:{label:"價格"}},[a("b-form-input",{attrs:{type:"number",state:t.formState.price},on:{input:t.onChangePrice,change:t.onChangePrice},model:{value:t.detailObj.price,callback:function(e){t.$set(t.detailObj,"price",e)},expression:"detailObj.price"}}),a("b-form-invalid-feedback",{attrs:{state:t.formState.price}},[t._v("\n                請設定值\n              ")])],1)],1),a("b-col",[a("b-form-group",{attrs:{label:"未稅價格",description:"價格輸入後，會自動幫您計算，最後請確認"}},[a("b-form-input",{attrs:{state:t.formState.price_no_tax,type:"number"},model:{value:t.detailObj.price_no_tax,callback:function(e){t.$set(t.detailObj,"price_no_tax",e)},expression:"detailObj.price_no_tax"}}),a("b-form-invalid-feedback",{attrs:{state:t.formState.price_no_tax}},[t._v("\n                請設定值\n              ")])],1)],1),a("b-col",[a("b-form-group",{attrs:{label:"稅率",description:"選取付款方式後，會自動幫您計算，最後請確認"}},[a("b-form-input",{attrs:{type:"number"},on:{change:t.onChangePrice},model:{value:t.detailObj.tax_rate,callback:function(e){t.$set(t.detailObj,"tax_rate",e)},expression:"detailObj.tax_rate"}})],1)],1),a("b-col",[a("b-form-group",{attrs:{label:"手續費",description:"價格輸入後，會自動幫您計算，最後請確認"}},[a("b-form-input",{attrs:{type:"number"},model:{value:t.detailObj.fee,callback:function(e){t.$set(t.detailObj,"fee",e)},expression:"detailObj.fee"}})],1)],1)],1),a("b-row"),a("b-form-group",{attrs:{label:"第三方廠商產品代碼"}},[a("b-form-input",{directives:[{name:"b-tooltip",rawName:"v-b-tooltip.hover",modifiers:{hover:!0}}],attrs:{type:"text",title:"這個代碼用於外部廠商配合需求"},model:{value:t.detailObj.external_product_id,callback:function(e){t.$set(t.detailObj,"external_product_id",e)},expression:"detailObj.external_product_id"}})],1),a("b-form-group",{attrs:{label:"付費週期"}},[a("b-row",{staticClass:"my-1"},[a("b-col",{attrs:{sm:"6"}},[a("b-form-input",{attrs:{type:"number"},model:{value:t.detailObj.duration_value,callback:function(e){t.$set(t.detailObj,"duration_value",e)},expression:"detailObj.duration_value"}}),a("b-form-invalid-feedback",{attrs:{state:t.formState.duration_unit}},[t._v("\n                  沒有設定付費週期\n                ")])],1),a("b-col",{attrs:{sm:"6"}},[a("b-form-select",{attrs:{state:t.formState.duration_unit,options:["","day","mon","year"]},model:{value:t.detailObj.duration_unit,callback:function(e){t.$set(t.detailObj,"duration_unit",e)},expression:"detailObj.duration_unit"}}),a("b-form-invalid-feedback",{attrs:{state:t.formState.duration_unit}},[t._v("\n                  沒有設定付費週期\n                ")])],1)],1)],1),a("b-form-group",{attrs:{label:"免費週期",description:"免費週期，只有在首購優惠有勾選下，才有效"}},[a("b-row",{staticClass:"my-1"},[a("b-col",{attrs:{sm:"6"}},[a("b-form-input",{attrs:{state:t.formState.duration,type:"number"},model:{value:t.detailObj.free_duration_value,callback:function(e){t.$set(t.detailObj,"free_duration_value",e)},expression:"detailObj.free_duration_value"}}),a("b-form-invalid-feedback",{attrs:{state:t.formState.duration}},[t._v("\n                  不能同時設定免費及優惠週期\n                ")])],1),a("b-col",{attrs:{sm:"6"}},[a("b-form-select",{attrs:{state:t.formState.free_duration_unit,options:["","day","mon","year"]},model:{value:t.detailObj.free_duration_unit,callback:function(e){t.$set(t.detailObj,"free_duration_unit",e)},expression:"detailObj.free_duration_unit"}}),a("b-form-invalid-feedback",{attrs:{state:t.formState.free_duration_unit}},[t._v("\n                  沒有設定免費週期的時間\n                ")])],1)],1)],1),a("b-form-group",{attrs:{label:"優惠週期",description:"和免費週期互斥，只能設定其中一項，優惠週期的時間單位如果和付費週期單位相同時，必須可以整除，ex: 付費週期是 2 mons ，這時候優惠週期如果也是用， 月 mons，必須是 2 mons 的倍數，你可以設定相同的 2 mons 或是 4 mons ，會依照倍數，去產生優惠價格的訂單，或是，優惠週期和付費週期的時間單位不同，系統，就產生單筆，優惠訂單"}},[a("b-row",{staticClass:"my-1"},[a("b-col",{attrs:{sm:"6"}},[a("b-form-input",{attrs:{state:t.formState.duration,type:"number"},model:{value:t.detailObj.discount_duration_value,callback:function(e){t.$set(t.detailObj,"discount_duration_value",e)},expression:"detailObj.discount_duration_value"}}),a("b-form-invalid-feedback",{attrs:{state:t.formState.duration}},[t._v("\n                  不能同時設定免費及優惠週期\n                ")])],1),a("b-col",{attrs:{sm:"6"}},[a("b-form-select",{attrs:{state:t.formState.discount_duration_unit,options:["","day","mon","year"]},model:{value:t.detailObj.discount_duration_unit,callback:function(e){t.$set(t.detailObj,"discount_duration_unit",e)},expression:"detailObj.discount_duration_unit"}}),a("b-form-invalid-feedback",{attrs:{state:t.formState.discount_duration_unit}},[t._v("\n                  沒有設定優惠週期的時間\n                ")])],1)],1)],1),a("b-form-group",{attrs:{label:"優惠價格"}},[a("b-form-input",{attrs:{state:t.formState.discount_price,type:"number"},model:{value:t.detailObj.discount_price,callback:function(e){t.$set(t.detailObj,"discount_price",e)},expression:"detailObj.discount_price"}}),a("b-form-invalid-feedback",{attrs:{state:t.formState.discount_price}},[t._v("\n                  沒有設定優惠週期的價格\n                ")])],1),"credit_card"==t.detailObj.payment_type?a("b-form-group",{attrs:{label:"家庭方案人數",description:"家庭方案人數，如非家庭方案，請留空，請注意一但方案上架，不要任意異動修改，人數，每次人數新增移除都依據此人數設定"}},[a("b-form-input",{attrs:{type:"number"},model:{value:t.detailObj.family,callback:function(e){t.$set(t.detailObj,"family",e)},expression:"detailObj.family"}})],1):t._e(),"credit_card"==t.detailObj.payment_type?a("b-card",{attrs:{title:"信用卡購買限制","bg-variant":"light"}},[a("b-form-group",{attrs:{label:"使用信用卡 Prefix 限制",description:"使用信用卡限制前綴號碼，每一組卡號前綴，請換行，不設定即無信用卡使用限制"}},[a("b-form-textarea",{attrs:{placeholder:"",rows:"3","max-rows":"6"},model:{value:t.detailObj.credit_card_prefix,callback:function(e){t.$set(t.detailObj,"credit_card_prefix",e)},expression:"detailObj.credit_card_prefix"}})],1),a("b-form-group",{attrs:{label:"是否需要輸入活動 RedeemCode 才可購買",description:"如有勾選，在 Redeem 系統下，Redeem Group 必須有此 Product ID 的 Redeem Code"}},[a("b-form-checkbox",{model:{value:t.detailObj.bundle.needRedeemCode,callback:function(e){t.$set(t.detailObj.bundle,"needRedeemCode",e)},expression:"detailObj.bundle.needRedeemCode"}},[t._v("需要輸入活動 RedeemCode 才可購買")])],1)],1):t._e(),a("b-row",[a("b-col",[a("b-form-group",{attrs:{label:"品項名稱",description:"請務必填寫"}},[a("b-form-input",{attrs:{state:t.formState.item_name,type:"text"},model:{value:t.detailObj.item_name,callback:function(e){t.$set(t.detailObj,"item_name",e)},expression:"detailObj.item_name"}}),a("b-form-invalid-feedback",{attrs:{state:t.formState.item_name}},[t._v("\n                  沒有設定品項名稱\n                ")])],1)],1),a("b-col",[a("b-form-group",{attrs:{label:"品項單位",description:"開發票的需求，請務必填寫"}},[a("b-form-input",{directives:[{name:"b-tooltip",rawName:"v-b-tooltip.hover",modifiers:{hover:!0}}],attrs:{title:"最多兩個字",maxlength:"2",state:t.formState.item_unit,type:"text",required:""},model:{value:t.detailObj.item_unit,callback:function(e){t.$set(t.detailObj,"item_unit",e)},expression:"detailObj.item_unit"}}),a("b-form-invalid-feedback",{attrs:{state:t.formState.item_unit}},[t._v("\n                  沒有設定品項單位\n                ")])],1)],1)],1),a("b-row",[a("b-col",[a("b-form-group",{attrs:{label:"國家"}},[a("b-form-select",{attrs:{options:["TW"]},model:{value:t.detailObj.country,callback:function(e){t.$set(t.detailObj,"country",e)},expression:"detailObj.country"}})],1)],1),a("b-col",[a("b-form-group",{attrs:{label:"幣別"}},[a("b-form-select",{attrs:{options:["NTD"]},model:{value:t.detailObj.currency,callback:function(e){t.$set(t.detailObj,"currency",e)},expression:"detailObj.currency"}})],1)],1)],1),a("b-form-group",{attrs:{label:"產品類別"}},[a("b-form-select",{attrs:{options:["Organic","Channel","MultiPlan","Other"]},model:{value:t.detailObj.category,callback:function(e){t.$set(t.detailObj,"category",e)},expression:"detailObj.category"}}),a("b-form-invalid-feedback",{attrs:{state:t.formState.category}},[t._v("\n              沒有設定產品類別\n            ")])],1),"credit_card"==t.detailObj.payment_type?a("b-card",{attrs:{title:"信用卡付款或活動專屬資訊(campaign)","bg-variant":"light"}},[a("b-row",{staticClass:"my-1"},[a("b-col",{attrs:{sm:"6"}},[a("b-form-group",{attrs:{label:"Description",description:"敘述，請用斷行隔開段落"}},[a("b-form-textarea",{attrs:{placeholder:"",rows:"3","max-rows":"6"},model:{value:t.detailObj.bundle.description,callback:function(e){t.$set(t.detailObj.bundle,"description",e)},expression:"detailObj.bundle.description"}})],1)],1),a("b-col",{attrs:{sm:"6"}},[a("b-form-group",{attrs:{label:"Package Description",description:"產品活動敘述，請用斷行隔開段落，超連結請參考範例格式  [Abc](https://google.com)"}},[a("b-form-textarea",{attrs:{placeholder:"",rows:"3","max-rows":"6"},model:{value:t.detailObj.bundle.package_description,callback:function(e){t.$set(t.detailObj.bundle,"package_description",e)},expression:"detailObj.bundle.package_description"}})],1)],1)],1),a("b-row",{staticClass:"my-1"},[a("b-col",{attrs:{sm:"3"}},[a("b-form-group",{attrs:{label:"Title",description:"Title"}},[a("b-form-input",{attrs:{type:"text",required:""},model:{value:t.detailObj.bundle.title,callback:function(e){t.$set(t.detailObj.bundle,"title",e)},expression:"detailObj.bundle.title"}})],1)],1),a("b-col",{attrs:{sm:"3"}},[a("b-form-group",{attrs:{label:"Sub Title",description:"Sub Title"}},[a("b-form-input",{attrs:{type:"text",required:""},model:{value:t.detailObj.bundle.subtitle,callback:function(e){t.$set(t.detailObj.bundle,"subtitle",e)},expression:"detailObj.bundle.subtitle"}})],1)],1),a("b-col",{attrs:{sm:"3"}},[a("b-form-group",{attrs:{label:"Text Color",description:"Text Color"}},[a("b-form-input",{attrs:{type:"text",required:""},model:{value:t.detailObj.bundle.text_color,callback:function(e){t.$set(t.detailObj.bundle,"text_color",e)},expression:"detailObj.bundle.text_color"}})],1)],1),a("b-col",{attrs:{sm:"3"}},[a("b-form-group",{attrs:{label:"URL Name",description:"campaign URL Name，用於活動頁面 url ，請務必填寫"}},[a("b-form-input",{attrs:{type:"text",required:""},model:{value:t.detailObj.bundle.url_name,callback:function(e){t.$set(t.detailObj.bundle,"url_name",e)},expression:"detailObj.bundle.url_name"}}),a("b-form-invalid-feedback",{attrs:{state:t.formState.url_name}},[t._v("\n                  沒有設定 URL Name\n                ")])],1)],1)],1),a("b-row",{staticClass:"my-1"},[a("b-col",{attrs:{sm:"4"}},[a("b-form-group",{attrs:{label:"Campaign Image",description:"圖片寬度最少要 375px，接受 image/jpeg, image/png 格式，2 個圖檔合計大小不可超過 1MB"}},[a("b-form-file",{ref:"image-input",attrs:{accept:"image/jpeg,image/png",required:""},model:{value:t.campaign_image,callback:function(e){t.campaign_image=e},expression:"campaign_image"}})],1),a("b-form-group",{attrs:{label:"預覽圖片",description:t.detailObj.campaign_image}},[t.detailObj.campaign_image_preview?a("b-img",{attrs:{src:t.detailObj.campaign_image_preview,thumbnail:"",width:200}}):t._e()],1)],1),a("b-col",{attrs:{sm:"4"}},[a("b-form-group",{attrs:{label:"Background Image",description:"Background Image，接受 image/jpeg, image/png 格式，2 個圖檔合計大小不可超過 1MB"}},[a("b-form-file",{ref:"image-input",attrs:{accept:"image/jpeg,image/png"},model:{value:t.background_image,callback:function(e){t.background_image=e},expression:"background_image"}})],1),a("b-form-group",{attrs:{label:"預覽圖片",description:t.detailObj.background_image}},[t.detailObj.background_image_preview?a("b-img",{attrs:{src:t.detailObj.background_image_preview,thumbnail:"",width:200}}):t._e()],1)],1),a("b-col",{attrs:{sm:"4"}},[a("b-form-group",{attrs:{label:"Background Color",description:"Background Color"}},[a("b-form-input",{attrs:{type:"text"},model:{value:t.detailObj.bundle.background_color,callback:function(e){t.$set(t.detailObj.bundle,"background_color",e)},expression:"detailObj.bundle.background_color"}})],1)],1)],1)],1):t._e(),a("b-form-checkbox",{model:{value:t.detailObj.active,callback:function(e){t.$set(t.detailObj,"active",e)},expression:"detailObj.active"}},[t._v("啟用")]),a("b-form-checkbox",{model:{value:t.detailObj.auto_renew,callback:function(e){t.$set(t.detailObj,"auto_renew",e)},expression:"detailObj.auto_renew"}},[t._v("自動更新(訂閱型產品)")]),a("b-form-checkbox",{model:{value:t.detailObj.as_subscribe,callback:function(e){t.$set(t.detailObj,"as_subscribe",e)},expression:"detailObj.as_subscribe"}},[t._v("適用首購優惠")]),a("b-form-group",{attrs:{label:"排序",description:"數字越小，排越前面"}},[a("b-form-input",{attrs:{type:"number"},model:{value:t.detailObj.sort,callback:function(e){t.$set(t.detailObj,"sort",e)},expression:"detailObj.sort"}})],1)],1)],1)],1)},za=[],Ha=(a("55dd"),{name:"Products",props:{hover:{type:Boolean,default:!0},striped:{type:Boolean,default:!0},bordered:{type:Boolean,default:!1},small:{type:Boolean,default:!1},fixed:{type:Boolean,default:!1}},data:function(){return{items:[],caption:"Products",isCampaign:!1,showAll:!1,showDetail:!1,detailTitle:"Hi detail",detailObj:{id:null,payment_type:"",tax_rate:0,price_no_tax:0,credit_card_prefix:"",family:0,bundle:{}},paymentOptions:[{value:"iap",text:"IAP (iOS)"},{value:"iab",text:"IAB (Android)"},{value:"credit_card",text:"信用卡"},{value:"cvs_code",text:"超商代碼繳費"},{value:"coupon",text:"COUPON (兌換序號)"},{value:"telecom",text:"電信付款"},{value:"mod",text:"MOD (中華電信)"},{value:"bandott",text:"BANDOTT (便當機上盒)"},{value:"tstar",text:"TSTAR (台灣之星)"}],background_image:null,campaign_image:null,campaignOptions:[{value:"credit_card",text:"信用卡"}],paymentCode:{iap:"01",iab:"02",credit_card:"00",cvs_code:"03",coupon:"04",telecom:"05",mod:"06",bandott:"07",tstar:"05"},alertCss:"",alertMsg:"",dismissCountDown:0,formState:{name:null,payment_type:null,item_unit:null,item_name:null,price:null,price_no_tax:null,duration:null,duration_unit:null,free_duration_unit:null,discount_duration_unit:null,discount_price:null,category:null},q:"",fields:[{key:"action",label:""},{key:"item_name",label:"名稱及代碼"},{key:"price",label:"價格"},{key:"tax_rate",label:"稅率"},{key:"duration",label:"週期"},{key:"payment_type",label:"付款方式"},{key:"auto_renew",label:"自動更新訂閱"},{key:"as_subscribe",label:"首購優惠"},{key:"active",label:"啟用"},{key:"info",label:""}],currentPage:1,perPage:0,totalRows:0}},mounted:function(){this.prepare()},computed:{filterItems:function(){var t,e=this.items;return e="checked"===this.isCampaign?e.filter(function(t){return 1==t.is_campaign}):e.filter(function(t){return 0==t.is_campaign}),"checked"===this.showAll?""===this.q?e:(t=this.q,e.filter(function(e){return e.name.includes(t)||e.item_name.includes(t)||e.payment_type.includes(t)})):""===this.q?e.filter(function(t){return!0===t.active}):(t=this.q,e.filter(function(e){return!0===e.active&&(e.name.includes(t)||e.item_name.includes(t)||e.payment_type.includes(t))}))}},watch:{$route:function(t,e){console.log("route change"),this.prepare()},titleID:function(t,e){""!=t&&t.length>1?(this.filterSuggestions(),this.isOpen=!0):this.isOpen=!1}},methods:{getBadge:function(t){var e;switch(t){case!0:e="success";break;case!1:e="warning";break}return e},getRowCount:function(t){return t.length},userLink:function(t){return"user/".concat(t.toString())},rowClicked:function(t){var e=this.userLink(t.id);this.$router.push({path:e})},countDownChanged:function(t){this.dismissCountDown=t},showAlert:function(){this.dismissCountDown=5},resetImage:function(){this.$refs["image-input"]&&this.$refs["image-input"].reset(),this.background_image=null,this.campaign_image=null},prepare:function(){this.resetFormStates(),this.resetDetailObj(),this.resetImage(),this.caption="Products",this.onFetch()},onFetch:function(){var t=this;P.request("get","/v3/console/product").then(function(e){if(e.data&&e.data.data&&e.data.data.products){t.items=e.data.data.products;for(var a=0;a<t.items.length;a++){var i=t.items[a];i.bundle&&i.bundle.url_name?t.items[a].is_campaign=!0:t.items[a].is_campaign=!1}}})},onOK:function(t){var e=this;if(t.preventDefault(),this.isValid()){var a=this;this.detailObj.duration_value&&""!=this.detailObj.duration_unit&&(this.detailObj.duration=this.detailObj.duration_value+" "+this.detailObj.duration_unit+"s"),this.detailObj.free_duration_value&&""!=this.detailObj.free_duration_unit&&this.detailObj.free_duration_value>0?this.detailObj.free_duration=this.detailObj.free_duration_value+" "+this.detailObj.free_duration_unit+"s":this.detailObj.free_duration="00:00:00",this.detailObj.discount_duration_value&&""!=this.detailObj.discount_duration_unit?this.detailObj.discount_duration=this.detailObj.discount_duration_value+" "+this.detailObj.discount_duration_unit+"s":this.detailObj.discount_duration="00:00:00",this.detailObj.payment_type&&(this.detailObj.payment_type_code=this.paymentCode[this.detailObj.payment_type]),this.detailObj.bundle||(this.detailObj.bundle={}),this.detailObj.credit_card_prefix?this.detailObj.bundle["prefix"]=this.detailObj.credit_card_prefix.split(/\r?\n/):delete this.detailObj.bundle["prefix"],this.detailObj.price=this.detailObj.price+"",this.detailObj.price_no_tax=this.detailObj.price_no_tax?this.detailObj.price_no_tax+"":"0",this.detailObj.discount_price=this.detailObj.discount_price?this.detailObj.discount_price+"":"0",this.detailObj.tax_rate=parseInt(this.detailObj.tax_rate),this.detailObj.fee=parseInt(this.detailObj.fee),this.detailObj.sort=parseInt(this.detailObj.sort),console.log(this.detailObj.family,this.detailObj.family>1),this.detailObj.family&&this.detailObj.family>1?this.detailObj.bundle["family"]=parseInt(this.detailObj.family):delete this.detailObj.bundle["family"],this.detailObj.bundle.description?this.detailObj.bundle["description"]=this.detailObj.bundle.description:delete this.detailObj.bundle["description"],this.detailObj.bundle.package_description?this.detailObj.bundle["package_description"]=this.detailObj.bundle.package_description:delete this.detailObj.bundle["package_description"],this.detailObj.bundle.title?this.detailObj.bundle["title"]=this.detailObj.bundle.title:delete this.detailObj.bundle["title"],this.detailObj.bundle.subtitle?this.detailObj.bundle["subtitle"]=this.detailObj.bundle.subtitle:delete this.detailObj.bundle["subtitle"],this.detailObj.bundle.text_color?this.detailObj.bundle["text_color"]=this.detailObj.bundle.text_color:delete this.detailObj.bundle["text_color"],this.detailObj.bundle.url_name?this.detailObj.bundle["url_name"]=this.detailObj.bundle.url_name:delete this.detailObj.bundle["url_name"],this.detailObj.bundle.campaign_image?this.detailObj.bundle["campaign_image"]=this.detailObj.bundle.campaign_image:delete this.detailObj.bundle["campaign_image"],this.detailObj.bundle.background_image?this.detailObj.bundle["background_image"]=this.detailObj.bundle.background_image:delete this.detailObj.bundle["background_image"],this.detailObj.bundle.background_color?this.detailObj.bundle["background_color"]=this.detailObj.bundle.background_color:delete this.detailObj.bundle["background_color"],console.log(this.detailObj);var i="/v3/console/product";P.request("post",i,this.detailObj).then(function(t){if(console.log(t),e.campaign_image||e.background_image){var i="/v3/console/productimage?id="+e.detailObj.id,n=new FormData;e.campaign_image&&n.append("file",e.campaign_image),e.background_image&&n.append("bkimage",e.background_image),P.request("post",i,n,{headers:{"Content-Type":"multipart/form-data"}}).then(function(){a.alertCss="success",a.alertMsg="儲存成功",a.showAlert(),a.onFetch()}).catch(function(){a.alertCss="warning",a.alertMsg="圖片儲存失敗",a.showAlert(),a.onFetch()})}else a.alertCss="success",a.alertMsg="儲存成功",a.showAlert(),a.onFetch();a.onCancel(),a.$refs.formModal.hide(),a.onFetch()}).catch(function(){a.alertCss="warning",a.alertMsg="儲存失敗",a.showAlert(),a.$refs.formModal.hide(),a.onFetch()})}},onChangePaymentType:function(){switch(this.detailObj.payment_type){case"credit_card":this.detailObj.bundle.needRedeemCode=!(!this.detailObj.bundle||!this.detailObj.bundle.needRedeemCode);break;case"mod":case"bandott":case"tstar":case"cvs_code":case"telecom":this.detailObj.tax_rate=5;break;case"iap":case"iab":this.detailObj.tax_rate=0;break;default:this.detailObj.tax_rate=0;break}},onChangePrice:function(){var t,e;switch(t=parseInt(this.detailObj.price),e=parseInt(this.detailObj.tax_rate),this.detailObj.payment_type){case"credit_card":this.detailObj.price_no_tax=Math.round(t/((100+e)/100)),this.detailObj.fee=Math.floor(.02*t);break;case"mod":this.detailObj.price_no_tax=Math.round(t/((100+e)/100)),this.detailObj.fee=Math.floor(.3*t);break;case"bandott":this.detailObj.price_no_tax=Math.round(t/((100+e)/100)),this.detailObj.fee=Math.floor(.2*t);break;case"tstar":this.detailObj.price_no_tax=Math.round(t/((100+e)/100));break;case"cvs_code":this.detailObj.price_no_tax=Math.round(t/((100+e)/100)),this.detailObj.fee=23;break;case"telecom":break;case"iap":this.detailObj.fee=Math.floor(.3*t),this.detailObj.tax_rate=0,this.detailObj.price_no_tax=t;break;case"iab":this.detailObj.fee=Math.floor(.15*t),this.detailObj.tax_rate=0,this.detailObj.price_no_tax=t;break;default:break}},resetFormStates:function(){this.formState={name:null,payment_type:null,item_unit:null,item_name:null,price:null,price_no_tax:null,duration:null,duration_unit:null,free_duration_unit:null,discount_duration_unit:null,discount_price:null}},isValid:function(){var t=!0;return this.resetFormStates(),console.log("start validate"),this.detailObj.name||(this.formState.name="invalid",t=!1),this.detailObj.payment_type||(this.formState.payment_type="invalid",t=!1),(!this.detailObj.price||this.detailObj.price<=0)&&(this.formState.price="invalid",t=!1),(!this.detailObj.price_no_tax||this.detailObj.price_no_tax<=0)&&(this.formState.price_no_tax="invalid",t=!1),this.detailObj.free_duration_value&&this.detailObj.discount_duration_value&&0!=this.detailObj.free_duration_value&&0!=this.detailObj.discount_duration_value&&(this.formState.duration="invalid",t=!1),this.detailObj.discount_duration_value&&this.detailObj.discount_price<=0&&(this.formState.discount_price="invalid",t=!1),this.detailObj.duration_value&&this.detailObj.duration_unit||(this.formState.duration_unit="invalid",t=!1),this.detailObj.free_duration_value>0&&!this.detailObj.free_duration_unit&&(this.formState.free_duration_unit="invalid",t=!1),this.detailObj.discount_duration_value>0&&!this.detailObj.discount_duration_unit&&(this.formState.discount_duration_unit="invalid",t=!1),this.detailObj.item_name||(this.formState.item_name="invalid",t=!1),this.detailObj.item_unit||(this.formState.item_unit="invalid",t=!1),this.detailObj.category||(this.formState.category="invalid",t=!1),t},onCancel:function(){this.detailObj={id:null,payment_type:"",tax_rate:0,price_no_tax:0,credit_card_prefix:"",family:0,bundle:{}},this.resetImage()},resetDetailObj:function(){this.onCancel()},onEdit:function(t){if(this.resetFormStates(),t.id&&(this.detailObj=JSON.parse(JSON.stringify(t))),t.id){this.detailTitle="修改 "+t.name,this.detailObj.price=parseInt(this.detailObj.price.replace(/\$|,/gi,"")),this.detailObj.price_no_tax=parseInt(this.detailObj.price_no_tax.replace(/\$|,/gi,"")),this.detailObj.discount_price=parseInt(this.detailObj.discount_price.replace(/\$|,/gi,""));var e=this.detailObj.duration.split(" "),a=this.detailObj.free_duration.split(" "),i=this.detailObj.discount_duration.split(" ");this.detailObj.credit_card_prefix="",2===e.length&&(this.detailObj.duration_value=parseInt(e[0]),this.detailObj.duration_unit=e[1].replace("s","")),2===a.length&&(this.detailObj.free_duration_value=parseInt(a[0]),this.detailObj.free_duration_unit=a[1].replace("s","")),2===i.length&&(this.detailObj.discount_duration_value=parseInt(i[0]),this.detailObj.discount_duration_unit=i[1].replace("s","")),this.detailObj.bundle&&this.detailObj.bundle.prefix&&(this.detailObj.credit_card_prefix=this.detailObj.bundle.prefix.join("\n")),this.detailObj.bundle&&this.detailObj.bundle.family&&(this.detailObj.family=this.detailObj.bundle.family),this.detailObj.bundle&&this.detailObj.bundle.campaign_image&&(this.campaign_image=this.detailObj.bundle.campaign_image,this.detailObj.campaign_image_preview=this.detailObj.bundle.campaign_preview),this.detailObj.bundle&&this.detailObj.bundle.background_image&&(this.background_image=this.detailObj.bundle.background_image,this.detailObj.background_image_preview=this.detailObj.bundle.background_preview)}else this.detailTitle="新增產品",this.detailObj={id:null,payment_type:"",tax_rate:0,price_no_tax:0,credit_card_prefix:"",family:0,bundle:{needRedeemCode:!1}};this.detailObj.bundle||(this.detailObj.bundle={}),this.showDetail=!0}}}),Ka=Ha,Ja=(a("3dd4"),Object(_["a"])(Ka,Na,za,!1,null,"83461c02",null)),Va=Ja.exports,Wa=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("b-row",[a("b-col",{attrs:{cols:"12",xl:"12"}},[a("transition",{attrs:{name:"slide"}},[a("b-card",{attrs:{header:t.caption}},[a("b-row",[a("b-col",{attrs:{sm:"12"}},[a("form",{on:{submit:function(e){return e.preventDefault(),t.onFetch(e)}}},[a("b-form-group",{attrs:{description:""}},[a("b-input-group",[a("b-input-group-append",[a("b-button",{directives:[{name:"b-tooltip",rawName:"v-b-tooltip.hover",modifiers:{hover:!0}}],attrs:{variant:"primary",title:"一個方案，可以有多個付費的方式，但是選取的 Product 都必須是有效的"},on:{click:function(e){return t.onEdit({product_ids:[]})}}},[t._v("新增方案")])],1)],1)],1)],1),a("small",[t._v("要顯示全部，請選顯示全部")]),a("b-form-checkbox",{attrs:{value:"checked","unchecked-value":"false"},model:{value:t.showAll,callback:function(e){t.showAll=e},expression:"showAll"}},[t._v("顯示全部")]),a("b-alert",{attrs:{show:t.dismissCountDown,dismissible:"",variant:t.alertCss},on:{dismissed:function(e){t.dismissCountDown=0},"dismiss-count-down":t.countDownChanged}},[t._v("\n                 "+t._s(t.alertMsg)+"\n          ")])],1)],1),t.items?a("b-table",{attrs:{hover:t.hover,striped:t.striped,bordered:t.bordered,small:t.small,fixed:t.fixed,responsive:"sm",items:t.filterItems,fields:t.fields,"current-page":t.currentPage,"per-page":t.perPage},scopedSlots:t._u([{key:"action",fn:function(e){return[a("b-button",{on:{click:function(a){return t.onEdit(e.item)}}},[a("i",{staticClass:"fa fa-edit"})])]}},{key:"active",fn:function(e){return[a("b-badge",{attrs:{variant:t.getBadge(e.item.active)}},[t._v(t._s(e.item.active))])]}}],null,!1,3149632372)}):t._e()],1)],1)],1),a("b-modal",{attrs:{size:"xl",title:t.detailTitle},on:{ok:t.onOK,cancel:t.onCancel},model:{value:t.showDetail,callback:function(e){t.showDetail=e},expression:"showDetail"}},[a("b-form-group",{attrs:{label:"Platform"}},[a("b-form-select",{attrs:{options:t.platformOptions},model:{value:t.detailObj.platform,callback:function(e){t.$set(t.detailObj,"platform",e)},expression:"detailObj.platform"}})],1),a("b-form-group",{attrs:{label:"價格"}},[a("b-form-input",{attrs:{type:"number"},model:{value:t.detailObj.price,callback:function(e){t.$set(t.detailObj,"price",e)},expression:"detailObj.price"}})],1),a("b-form-group",{attrs:{label:"週期"}},[a("b-form-input",{attrs:{type:"text"},model:{value:t.detailObj.duration,callback:function(e){t.$set(t.detailObj,"duration",e)},expression:"detailObj.duration"}})],1),a("b-form-group",{attrs:{label:"Title",description:"最多8個字"}},[a("b-form-input",{attrs:{type:"text",maxlength:"8"},model:{value:t.detailObj.title,callback:function(e){t.$set(t.detailObj,"title",e)},expression:"detailObj.title"}})],1),a("b-form-group",{attrs:{label:"Description",description:"最多30個字"}},[a("b-form-input",{attrs:{type:"text",maxlength:"30"},model:{value:t.detailObj.description,callback:function(e){t.$set(t.detailObj,"description",e)},expression:"detailObj.description"}})],1),a("b-form-group",{attrs:{label:"Highlight",description:"最多12個字"}},[a("b-form-input",{attrs:{type:"text",maxlength:"12"},model:{value:t.detailObj.highlight,callback:function(e){t.$set(t.detailObj,"highlight",e)},expression:"detailObj.highlight"}})],1),a("b-form-group",{attrs:{label:"Button Text"}},[a("b-form-input",{attrs:{type:"text"},model:{value:t.detailObj.button_text,callback:function(e){t.$set(t.detailObj,"button_text",e)},expression:"detailObj.button_text"}})],1),a("b-form-group",{attrs:{label:"Label",description:"最多15個字"}},[a("b-form-input",{attrs:{type:"text",maxlength:"15"},model:{value:t.detailObj.label,callback:function(e){t.$set(t.detailObj,"label",e)},expression:"detailObj.label"}})],1),a("b-form-group",{attrs:{label:"Promotion",description:"最多30個字"}},[a("b-form-input",{attrs:{type:"text",maxlength:"30"},model:{value:t.detailObj.promotion,callback:function(e){t.$set(t.detailObj,"promotion",e)},expression:"detailObj.promotion"}})],1),a("b-form-group",{attrs:{label:"product id (可以選多個)"}},[a("b-form-select",{attrs:{multiple:"",options:t.samePriceOptions,"select-size":8},model:{value:t.detailObj.product_ids,callback:function(e){t.$set(t.detailObj,"product_ids",e)},expression:"detailObj.product_ids"}})],1),a("b-form-group",[a("b-form-checkbox",{model:{value:t.detailObj.auto_renew,callback:function(e){t.$set(t.detailObj,"auto_renew",e)},expression:"detailObj.auto_renew"}},[t._v("是否週期扣款(訂閱制)")])],1),a("b-form-checkbox",{model:{value:t.detailObj.active,callback:function(e){t.$set(t.detailObj,"active",e)},expression:"detailObj.active"}},[t._v("啟用")]),a("b-form-group",{attrs:{label:"排序",description:"數字越小，排越前面"}},[a("b-form-input",{attrs:{type:"number"},model:{value:t.detailObj.sort,callback:function(e){t.$set(t.detailObj,"sort",e)},expression:"detailObj.sort"}})],1)],1)],1)},Ga=[],Ya=(Fa={name:"Packages",props:{caption:{type:String,default:"Packages"},hover:{type:Boolean,default:!0},striped:{type:Boolean,default:!0},bordered:{type:Boolean,default:!1},small:{type:Boolean,default:!1},fixed:{type:Boolean,default:!1}},data:function(){return{items:[],products:[],showAll:!1,showDetail:!1,detailTitle:"",detailObj:{id:null,product_ids:[]},pkgOptions:[],platformOptions:["web","android","ios","campaign"],alertCss:"",alertMsg:"",dismissCountDown:0,q:"",fields:[{key:"action",label:""},{key:"platform",label:"platform"},{key:"price",label:"價格"},{key:"duration",label:"週期"},{key:"title",label:"title"},{key:"description",label:"description"},{key:"button_text",label:"button_text"},{key:"label",label:"label"},{key:"product_ids",label:"方案 product id"},{key:"active",label:"啟用"},{key:"sort",label:"排序"}],currentPage:1,perPage:0,totalRows:0}},computed:{},mounted:function(){this.$route.query.q&&(this.q=this.$route.query.q),this.onProduct()}},Object(B["a"])(Fa,"computed",{filterItems:function(){return"checked"===this.showAll?this.items:this.items.filter(function(t){return!0===t.active})},samePriceOptions:function(){var t=this;return this.detailObj.price?(console.log(this.detailObj.price),this.pkgOptions.filter(function(e){return e.text.includes(t.detailObj.price)})):this.pkgOptions}}),Object(B["a"])(Fa,"methods",{getBadge:function(t){var e;switch(t){case!0:e="success";break;case!1:e="warning";break}return e},getRowCount:function(t){return t.length},userLink:function(t){return"user/".concat(t.toString())},rowClicked:function(t){var e=this.userLink(t.id);this.$router.push({path:e})},countDownChanged:function(t){this.dismissCountDown=t},showAlert:function(){this.dismissCountDown=5},onProduct:function(){var t=this;P.request("get","/v3/console/product").then(function(e){if(e.data&&e.data.data&&e.data.data.products){t.products=e.data.data.products.filter(function(t){return!0===t.active}),t.pkgOptions=[];for(var a=0;a<t.products.length;a++){var i={};i["value"]=t.products[a].id,i["text"]=t.products[a].id+" => "+t.products[a].price.replace(/,/gi,"")+" "+t.products[a].name+"("+t.products[a].item_name+","+t.products[a].payment_type+")",t.pkgOptions.push(i)}t.onFetch()}})},onFetch:function(){var t=this;P.request("get","/v3/console/package").then(function(e){if(e.data&&e.data.data&&e.data.data.pkgs){t.items=e.data.data.pkgs;for(var a=0;a<t.items.length;a++)t.items[a].product_ids=t.items[a].product_ids.map(function(t){return t.toString()})}})},onOK:function(){var t=this;this.detailObj.product_ids&&(this.detailObj.product_ids=this.detailObj.product_ids.map(function(t){return parseInt(t)})),this.detailObj.price=this.detailObj.price+"",this.detailObj.sort=parseInt(this.detailObj.sort);var e="/v3/console/package",a=this.onValid();if(""!=a)return t.alertCss="warning",t.alertMsg=a,void t.showAlert();P.request("post",e,this.detailObj).then(function(e){t.onFetch()}).catch(function(){t.alertCss="warning",t.alertMsg="儲存失敗",t.showAlert(),t.onFetch()})},onValid:function(){var t="";return this.detailObj.title||(t="沒有填寫 title"),this.detailObj.button_text||(t="沒有填寫 button_text"),t},onCancel:function(){this.detailObj={id:null,product_ids:[]}},onEdit:function(t){this.detailObj=Object.assign({},t),t.id?(this.detailTitle="修改 "+t.title,this.detailObj.price=parseInt(this.detailObj.price.replace(/\$|,/gi,""))):this.detailTitle="新增方案",this.showDetail=!0}}),Fa),Xa=Ya,Za=(a("ee53"),Object(_["a"])(Xa,Wa,Ga,!1,null,"83414858",null)),Qa=Za.exports,ti=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("b-row",[a("b-col",{attrs:{cols:"12",xl:"12"}},[a("transition",{attrs:{name:"slide"}},[a("b-card",{attrs:{header:t.caption}},[a("b-row",[a("b-col",{attrs:{sm:"12"}},[a("form",{on:{submit:function(e){return e.preventDefault(),t.onFetch(e)}}},[a("b-form-group",{attrs:{description:"輸入 EpisodID"}},[a("b-input-group",[a("b-form-input",{attrs:{type:"text",id:"name"},model:{value:t.q,callback:function(e){t.q=e},expression:"q"}}),a("b-input-group-append",[a("b-button",{attrs:{variant:"primary"},on:{click:t.onFetch}},[a("i",{staticClass:"fa",class:{"fa-refresh":t.loading,"fa-spin":t.loading}}),t._v("\n                  Submit\n                  ")])],1)],1)],1)],1)])],1),t.items?a("b-table",{attrs:{hover:t.hover,"sort-by":"StartTimestamp","sort-desc":!0,striped:t.striped,bordered:t.bordered,small:t.small,fixed:t.fixed,responsive:"sm",items:t.filterItems,fields:t.fields,"current-page":t.currentPage,"per-page":t.perPage},scopedSlots:t._u([{key:"StartTimestamp",fn:function(e){return[t._v("\n          "+t._s(new Date(e.item.StartTimestamp).toLocaleString())+"\n        ")]}},{key:"episodid",fn:function(e){return[a("strong",[t._v(t._s(e.item.episodid))])]}},{key:"dispatch",fn:function(e){return[a("b-badge",{attrs:{variant:t.getBadge(e.item.dispatch[1])}},[t._v(t._s(e.item.dispatch[1]))])]}},{key:"Transcode",fn:function(e){return[a("b-badge",{attrs:{variant:t.getBadge(e.item.Transcode[1])}},[t._v(t._s(e.item.Transcode[1]))]),a("br"),t._v("\n          Transcode ("),a("strong",[t._v(t._s(e.item.Transcode[2]))]),t._v(")\n        ")]}},{key:"thumbnail",fn:function(e){return[e.item.thumbnail?a("b-badge",{attrs:{variant:t.getBadge(e.item.thumbnail[1])}},[t._v(t._s(e.item.thumbnail[1]))]):t._e()]}},{key:"Encryption",fn:function(e){return[e.item.Encryption?a("b-badge",{attrs:{variant:t.getBadge(e.item.Encryption[1])}},[t._v(t._s(e.item.Encryption[1]))]):t._e()]}},{key:"Adaptive",fn:function(e){return[e.item.Adaptive?a("b-badge",{attrs:{variant:t.getBadge(e.item.Adaptive[1])}},[t._v(t._s(e.item.Adaptive[1]))]):t._e()]}},{key:"Cleanup",fn:function(e){return[e.item.Cleanup?a("span",[a("b-badge",{attrs:{variant:t.getBadge(e.item.Cleanup[1])}},[t._v(t._s(e.item.Cleanup[1]))]),a("br"),a("strong",[t._v(t._s(new Date(e.item.Cleanup[0]).toLocaleString())+" ")])],1):t._e()]}}],null,!1,1480733092)}):t._e()],1)],1)],1)],1)},ei=[];function ai(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),a.push.apply(a,i)}return a}function ii(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?ai(a,!0).forEach(function(e){Object(B["a"])(t,e,a[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):ai(a).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))})}return t}var ni={name:"Encoder",props:{caption:{type:String,default:"Encoding Status"},hover:{type:Boolean,default:!0},striped:{type:Boolean,default:!0},bordered:{type:Boolean,default:!1},small:{type:Boolean,default:!1},fixed:{type:Boolean,default:!1}},data:function(){return{itemMap:{},items:[],q:"",fields:[{key:"StartTimestamp",sortable:!0},{key:"episodid"},{key:"dispatch"},{key:"Transcode"},{key:"thumbnail"},{key:"Encryption"},{key:"Adaptive"},{key:"Cleanup"}],currentPage:1,perPage:0,totalRows:0}},mounted:function(){this.onFetch()},computed:ii({filterItems:function(){if(""===this.q)return this.items;var t=this.q;return this.items.filter(function(e){return e.episodid.includes(t)})}},Object(F["b"])(["loading"])),methods:{getBadge:function(t){var e;switch(t){case"COMPLETED":case"CLOSED":e="success";break;case"FAILED":e="danger";break;case"OPEN":e="warning";break}return e},getRowCount:function(t){return t.length},userLink:function(t){return"user/".concat(t.toString())},rowClicked:function(t){var e=this.userLink(t.id);this.$router.push({path:e})},checkTargetStage:function(t){return!!(t.startsWith("dispatch")||t.startsWith("thumbnail")||t.startsWith("CENC")||t.startsWith("Adaptive")||t.startsWith("Cleanup"))},cookItem:function(){for(var t in this.items=[],this.itemMap){var e={};e["episodid"]=t,e["jobs"]=this.itemMap[t],e[""];for(var a={},i="",n=[],r=0,s=0;s<e["jobs"].length;s++){var o=e["jobs"][s];0===s&&(e["StartTimestamp"]=o["StartTimestamp"]);var l=o["Execution"]["WorkflowId"].split("-")[0];this.checkTargetStage(l)&&(e[l]=[o["StartTimestamp"],o["ExecutionStatus"]],o["CloseStatus"]&&(e[l]=[o["StartTimestamp"],o["CloseStatus"]])),"Transcode"==l&&(a=o),o["Execution"]["WorkflowId"].startsWith("Transcode p")&&(o["ExecutionStatus"]&&"CLOSED"===o["ExecutionStatus"]&&(r+=1),n.push(o))}e["CENC Encryption"]&&(e["Encryption"]=e["CENC Encryption"]),n.length>0&&a?(i=r+"/"+n.length,e["Transcode"]=[a["StartTimestamp"],a["ExecutionStatus"],i],"CLOSED"!==a["ExecutionStatus"]&&(e["_rowVariant"]="danger")):e["Transcode"]=[,,],e["Encryption"]&&e["Transcode"]&&e["thumbnail"]&&e["Encryption"]&&e["Adaptive"]&&e["Cleanup"]||(e["_rowVariant"]="warning"),this.items.push(e)}},onFetch:function(){var t=this;P.request("get","/v3/console/encoder").then(function(e){e.data&&e.data.data&&e.data.data.jobs&&(t.itemMap=e.data.data.jobs,t.cookItem())})}}},ri=ni,si=(a("a8da"),Object(_["a"])(ri,ti,ei,!1,null,"8d3e44b8",null)),oi=si.exports,li=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("b-row",[a("b-col",{attrs:{cols:"12",xl:"12"}},[a("transition",{attrs:{name:"slide"}},[a("b-card",{attrs:{header:t.caption}},[a("b-row",[a("b-col",{attrs:{sm:"12"}},[a("form",{on:{submit:function(e){return e.preventDefault(),t.onFetch(e)}}},[a("b-form-group",{attrs:{description:"輸入 EpisodID"}},[a("b-input-group",[a("b-form-input",{attrs:{type:"text",id:"name"},model:{value:t.q,callback:function(e){t.q=e},expression:"q"}}),a("b-input-group-append",[a("b-button",{attrs:{variant:"primary"},on:{click:t.onFetch}},[a("i",{staticClass:"fa",class:{"fa-refresh":t.loading,"fa-spin":t.loading}}),t._v("\n                  Submit\n                  ")])],1)],1)],1)],1)])],1),t.items?a("b-table",{attrs:{hover:t.hover,"sort-by":"episode_id","sort-desc":!0,striped:t.striped,bordered:t.bordered,small:t.small,fixed:t.fixed,responsive:"sm",items:t.filterItems,fields:t.fields,"current-page":t.currentPage,"per-page":t.perPage},scopedSlots:t._u([{key:"env",fn:function(e){return[a("strong",[t._v(t._s(e.item.env))]),a("i",{directives:[{name:"b-tooltip",rawName:"v-b-tooltip.hover",modifiers:{hover:!0}}],staticClass:"fa",class:{"fa-plane":"sq"==e.item.queue,"fa-rocket":"pq"==e.item.queue},attrs:{title:"飛機(一般)，火箭(加速)"}}),a("br"),a("br"),a("br"),a("b-button",{directives:[{name:"b-tooltip",rawName:"v-b-tooltip.hover",modifiers:{hover:!0}}],attrs:{size:"sm",title:"您已確定此轉檔工作已完成，不需要再追蹤狀態，由 Encoder Dash 移除"},on:{click:function(a){return t.onRemove(e.item.episode_id)}}},[a("span",[a("i",{staticClass:"fa fa-ban"})])])]}},{key:"episod_id",fn:function(e){return[a("strong",[t._v(t._s(e.item.episod_id))])]}},{key:"file",fn:function(e){return[a("strong",[t._v(t._s(e.item.file))]),a("br"),e.item.source_url?a("small",[t._v(t._s(decodeURIComponent(e.item.source_url)))]):t._e(),a("br"),e.item.job_id?a("small",[t._v(t._s(e.item.job_id))]):t._e()]}},{key:"status",fn:function(e){return[a("b-progress",{staticClass:"mt-1"},t._l(["0","1","2","3","4","5","6","7"],function(i,n){return a("b-progress-bar",{key:n,attrs:{value:15,variant:e.item.status>7?"danger":e.item.status>=i?"success":"warning"}},[a("span",{staticStyle:{"font-size":"12px"}},[t._v(t._s(e.item.status>7?"Failed":t.statusMap[i])+"｜")])])}),1)]}},{key:"updated",fn:function(e){return[a("small",[t._v(t._s(new Date(1e3*e.item.updated).toLocaleString()))])]}}],null,!1,839997044)}):t._e()],1)],1)],1)],1)},ci=[];function ui(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),a.push.apply(a,i)}return a}function di(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?ui(a,!0).forEach(function(e){Object(B["a"])(t,e,a[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):ui(a).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))})}return t}var pi={name:"EncoderBV",props:{caption:{type:String,default:"BV Encoding Status"},hover:{type:Boolean,default:!0},striped:{type:Boolean,default:!0},bordered:{type:Boolean,default:!1},small:{type:Boolean,default:!1},fixed:{type:Boolean,default:!1}},data:function(){return{statusMap:{0:"Create",1:"Start",2:"Manifest",3:"CopyDash",4:"CopyHls",5:"Thumbnail",6:"Cleanup",7:"Done",8:"Failed"},items:[],q:"",fields:[{key:"env",sortable:!0,label:""},{key:"episode_id",sortable:!0},{key:"file"},{key:"status"},{key:"updated",label:"異動時間"}],currentPage:1,perPage:0,totalRows:0}},mounted:function(){this.onFetch()},computed:di({filterItems:function(){if(""===this.q)return this.items;var t=this.q;return this.items.filter(function(e){return e.episodid.includes(t)})}},Object(F["b"])(["loading"])),methods:{getBadge:function(t){var e;switch(t){case"Success":e="success";break;case"FAILED":e="danger";break;case"JobCreate":e="warning";break}return e},getRowCount:function(t){return t.length},userLink:function(t){return"user/".concat(t.toString())},rowClicked:function(t){var e=this.userLink(t.id);this.$router.push({path:e})},onRemove:function(t){var e=this;P.request("delete","/v3/console/encoderBV?episodeID="+t).then(function(t){e.onFetch()}).catch(function(t){alert(t)})},onFetch:function(){var t=this;P.request("get","/v3/console/encoderBV").then(function(e){e.data&&e.data.data&&(t.items=e.data.data)})}}},mi=pi,bi=(a("9adf"),Object(_["a"])(mi,li,ci,!1,null,"c5393ea2",null)),fi=bi.exports,hi=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("b-row",[a("b-col",{attrs:{cols:"12",xl:"12"}},[a("transition",{attrs:{name:"slide"}},[a("b-card",{attrs:{header:t.caption}},[a("b-row",[a("b-col",{attrs:{sm:"12"}},[a("form",{on:{submit:function(e){return e.preventDefault(),t.onFetch(e)}}},[a("b-form-group",{attrs:{description:""}},[a("b-input-group",[a("b-form-input",{attrs:{type:"text",id:"name"},model:{value:t.q,callback:function(e){t.q=e},expression:"q"}}),a("b-input-group-append",[a("b-button",{directives:[{name:"b-tooltip",rawName:"v-b-tooltip.hover",modifiers:{hover:!0}}],attrs:{variant:"primary",title:"開始搜尋後，這個轉轉轉，停了，就是查完了喔"},on:{click:t.onFetch}},[a("i",{staticClass:"fa",class:{"fa-refresh":t.loading,"fa-spin":t.loading}}),t._v("\n                  Submit\n                  ")])],1)],1)],1)],1),a("small",{domProps:{innerHTML:t._s("輸入要查詢的 Title ID or Title Name ，<span style='font-weight: bold;color: #e67300;'>查詢多筆可以用空格隔開，Title ID，Title Name 可以不用打完，由起始比對，符合就會出現</span>")}}),a("b-alert",{attrs:{show:t.dismissCountDown,dismissible:"",variant:"warning"},on:{dismissed:function(e){t.dismissCountDown=0},"dismiss-count-down":t.countDownChanged}},[t._v("\n                 "+t._s(t.alertMsg)+"\n          ")])],1)],1),t.items?a("b-table",{attrs:{hover:t.hover,striped:t.striped,bordered:t.bordered,small:t.small,fixed:t.fixed,responsive:"sm",items:t.items,fields:t.fields,"current-page":t.currentPage,"per-page":t.perPage},scopedSlots:t._u([{key:"id",fn:function(e){return[a("strong",[t._v(t._s(e.item.id))]),a("br"),a("strong",[t._v(t._s(e.item.name))]),a("p",[a("b-button",{directives:[{name:"b-tooltip",rawName:"v-b-tooltip.hover",modifiers:{hover:!0}}],attrs:{variant:"primary",title:"點擊進入此 Title 有效 Series 及 Episode 列表"},on:{click:function(a){return t.onSeries(e.item.id)}}},[t._v("Series")]),t._v(" \n          "),a("b-button",{directives:[{name:"b-tooltip",rawName:"v-b-tooltip.hover",modifiers:{hover:!0}}],attrs:{variant:"primary",title:"點擊進入此 Title 所有 extra 列表"},on:{click:function(a){return t.onExtra(e.item.id)}}},[t._v("Extra")])],1),e.item.meta.cover?a("br"):t._e(),a("img",{attrs:{src:e.item.meta.cover}})]}},{key:"meta_str",fn:function(e){return[a("textarea",{directives:[{name:"model",rawName:"v-model",value:e.item.release_info,expression:"data.item.release_info"}],attrs:{cols:"50",rows:"1",placeholder:"跟播資訊"},domProps:{value:e.item.release_info},on:{input:function(a){a.target.composing||t.$set(e.item,"release_info",a.target.value)}}}),a("br"),a("textarea",{directives:[{name:"model",rawName:"v-model",value:e.item.editor_comments,expression:"data.item.editor_comments"}],attrs:{cols:"50",rows:"3",placeholder:"劇我所知"},domProps:{value:e.item.editor_comments},on:{input:function(a){a.target.composing||t.$set(e.item,"editor_comments",a.target.value)}}}),a("br"),a("b-button",{directives:[{name:"b-tooltip",rawName:"v-b-tooltip.hover",modifiers:{hover:!0}}],attrs:{variant:"warning",title:"直接儲存這個 title 的跟播資訊及劇我所知，您必須有 content 的權限"},on:{click:function(a){return t.onUpdate(e.item,a)}}},[a("i",{staticClass:"fa"}),t._v("更新 跟播資訊 及 劇我所知")]),a("p"),e.item.meta_str?a("textarea",{directives:[{name:"model",rawName:"v-model",value:e.item.meta_str,expression:"data.item.meta_str"}],attrs:{cols:"50",rows:"6",readonly:""},domProps:{value:e.item.meta_str},on:{input:function(a){a.target.composing||t.$set(e.item,"meta_str",a.target.value)}}}):t._e(),t._l(t.introFields,function(i,n){return a("li",{key:n},[t._v("\n            "+t._s(i)+": "),e.item.meta[i]?a("span",[t._v(t._s(e.item.meta[i]))]):t._e()])}),e.item.meta["stills"]?a("span",[a("li",[t._v("stills:")]),t._l(e.item.meta["stills"],function(e,i){return a("span",{key:i},[a("img",{attrs:{src:e,width:"80"}}),t._v("\n            st"+t._s(("00"+(i+1)).slice(-2))+" "+t._s(e)+" "),a("br")])})],2):t._e()]}}],null,!1,568767213)}):t._e()],1)],1)],1)],1)},gi=[];function vi(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),a.push.apply(a,i)}return a}function _i(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?vi(a,!0).forEach(function(e){Object(B["a"])(t,e,a[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):vi(a).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))})}return t}var yi={name:"Titles",props:{caption:{type:String,default:"Titles"},hover:{type:Boolean,default:!0},striped:{type:Boolean,default:!0},bordered:{type:Boolean,default:!1},small:{type:Boolean,default:!1},fixed:{type:Boolean,default:!1}},data:function(){return{items:[],dismissCountDown:0,alertMsg:"",q:"",fields:[{key:"id",label:"TitleID"},{key:"meta_str",label:"Meta"}],introFields:["cover","tags","genres","themes","title_type"],currentPage:1,perPage:0,totalRows:0}},computed:_i({},Object(F["b"])(["loading"])),mounted:function(){this.$route.query.q&&(this.q=this.$route.query.q,this.onFetch())},methods:{countDownChanged:function(t){this.dismissCountDown=t},showAlert:function(){this.dismissCountDown=5},getRowCount:function(t){return t.length},userLink:function(t){return"user/".concat(t.toString())},rowClicked:function(t){var e=this.userLink(t.id);this.$router.push({path:e})},onSeries:function(t){this.$router.push({path:"/content/series",query:{q:t}})},onExtra:function(t){this.$router.push({path:"/content/extra",query:{q:t}})},onUpdate:function(t,e){var a=this;console.log(t.id,t.release_info,t.editor_comments);var i=e.target,n=i.firstChild;n.classList.add("fa-refresh"),n.classList.add("fa-spin"),i.classList.remove("btn-warning");var r="/v3/console/title/".concat(t.id),s={};s["release_info"]=t.release_info,s["editor_comments"]=t.editor_comments,P.request("put",r,s).then(function(t){i.classList.add("btn-success"),n.classList.remove("fa-refresh","fa-spin"),n.classList.add("fa-check"),a.alertMsg="更新成功",a.showAlert(),a.onFetch()}).catch(function(t){i.classList.add("btn-danger"),n.classList.remove("fa-refresh","fa-spin"),n.classList.add("fa-warning"),a.alertMsg="您的權限不足",a.showAlert()}).finally(function(){setTimeout(function(){i.classList.remove("btn-success","btn-danger"),i.classList.add("btn-warning"),n.classList.remove("fa-warning","fa-check")},2e3)})},onFetch:function(){var t=this;this.$router.push({path:"/content/title",query:{q:this.q}}),""!==this.q&&P.request("get","/v3/console/title?q="+this.q).then(function(e){e.data&&e.data.data&&e.data.data.titles?t.items=e.data.data.titles:t.items=[]})}}},wi=yi,Oi=(a("b0fa"),Object(_["a"])(wi,hi,gi,!1,null,"67327999",null)),ki=Oi.exports,ji=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("b-row",[a("b-col",{attrs:{cols:"12",xl:"12"}},[a("transition",{attrs:{name:"slide"}},[a("b-card",{attrs:{header:t.caption}},[a("b-row",[a("b-col",{attrs:{sm:"12"}},[a("form",{on:{submit:function(e){return e.preventDefault(),t.onFetch(e)}}},[a("b-form-group",{attrs:{description:"輸入 SeriesID 記得多少就打多少，多個請用空格，隔開"}},[a("b-input-group",[a("b-form-input",{attrs:{type:"text",id:"name"},model:{value:t.q,callback:function(e){t.q=e},expression:"q"}}),a("b-input-group-append",[a("b-button",{attrs:{variant:"primary"},on:{click:t.onFetch}},[a("i",{staticClass:"fa",class:{"fa-refresh":t.loading,"fa-spin":t.loading}}),t._v("\n                  Submit\n                  ")])],1)],1)],1)],1),a("b-alert",{attrs:{show:t.dismissCountDown,dismissible:"",variant:t.alertCss},on:{dismissed:function(e){t.dismissCountDown=0},"dismiss-count-down":t.countDownChanged}},[t._v("\n            "+t._s(t.alertMsg)+"\n          ")])],1)],1),t.items?a("b-table",{attrs:{hover:t.hover,striped:t.striped,bordered:t.bordered,small:t.small,fixed:t.fixed,responsive:"sm",items:t.items,fields:t.fields,"current-page":t.currentPage,"per-page":t.perPage},scopedSlots:t._u([{key:"action",fn:function(t){}},{key:"id",fn:function(e){return[a("strong",[t._v("\n            "+t._s(e.item.id)+" "+t._s(e.item.meta.title_type)+"\n          "),a("br"),t._v("\n          "+t._s(e.item.meta.title_name)+"\n          "),a("br"),t._v("\n          "+t._s(e.item.meta["license_start"])+"\n          "),a("br"),t._v("\n          "+t._s(e.item.meta["license_end"])+"\n          ")])]}},{key:"episode",fn:function(e){return[e.item.series&&e.item.series.episodes?a("span",{staticStyle:{"list-style-type":"none"}},[a("table",{staticStyle:{padding:"0",width:"100%"}},t._l(e.item.series.episodes,function(i){return a("tr",{key:i.id},[a("td",[i.still?a("b-img",{attrs:{left:"",src:i.still,width:"100"}}):t._e(),t._v("\n                  "+t._s(i.id)+" "+t._s(i.title)+" "+t._s(parseInt(i.duration))+" 秒\n                  "),a("b-badge",{attrs:{variant:t.getBadge(i)}},[t._v(t._s(t.getBadge(i)))]),a("br"),a("span",{class:{"text-light bg-dark":new Date<new Date(1e3*i.pub)}},[t._v("上:"+t._s(new Date(1e3*i.pub).toLocaleString()))]),a("span",{class:{"text-light bg-dark":new Date>new Date(1e3*i.unpub)}},[t._v("下:"+t._s(new Date(1e3*i.unpub).toLocaleString()))])],1),a("td",[a("b-button",{attrs:{size:"sm"},on:{click:function(e){return t.onModalPublishShow(i)}}},[a("i",{staticClass:"fa",class:{"fa-refresh":t.loading,"fa-spin":t.loading}}),t._v("上下架")]),a("b-button",{attrs:{size:"sm",variant:"primary"},on:{click:function(e){return t.onPublishNow(i)}}},[a("i",{staticClass:"fa",class:{"fa-refresh":t.loading,"fa-spin":t.loading}}),t._v("立刻上架")]),a("b-button",{attrs:{size:"sm",variant:"danger"},on:{click:function(e){return t.onUnPublishNow(i)}}},[a("i",{staticClass:"fa",class:{"fa-refresh":t.loading,"fa-spin":t.loading}}),t._v("立刻下架")]),"live"==e.item.meta.title_type?a("b-button",{attrs:{size:"sm",variant:"primary"},on:{click:function(e){return t.onModalShow(i)}}},[a("i",{staticClass:"fa",class:{"fa-refresh":t.loading,"fa-spin":t.loading}}),t._v("編輯")]):t._e(),a("b-button",{staticStyle:{"margin-left":"20px"},attrs:{size:"sm",variant:"warning"},on:{click:function(e){return t.onCreateInvalidation(i.id)}}},[a("i",{staticClass:"fa",class:{"fa-refresh":t.loading,"fa-spin":t.loading}}),t._v("更新縮圖")])],1)])}),0)]):t._e()]}}],null,!1,447711730)}):t._e()],1)],1)],1),a("b-modal",{ref:"formModal",attrs:{size:"xl",title:"Edit live episode"},on:{ok:t.onOK,cancel:t.onCancel},model:{value:t.showModal,callback:function(e){t.showModal=e},expression:"showModal"}},[a("form",{ref:"form",on:{submit:function(e){return e.stopPropagation(),e.preventDefault(),t.handleSubmit(e)}}},[a("b-form-group",{attrs:{label:"Dash",description:"Dash URL"}},[a("b-form-input",{attrs:{type:"text"},model:{value:t.dashURL,callback:function(e){t.dashURL=e},expression:"dashURL"}})],1),a("b-form-group",{attrs:{label:"HLS",description:"HLS URL"}},[a("b-form-input",{attrs:{type:"text"},model:{value:t.hlsURL,callback:function(e){t.hlsURL=e},expression:"hlsURL"}})],1),a("b-row",[a("b-col",{attrs:{sm:"6"}},[a("b-form-group",{attrs:{label:"License Start",description:"上架時間"}},[a("flat-pickr",{attrs:{config:t.flatpickrConfig},model:{value:t.licenseStart,callback:function(e){t.licenseStart=e},expression:"licenseStart"}})],1)],1),a("b-col",{attrs:{sm:"6"}},[a("b-form-group",{attrs:{label:"License End",description:"下架時間"}},[a("flat-pickr",{attrs:{config:t.flatpickrConfig},model:{value:t.licenseEnd,callback:function(e){t.licenseEnd=e},expression:"licenseEnd"}})],1)],1)],1)],1)]),a("b-modal",{ref:"formModal",attrs:{size:"xl",title:"Episode Publish/Unpublish"},on:{ok:t.onPublishOK,cancel:t.onCancel},model:{value:t.showPublishModal,callback:function(e){t.showPublishModal=e},expression:"showPublishModal"}},[a("form",{ref:"form",on:{submit:function(e){return e.stopPropagation(),e.preventDefault(),t.handlePublishSubmit(e)}}},[t._v("\n      EpisodeID "),a("b",[t._v(t._s(t.detailObj.id))]),a("b-row",[a("b-col",{attrs:{sm:"6"}},[a("b-form-group",{attrs:{label:"Publish Start",description:"上架時間"}},[a("flat-pickr",{attrs:{config:t.flatpickrConfig},model:{value:t.licenseStart,callback:function(e){t.licenseStart=e},expression:"licenseStart"}})],1)],1),a("b-col",{attrs:{sm:"6"}},[a("b-form-group",{attrs:{label:"Publish End",description:"下架時間"}},[a("flat-pickr",{attrs:{config:t.flatpickrConfig},model:{value:t.licenseEnd,callback:function(e){t.licenseEnd=e},expression:"licenseEnd"}})],1)],1)],1)],1)])],1)},xi=[];function Si(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),a.push.apply(a,i)}return a}function Di(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?Si(a,!0).forEach(function(e){Object(B["a"])(t,e,a[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):Si(a).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))})}return t}var Ci={name:"Series",props:{caption:{type:String,default:"Series"},hover:{type:Boolean,default:!0},striped:{type:Boolean,default:!0},bordered:{type:Boolean,default:!1},small:{type:Boolean,default:!1},fixed:{type:Boolean,default:!1}},data:function(){return{items:[],q:"",fields:[{key:"id",label:"SeriesID"},{key:"episode",label:"Episodes"}],introFields:["content_agent","content_provider","themes","genres","casts","tags","license_start","license_end"],alertCss:"",alertMsg:"",dismissCountDown:0,detailObj:{id:null},showModal:!1,showPublishModal:!1,licenseStart:new Date,licenseEnd:new Date,flatpickrConfig:{enableTime:!0,altFormat:"Y-m-d H:i",altInput:!0,dateFormat:"Z",allowInput:!0},currentPage:1,perPage:0,totalRows:0,dashURL:"",hlsURL:"",dummySize:{size:**********,sizes:{"1080p":**********,"240p":199660608,"360p":553252812,"480p":**********,"720p":**********},uri:"",uri_playzone:""},dummyPlayback:{p0:{bitrate:279723,resolution:"240p",size:199660608,uri:""},p1:{bitrate:477803,resolution:"360p",size:341046097,uri:""},p2:{bitrate:775103,resolution:"360p",size:553252812,uri:""},p3:{bitrate:1627251,resolution:"480p",size:**********,uri:""},p4:{bitrate:2674730,resolution:"720p",size:**********,uri:""},p5:{bitrate:5135973,resolution:"1080p",size:**********,uri:""}}}},computed:Di({},Object(F["b"])(["loading"])),mounted:function(){this.$route.query.q&&(this.q=this.$route.query.q,this.onFetch())},methods:{getBadge:function(t){var e=[],a="";t.title||e.push("缺 title"),t.end_year<t.start_year&&e.push("end_year start_year 錯誤"),t.duration>10&&t.mezzanines&&t.mezzanines.dash&&t.mezzanines.hls||e.push("轉檔失敗"),t.still||e.push("缺 still"),t.available||e.push("available 是 false"),t.has_subtitles&&!t.subtitles&&e.push("no subtitles but needed");var i=(new Date).getTime();return i<new Date(1e3*t.license_start)&&e.push("license_start 還沒到"),i>new Date(1e3*t.license_end)&&e.push("license_end 已過期"),i<new Date(1e3*t.publish_time)&&e.push("publish_time 還沒到"),t.pub&&i<new Date(1e3*t.pub)&&e.push("publish_time 還沒到"),t.unpub&&i>new Date(1e3*t.unpub)&&e.push("publish_time 已經結束"),a=e.length>0?e.join(","):"success",a},getRowCount:function(t){return t.length},onOK:function(t){t.preventDefault(),this.handleSubmit()},onCancel:function(){console.log("cancel")},countDownChanged:function(t){this.dismissCountDown=t},showAlert:function(){this.dismissCountDown=5},onModalShow:function(t){this.detailObj=t,t&&t.mezzanines&&t.mezzanines.dash&&(this.dashURL=t.mezzanines.dash.uri),t&&t.mezzanines&&t.mezzanines.hls&&(this.hlsURL=t.mezzanines.hls.uri),t.license_start&&(this.licenseStart=new Date(1e3*t.license_start)),t.license_end&&(this.licenseEnd=new Date(1e3*t.license_end)),this.showModal=!0},onPublishOK:function(t){t.preventDefault(),this.handlePublishSubmit()},onModalPublishShow:function(t){this.detailObj=t,t.pub&&(this.licenseStart=new Date(1e3*t.pub)),t.unpub&&(this.licenseEnd=new Date(1e3*t.unpub)),this.showPublishModal=!0},onPublishNow:function(t){this.detailObj=t;var e=new Date;e.setDate(e.getDate()-1),this.licenseStart=e,t.unpub&&(this.licenseEnd=new Date(1e3*t.unpub)),this.handlePublishSubmit()},onUnPublishNow:function(t){this.detailObj=t,t.pub&&(this.licenseStart=new Date(1e3*t.pub));var e=new Date;e.setDate(e.getDate()-1),this.licenseEnd=e,this.handlePublishSubmit()},onCreateInvalidation:function(t){var e=this,a=window.confirm("確定更新此筆縮圖 "+t+" ?");if(a){var i="/v3/console/episode/".concat(t,"/invalidation");P.request("post",i).then(function(t){e.alertCss="success",e.alertMsg="派送更新縮圖工作成功，1~2分鐘後更新",e.showAlert(),e.onFetch()}).catch(function(t){e.alertCss="warning",e.alertMsg="派送更新縮圖工作失敗",e.showAlert()})}},handlePublishSubmit:function(){console.log("Submit Publish");var t=this,e=Date.parse(this.licenseStart)/1e3,a=Date.parse(this.licenseEnd)/1e3,i={};i["pub"]=e,i["unpub"]=a,i["episode_id"]=this.detailObj.id,console.log(i);var n="/v3/console/episode_publish";P.request("put",n,i).then(function(e){t.alertCss="success",t.alertMsg="儲存成功",t.showAlert(),t.onFetch(),t.onCancel(),t.$refs.formModal.hide()}).catch(function(e){t.alertCss="warning",t.alertMsg="這位大大，您的權限不足",t.onCancel(),t.$refs.formModal.hide(),t.showAlert()})},handleSubmit:function(){console.log("Submit");var t=this,e=Date.parse(this.licenseStart)/1e3,a=Date.parse(this.licenseEnd)/1e3,i={dash_uris_nosub:{playback:null},dash_uris_sub:{playback:null},hls_uris_nosub:{playback:null},hls_uris_sub:{playback:null}},n=JSON.parse(JSON.stringify(this.dummySize)),r=JSON.parse(JSON.stringify(this.dummyPlayback)),s=JSON.parse(JSON.stringify(this.dummySize)),o=JSON.parse(JSON.stringify(this.dummyPlayback));n.uri=this.dashURL,n.uri_playzone=this.dashURL,s.uri=this.hlsURL,s.uri_playzone=this.hlsURL,r["p0"].uri=this.dashURL,r["p1"].uri=this.dashURL,r["p2"].uri=this.dashURL,r["p3"].uri=this.dashURL,r["p4"].uri=this.dashURL,r["p5"].uri=this.dashURL,o["p0"].uri=this.hlsURL,o["p1"].uri=this.hlsURL,o["p2"].uri=this.hlsURL,o["p3"].uri=this.hlsURL,o["p4"].uri=this.hlsURL,o["p5"].uri=this.hlsURL,i["dash"]=n,i["dash_uris_nosub"]["playback"]=r,i["dash_uris_sub"]["playback"]=r,i["hls"]=s,i["hls_uris_nosub"]["playback"]=o,i["hls_uris_sub"]["playback"]=o,i["license_start"]=e,i["license_end"]=a,i["episode_id"]=this.detailObj.id,console.log(i);var l="/v3/console/episode";P.request("put",l,i).then(function(e){t.alertCss="success",t.alertMsg="儲存成功",t.showAlert(),t.onFetch(),t.onCancel(),t.$refs.formModal.hide()}).catch(function(e){t.alertCss="warning",t.alertMsg="這位大大，您的權限不足",t.onCancel(),t.$refs.formModal.hide(),t.showAlert()})},onFetch:function(){var t=this;this.$router.push({path:"/content/series",query:{q:this.q}}),""!==this.q&&P.request("get","/v3/console/series?q="+this.q).then(function(e){e.data&&e.data.data&&e.data.data.series?t.items=e.data.data.series:t.items=[]})}}},Pi=Ci,qi=(a("d0f3"),Object(_["a"])(Pi,ji,xi,!1,null,"0ad1f310",null)),Mi=qi.exports,Ai=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("b-row",[a("b-col",{attrs:{cols:"12",xl:"12"}},[a("transition",{attrs:{name:"slide"}},[a("b-card",{attrs:{header:t.caption}},[a("b-row",[a("b-col",{attrs:{sm:"12"}},[a("form",{on:{submit:function(e){return e.preventDefault(),t.onFetch(e)}}},[a("b-form-group",{attrs:{description:"輸入任意日期，抓取該日期起7天，上架 EpisodeID，預設今天"}},[a("b-input-group",[a("b-form-input",{attrs:{type:"text",id:"name"},model:{value:t.q,callback:function(e){t.q=e},expression:"q"}}),a("b-input-group-append",[a("b-button",{attrs:{variant:"primary"},on:{click:t.onFetch}},[a("i",{staticClass:"fa",class:{"fa-refresh":t.loading,"fa-spin":t.loading}}),t._v("\n                  Submit\n                  ")])],1)],1)],1)],1),a("b-alert",{attrs:{show:t.dismissCountDown,dismissible:"",variant:t.alertCss},on:{dismissed:function(e){t.dismissCountDown=0},"dismiss-count-down":t.countDownChanged}},[t._v("\n            "+t._s(t.alertMsg)+"\n          ")]),a("b-form-group",{attrs:{description:"一行代表一筆上架資料，可以一次多行，格式範例如上，時間打 now 就用現在時間，例: 00000338010001,2021-01-14T12:00 或是 0000033801001,now"}},[a("b-form-textarea",{attrs:{id:"textarea",placeholder:"00000338010001,2031-03-12T12:00",rows:"6"},model:{value:t.publish,callback:function(e){t.publish=e},expression:"publish"}})],1),a("b-button",{attrs:{variant:"primary"},on:{click:t.onPublish}},[a("i",{staticClass:"fa",class:{"fa-refresh":t.loading,"fa-spin":t.loading}}),t._v("\n              Publish\n            ")]),a("p")],1)],1),a("table",{staticClass:"table b-table table-striped table-bordered table-hover"},[a("tbody",{attrs:{role:"rowgroup"}},t._l(t.week,function(e){return a("tr",{attrs:{role:"row"}},[a("td",{attrs:{width:"95px;"}},[t._v("\n              "+t._s(("0"+e.day.getDate()).slice(-2))+" "),a("b-badge",{attrs:{variant:0==e.day.getDay()||6==e.day.getDay()?"danger":"secondary"}},[t._v("\n              "+t._s(e.label)+"\n              ")])],1),a("td",{attrs:{role:"cell","aria-colindex":"1"}},[t.weekmap[e.day]&&t.weekmap[e.day].length>0?a("span",t._l(t.weekmap[e.day],function(e){return a("p",[t._v("\n                "+t._s(e.meta.title_name)+" "+t._s(e.name)+" "+t._s(e.id)+" "+t._s(new Date(e.pub).toLocaleDateString())+" "+t._s(new Date(e.pub).toTimeString())+"\n                ")])}),0):t._e()])])}),0)])],1)],1)],1)],1)},Ti=[];function Ii(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),a.push.apply(a,i)}return a}function $i(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?Ii(a,!0).forEach(function(e){Object(B["a"])(t,e,a[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):Ii(a).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))})}return t}var Bi={name:"Publish",props:{caption:{type:String,default:"Publish"},hover:{type:Boolean,default:!0},striped:{type:Boolean,default:!0},bordered:{type:Boolean,default:!1},small:{type:Boolean,default:!1},fixed:{type:Boolean,default:!1}},data:function(){return{items:[],q:"",fields:[{key:"id",label:"SeriesID"},{key:"episode",label:"Episodes"}],introFields:["content_agent","content_provider","themes","genres","casts","tags","license_start","license_end"],publish:"",week:[],weekmap:{},alertCss:"",alertMsg:"",dismissCountDown:0,detailObj:{id:null},showModal:!1,showPublishModal:!1,licenseStart:new Date,licenseEnd:new Date,start:"",flatpickrConfig:{enableTime:!0,altFormat:"Y-m-d H:i",altInput:!0,dateFormat:"Z",allowInput:!0},currentPage:1,perPage:0,totalRows:0}},computed:$i({},Object(F["b"])(["loading"])),mounted:function(){if(this.$route.query.q&&(this.q=this.$route.query.q,this.onFetch()),""===this.q){var t=new Date;this.q=t.getFullYear()+"-"+("0"+(t.getMonth()+1)).slice(-2)+"-"+("0"+t.getDate()).slice(-2),this.onFetch()}},methods:{getBadge:function(){var t="success";return t},getRowCount:function(t){return t.length},onOK:function(t){t.preventDefault(),this.handleSubmit()},onCancel:function(){console.log("cancel")},countDownChanged:function(t){this.dismissCountDown=t},showAlert:function(){this.dismissCountDown=5},onPublish:function(){var t=this;console.log("Submit Publish");for(var e=this,a=[],i=this.publish.split(/\r\n|\r|\n/),n=0;n<i.length;n++){var r=i[n];if(!r.includes(","))return void alert(r+"沒有逗點");var s,o=r.trim().split(","),l=o[0].trim(),c=o[1].trim().toUpperCase();if(s="NOW"===c?((new Date).getTime()/1e3).toFixed(0):(new Date(c).getTime()/1e3).toFixed(0),s<113616e4||isNaN(s))return console.log(s),void alert("時間錯誤"+c);a.push(l+","+s)}var u="/v3/console/publish";P.request("put",u,a).then(function(t){e.alertCss="success",e.alertMsg="儲存成功",e.showAlert(),e.onFetch(),e.onCancel()}).catch(function(a){e.alertCss="warning",a.response&&a.response.data&&a.response.data.status&&a.response.data.status.message?t.alertMsg="更新失敗: "+a.response.data.status.message:t.alertMsg="您的權限不足",e.onCancel(),e.showAlert()})},onProcess:function(){var t=14;this.week=[],this.weekmap={};for(var e=new Date(this.q),a=0;a<t;a++){var i=e.getTime()+864e5*a,n=new Date(i),r=n.toLocaleString(navigator.language,{weekday:"short"});this.week.push({day:n,label:r}),this.weekmap[n]=[]}for(var s=0;s<this.items.length;s++){var o=new Date(this.items[s].pub),l=o.getFullYear()+"-"+("0"+(o.getMonth()+1)).slice(-2)+"-"+("0"+o.getDate()).slice(-2),c=new Date(l),u={};u=JSON.parse(this.items[s].meta_str),this.items[s].meta=u,this.weekmap[c]&&this.weekmap[c].push(this.items[s])}},onFetch:function(){var t,e=this;""!==this.q&&(t=new Date(this.q).getTime()/1e3,P.request("get","/v3/console/publish?q="+t).then(function(t){t.data&&t.data.data&&t.data.data.episodes?(e.items=t.data.data.episodes,e.onProcess()):e.items=[]}))}}},Fi=Bi,Ei=(a("86db"),Object(_["a"])(Fi,Ai,Ti,!1,null,"93537ed8",null)),Li=Ei.exports,Ui=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("b-row",[a("b-col",{attrs:{cols:"12",xl:"12"}},[a("transition",{attrs:{name:"slide"}},[a("b-card",{attrs:{header:t.caption}},[a("b-row",[a("b-col",{attrs:{sm:"12"}},[a("form",{on:{submit:function(e){return e.preventDefault(),t.onFetch(e)}}},[a("b-form-group",{attrs:{description:"輸入 TitleID 一次只能搜尋單個"}},[a("b-input-group",[a("b-form-input",{attrs:{type:"text",id:"name"},model:{value:t.q,callback:function(e){t.q=e},expression:"q"}}),a("b-input-group-append",[a("b-button",{attrs:{variant:"primary"},on:{click:t.onFetch}},[a("i",{staticClass:"fa",class:{"fa-refresh":t.loading,"fa-spin":t.loading}}),t._v("\n                  Submit\n                  ")])],1)],1)],1)],1),a("b-alert",{attrs:{show:t.dismissCountDown,dismissible:"",variant:"warning"},on:{dismissed:function(e){t.dismissCountDown=0},"dismiss-count-down":t.countDownChanged}},[t._v("\n                 "+t._s(t.alertMsg)+"\n          ")])],1)],1),t.items?a("b-table",{attrs:{hover:t.hover,striped:t.striped,bordered:t.bordered,small:t.small,fixed:t.fixed,responsive:"sm",items:t.items,fields:t.fields,"current-page":t.currentPage,"per-page":t.perPage},scopedSlots:t._u([{key:"action",fn:function(t){}},{key:"id",fn:function(e){return[a("strong",[t._v("\n            "+t._s(e.item.id)+"\n          "),a("br"),a("b-form-input",{attrs:{type:"text"},on:{blur:function(a){return t.onBlurUpdate(e.item.title_id,e.item.id,e.item.name)}},model:{value:e.item.name,callback:function(a){t.$set(e.item,"name",a)},expression:"data.item.name"}}),a("b-button",{directives:[{name:"b-tooltip",rawName:"v-b-tooltip.hover",modifiers:{hover:!0}}],staticClass:"fa",attrs:{variant:"warning",title:"直接儲存這一筆資料的標題，您必須有 content 的權限"},on:{click:function(a){return t.onUpdate(e.item.title_id,e.item.id,e.item.name,a)}}},[a("i",{staticClass:"fa"}),t._v(" 更新標題 ")]),a("div",{staticStyle:{width:"10px",height:"auto",display:"inline-block"}}),a("b-button",{directives:[{name:"b-tooltip",rawName:"v-b-tooltip.hover",modifiers:{hover:!0}}],staticClass:"fa",attrs:{variant:"danger",title:"直接刪除這一筆資料，您必須有 content 的權限"},on:{click:function(a){return t.onDelete(e.item.title_id,e.item.id,a)}}},[a("i",{staticClass:"fa"}),t._v(" 刪除 ")]),a("p")],1)]}},{key:"episode",fn:function(e){return[e.item.meta?a("span",[e.item.meta.still?a("img",{attrs:{src:e.item.meta.still,width:"100"}}):t._e(),t._v("\n            Duration: "+t._s(parseInt(e.item.meta.duration))+" 秒, Encoder: "),a("b-badge",{attrs:{variant:t.getBadge(e.item.meta)}},[t._v(t._s(t.getBadge(e.item.meta)))])],1):t._e()]}}],null,!1,1708174511)}):t._e()],1)],1)],1)],1)},Ri=[];function Ni(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),a.push.apply(a,i)}return a}function zi(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?Ni(a,!0).forEach(function(e){Object(B["a"])(t,e,a[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):Ni(a).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))})}return t}var Hi={name:"Extra",props:{caption:{type:String,default:"Extra"},hover:{type:Boolean,default:!0},striped:{type:Boolean,default:!0},bordered:{type:Boolean,default:!1},small:{type:Boolean,default:!1},fixed:{type:Boolean,default:!1}},data:function(){return{items:[],dismissCountDown:0,alertMsg:"",q:"",fields:[{key:"id",label:"ID"},{key:"episode",label:"Episode"}],introFields:["content_agent","content_provider","themes","genres","casts","tags","license_start","license_end","publish_time"],currentPage:1,perPage:0,totalRows:0}},computed:zi({},Object(F["b"])(["loading"])),mounted:function(){this.$route.query.q&&(this.q=this.$route.query.q,this.onFetch())},methods:{countDownChanged:function(t){this.dismissCountDown=t},showAlert:function(){this.dismissCountDown=5},getBadge:function(t){var e="";return e=t.duration&&t.still&&t.dash&&t.hls?"success":"not ready",e},getRowCount:function(t){return t.length},onUpdate:function(t,e,a,i){var n=this,r=i.target,s=i.target.firstChild;s.classList.add("fa-refresh"),s.classList.add("fa-spin"),r.classList.remove("btn-warning");var o="/v3/console/extra/".concat(t,"/").concat(e,"?name=").concat(a);P.request("put",o).then(function(t){r.classList.add("btn-success"),s.classList.remove("fa-refresh","fa-spin"),s.classList.add("fa-check"),n.onFetch()}).catch(function(t){r.classList.add("btn-danger"),s.classList.remove("fa-refresh","fa-spin"),s.classList.add("fa-warning"),n.alertMsg="您的權限不足",n.showAlert()}).finally(function(){setTimeout(function(){r.classList.remove("btn-success","btn-danger"),r.classList.add("btn-warning"),s.classList.remove("fa-warning","fa-check")},2e3)})},onBlurUpdate:function(t,e,a){var i=this,n="/v3/console/extra/".concat(t,"/").concat(e,"?name=").concat(a);P.request("put",n).then(function(t){i.onFetch()}).catch(function(t){alert("您的權限不足")})},onDelete:function(t,e,a){var i=this,n=window.confirm("確定刪除此筆 "+e+" ?");if(n){var r="/v3/console/extra/".concat(t,"/").concat(e);P.request("delete",r).then(function(t){alert("刪除成功"),i.onFetch()}).catch(function(t){alert("刪除失敗")})}},onFetch:function(){var t=this;this.$router.push({path:"/content/extra",query:{q:this.q}}),""!==this.q&&P.request("get","/v3/console/extra?q="+this.q).then(function(e){e.data&&e.data.data&&e.data.data.extra?t.items=e.data.data.extra:t.items=[]})}}},Ki=Hi,Ji=(a("2f63"),Object(_["a"])(Ki,Ui,Ri,!1,null,"154e0a11",null)),Vi=Ji.exports,Wi=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("b-row",[a("b-col",{attrs:{cols:"12",xl:"12"}},[a("transition",{attrs:{name:"slide"}},[a("b-card",{attrs:{header:t.caption}},[a("form",{on:{submit:function(e){return e.preventDefault(),t.onFetch(e)}}},[a("b-row",[a("b-col",{attrs:{sm:"10"}}),a("b-col",{attrs:{sm:"2"}},[a("b-input-group-append",[a("b-button",{attrs:{variant:"primary"},on:{click:function(e){return t.onModalShow({})}}},[t._v("新增")]),t._v(" \n                "),a("b-button",{attrs:{variant:"success"},on:{click:t.onSave}},[a("i",{staticClass:"fa",class:{"fa-refresh":t.loading,"fa-spin":t.loading}}),t._v("\n                  儲存目前排序\n                  ")])],1)],1)],1),a("b-row",[a("b-col",{attrs:{sm:"12"}},[a("b-form-group",{attrs:{description:""}},[a("b-form-radio-group",{attrs:{name:"radio-sub-component"},model:{value:t.filter,callback:function(e){t.filter=e},expression:"filter"}},[a("b-form-radio",{attrs:{value:""}},[t._v("全部顯示")]),a("b-form-radio",{attrs:{value:"web"}},[t._v("Web 及 Mobile 裝置")]),a("b-form-radio",{attrs:{value:"tv"}},[t._v("TV 裝置")])],1)],1),a("small",{domProps:{innerHTML:t._s("要用拖拉的方式調整排序時，介面選項，必須是 <span style='font-weight: bold;color: #e67300;'>全部顯示</span> 調整好後，點 Save 後，才正式儲存並生效")}}),a("b-alert",{attrs:{show:t.dismissCountDown,dismissible:"",variant:t.alertCss},on:{dismissed:function(e){t.dismissCountDown=0},"dismiss-count-down":t.countDownChanged}},[t._v("\n                    "+t._s(t.alertMsg)+"\n              ")])],1)],1)],1),a("table",{staticClass:"table table-striped"},[a("tbody",[a("draggable",{staticStyle:{margin:"0 auto"},attrs:{width:"100%"},model:{value:t.items,callback:function(e){t.items=e},expression:"items"}},t._l(t.filterItems,function(e,i){return a("tr",{key:i},[a("th",{attrs:{scope:"row"}},[t._v("\n              "+t._s(("00"+(i+1)).slice(-2))+"\n                "),a("button",{staticClass:"btn btn-secondary",attrs:{type:"button"},on:{click:function(a){return t.onModalShow(e)}}},[a("i",{staticClass:"fa fa-edit"})]),t._v(" \n                "),a("button",{staticClass:"btn btn-secondary",attrs:{type:"button"},on:{click:function(a){return t.onDelete(e)}}},[a("i",{staticClass:"fa fa-trash"})])]),a("td",{attrs:{scope:"row"}},[a("b-img",{attrs:{src:e.image_preview,alt:"",width:160}}),e.logo_preview?a("span",[a("b-img",{attrs:{src:e.logo_preview,alt:"",width:100}})],1):t._e()],1),a("td",{attrs:{role:"cell"}},[t._v("\n                title: "+t._s(e.title)),a("br"),t._v("\n                subtitle: "+t._s(e.subtitle)),a("br")]),a("td",{attrs:{role:"cell"}},[t._v("\n                type: "+t._s(e.collection_type)),a("br"),t._v("\n                name: "+t._s(e.collection_name)+"\n              ")]),a("td",{attrs:{role:"cell"}},[t._v("\n                平台: "+t._s(e.platform)+"\n              ")])])}),0)],1)])])],1)],1),a("b-modal",{ref:"formModal",attrs:{title:t.detailTitle,"no-close-on-backdrop":"","no-close-on-esc":""},on:{ok:t.onOK,cancel:t.onCancel},model:{value:t.showModal,callback:function(e){t.showModal=e},expression:"showModal"}},[a("form",{ref:"form",on:{submit:function(e){return e.stopPropagation(),e.preventDefault(),t.handleSubmit(e)}}},[a("b-form-group",{attrs:{label:"Title",description:"App 顯示的名稱，例如家庭鄉土"}},[a("b-form-input",{attrs:{type:"text",state:t.formState.title,required:""},model:{value:t.detailObj.title,callback:function(e){t.$set(t.detailObj,"title",e)},expression:"detailObj.title"}})],1),a("b-form-group",{attrs:{label:"Sub Title",description:"App 顯示的描述，例如《黑色十人之女》九位小三團結力量大"}},[a("b-form-input",{attrs:{type:"text"},model:{value:t.detailObj.subtitle,callback:function(e){t.$set(t.detailObj,"subtitle",e)},expression:"detailObj.subtitle"}})],1),a("b-form-group",{attrs:{label:"Collection Type",description:"Series 裡的欄位名稱，例如 country 或 genre"}},[a("b-form-select",{attrs:{options:t.collectionOptions,state:t.formState.collection_type,required:"","invalid-feedback":"Can't be blank"},model:{value:t.detailObj.collection_type,callback:function(e){t.$set(t.detailObj,"collection_type",e)},expression:"detailObj.collection_type"}})],1),a("b-form-group",{attrs:{label:"Collection Name",description:"Series 裡的欄位數值，例如 Taiwan 或 Japan"}},[a("b-form-input",{attrs:{type:"text",state:t.formState.collection_name,required:""},model:{value:t.detailObj.collection_name,callback:function(e){t.$set(t.detailObj,"collection_name",e)},expression:"detailObj.collection_name"}})],1),a("b-form-group",{attrs:{label:"Platform",description:"會出現在那一種裝置平台"}},[a("b-form-checkbox-group",{model:{value:t.detailObj.platform,callback:function(e){t.$set(t.detailObj,"platform",e)},expression:"detailObj.platform"}},[a("b-form-checkbox",{attrs:{value:"web"}},[t._v("Web 及 Mobile")]),a("b-form-checkbox",{attrs:{value:"tv"}},[t._v("TV 裝置")])],1)],1),a("b-form-group",{attrs:{label:"Image",description:"顯示的圖片，目前限制上傳 image/jpeg 格式的圖檔",state:t.imageState,"invalid-feedback":"Can't be blank"}},[a("b-form-file",{ref:"image-input",attrs:{accept:"image/jpeg",state:t.imageState,required:""},model:{value:t.image,callback:function(e){t.image=e},expression:"image"}})],1),a("b-form-group",{attrs:{label:"預覽圖片",description:t.detailObj.source_image}},[t.detailObj.image_preview?a("b-img",{attrs:{src:t.detailObj.image_preview,thumbnail:"",width:200}}):t._e()],1),a("b-form-group",{attrs:{label:"Logo",description:"目前限制上傳 image/png 格式的圖檔，檔案大小，和上面的 image 合併計算不能超過 1Mb"}},[a("b-form-file",{ref:"image-input",attrs:{accept:"image/png"},model:{value:t.logo,callback:function(e){t.logo=e},expression:"logo"}}),t.detailObj.logo_preview?a("b-img",{attrs:{src:t.detailObj.logo_preview,thumbnail:"",width:100}}):t._e()],1)],1)])],1)},Gi=[],Yi=a("310e"),Xi=a.n(Yi);function Zi(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),a.push.apply(a,i)}return a}function Qi(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?Zi(a,!0).forEach(function(e){Object(B["a"])(t,e,a[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):Zi(a).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))})}return t}var tn={name:"Browse",components:{draggable:Xi.a},props:{caption:{type:String,default:"Browse"},hover:{type:Boolean,default:!0},striped:{type:Boolean,default:!0},bordered:{type:Boolean,default:!1},small:{type:Boolean,default:!1},fixed:{type:Boolean,default:!1}},data:function(){return{q:"",filter:"",items:[],showAll:!1,showModal:!1,detailTitle:"",detailObj:{id:null},image:null,logo:null,titleOptions:[],uiOptions:["country","genre"],collectionOptions:["country","genre","content_agent","title_type","tag"],trailerOptions:[],sinceDate:new Date,untilDate:new Date,displayDate:null,flatpickrConfig:{enableTime:!0,altFormat:"Y-m-d H:i",altInput:!0,dateFormat:"Z",allowInput:!0},formState:{title:null,collection_type:null,collection_name:null,ui_type:null},imageState:null,alertCss:"",alertMsg:"",dismissCountDown:0,fields:[{key:"action",label:""},{key:"order",label:"排序"},{key:"image_preview",label:"Image Preview"},{key:"logo_preview",label:"Logo Preview"},{key:"title",label:"Browse 標題"},{key:"subtitle",label:"副標題"},{key:"collection_type"},{key:"collection_name"},{key:"image"},{key:"ui_type",label:"UI Type"}],currentPage:1,perPage:0,totalRows:0}},computed:Qi({filterItems:function(){var t=this;return this.filter?this.items.filter(function(e){return e.platform||(e.platform=[]),e.platform.includes(t.filter)}):this.items}},Object(F["b"])(["loading"])),mounted:function(){this.$route.query.q&&(this.q=this.$route.query.q),this.onFetch()},methods:{getBadge:function(t){var e;switch(t){case!0:e="success";break;case!1:e="warning";break}return e},getLocalTime:function(t){return new Date(t).toLocaleString("default",{hour12:!1,year:"numeric",month:"short",day:"2-digit",hour:"numeric",minute:"numeric"})},resetFormStates:function(){this.imageState=null,this.formState={title:null,collection_type:null,collection_name:null}},resetDetailObj:function(){this.detailObj={id:null}},resetImage:function(){this.$refs["image-input"].reset(),this.image=null,this.logo=null},resetSelectedExtra:function(){this.$refs["extra-input"].clearSelection(),this.trailerOptions=[]},countDownChanged:function(t){this.dismissCountDown=t},showAlert:function(){this.dismissCountDown=5},onFetch:function(){var t=this;P.request("get","/v3/console/browse").then(function(e){e.data&&e.data.data&&(t.items=e.data.data)})},onModalShow:function(t){this.detailObj=Object.assign({},t),t.id?this.detailTitle="修改 "+t.title:this.detailTitle="新增 Browse 選單",this.showModal=!0},onSave:function(){var t=this,e="/v3/console/browse";P.request("put",e,this.items).then(function(e){t.alertCss="success",t.alertMsg="儲存成功",t.showAlert(),t.onFetch()}).catch(function(e){console.log(e),e.response&&e.response.data&&e.response.data.status&&e.response.data.status.message?(t.alertCss="warning",t.alertMsg=e.response.data.status.message):(t.alertCss="warning",t.alertMsg="Unknow Error"),t.showAlert()})},isValid:function(){var t=!0;for(var e in this.resetFormStates(),console.log("start validate"),this.formState){var a=parseInt(this.detailObj[e],10);a?a<=0?(this.formState[e]="invalid",t=!1):this.detailObj[e]=a:this.detailObj[e]||(t=!1,this.formState[e]="invalid")}return this.detailObj.image_source||this.image||(this.imageState="invalid",t=!1),t},handleSubmit:function(){var t=this,e=this;if(this.isValid()){var a="/v3/console/browse";console.log(this.detailObj),P.request("post",a,this.detailObj).then(function(a){if(console.log(a),t.detailObj.id||(t.detailObj.id=a.data.data.id),t.image||t.logo){var i="/v3/console/browseimage?id="+t.detailObj.id,n=new FormData;t.image&&n.append("file",t.image),t.logo&&n.append("logo",t.logo),P.request("post",i,n,{headers:{"Content-Type":"multipart/form-data"}}).then(function(){e.alertCss="success",e.alertMsg="儲存成功",e.showAlert(),e.onFetch()}).catch(function(){e.alertCss="warning",e.alertMsg="圖片儲存失敗",e.showAlert(),e.onFetch()})}else e.alertCss="success",e.alertMsg="儲存成功",e.showAlert(),e.onFetch();e.onCancel(),e.$refs.formModal.hide()}).catch(function(t){t.response&&t.response.data&&t.response.data.status&&t.response.data.status.message?(e.alertCss="warning",e.alertMsg=t.response.data.status.message):(e.alertCss="warning",e.alertMsg="Unknow Error"),e.$refs.formModal.hide(),e.showAlert()})}},onOK:function(t){t.preventDefault(),this.handleSubmit()},onCancel:function(){this.resetFormStates(),this.resetDetailObj(),this.resetImage()},onDelete:function(t){console.log(t.id);var e=window.confirm("Are you sure you want to delete this item?");if(e){var a=this,i="/v3/console/browse?id="+t.id;console.log(i),P.request("delete",i).then(function(){a.alertCss="success",a.alertMsg="刪除成功",a.showAlert(),a.onFetch()}).catch(function(){a.alertCss="warning",a.alertMsg="刪除失敗",a.showAlert(),a.onFetch()})}}}},en=tn,an=(a("92bb"),Object(_["a"])(en,Wi,Gi,!1,null,"b0c1e828",null)),nn=an.exports,rn=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("b-row",[a("b-col",{attrs:{cols:"12",xl:"12"}},[a("transition",{attrs:{name:"slide"}},[a("b-card",{attrs:{"no-header":""}},[a("template",{slot:"header"},[t._v("\n        Title List\n      ")]),a("b-row",[a("b-col",{attrs:{sm:"12"}},[a("form",{on:{submit:function(e){return e.preventDefault(),t.onFetch(e)}}},[a("b-form-group",[a("b-input-group",[a("b-alert",{attrs:{show:t.dismissCountDown,dismissible:"",variant:"warning"},on:{dismissed:function(e){t.dismissCountDown=0},"dismiss-count-down":t.countDownChanged}},[t._v("\n     這位大大，您的權限不足\n    ")])],1)],1)],1)])],1),t.items?a("b-table",{attrs:{hover:t.hover,striped:t.striped,bordered:t.bordered,small:t.small,fixed:t.fixed,responsive:"sm",items:Object.keys(t.titleMap),fields:t.fields,"current-page":t.currentPage,"per-page":t.perPage},scopedSlots:t._u([{key:"title_key",fn:function(e){return[a("strong",[t._v(t._s(t.titleMap[e.item]))]),t._v("\n           "),a("b-button",{on:{click:function(a){return t.onLink(e.item)}}},[a("i",{staticClass:"fa fa-edit"}),t._v(" Edit")])]}}],null,!1,4062588017)}):t._e()],2)],1)],1)],1)},sn=[];function on(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),a.push.apply(a,i)}return a}function ln(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?on(a,!0).forEach(function(e){Object(B["a"])(t,e,a[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):on(a).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))})}return t}var cn={name:"TitleList",props:{caption:{type:String,default:"Title List"},hover:{type:Boolean,default:!0},striped:{type:Boolean,default:!0},bordered:{type:Boolean,default:!1},small:{type:Boolean,default:!1},fixed:{type:Boolean,default:!1}},data:function(){return{items:[],titleMap:{},dismissCountDown:0,q:"",fields:[{key:"title_key",label:"片單"}],currentPage:1,perPage:0,totalRows:0}},computed:ln({},Object(F["b"])(["loading"])),mounted:function(){this.onFetch()},methods:{goBack:function(){this.$router.go(-1)},getBadge:function(t){return"Active"===t?"success":"Inactive"===t?"secondary":"Pending"===t?"warning":"Banned"===t?"danger":"primary"},getRowCount:function(t){return t.length},countDownChanged:function(t){this.dismissCountDown=t},showAlert:function(){this.dismissCountDown=5},onLink:function(t){var e="/content/titlelist/"+t;console.log(e),this.$router.push({path:e})},onFetch:function(){var t=this;P.request("get","/v3/console/titlelist").then(function(e){if(e.data&&e.data.data)for(var a in e.data.data)t.$set(t.titleMap,a,e.data.data[a])})}}},un=cn,dn=(a("2a8f"),Object(_["a"])(un,rn,sn,!1,null,"6d4a509c",null)),pn=dn.exports,mn=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("b-row",[a("b-col",{attrs:{cols:"12",xl:"12"}},[a("transition",{attrs:{name:"slide"}},[a("b-card",{attrs:{"no-header":""}},[a("template",{slot:"header"},[a("b-button",{on:{click:t.goBack}},[t._v("<")]),t._v(" \n        Title List Detail "+t._s(t.titlename)+"\n      ")],1),a("b-row",[a("b-col",{attrs:{sm:"12"}},[a("form",{on:{submit:function(e){return e.preventDefault(),t.onAdd(e)}}},[a("b-form-group",{attrs:{description:""}},[a("b-input-group",[a("b-form-input",{attrs:{type:"text",id:"name"},model:{value:t.q,callback:function(e){t.q=e},expression:"q"}}),a("b-button",{attrs:{variant:"primary"},on:{click:t.onAdd}},[a("i",{staticClass:"fa",class:{"fa-refresh":t.loading,"fa-spin":t.loading}}),t._v("\n                  Add\n                  ")]),t._v("  \n                "),a("b-button",{attrs:{variant:"success"},on:{click:t.onSave}},[a("i",{staticClass:"fa",class:{"fa-refresh":t.loading,"fa-spin":t.loading}}),t._v("\n                  Save\n                  ")])],1)],1)],1),a("small",{domProps:{innerHTML:t._s("輸入完想加入片單的 title id 後點 Add 即會加入片單，<b>多個 titleid 可以用空白隔開</b>，決定好片單後，點 Save 後，片單才正式儲存並生效")}}),a("b-alert",{attrs:{show:t.dismissCountDown,dismissible:"",variant:"warning"},on:{dismissed:function(e){t.dismissCountDown=0},"dismiss-count-down":t.countDownChanged}},[t._v("\n           這位大大，您的權限不足\n          ")])],1)],1),a("table",{staticClass:"table b-table table-striped",attrs:{"aria-colcount":"1"}},[a("thead",{},[a("tr",[a("th",{attrs:{"aria-colindex":"1"}},[t._v("片單 (請用拖拉方式調正順序)")])])]),a("tbody",{},[a("draggable",{model:{value:t.items,callback:function(e){t.items=e},expression:"items"}},t._l(t.items,function(e,i){return a("tr",{key:i},[a("td",[t._v("\n                "+t._s(("00"+(i+1)).slice(-2))+" "),a("b-button",{on:{click:function(a){return t.onDelete(e)}}},[a("i",{staticClass:"fa fa-trash"}),t._v(" Delete")]),t._v("\n                 \n                "),a("b-button",{attrs:{variant:"info"},on:{click:function(a){return t.onTitle(e)}}},[t._v(t._s(e))]),t._v(" \n                "),t.titleMap[e]?a("span",[t.titleMap[e].cover?a("img",{attrs:{src:t.titleMap[e].cover,width:"35"}}):t._e(),t._v("\n                  "+t._s(t.titleMap[e].name)+"\n                ")]):t._e()],1)])}),0)],1)])],2)],1)],1)],1)},bn=[];function fn(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),a.push.apply(a,i)}return a}function hn(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?fn(a,!0).forEach(function(e){Object(B["a"])(t,e,a[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):fn(a).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))})}return t}var gn={name:"TitleListDetail",components:{draggable:Xi.a},props:{caption:{type:String,default:"Title List Detail"},hover:{type:Boolean,default:!0},striped:{type:Boolean,default:!0},bordered:{type:Boolean,default:!1},small:{type:Boolean,default:!1},fixed:{type:Boolean,default:!1}},data:function(){return{items:[],titleMap:{},title:"",titlename:"",titlekey:"",dismissCountDown:0,q:"",fields:[{key:"titlekey",label:"片單"}],currentPage:1,perPage:0,totalRows:0}},computed:hn({},Object(F["b"])(["loading"])),mounted:function(){this.onFetch()},methods:{goBack:function(){this.$router.go(-1)},getBadge:function(t){return"Active"===t?"success":"Inactive"===t?"secondary":"Pending"===t?"warning":"Banned"===t?"danger":"primary"},getRowCount:function(t){return t.length},countDownChanged:function(t){this.dismissCountDown=t},showAlert:function(){this.dismissCountDown=5},onTitle:function(t){this.$router.push({path:"/content/title",query:{q:t}})},onSave:function(){var t=this,e="/v3/console/titlelist/"+this.titlekey;P.request("put",e,{titles:this.items}).then(function(t){}).catch(function(e){t.showAlert()})},onTitleMap:function(){var t=this,e="";e=this.items.join(" "),P.request("get","/v3/console/titlehint?q="+e).then(function(e){if(e.data&&e.data.data&&e.data.data.titles)for(var a=e.data.data.titles,i=0;i<a.length;i++){var n=a[i];t.$set(t.titleMap,n.id,n)}})},onDelete:function(t){this.items=this.items.filter(function(e){return e!==t})},onAdd:function(){if(""!==this.q){for(var t=this.q.split(" "),e=0;e<t.length;e++)t[e].trim()&&this.items.push(t[e].trim());this.q="",this.onTitleMap()}},onFetch:function(){var t=this,e=this.$route.params.titlekey;P.request("get","/v3/console/titlelist/"+e).then(function(e){e.data&&e.data.data&&(t.$set(t,"titleMap",e),e.data.data.titles&&(t.items=e.data.data.titles),e.data.data.name&&(t.titlename=e.data.data.name),e.data.data.title&&(t.titlekey=e.data.data.title),t.onTitleMap())})}}},vn=gn,_n=(a("c3ab"),Object(_["a"])(vn,mn,bn,!1,null,"154b3c00",null)),yn=_n.exports,wn=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("b-row",[a("b-col",{attrs:{cols:"12",xl:"12"}},[a("transition",{attrs:{name:"slide"}},[a("b-card",{attrs:{"no-header":""}},[a("template",{slot:"header"},[t._v("\n        "+t._s(t.caption)+"\n      ")]),a("b-row",[a("b-col",{attrs:{sm:"12"}},[a("form",{on:{submit:function(e){return e.preventDefault(),t.onAdd(e)}}},[a("b-form-group",{attrs:{description:""}},[a("b-input-group",[a("b-form-input",{attrs:{type:"text",id:"name"},model:{value:t.q,callback:function(e){t.q=e},expression:"q"}}),a("b-button",{attrs:{variant:"primary"},on:{click:t.onAdd}},[a("i",{staticClass:"fa",class:{"fa-refresh":t.loading,"fa-spin":t.loading}}),t._v("\n                  Add\n                  ")]),t._v("  \n                "),a("b-button",{attrs:{variant:"success"},on:{click:t.onSave}},[a("i",{staticClass:"fa",class:{"fa-refresh":t.loading,"fa-spin":t.loading}}),t._v("\n                  Save\n                  ")])],1)],1)],1),a("small",{domProps:{innerHTML:t._s("輸入想加入的熱門關鍵字後 (<span style='font-weight: bold;color: #e67300;'>一次輸入多個，請用半形英數分號隔開</span>)，點 Add 即會加入，編輯好後，點 Save 後，才正式儲存並生效")}}),a("b-alert",{attrs:{show:t.dismissCountDown,dismissible:"",variant:t.alertCss},on:{dismissed:function(e){t.dismissCountDown=0},"dismiss-count-down":t.countDownChanged}},[t._v("\n                 "+t._s(t.alertMsg)+"\n          ")])],1)],1),a("table",{staticClass:"table b-table table-striped",attrs:{"aria-colcount":"1"}},[a("thead",{},[a("tr",[a("th",{attrs:{"aria-colindex":"1"}},[t._v("熱門關鍵字 (請用拖拉方式調正順序)")])])]),a("tbody",{},[a("draggable",{model:{value:t.items,callback:function(e){t.items=e},expression:"items"}},t._l(t.items,function(e,i){return a("tr",{key:i},[a("td",[t._v("\n                "+t._s(("00"+(i+1)).slice(-2))+" "),a("b-button",{on:{click:function(a){return t.onDelete(e)}}},[a("i",{staticClass:"fa fa-trash"})]),t._v("\n                  "+t._s(e)+"\n              ")],1)])}),0)],1)])],2)],1)],1)],1)},On=[];function kn(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),a.push.apply(a,i)}return a}function jn(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?kn(a,!0).forEach(function(e){Object(B["a"])(t,e,a[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):kn(a).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))})}return t}var xn={name:"HotKeyWord",components:{draggable:Xi.a},props:{caption:{type:String,default:"HotKeyWord"},hover:{type:Boolean,default:!0},striped:{type:Boolean,default:!0},bordered:{type:Boolean,default:!1},small:{type:Boolean,default:!1},fixed:{type:Boolean,default:!1}},data:function(){return{items:[],titleMap:{},title:"",titlename:"",titlekey:"",dismissCountDown:0,alertCss:"",alertMsg:"",q:"",fields:[{key:"titlekey",label:"熱門關鍵字"}],currentPage:1,perPage:0,totalRows:0}},computed:jn({},Object(F["b"])(["loading"])),mounted:function(){this.onFetch()},methods:{goBack:function(){this.$router.go(-1)},countDownChanged:function(t){this.dismissCountDown=t},showAlert:function(){this.dismissCountDown=5},onSave:function(){var t=this,e="/v3/console/hotkeyword";P.request("put",e,{keywords:this.items}).then(function(e){e.data.data.keywords&&(t.items=e.data.data.keywords,t.alertCss="success",t.alertMsg="儲存成功",t.showAlert())}).catch(function(e){t.alertCss="warning",t.alertMsg="這位大大，您的權限不足",t.showAlert()})},onDelete:function(t){this.items=this.items.filter(function(e){return e!==t})},onAdd:function(){if(""!==this.q){for(var t=this.q.split(";"),e=0;e<t.length;e++)t[e].trim()&&this.items.push(t[e].trim());this.q=""}},onFetch:function(){var t=this;P.request("get","/v3/hot_keywords").then(function(e){e.data&&e.data.data&&e.data.data.keywords&&(t.items=e.data.data.keywords)})}}},Sn=xn,Dn=(a("6508"),Object(_["a"])(Sn,wn,On,!1,null,"7492f24f",null)),Cn=Dn.exports,Pn=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("b-row",[a("b-col",{attrs:{cols:"12",xl:"12"}},[a("transition",{attrs:{name:"slide"}},[a("b-card",{attrs:{"no-header":""}},[a("template",{slot:"header"},[t._v("\n        "+t._s(t.caption)+"\n      ")]),a("b-row",[a("b-col",{attrs:{sm:"12"}},[a("form",{on:{submit:function(e){return e.preventDefault(),t.onAdd(e)}}},[a("div",{staticClass:"form-row"},[a("div",{staticClass:"col"},[a("b-form-group",{attrs:{label:"訊息內容",description:""}},[a("b-form-input",{attrs:{type:"text",required:"",placeholder:""},model:{value:t.newMessage,callback:function(e){t.newMessage=e},expression:"newMessage"}})],1)],1),a("div",{staticClass:"col"},[a("b-form-group",{attrs:{label:"URL"}},[a("b-input-group",[a("b-form-input",{attrs:{type:"text"},model:{value:t.newUrl,callback:function(e){t.newUrl=e},expression:"newUrl"}}),a("b-button",{attrs:{variant:"primary"},on:{click:t.onAdd}},[a("i",{staticClass:"fa",class:{"fa-refresh":t.loading,"fa-spin":t.loading}}),t._v("\n                        Add\n                        ")]),t._v("  \n                      "),a("b-button",{attrs:{variant:"success"},on:{click:t.onSave}},[a("i",{staticClass:"fa",class:{"fa-refresh":t.loading,"fa-spin":t.loading}}),t._v("\n                        Save All\n                        ")])],1)],1)],1)])]),a("small",{domProps:{innerHTML:t._s("輸入完，點 Add 即會加入，預設時間現在，編輯好後，點 Save，才正式儲存並生效，<span style='font-weight: bold;color: #e67300;'>過期已發佈的資訊，請刪除，大家減碳救地球</span>")}}),a("b-alert",{attrs:{show:t.dismissCountDown,dismissible:"",variant:t.alertCss},on:{dismissed:function(e){t.dismissCountDown=0},"dismiss-count-down":t.countDownChanged}},[t._v("\n                 "+t._s(t.alertMsg)+"\n          ")])],1)],1),t.items?a("b-table",{attrs:{hover:t.hover,striped:t.striped,bordered:t.bordered,small:t.small,fixed:t.fixed,responsive:"sm",items:t.items,fields:t.fields,"current-page":t.currentPage,"per-page":t.perPage},scopedSlots:t._u([{key:"action",fn:function(e){return[t._v("\n            "+t._s(("00"+(e.index+1)).slice(-2))+"\n            "),a("b-button",{on:{click:function(a){return t.onDelete(e.index)}}},[a("i",{staticClass:"fa fa-trash"})])]}},{key:"message",fn:function(e){return[e.item.end_time&&1e3*e.item.end_time<(new Date).getTime()?a("strike",[a("b-form-input",{attrs:{type:"text"},model:{value:e.item.message,callback:function(a){t.$set(e.item,"message",a)},expression:"data.item.message"}})],1):t._e(),e.item.end_time&&1e3*e.item.end_time>=(new Date).getTime()?a("span",[a("b-form-input",{attrs:{type:"text"},model:{value:e.item.message,callback:function(a){t.$set(e.item,"message",a)},expression:"data.item.message"}})],1):t._e()]}},{key:"url",fn:function(e){return[a("b-form-input",{attrs:{type:"text"},model:{value:e.item.url,callback:function(a){t.$set(e.item,"url",a)},expression:"data.item.url"}})]}},{key:"start",fn:function(e){return[a("flat-pickr",{attrs:{config:t.config},on:{"on-close":function(a){return t.onTimeChange(e.index,"start")}},model:{value:e.item.start,callback:function(a){t.$set(e.item,"start",a)},expression:"data.item.start"}})]}},{key:"end",fn:function(e){return[a("flat-pickr",{attrs:{config:t.config},on:{"on-close":function(a){return t.onTimeChange(e.index,"end")}},model:{value:e.item.end,callback:function(a){t.$set(e.item,"end",a)},expression:"data.item.end"}})]}}],null,!1,3740500701)}):t._e()],2)],1)],1)],1)},qn=[];function Mn(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),a.push.apply(a,i)}return a}function An(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?Mn(a,!0).forEach(function(e){Object(B["a"])(t,e,a[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):Mn(a).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))})}return t}var Tn={name:"Announce",components:{},props:{caption:{type:String,default:"Announce"},hover:{type:Boolean,default:!0},striped:{type:Boolean,default:!0},bordered:{type:Boolean,default:!1},small:{type:Boolean,default:!1},fixed:{type:Boolean,default:!1}},data:function(){return{items:[],titleMap:{},title:"",titlename:"",titlekey:"",dismissCountDown:0,newMessage:"",newUrl:"",alertCss:"",alertMsg:"",config:{enableTime:!0,altFormat:"Y-m-d H:i",altInput:!0,dateFormat:"Y-m-d H:i"},q:"",fields:[{key:"action",label:""},{key:"message",label:"訊息內容"},{key:"url",label:"URL"},{key:"start",label:"開始"},{key:"end",label:"結束"}],currentPage:1,perPage:0,totalRows:0}},computed:An({},Object(F["b"])(["loading"])),mounted:function(){this.onFetch()},methods:{goBack:function(){this.$router.go(-1)},countDownChanged:function(t){this.dismissCountDown=t},showAlert:function(){this.dismissCountDown=5},onSave:function(){var t=this,e="/v3/console/announce";P.request("put",e,{announcements:this.items}).then(function(e){t.alertCss="success",t.alertMsg="儲存成功",t.showAlert(),t.onFetch()}).catch(function(e){t.alertCss="warning",t.alertMsg="這位大大，您的權限不足",t.showAlert()})},onDelete:function(t){console.log(t),this.items=this.items.filter(function(e,a){return console.log(a),a!==t})},onTimeChange:function(t,e){switch(e){case"start":var a=this.items[t].start;this.items[t].start_time=parseInt(new Date(a).getTime()/1e3);break;case"end":a=this.items[t].end;this.items[t].end_time=parseInt(new Date(a).getTime()/1e3);break}this.$forceUpdate()},onAdd:function(){if(""!==this.newMessage){var t={};t.message=this.newMessage,t.url=this.newUrl,t.start_time=parseInt((new Date).getTime()/1e3),t.end_time=parseInt((new Date).getTime()/1e3),this.items.push(t),this.newMessage="",this.newUrl="",this.cookItem()}},cookItem:function(){for(var t=0;t<this.items.length;t++)console.log(this.items[t].start_time,this.items[t].end_time),this.items[t].start=new Date(1e3*this.items[t].start_time),this.items[t].end=new Date(1e3*this.items[t].end_time)},onFetch:function(){var t=this;P.request("get","/v3/console/announce").then(function(e){console.log(e),e.data&&e.data.data&&e.data.data.announcements&&(t.items=e.data.data.announcements,t.cookItem())})}}},In=Tn,$n=(a("0671"),Object(_["a"])(In,Pn,qn,!1,null,"05f3ebcc",null)),Bn=$n.exports,Fn=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("b-row",[a("b-col",{attrs:{cols:"12",xl:"12"}},[a("transition",{attrs:{name:"slide"}},[a("b-card",{attrs:{"no-header":""}},[a("template",{slot:"header"},[t._v("\n        "+t._s(t.caption)+"\n      ")]),a("b-row",[a("b-col",{attrs:{sm:"12"}},[a("form",{on:{submit:function(e){return e.preventDefault(),t.onAdd(e)}}},[a("div",{staticClass:"form-row"},[a("div",{staticClass:"col"},[a("b-form-group",{attrs:{label:"訊息內容",description:""}},[a("b-form-input",{attrs:{type:"text",required:"",placeholder:""},model:{value:t.newMessage,callback:function(e){t.newMessage=e},expression:"newMessage"}})],1)],1),a("div",{staticClass:"col"},[a("b-form-group",{attrs:{label:"URL"}},[a("b-input-group",[a("b-form-input",{attrs:{type:"text"},model:{value:t.newUrl,callback:function(e){t.newUrl=e},expression:"newUrl"}}),a("b-button",{directives:[{name:"b-tooltip",rawName:"v-b-tooltip.hover",modifiers:{hover:!0}}],attrs:{variant:"primary",title:"加入下列編輯列表，可以編輯，但尚未存入後端"},on:{click:t.onAdd}},[a("i"),t._v("\n                        Add\n                        ")]),t._v("  \n                      "),a("b-button",{directives:[{name:"b-tooltip",rawName:"v-b-tooltip.hover",modifiers:{hover:!0}}],attrs:{variant:"success",title:"將目前所有修改，存入後端，並且生效"},on:{click:t.onSave}},[a("i",{staticClass:"fa",class:{"fa-refresh":t.loading,"fa-spin":t.loading}}),t._v("\n                        Save All\n                        ")])],1)],1)],1)])]),a("small",{domProps:{innerHTML:t._s("輸入完，點 Add 即會加入，預設時間現在，編輯好後，點 Save，才正式儲存並生效，<span style='font-weight: bold;color: #e67300;'>過期已發佈的資訊，請刪除，大家減碳救地球</span>")}}),a("b-alert",{attrs:{show:t.dismissCountDown,dismissible:"",variant:t.alertCss},on:{dismissed:function(e){t.dismissCountDown=0},"dismiss-count-down":t.countDownChanged}},[t._v("\n                 "+t._s(t.alertMsg)+"\n          ")])],1)],1),t.items?a("b-table",{attrs:{hover:t.hover,striped:t.striped,bordered:t.bordered,small:t.small,fixed:t.fixed,responsive:"sm",items:t.items,fields:t.fields,"current-page":t.currentPage,"per-page":t.perPage},scopedSlots:t._u([{key:"action",fn:function(e){return[t._v("\n            "+t._s(("00"+(e.index+1)).slice(-2))+"\n            "),a("b-button",{on:{click:function(a){return t.onDelete(e.index)}}},[a("i",{staticClass:"fa fa-trash"})]),t._v(" \n            "),e.item.image?a("img",{staticClass:"img-thumbnail",attrs:{src:e.item.image}}):t._e()]}},{key:"image",fn:function(e){return[a("b-form-file",{staticClass:"mb-2",attrs:{accept:"image/jpeg",placeholder:"","drop-placeholder":"Drop file"},model:{value:e.item.file,callback:function(a){t.$set(e.item,"file",a)},expression:"data.item.file"}}),a("b-button",{directives:[{name:"b-tooltip",rawName:"v-b-tooltip.hover",modifiers:{hover:!0}}],attrs:{type:"submit",variant:"primary",title:"將此圖上傳，並將目前所有修改，存入後端，並且生效"},on:{click:function(a){return t.imageUpload(e.index)}}},[t._v("儲存及上傳")])]}},{key:"message",fn:function(e){return[e.item.end_time&&1e3*e.item.end_time<(new Date).getTime()?a("strike",[a("b-form-input",{attrs:{type:"text"},model:{value:e.item.message,callback:function(a){t.$set(e.item,"message",a)},expression:"data.item.message"}})],1):t._e(),e.item.end_time&&1e3*e.item.end_time>=(new Date).getTime()?a("span",[a("b-form-input",{directives:[{name:"b-tooltip",rawName:"v-b-tooltip.hover",modifiers:{hover:!0}}],attrs:{type:"text",maxlength:"50",title:"長度限制 50 字"},model:{value:e.item.message,callback:function(a){t.$set(e.item,"message",a)},expression:"data.item.message"}})],1):t._e()]}},{key:"url",fn:function(e){return[a("b-form-input",{attrs:{type:"text"},model:{value:e.item.url,callback:function(a){t.$set(e.item,"url",a)},expression:"data.item.url"}})]}},{key:"roles",fn:function(e){return[a("b-form-checkbox-group",{attrs:{id:"checkboxes2",name:"roles"},model:{value:e.item.roles,callback:function(a){t.$set(e.item,"roles",a)},expression:"data.item.roles"}},[a("b-form-checkbox",{attrs:{value:"guest"}},[t._v("Guest")]),a("br"),a("b-form-checkbox",{attrs:{value:"freetrial"}},[t._v("Free Trial")]),a("br"),a("b-form-checkbox",{attrs:{value:"expired"}},[t._v("Expired")]),a("br"),a("b-form-checkbox",{attrs:{value:"premium"}},[t._v("KKTV VIP")]),a("br"),a("b-form-checkbox",{attrs:{value:"prime"}},[t._v("KKBOX Prime")])],1)]}},{key:"start",fn:function(e){return[a("flat-pickr",{attrs:{config:t.config},on:{"on-close":function(a){return t.onTimeChange(e.index,"start")}},model:{value:e.item.start,callback:function(a){t.$set(e.item,"start",a)},expression:"data.item.start"}})]}},{key:"end",fn:function(e){return[a("flat-pickr",{attrs:{config:t.config},on:{"on-close":function(a){return t.onTimeChange(e.index,"end")}},model:{value:e.item.end,callback:function(a){t.$set(e.item,"end",a)},expression:"data.item.end"}})]}}],null,!1,1647252896)}):t._e()],2)],1)],1)],1)},En=[];function Ln(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),a.push.apply(a,i)}return a}function Un(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?Ln(a,!0).forEach(function(e){Object(B["a"])(t,e,a[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):Ln(a).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))})}return t}var Rn={name:"Event",components:{},props:{caption:{type:String,default:"Announce"},hover:{type:Boolean,default:!0},striped:{type:Boolean,default:!0},bordered:{type:Boolean,default:!1},small:{type:Boolean,default:!1},fixed:{type:Boolean,default:!1}},data:function(){return{items:[],titleMap:{},title:"",titlename:"",titlekey:"",dismissCountDown:0,newMessage:"",newUrl:"",alertCss:"",alertMsg:"",config:{enableTime:!0,altFormat:"Y-m-d H:i",altInput:!0,dateFormat:"Y-m-d H:i"},q:"",fields:[{key:"action",label:""},{key:"image",label:"圖片"},{key:"message",label:"訊息內容"},{key:"url",label:"URL"},{key:"roles",label:"可見會員類型"},{key:"start",label:"開始"},{key:"end",label:"結束"}],currentPage:1,perPage:0,totalRows:0}},computed:Un({},Object(F["b"])(["loading"])),mounted:function(){this.onFetch()},methods:{goBack:function(){this.$router.go(-1)},countDownChanged:function(t){this.dismissCountDown=t},showAlert:function(){this.dismissCountDown=5},onTimeChange:function(t,e){switch(e){case"start":var a=this.items[t].start;this.items[t].start=new Date(a.replace(/-/g,"/")),this.items[t].start_time=parseInt(this.items[t].start.getTime()/1e3);break;case"end":a=this.items[t].end;this.items[t].end=new Date(a.replace(/-/g,"/")),this.items[t].end_time=parseInt(this.items[t].end.getTime()/1e3);break}this.$forceUpdate()},onAdd:function(){if(""!==this.newMessage){var t={};t.message=this.newMessage,t.url=this.newUrl,t.start_time=parseInt((new Date).getTime()/1e3),t.end_time=parseInt((new Date).getTime()/1e3),t.id=(new Date).getTime()+"",this.items.push(t),this.newMessage="",this.newUrl="",this.cookItem()}},onDelete:function(t){var e=this,a=this.items[t].id,i=this.items[t].image;if(console.log(t,(new Date).getTime()),this.items=this.items.filter(function(e,a){return console.log(a,e),a!==t}),i){console.log("ImageID",a);var n="/v3/console/eventimage?id="+a;console.log(n),P.request("delete",n).then(function(){e.alertCss="success",e.alertMsg="圖片先行刪除",e.showAlert()}).catch(function(){e.alertCss="warning",e.alertMsg="圖片刪除失敗",e.showAlert()})}},onSave:function(t){var e=this,a="/v3/console/event";console.log("onSave",this.items),P.request("put",a,{events:this.items}).then(function(a){if(console.log(a),t.file){console.log("got file");var i=new FormData;i.append("file",t.file);var n="/v3/console/eventimage?id="+t.id;console.log(n),P.request("post",n,i,{headers:{"Content-Type":"multipart/form-data"}}).then(function(){e.alertCss="success",e.alertMsg="儲存成功",e.showAlert(),e.onFetch()}).catch(function(){e.alertCss="warning",e.alertMsg="圖片儲存失敗",e.showAlert(),e.onFetch()})}else e.alertCss="success",e.alertMsg="儲存成功",e.showAlert(),e.onFetch()}).catch(function(t){e.alertCss="warning",e.alertMsg="這位大大，您的權限不足",e.showAlert()})},imageUpload:function(t){this.items[t].file?this.onSave(this.items[t]):(this.alertCss="warning",this.alertMsg="您未選取任何圖片",this.showAlert())},imageReset:function(t){console.log("Reset imgage",t),this.items[t].file=null,console.log(this.items[t].file)},cookItem:function(){for(var t=0;t<this.items.length;t++)this.items[t].start=new Date(1e3*this.items[t].start_time),this.items[t].end=new Date(1e3*this.items[t].end_time),this.items[t].file=null},onFetch:function(){var t=this;P.request("get","/v3/console/event").then(function(e){console.log(e),e.data&&e.data.data&&e.data.data.events&&(t.items=e.data.data.events,t.cookItem())})}}},Nn=Rn,zn=(a("2a16"),Object(_["a"])(Nn,Fn,En,!1,null,"46711da8",null)),Hn=zn.exports,Kn=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("b-row",[a("b-col",{attrs:{cols:"12",xl:"12"}},[a("transition",{attrs:{name:"slide"}},[a("b-card",{attrs:{header:t.listTypeStr}},[a("form",{on:{submit:function(e){return e.preventDefault(),t.onFetch(e)}}},[a("b-row",[a("b-col",{attrs:{sm:"5"}},[a("b-form-group",{attrs:{description:"輸入時間可以過濾出該時間點看得到的片單"}},[a("flat-pickr",{attrs:{config:t.flatpickrConfig},model:{value:t.displayDate,callback:function(e){t.displayDate=e},expression:"displayDate"}})],1)],1),a("b-col",{attrs:{sm:"7"}},[a("b-form-group",{attrs:{description:"輸入字串可以過濾出 Topic 或 Caption 符合的片單"}},[a("b-input-group",[a("b-form-input",{attrs:{type:"text",id:"name"},model:{value:t.q,callback:function(e){t.q=e},expression:"q"}}),a("b-input-group-append",[a("b-button",{attrs:{variant:"primary"},on:{click:function(e){return t.onModalShow({meta:{title_id:[]}})}}},[t._v("新增")])],1)],1)],1)],1)],1),a("b-row",[a("b-col",{attrs:{sm:"12"}},[a("b-form-group",{attrs:{description:""}},[a("b-form-checkbox",{attrs:{value:"checked","unchecked-value":"false"},model:{value:t.showAll,callback:function(e){t.showAll=e},expression:"showAll"}},[t._v("顯示全部")])],1),a("b-alert",{attrs:{show:t.dismissCountDown,dismissible:"",variant:t.alertCss},on:{dismissed:function(e){t.dismissCountDown=0},"dismiss-count-down":t.countDownChanged}},[t._v("\n                    "+t._s(t.alertMsg)+"\n              ")])],1)],1)],1),a("b-form-group",{attrs:{description:"修改預設片單的id"}},[a("b-input-group",[a("b-form-input",{attrs:{type:"text",id:"default-titlelist"},model:{value:t.titlelistDefault,callback:function(e){t.titlelistDefault=e},expression:"titlelistDefault"}}),a("b-input-group-append",[a("b-button",{attrs:{variant:"primary"},on:{click:function(e){return t.onStoreDefaultTitlelist()}}},[t._v("儲存")])],1)],1)],1),a("small",{domProps:{innerHTML:t._s("預設只顯示啟用中的片單，要顯示所有片單，請勾 <span style='font-weight: bold;color: #e67300;'>顯示全部</span>")}}),t.items?a("b-table",{staticStyle:{"overflow-x":"auto"},attrs:{hover:t.hover,striped:t.striped,bordered:t.bordered,small:t.small,fixed:t.fixed,responsive:"sm",items:t.filterItems,fields:t.fields,"current-page":t.currentPage,"per-page":t.perPage},scopedSlots:t._u([{key:"action",fn:function(e){return[a("b-button",{on:{click:function(a){return t.onModalShow(e.item)}}},[a("i",{staticClass:"fa fa-edit"})]),t._v(" \n            "),a("b-button",{on:{click:function(a){return t.onDelete(e.item)}}},[a("i",{staticClass:"fa fa-trash"})])]}},{key:"enabled",fn:function(e){return[a("b-badge",{attrs:{variant:t.getBadge(e.item.enabled)}},[t._v(t._s(e.item.enabled))])]}},{key:"duration",fn:function(e){return[a("span",{staticStyle:{"white-space":"nowrap"}},[t._v("\n              "+t._s(t.getLocalTime(e.item.visible_since))+" ～"),a("br"),t._v("\n              "+t._s(t.getLocalTime(e.item.visible_until))+"\n            ")])]}},{key:"image",fn:function(t){return[a("b-img",{attrs:{src:t.item.image,width:"100",alt:""}})]}},{key:"topic",fn:function(e){return[a("span",{staticStyle:{"white-space":"nowrap"}},[t._v("\n              "+t._s(e.item.topic)),a("br"),a("b",[t._v(t._s(e.item.title))]),a("br"),t._v("\n              "+t._s(e.item.summary)+" "),a("br")])]}},{key:"title",fn:function(e){return["title"===e.item.list_type?a("span",[t._v("\n              "+t._s(e.item.title_id)+" "),a("br"),a("b",[t._v(t._s(e.item.title_name))]),a("br"),a("br"),t._v("\n              Trailer: "),a("br"),t._v("\n                自動播放： "),a("b-badge",{attrs:{variant:t.getBadge(e.item.trailer_autoplay_enabled)}},[t._v(t._s(e.item.trailer_autoplay_enabled))]),a("br"),t._v("\n                指定預告: "+t._s(e.item.trailer_episode_id?e.item.trailer_episode_id:"(隨機播放)")+"\n            ")],1):t._e(),"link"===e.item.list_type?a("span",[t._v("\n              App: "),a("a",{attrs:{href:e.item.uri}},[t._v(t._s(e.item.uri))]),a("br"),t._v("\n              Web: "),a("a",{attrs:{href:e.item.url}},[t._v(t._s(e.item.url))])]):t._e()]}}],null,!1,169493301)}):t._e()],1)],1)],1),a("b-modal",{ref:"formModal",attrs:{size:"xl",title:t.detailTitle,"no-close-on-backdrop":"","no-close-on-esc":""},on:{ok:t.onOK,cancel:t.onCancel},model:{value:t.showModal,callback:function(e){t.showModal=e},expression:"showModal"}},[a("form",{ref:"form",on:{submit:function(e){return e.stopPropagation(),e.preventDefault(),t.handleSubmit(e)}}},[a("b-form-group",[a("b-form-checkbox",{model:{value:t.detailObj.enabled,callback:function(e){t.$set(t.detailObj,"enabled",e)},expression:"detailObj.enabled"}},[t._v("啟用")])],1),a("b-form-group",{attrs:{label:"Order",description:"輪播的順序，數字越小排越前面",state:t.orderState,"invalid-feedback":"Must greater than 0"}},[a("b-form-input",{attrs:{type:"number",min:"1",state:t.orderState,required:""},model:{value:t.detailObj.order,callback:function(e){t.$set(t.detailObj,"order",e)},expression:"detailObj.order"}})],1),["link","title"].includes(t.detailObj.list_type)?a("b-form-group",{attrs:{label:"Topic",description:"主題文字"}},[a("b-form-input",{attrs:{type:"text"},model:{value:t.detailObj.topic,callback:function(e){t.$set(t.detailObj,"topic",e)},expression:"detailObj.topic"}})],1):t._e(),a("b-form-group",{attrs:{label:"Caption",description:"標題 字數限制在15字內",state:t.captionState,"invalid-feedback":"Can't be blank"}},[a("b-form-input",{attrs:{type:"text",state:t.captionState,required:""},model:{value:t.detailObj.title,callback:function(e){t.$set(t.detailObj,"title",e)},expression:"detailObj.title"}})],1),["link","title"].includes(t.detailObj.list_type)?a("b-form-group",{attrs:{label:"Summary",description:"說明文字"}},[a("b-form-input",{attrs:{type:"text"},model:{value:t.detailObj.summary,callback:function(e){t.$set(t.detailObj,"summary",e)},expression:"detailObj.summary"}})],1):t._e(),a("b-row",[a("b-col",{attrs:{sm:"6"}},[a("b-form-group",{attrs:{label:"Visible Since",description:"上架時間"}},[a("flat-pickr",{attrs:{config:t.flatpickrConfig},model:{value:t.sinceDate,callback:function(e){t.sinceDate=e},expression:"sinceDate"}})],1)],1),a("b-col",{attrs:{sm:"6"}},[a("b-form-group",{attrs:{label:"Visible Until",description:"下架時間"}},[a("flat-pickr",{attrs:{config:t.flatpickrConfig},model:{value:t.untilDate,callback:function(e){t.untilDate=e},expression:"untilDate"}})],1)],1)],1),["link","title","highlight"].includes(t.detailObj.list_type)?a("b-form-group",{attrs:{label:"Image",description:"顯示的圖片",state:t.imageState,"invalid-feedback":"Can't be blank"}},[a("b-form-file",{ref:"image-input",attrs:{accept:"image/jpeg",state:t.imageState},model:{value:t.image,callback:function(e){t.image=e},expression:"image"}})],1):t._e(),["link","title","highlight"].includes(t.detailObj.list_type)?a("b-form-group",{attrs:{label:"預覽圖片",description:t.detailObj.source_image}},[t.detailObj.image?a("b-img",{attrs:{src:t.detailObj.image,width:"150",thumbnail:""}}):t._e()],1):t._e(),["link","title","highlight"].includes(t.detailObj.list_type)?a("b-form-group",{attrs:{label:"主色色碼",description:"圖片上傳的時候會自動計算，如果要自行定義，請再上傳圖片後，自行更新色碼後，再儲存一次，已覆蓋原先上傳圖片自動計算的色碼",state:t.colorState,"invalid-feedback":"color code invalid"}},[a("b-form-input",{attrs:{state:t.colorState},model:{value:t.detailObj.dominant_color,callback:function(e){t.$set(t.detailObj,"dominant_color",e)},expression:"detailObj.dominant_color"}})],1):t._e(),a("b-form-group",{attrs:{label:"Type",description:"類型",state:t.typeState,"invalid-feedback":"Can't be blank"}},[a("b-form-select",{attrs:{options:t.supportTypeOptions,state:t.typeState,required:""},model:{value:t.detailObj.list_type,callback:function(e){t.$set(t.detailObj,"list_type",e)},expression:"detailObj.list_type"}})],1),["choice","highlight"].includes(t.detailObj.list_type)?a("b-form-group",{attrs:{label:"Share Link",description:"分享連結"}},[a("div",{staticClass:"input-group mb-3"},[a("div",{staticClass:"input-group-prepend"},[a("span",{staticClass:"input-group-text",attrs:{id:"baseShareLink"}},[t._v(t._s(t.shareLinkBaseUrl))])]),a("input",{directives:[{name:"model",rawName:"v-model",value:t.detailObj.meta.share_id,expression:"detailObj.meta.share_id"}],staticClass:"form-control",attrs:{type:"text"},domProps:{value:t.detailObj.meta.share_id},on:{input:function(e){e.target.composing||t.$set(t.detailObj.meta,"share_id",e.target.value)}}}),"新增片單"!==this.detailTitle?a("b-button",{attrs:{variant:"primary"},on:{click:function(e){return t.onStoreTitleShareId()}}},[t._v("儲存")]):t._e()],1)]):t._e(),["choice","highlight"].includes(t.detailObj.list_type)?a("b-form-group",{attrs:{label:"片單圖",description:"片單獨立頁顯示的圖片，限制比例為16:9，尺寸為1920X1080px，檔案大小限制為2mb",state:t.titlelistDetailImageState,"invalid-feedback":t.detailImageFeedback}},[a("b-form-file",{ref:"image-input",attrs:{accept:"image/jpeg",state:t.titlelistDetailImageState},model:{value:t.backgroundImage,callback:function(e){t.backgroundImage=e},expression:"backgroundImage"}})],1):t._e(),["choice","highlight"].includes(t.detailObj.list_type)?a("b-form-group",{attrs:{label:"預覽圖片",description:t.detailObj.meta.background_image_url}},[t.detailObj.meta.background_image_url?a("b-img",{attrs:{src:t.detailObj.meta.background_image_url,width:"150",thumbnail:""}}):t._e()],1):t._e(),["choice","highlight"].includes(t.detailObj.list_type)?a("b-form-group",{attrs:{label:"og圖",description:"og顯示的圖片，限制尺寸為1200X630px，檔案限制大小為2mb",state:t.ogImageState,"invalid-feedback":t.ogFeedback}},[a("b-form-file",{ref:"image-input",attrs:{accept:"image/jpeg",state:t.ogImageState},model:{value:t.ogImage,callback:function(e){t.ogImage=e},expression:"ogImage"}})],1):t._e(),["choice","highlight"].includes(t.detailObj.list_type)?a("b-form-group",{attrs:{label:"預覽圖片",description:t.detailObj.meta.og_image}},[t.detailObj.meta.og_image?a("b-img",{attrs:{src:t.detailObj.meta.og_image,width:"150",thumbnail:""}}):t._e()],1):t._e(),["choice","highlight"].includes(t.detailObj.list_type)?a("b-form-group",{attrs:{label:"Description",description:"片單簡介,42字以內",state:t.descriptionState,"invalid-feedback":"Description should less than 42 character"}},[a("b-form-input",{attrs:{type:"text"},model:{value:t.detailObj.meta.description,callback:function(e){t.$set(t.detailObj.meta,"description",e)},expression:"detailObj.meta.description"}})],1):t._e(),["choice","highlight"].includes(t.detailObj.list_type)?a("b-form-group",{attrs:{label:"Copyright",description:"版權"}},[a("b-form-input",{attrs:{type:"text"},model:{value:t.detailObj.meta.copyright,callback:function(e){t.$set(t.detailObj.meta,"copyright",e)},expression:"detailObj.meta.copyright"}})],1):t._e(),"title"===t.detailObj.list_type?a("div",[a("b-form-group",{attrs:{label:"Title ID",description:"目標戲劇 ID",state:t.titleIdState,"invalid-feedback":"Can't be blank"}},[a("v-select",{attrs:{options:this.titleOptions,label:"name",reduce:function(t){return t.id},state:t.titleIdState},on:{input:t.getTrailerOptions},model:{value:t.detailObj.title_id,callback:function(e){t.$set(t.detailObj,"title_id",e)},expression:"detailObj.title_id"}})],1),a("b-form-group",{attrs:{description:"若不勾選則不會播放 Trailer，只會顯示 Image"}},[a("b-form-checkbox",{model:{value:t.detailObj.trailer_autoplay_enabled,callback:function(e){t.$set(t.detailObj,"trailer_autoplay_enabled",e)},expression:"detailObj.trailer_autoplay_enabled"}},[t._v("自動播放預告片")])],1),a("b-form-group",{attrs:{label:"指定 Trailer",description:"非必填，如不指定，則會隨機播放"}},[a("v-select",{ref:"extra-input",attrs:{options:this.trailerOptions,label:"name",reduce:function(t){return t.id}},model:{value:t.detailObj.trailer_episode_id,callback:function(e){t.$set(t.detailObj,"trailer_episode_id",e)},expression:"detailObj.trailer_episode_id"}})],1)],1):t._e(),"link"===t.detailObj.list_type?a("div",[a("b-form-group",{attrs:{label:"Deep Link",description:"App 點選 Headline 後的行為，關於 Deep Link 設定，請參考：https://reurl.cc/odgl6v",state:t.uriState,"invalid-feedback":"Can't be blank"}},[a("b-form-input",{attrs:{type:"text",state:t.uriState},model:{value:t.detailObj.uri,callback:function(e){t.$set(t.detailObj,"uri",e)},expression:"detailObj.uri"}})],1),a("b-form-group",{attrs:{label:"Web URL",description:"網頁 User 點選 Headline 後轉換的網址，外部連結與內部連結設定不同，請參考：https://reurl.cc/odgl6v"}},[a("b-form-input",{attrs:{type:"text"},model:{value:t.detailObj.url,callback:function(e){t.$set(t.detailObj,"url",e)},expression:"detailObj.url"}})],1)],1):t._e(),["link","title"].includes(t.detailObj.list_type)?t._e():a("div",[a("b-form-group",{attrs:{label:"Title ID",description:"輸入title ID ，若要一次輸入多筆，請用空格隔開",state:t.metaTitleIdState,"invalid-feedback":"Can't be blank"}},[a("b-form-input",{attrs:{type:"text"},on:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.onAdd(e)}},model:{value:t.titleID,callback:function(e){t.titleID=e},expression:"titleID"}}),a("b-button",{attrs:{variant:"primary"},on:{click:t.onAdd}},[t._v("加入")]),a("ul",{directives:[{name:"show",rawName:"v-show",value:t.isOpen,expression:"isOpen"}],staticClass:"list-group mb-3"},t._l(t.suggestions,function(e,i){return a("li",{key:i,staticClass:"list-group-item small-list-item",class:{active:i==t.indexCounter},on:{click:function(a){return t.setTitleID(e)},mouseover:function(e){t.indexCounter=i}}},[t._v("\n              "+t._s(e.id)+" "+t._s(e.name)+"\n            ")])}),0)],1),a("b-form-group",{attrs:{label:"調整 titleid 排序或移除",description:"請用拖拉的方式調整 title 排序"}},[t.detailObj.meta&&t.detailObj.meta.title_id?a("b-list-group",[a("draggable",{model:{value:t.detailObj.meta.title_id,callback:function(e){t.$set(t.detailObj.meta,"title_id",e)},expression:"detailObj.meta.title_id"}},t._l(t.detailObj.meta.title_id,function(e,i){return a("b-list-group-item",{key:i},[t._v("\n                "+t._s(t.renderSelected(e))+"\n                "),a("b-button",{on:{click:function(e){return t.detailObj.meta.title_id.splice(i,1)}}},[a("i",{staticClass:"fa fa-trash"})])],1)}),1)],1):t._e()],1)],1)],1)])],1)},Jn=[],Vn=a("7618"),Wn={name:"TitleList",components:{draggable:Xi.a},props:{caption:{type:String,default:"TitleList"},hover:{type:Boolean,default:!0},striped:{type:Boolean,default:!0},bordered:{type:Boolean,default:!1},small:{type:Boolean,default:!1},fixed:{type:Boolean,default:!1}},data:function(){return{q:"",items:[],suggestions:[],isOpen:!1,indexCounter:-1,showAll:!1,showModal:!1,detailTitle:"",titleID:"",listTypeStr:"",detailObj:{id:null,meta:{title_id:[]}},image:null,supportTypeOptions:[],supportListType:[],titleOptions:[],titleDict:{},trailerOptions:[],sinceDate:new Date,untilDate:new Date,displayDate:null,flatpickrConfig:{enableTime:!0,altFormat:"Y-m-d H:i",altInput:!0,dateFormat:"Z",allowInput:!0},orderState:null,captionState:null,imageState:null,titlelistDetailImageState:null,detailImageFeedback:null,ogImageState:null,ogFeedback:null,colorState:null,typeState:null,titleIdState:null,metaTitleIdState:null,uriState:null,descriptionState:null,alertCss:"",alertMsg:"",dismissCountDown:0,fields:[{key:"action",label:""},{key:"order",label:"排序"},{key:"enabled",label:"啟用"},{key:"duration"},{key:"image"},{key:"topic",label:"Topic / Caption / Summary"},{key:"list_type"},{key:"title",label:"劇名 or URL"}],currentPage:1,perPage:0,totalRows:0,shareLinkBaseUrl:"",titlelistDefault:"",backgroundImage:null,ogImage:null}},computed:{filterItems:function(){var t=[];if("checked"===this.showAll)t=this.items;else{if(t=this.items.filter(function(t){return!0===t.enabled}),this.displayDate){var e=Date.parse(this.displayDate);t=t.filter(function(t){return Date.parse(t.visible_since)<=e&&Date.parse(t.visible_until)>=e})}if(""!==this.q){var a=this.q;t=t.filter(function(t){return t.title&&t.title.includes(a)||t.topic&&t.topic.includes(a)})}}return t}},mounted:function(){this.prepare()},watch:{$route:function(t,e){this.prepare()},titleID:function(t,e){""!=t&&t.length>1?(this.filterSuggestions(),this.isOpen=!0):this.isOpen=!1}},methods:{getBadge:function(t){var e;switch(t){case!0:e="success";break;case!1:e="warning";break}return e},getLocalTime:function(t){return new Date(t).toLocaleString("default",{hour12:!1,year:"numeric",month:"short",day:"2-digit",hour:"numeric",minute:"numeric"})},getTitleOptions:function(){var t=this;0===this.titleOptions.length&&(console.log("Load title options"),P.request("get","/v3/console/titlehint").then(function(e){if(e.data&&e.data.data&&e.data.data.titles){t.titleOptions=e.data.data.titles;for(var a=0;a<t.titleOptions.length;a++){var i=t.titleOptions[a];t.titleDict[i["id"]]=i}}}))},filterSuggestions:function(){var t=this.titleID.split(" ").filter(function(t){return t.trim()}).map(function(t){return t.trim()});if(t.length>0){var e=t[t.length-1];this.suggestions=this.titleOptions.filter(function(t){return t.id.startsWith(e)||t.name.includes(e)})}},setTitleID:function(t){var e=this.titleID.split(" ").filter(function(t){return t.trim()}).map(function(t){return t.trim()});e.pop(),e.push(t.id),this.detailObj.meta.title_id?this.detailObj.meta.title_id=this.detailObj.meta.title_id.concat(e):this.detailObj.meta.title_id=e,this.titleID=""},onAdd:function(){var t=this.titleID.split(" ").filter(function(t){return t.trim()}).map(function(t){return t.trim()});this.detailObj.meta.title_id?this.detailObj.meta.title_id=this.detailObj.meta.title_id.concat(t):this.detailObj.meta.title_id=t,this.titleID=""},prepare:function(){this.supportTypeOptions=[],this.resetFormStates(),this.resetDetailObj(),this.resetImage();var t=this;""!==this.$route.params.list_type&&(this.listTypeStr=this.$route.params.list_type,this.listTypeStr.split(",").forEach(function(e){t.supportListType.push(e),t.supportTypeOptions.push({value:e,text:e.charAt(0).toUpperCase()+e.slice(1)})})),this.onFetch()},getTrailerOptions:function(){var t=this;if("title"===this.detailObj.list_type)if(this.detailObj.title_id){var e="/v3/console/extrahint?series_id="+this.detailObj.title_id+"tr";console.log("Load trailer options from "+e),P.request("get",e).then(function(e){e.data&&e.data.data&&e.data.data.extra?t.trailerOptions=e.data.data.extra:(console.log("Extra not found"),t.resetSelectedExtra())})}else console.log("No title selected"),this.resetSelectedExtra()},resetFormStates:function(){this.orderState=null,this.captionState=null,this.imageState=null,this.colorState=null,this.typeState=null,this.titleIdState=null,this.metaTitleIdState=null,this.uriState=null,this.titlelistDetailImageState=null,this.ogImageState=null},resetDetailObj:function(){this.detailObj={id:null,meta:{title_id:[]}}},resetImage:function(){this.image&&this.$refs["image-input"]&&this.$refs["image-input"].reset(),this.image=null},resetSelectedExtra:function(){this.$refs["extra-input"]&&this.$refs["extra-input"].clearSelection(),this.trailerOptions=[]},countDownChanged:function(t){this.dismissCountDown=t},showAlert:function(){this.dismissCountDown=5},renderSelected:function(t){return this.titleDict[t]?this.titleDict[t].id+" "+this.titleDict[t].name:t+" Title Not Found"},onFetch:function(){var t=this;P.request("get","/v3/console/metatitlelist?list_type="+this.listTypeStr).then(function(e){e.data&&e.data.data&&e.data.data.titlelist?t.items=e.data.data.titlelist:t.items=[]}),this.getTitleOptions(),this.getDefaultTitleListId()},getDefaultTitleListId:function(){var t=this;P.request("get","/v3/console/default_titlelist").then(function(e){t.titlelistDefault=e.data.data.default_titlelist_id,t.shareLinkBaseUrl=e.data.data.base_url})},onStoreDefaultTitlelist:function(){var t=this;P.request("post","/v3/console/default_titlelist?defaultId="+this.titlelistDefault).then(function(){t.alertCss="success",t.alertMsg="儲存成功",t.showAlert(),t.onFetch()}).catch(function(){t.alertCss="warning",t.alertMsg="儲存失敗",t.showAlert(),t.onFetch()})},onStoreTitleShareId:function(){var t=this;P.request("put","/v3/console/titlelist_shareId?id="+this.detailObj.id+"&shareId="+this.detailObj.meta.share_id).then(function(){t.alertCss="success",t.alertMsg="儲存成功",t.showAlert(),t.onFetch()}).catch(function(){t.alertCss="warning",t.alertMsg="儲存失敗",t.showAlert(),t.onFetch()})},onModalShow:function(t){this.detailObj=Object.assign({},t),this.getTrailerOptions(),t.id?(this.detailTitle="修改 "+t.title,this.sinceDate=this.detailObj.visible_since,this.untilDate=this.detailObj.visible_until):(this.detailTitle="新增片單",this.sinceDate=new Date,this.untilDate=new Date,this.detailObj.enabled=!0),this.showModal=!0},validateOGImage:function(t){return 40/21==t.width/t.height&&!(t.width>1200||t.height>630)},getSize:function(t){return new Promise(function(e,a){var i=window.URL||window.webkitURL,n=new Image;n.onload=function(){return e({height:n.height,width:n.width})},n.onerror=a,console.log("file:",t,"type:",Object(Vn["a"])(t)),n.src=i.createObjectURL(t)})},validateBackGroundImage:function(t){return 16/9==t.width/t.height&&!(t.width>1600||t.height>900)},isValid:function(){var t=Object(le["a"])(regeneratorRuntime.mark(function t(){var e,a,i,n,r;return regeneratorRuntime.wrap(function(t){while(1)switch(t.prev=t.next){case 0:e=!0,this.resetFormStates(),console.log("start validate"),this.detailObj.order&&this.detailObj.order>0||(this.orderState="invalid",e=!1),this.detailObj.title||(this.captionState="invalid",e=!1),this.detailObj.list_type||(this.typeState="invalid",e=!1),t.t0=this.detailObj.list_type,t.next="link"===t.t0?9:"title"===t.t0?13:"highlight"===t.t0?17:"choice"===t.t0?32:45;break;case 9:return this.detailObj.source_image||this.image||(this.imageState="invalid",e=!1),this.detailObj.dominant_color&&!/^[0-9A-F]{6}$/i.test(this.detailObj.dominant_color)&&(this.colorState="invalid",e=!1),this.detailObj.uri||(this.uriState="invalid",e=!1),t.abrupt("break",45);case 13:return this.detailObj.source_image||this.image||(this.imageState="invalid",e=!1),this.detailObj.dominant_color&&!/^[0-9A-F]{6}$/i.test(this.detailObj.dominant_color)&&(this.colorState="invalid",e=!1),this.detailObj.title_id||(this.titleIdState="invalid",e=!1),t.abrupt("break",45);case 17:if(this.detailObj.source_image||this.image||(this.imageState="invalid",e=!1),this.detailObj.dominant_color&&!/^[0-9A-F]{6}$/i.test(this.detailObj.dominant_color)&&(this.colorState="invalid",e=!1),(!this.detailObj.meta||!this.detailObj.meta.title_id||this.detailObj.meta.title_id.length<0)&&(e=!1),this.detailObj.meta.description&&this.detailObj.meta.description.length>42&&(this.descriptionState="invalid",e=!1),!this.ogImage){t.next=26;break}return t.next=24,this.getSize(this.ogImage).then(function(t){return t});case 24:a=t.sent,this.validateOGImage(a)||(this.ogImageState="invalid",this.ogFeedback="image size wrong, should fit 1.91:1",e=!1);case 26:if(!this.backgroundImage){t.next=31;break}return t.next=29,this.getSize(this.backgroundImage).then(function(t){return t});case 29:i=t.sent,this.validateBackGroundImage(i)||(this.titlelistDetailImageState="invalid",this.detailImageFeedback="image size wrong, should fit 16:9",e=!1);case 31:return t.abrupt("break",45);case 32:if((!this.detailObj.meta||!this.detailObj.meta.title_id||this.detailObj.meta.title_id.length<0)&&(e=!1),this.detailObj.meta.description&&this.detailObj.meta.description.length>42&&(this.descriptionState="invalid",e=!1),!this.ogImage){t.next=39;break}return t.next=37,this.getSize(this.ogImage).then(function(t){return t});case 37:n=t.sent,this.validateOGImage(n)||(this.ogImageState="invalid",this.ogFeedback="image size wrong, should fit 1.91:1",e=!1);case 39:if(!this.backgroundImage){t.next=44;break}return t.next=42,this.getSize(this.backgroundImage).then(function(t){return t});case 42:r=t.sent,this.validateBackGroundImage(r)||(this.titlelistDetailImageState="invalid",this.detailImageFeedback="image size wrong, should fit 16:9",e=!1);case 44:return t.abrupt("break",45);case 45:return t.abrupt("return",e);case 46:case"end":return t.stop()}},t,this)}));function e(){return t.apply(this,arguments)}return e}(),handleSubmit:function(){var t=Object(le["a"])(regeneratorRuntime.mark(function t(){var e,a,i,n,r,s=this;return regeneratorRuntime.wrap(function(t){while(1)switch(t.prev=t.next){case 0:return e=this,a=Date.parse(this.sinceDate),i=Date.parse(this.untilDate),a>i&&(i=[a,a=i][0]),this.detailObj.visible_since=new Date(a).toISOString(),this.detailObj.visible_until=new Date(i).toISOString(),this.detailObj.order=parseInt(this.detailObj.order),this.detailObj.dominant_color?this.detailObj.dominant_color=this.detailObj.dominant_color.replace("#",""):this.detailObj.dominant_color=null,t.next=10,this.isValid();case 10:if(n=t.sent,n){t.next=13;break}return t.abrupt("return");case 13:r="/v3/console/metatitlelist",P.request("post",r,this.detailObj).then(function(t){if(s.detailObj.id||(s.detailObj.id=t.data.data.id),s.image){var a="/v3/console/metatitlelistimage?id="+s.detailObj.id,i=new FormData;i.append("file",s.image),P.request("post",a,i,{headers:{"Content-Type":"multipart/form-data"}}).then(function(){e.alertCss="success",e.alertMsg="儲存成功",e.showAlert(),e.onFetch()}).catch(function(){e.alertCss="warning",e.alertMsg="圖片儲存失敗",e.showAlert(),e.onFetch()})}else e.alertCss="success",e.alertMsg="儲存成功",e.showAlert(),e.onFetch();if(s.backgroundImage){var n="/v3/console/titlelist_meta_image?id="+s.detailObj.id+"&imageType=backgroundImage",r=new FormData;r.append("file",s.backgroundImage),P.request("post",n,r,{headers:{"Content-Type":"multipart/form-data"}}).then(function(){e.alertCss="success",e.alertMsg="儲存成功",e.showAlert(),e.onFetch()}).catch(function(){e.alertCss="warning",e.alertMsg="圖片儲存失敗",e.showAlert(),e.onFetch()})}else e.alertCss="success",e.alertMsg="儲存成功",e.showAlert(),e.onFetch();if(s.ogImage){var o="/v3/console/titlelist_meta_image?id="+s.detailObj.id+"&imageType=ogImage",l=new FormData;l.append("file",s.ogImage),P.request("post",o,l,{headers:{"Content-Type":"multipart/form-data"}}).then(function(){e.alertCss="success",e.alertMsg="儲存成功",e.showAlert(),e.onFetch()}).catch(function(){e.alertCss="warning",e.alertMsg="圖片儲存失敗",e.showAlert(),e.onFetch()})}else e.alertCss="success",e.alertMsg="儲存成功",e.showAlert(),e.onFetch();e.onCancel(),e.$refs.formModal.hide()}).catch(function(t){e.alertCss="warning",e.alertMsg="這位大大，您的權限不足",e.onCancel(),e.$refs.formModal.hide(),e.showAlert()});case 15:case"end":return t.stop()}},t,this)}));function e(){return t.apply(this,arguments)}return e}(),onOK:function(t){t.preventDefault(),this.handleSubmit()},onCancel:function(){this.resetFormStates(),this.resetDetailObj(),this.resetImage()},onDelete:function(t){var e=window.confirm("Are you sure you want to delete this title list ?");if(e){var a=this,i="/v3/console/metatitlelist?id="+t.id;console.log(i),P.request("delete",i).then(function(){a.alertCss="success",a.alertMsg="刪除成功",a.showAlert(),a.onFetch()}).catch(function(){a.alertCss="warning",a.alertMsg="刪除失敗",a.showAlert(),a.onFetch()})}}}},Gn=Wn,Yn=(a("8bca"),Object(_["a"])(Gn,Kn,Jn,!1,null,"6b7f416e",null)),Xn=Yn.exports,Zn=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("b-row",[a("b-col",{attrs:{cols:"12",xl:"12"}},[a("transition",{attrs:{name:"slide"}},[a("b-card",{attrs:{header:t.caption}},[a("form",{on:{submit:function(e){return e.preventDefault(),t.onAdd(e)}}},[a("b-row",[a("b-col",{attrs:{sm:"5"}},[a("b-form-group",{attrs:{label:"廣告 ID"}},[a("b-form-input",{attrs:{type:"text",required:"",placeholder:""},model:{value:t.newAdID,callback:function(e){t.newAdID=e},expression:"newAdID"}})],1)],1),a("b-col",{attrs:{sm:"7"}},[a("b-form-group",{attrs:{label:"活動名稱"}},[a("b-input-group",[a("b-form-input",{attrs:{type:"text"},model:{value:t.newAdName,callback:function(e){t.newAdName=e},expression:"newAdName"}}),t._v(" \n                  "),a("b-button",{directives:[{name:"b-tooltip",rawName:"v-b-tooltip.hover",modifiers:{hover:!0}}],attrs:{variant:"primary",title:"加入下列編輯列表，可以編輯，但尚未存入後端"},on:{click:t.onAdd}},[t._v("Add")]),t._v(" \n                  "),a("b-button",{directives:[{name:"b-tooltip",rawName:"v-b-tooltip.hover",modifiers:{hover:!0}}],attrs:{variant:"success",title:"將目前所有修改，存入後端，並且生效"},on:{click:t.onSave}},[a("i",{staticClass:"fa",class:{"fa-refresh":t.loading,"fa-spin":t.loading}}),t._v("\n                    Save All\n                  ")])],1)],1)],1)],1),a("b-row",[a("b-col",{attrs:{sm:"12"}},[a("b-form-group",{attrs:{description:""}}),a("b-alert",{attrs:{show:t.dismissCountDown,dismissible:"",variant:t.alertCss},on:{dismissed:function(e){t.dismissCountDown=0},"dismiss-count-down":t.countDownChanged}},[t._v("\n                    "+t._s(t.alertMsg)+"\n              ")])],1)],1)],1),a("small",{domProps:{innerHTML:t._s("輸入完，點 Add 就會產生一筆新的廣告。在下方編輯好後點 Save 才會正式儲存並生效喔。<span style='font-weight: bold;color: #e67300;'>過期已發佈的資訊請刪除，大家減碳救地球</span>")}}),t.items?a("b-table",{staticStyle:{"overflow-x":"auto"},attrs:{hover:t.hover,striped:t.striped,bordered:t.bordered,small:t.small,fixed:t.fixed,responsive:"sm",items:t.items,fields:t.fields,"current-page":t.currentPage,"per-page":t.perPage},scopedSlots:t._u([{key:"action",fn:function(e){return[t._v("\n            "+t._s(e.item.id)+"\n            "),a("b-button",{on:{click:function(a){return t.onDelete(e.index)}}},[a("i",{staticClass:"fa fa-trash"})]),a("div",{staticClass:"img-preview mt-2"},[e.item.image_url?a("img",{staticClass:"img-thumbnail",attrs:{src:e.item.image_url}}):t._e()])]}},{key:"details",fn:function(e){return[a("b-row",[a("b-col",{attrs:{sm:"12"}},[a("b-form-group",{attrs:{label:"活動名稱",description:"盡量不要重複，長度限制 50 字"}},[e.item.isExpired?a("strike",[a("b-form-input",{attrs:{type:"text",maxlength:"50"},model:{value:e.item.name,callback:function(a){t.$set(e.item,"name",a)},expression:"data.item.name"}})],1):a("span",[a("b-form-input",{attrs:{type:"text",maxlength:"50"},model:{value:e.item.name,callback:function(a){t.$set(e.item,"name",a)},expression:"data.item.name"}})],1)],1)],1)],1),a("b-row",[a("b-col",{attrs:{sm:"6"}},[a("b-form-group",{attrs:{label:"廣告來源",description:"內部 In House\b；外部 Outsource"}},[a("v-select",{attrs:{options:t.adSourceOptions,reduce:function(t){return t.value}},model:{value:e.item.source,callback:function(a){t.$set(e.item,"source",a)},expression:"data.item.source"}})],1)],1),a("b-col",{attrs:{sm:"6"}},[a("b-form-group",{attrs:{label:"廣告主",description:"含 KKTV/KKBOX"}},[a("v-select",{attrs:{taggable:"",options:t.adProviderOptions,reduce:function(t){return t.value},"create-option":function(t){return{label:t,value:t}}},model:{value:e.item.provider,callback:function(a){t.$set(e.item,"provider",a)},expression:"data.item.provider"}})],1)],1)],1),a("b-row",[a("b-col",{attrs:{sm:"6"}},[a("b-form-group",{attrs:{label:"開始時間",description:"預設為現在時間"}},[a("flat-pickr",{attrs:{config:t.flatpickrConfig},on:{"on-close":function(a){return t.onTimeChange(e.index,"start")}},model:{value:e.item.start,callback:function(a){t.$set(e.item,"start",a)},expression:"data.item.start"}})],1)],1),a("b-col",{attrs:{sm:"6"}},[a("b-form-group",{attrs:{label:"結束時間",description:"預設為一天後"}},[a("flat-pickr",{attrs:{config:t.flatpickrConfig},on:{"on-close":function(a){return t.onTimeChange(e.index,"end")}},model:{value:e.item.end,callback:function(a){t.$set(e.item,"end",a)},expression:"data.item.end"}})],1)],1)],1),a("b-row",[a("b-col",{attrs:{sm:"12"}},[a("b-form-group",{attrs:{label:"可見會員類型",description:"若一次有多個會員類型，可以多選"}},[a("v-select",{attrs:{multiple:"",options:t.adTargetOptions,reduce:function(t){return t.value}},model:{value:e.item.target,callback:function(a){t.$set(e.item,"target",a)},expression:"data.item.target"}})],1)],1)],1),a("b-row",[a("b-col",{attrs:{sm:"12"}},[a("b-form-group",{attrs:{label:"Image",description:"Current: "+e.item.image_url}},[a("b-input-group",[a("b-form-file",{staticClass:"mr-2",attrs:{accept:"image/jpeg"},model:{value:e.item.file,callback:function(a){t.$set(e.item,"file",a)},expression:"data.item.file"}}),a("b-button",{directives:[{name:"b-tooltip",rawName:"v-b-tooltip.hover",modifiers:{hover:!0}}],attrs:{type:"submit",variant:"primary",size:"sm",title:"將此圖上傳，並將目前所有修改，存入後端，並且生效"},on:{click:function(a){return t.imageUpload(e.index)}}},[t._v("儲存及上傳")])],1)],1)],1)],1),a("b-row",[a("b-col",{attrs:{sm:"12"}},[a("b-form-group",{attrs:{label:"Call To Action Link 網址",description:"活動頁面或網站。沒有填寫的話，就不會出現 CTA Button"}},[a("b-form-input",{model:{value:e.item.action_link,callback:function(a){t.$set(e.item,"action_link",a)},expression:"data.item.action_link"}})],1)],1)],1),a("b-row",[a("b-col",{attrs:{sm:"6"}},[a("b-form-group",{attrs:{label:"Call To Action 按鈕色碼",description:"預設為黑色(#000000)。若沒有 CTA Link 的話可以不用填寫"}},[a("b-form-input",{model:{value:e.item.action_link_bg,callback:function(a){t.$set(e.item,"action_link_bg",a)},expression:"data.item.action_link_bg"}})],1)],1),a("b-col",{attrs:{sm:"6"}},[a("b-form-group",{attrs:{label:"Call To Action 按鈕文字",description:"預設是 '瞭解更多'。若沒有 CTA Link 的話可以不用填寫"}},[a("b-form-input",{model:{value:e.item.action_link_text,callback:function(a){t.$set(e.item,"action_link_text",a)},expression:"data.item.action_link_text"}})],1)],1)],1)]}}],null,!1,2884911333)}):t._e()],1)],1)],1)],1)},Qn=[];function tr(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),a.push.apply(a,i)}return a}function er(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?tr(a,!0).forEach(function(e){Object(B["a"])(t,e,a[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):tr(a).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))})}return t}var ar={name:"Ads",components:{},props:{caption:{type:String,default:"Ads"},hover:{type:Boolean,default:!0},striped:{type:Boolean,default:!0},bordered:{type:Boolean,default:!1},small:{type:Boolean,default:!1},fixed:{type:Boolean,default:!1}},data:function(){return{q:"",items:[],alertCss:"",alertMsg:"",dismissCountDown:0,newAdID:"",newAdName:"",adSourceOptions:[{value:"in_house",label:"In House"},{value:"outsource",label:"Outsource"}],adProviderOptions:[{value:"KKBOX",label:"KKBOX"},{value:"KKTV",label:"KKTV"}],adTargetOptions:[{value:"guest",label:"Guest"},{value:"freetrial",label:"Free Trial"},{value:"expired",label:"Expired"},{value:"premium",label:"KKTV VIP"},{value:"prime",label:"KKBOX Prime"}],flatpickrConfig:{enableTime:!0,altFormat:"Y-m-d H:i",altInput:!0,dateFormat:"Y-m-d H:i"},fields:[{key:"action",label:""},{key:"details",label:"活動細節"}],currentPage:1,perPage:0,totalRows:0}},computed:er({},Object(F["b"])(["loading"])),mounted:function(){this.onFetch()},methods:{countDownChanged:function(t){this.dismissCountDown=t},showAlert:function(){this.dismissCountDown=5},cookItem:function(){for(var t=0;t<this.items.length;t++)this.items[t].start=new Date(1e3*this.items[t].start_time),this.items[t].end=new Date(1e3*this.items[t].end_time),this.items[t].isExpired=1e3*this.items[t].end_time<(new Date).getTime(),this.items[t].file=null},onFetch:function(){var t=this;P.request("get","/v3/console/ads").then(function(e){console.log(e),e.data&&e.data.data&&e.data.data.ads&&(t.items=e.data.data.ads,t.cookItem())})},onTimeChange:function(t,e){switch(e){case"start":var a=this.items[t].start;this.items[t].start=new Date(a.replace(/-/g,"/")),this.items[t].start_time=parseInt(this.items[t].start.getTime()/1e3);break;case"end":a=this.items[t].end;this.items[t].end=new Date(a.replace(/-/g,"/")),this.items[t].end_time=parseInt(this.items[t].end.getTime()/1e3);break}this.$forceUpdate()},onAdd:function(){if(""!==this.newAdID){var t={},e=new Date;t.id=this.newAdID,t.name=this.newAdName,t.start_time=parseInt(e.getTime()/1e3),t.end_time=parseInt(e.setDate(e.getDate()+1)/1e3),t.action_link_bg="#000000",t.action_link_text="瞭解更多",this.items.push(t),this.newAdID="",this.newAdName="",this.cookItem()}},onDelete:function(t){var e=this,a=this.items[t].id,i=this.items[t].image_url;if(this.items=this.items.filter(function(e,a){return a!==t}),i){var n="/v3/console/ads_image?id="+a;P.request("delete",n).then(function(){e.alertCss="success",e.alertMsg="圖片先行刪除",e.showAlert()}).catch(function(){e.alertCss="warning",e.alertMsg="圖片刪除失敗",e.showAlert()})}},onSave:function(t){var e=this,a="/v3/console/ads";P.request("put",a,{ads:this.items}).then(function(a){if(console.log(a),console.log(t),t.file){var i=new FormData;i.append("file",t.file);var n="/v3/console/ads_image?id="+t.id;P.request("post",n,i,{headers:{"Content-Type":"multipart/form-data"}}).then(function(){e.alertCss="success",e.alertMsg="儲存成功",e.showAlert(),e.onFetch()}).catch(function(){e.alertCss="warning",e.alertMsg="圖片儲存失敗",e.showAlert(),e.onFetch()})}else e.alertCss="success",e.alertMsg="儲存成功",e.showAlert(),e.onFetch()}).catch(function(t){e.alertCss="warning",e.alertMsg="這位大大，您的權限不足",e.showAlert()})},imageUpload:function(t){this.items[t].file?this.onSave(this.items[t]):(this.alertCss="warning",this.alertMsg="您未選取任何圖片",this.showAlert())}}},ir=ar,nr=(a("9f31"),Object(_["a"])(ir,Zn,Qn,!1,null,"251384cc",null)),rr=nr.exports,sr=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("b-row",[a("b-col",{attrs:{cols:"12",xl:"12"}},[a("transition",{attrs:{name:"slide"}},[a("b-card",{attrs:{header:t.caption}},[a("b-row",[a("b-col",{attrs:{sm:"12"}},[a("b-form-group",{attrs:{description:"編輯確定後，直接按 Save All ，會立刻生效"}},[a("b-input-group",[a("b-button",{attrs:{variant:"primary"},on:{click:t.onFetch}},[a("i",{staticClass:"fa",class:{"fa-refresh":t.loading,"fa-spin":t.loading}}),t._v("\n                Reload\n                ")]),t._v(" \n              "),a("b-button",{attrs:{variant:"primary"},on:{click:t.onSave}},[a("i",{staticClass:"fa",class:{"fa-refresh":t.loading,"fa-spin":t.loading}}),t._v("\n                Save All\n                ")])],1)],1),a("b-alert",{attrs:{show:t.dismissCountDown,dismissible:"",variant:"warning"},on:{dismissed:function(e){t.dismissCountDown=0},"dismiss-count-down":t.countDownChanged}},[t._v("\n                 "+t._s(t.alertMsg)+"\n          ")])],1)],1),a("table",{staticClass:"table b-table table-striped",attrs:{"aria-colcount":"1"}},[a("thead",{},[a("tr",[a("th",{attrs:{"aria-colindex":"1"}},[t._v("平台")]),a("th",[t._v("設定檔")])])]),a("tbody",{},[a("tr",[a("td",[t._v(" Android ")]),a("td",[a("b-form-input",{attrs:{type:"text"},model:{value:t.android,callback:function(e){t.android=e},expression:"android"}})],1)]),a("tr",[a("td",[t._v("\n                  AndroidTV\n                ")]),a("td",[a("b-form-input",{attrs:{type:"text"},model:{value:t.androidtv,callback:function(e){t.androidtv=e},expression:"androidtv"}})],1)]),a("tr",[a("td",[t._v("\n                  iOS\n                ")]),a("td",[a("b-form-input",{attrs:{type:"text"},model:{value:t.ios,callback:function(e){t.ios=e},expression:"ios"}})],1)]),a("tr",[a("td",[t._v("\n                  AppleTV\n                ")]),a("td",[a("b-form-input",{attrs:{type:"text"},model:{value:t.appletv,callback:function(e){t.appletv=e},expression:"appletv"}})],1)]),a("tr",[a("td",[t._v("\n                  Web\n                ")]),a("td",[a("b-form-input",{attrs:{type:"text"},model:{value:t.web,callback:function(e){t.web=e},expression:"web"}})],1)])])])],1)],1)],1)],1)},or=[];function lr(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),a.push.apply(a,i)}return a}function cr(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?lr(a,!0).forEach(function(e){Object(B["a"])(t,e,a[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):lr(a).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))})}return t}var ur={name:"RemoteConfig",props:{caption:{type:String,default:"RemoteConfig"},hover:{type:Boolean,default:!0},small:{type:Boolean,default:!1},fixed:{type:Boolean,default:!1}},data:function(){return{dismissCountDown:0,alertMsg:"",android:null,ios:null,web:null,androidtv:null,appletv:null}},computed:cr({},Object(F["b"])(["loading"])),mounted:function(){this.onFetch()},methods:{getBadge:function(t){var e;switch(t){case!0:e="success";break;case!1:e="warning";break}return e},countDownChanged:function(t){this.dismissCountDown=t},showAlert:function(){this.dismissCountDown=5},onSave:function(){var t=this,e="/v3/console/remote_config",a=null,i=null,n=null,r=null,s=null;try{a=JSON.parse(this.ios),i=JSON.parse(this.android),n=JSON.parse(this.web),r=JSON.parse(this.androidtv),s=JSON.parse(this.appletv)}catch(l){console.log(l)}if(null==a||null==i)return this.alertCss="warning",this.alertMsg="這位大大，資料格式有誤",void this.showAlert();var o={};o["android"]=i,o["androidtv"]=r,o["ios"]=a,o["appletv"]=s,o["web"]=n,P.request("put",e,o).then(function(e){console.log(e),t.alertCss="success",t.alertMsg="儲存成功",t.showAlert()}).catch(function(e){t.alertCss="warning",t.alertMsg="這位大大，您的權限不足",t.showAlert()})},onFetch:function(){var t=this;P.request("get","/v3/console/remote_config").then(function(e){if(e.data&&e.data.data){var a=e.data.data;a.android&&(t.android=JSON.stringify(a.android)),a.androidtv&&(t.androidtv=JSON.stringify(a.androidtv)),a.ios&&(t.ios=JSON.stringify(a.ios)),a.appletv&&(t.appletv=JSON.stringify(a.appletv)),a.web&&(t.web=JSON.stringify(a.web))}})}}},dr=ur,pr=(a("6b43"),Object(_["a"])(dr,sr,or,!1,null,"61c08eb4",null)),mr=pr.exports,br=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("b-row",[a("b-col",{attrs:{cols:"12",xl:"12"}},[a("transition",{attrs:{name:"slide"}},[a("b-card",{attrs:{header:t.caption}},[a("form",{on:{submit:function(e){return e.preventDefault(),t.onFetch(e)}}},[a("b-row",[a("b-col",{attrs:{sm:"5"}},[a("b-form-group",{attrs:{description:"輸入時間可以過濾出該時間點看得到的 TVEvents"}},[a("flat-pickr",{attrs:{config:t.flatpickrConfig},model:{value:t.displayDate,callback:function(e){t.displayDate=e},expression:"displayDate"}})],1)],1),a("b-col",{attrs:{sm:"7"}},[a("b-form-group",{attrs:{description:"輸入字串可以過濾出 Vendors 或 Models 符合的 TVEvents"}},[a("b-input-group",[a("b-form-input",{attrs:{type:"text",id:"name"},model:{value:t.q,callback:function(e){t.q=e},expression:"q"}}),a("b-input-group-append",[a("b-button",{attrs:{variant:"primary"},on:{click:function(e){return t.onModalShow({})}}},[t._v("新增")])],1)],1)],1)],1)],1),a("b-row",[a("b-col",{attrs:{sm:"12"}},[a("b-alert",{attrs:{show:t.dismissCountDown,dismissible:"",variant:t.alertCss},on:{dismissed:function(e){t.dismissCountDown=0},"dismiss-count-down":t.countDownChanged}},[t._v("\n                    "+t._s(t.alertMsg)+"\n              ")])],1)],1)],1),t.items?a("b-table",{staticStyle:{"overflow-x":"auto"},attrs:{hover:t.hover,striped:t.striped,bordered:t.bordered,small:t.small,fixed:t.fixed,responsive:"sm",items:t.filterItems,fields:t.fields,"current-page":t.currentPage,"per-page":t.perPage},scopedSlots:t._u([{key:"action",fn:function(e){return[a("b-button",{on:{click:function(a){return t.onModalShow(e.item)}}},[a("i",{staticClass:"fa fa-edit"})]),t._v(" \n          ")]}},{key:"duration",fn:function(e){return[a("span",{staticStyle:{"white-space":"nowrap"}},[t._v("\n              "+t._s(t.getLocalTime(e.item.StartTime))+" ～ "+t._s(t.getLocalTime(e.item.EndTime))+"\n            ")])]}},{key:"vendor",fn:function(e){return[a("span",{staticStyle:{"white-space":"nowrap"}},[t._v("\n              "+t._s(e.item.Name)),a("br"),t._v("\n              "+t._s(e.item.Vendors)),a("br"),e.item.Models.length>90?a("span",[a("b",[t._v(t._s(e.item.Models.substring(0,90))+"...")]),a("br")]):a("span",[a("b",[t._v(t._s(e.item.Models))]),a("br")]),a("b",[t._v(t._s(e.item.Devices))]),a("br")])]}}],null,!1,3917835197)}):t._e()],1)],1)],1),a("b-modal",{ref:"formModal",attrs:{title:t.detailTitle,"no-close-on-backdrop":"","no-close-on-esc":""},on:{ok:t.onOK,cancel:t.onCancel},model:{value:t.showModal,callback:function(e){t.showModal=e},expression:"showModal"}},[a("form",{ref:"form",on:{submit:function(e){return e.stopPropagation(),e.preventDefault(),t.handleSubmit(e)}}},[a("b-form-group",{staticStyle:{color:"#fc8803"},attrs:{label:"Name（*）",description:"活動名稱",state:t.nameState,"invalid-feedback":"*必填欄位"}},[a("b-form-input",{attrs:{type:"text",state:t.nameState,required:""},model:{value:t.detailObj.Name,callback:function(e){t.$set(t.detailObj,"Name",e)},expression:"detailObj.Name"}})],1),a("b-form-group",{staticStyle:{color:"#fc8803"},attrs:{label:"Summary（*）",description:"活動文案",state:t.summaryState,"invalid-feedback":"*必填欄位"}},[a("b-form-textarea",{attrs:{rows:"6","max-rows":"12",state:t.summaryState,required:""},model:{value:t.detailObj.Summary,callback:function(e){t.$set(t.detailObj,"Summary",e)},expression:"detailObj.Summary"}})],1),a("b-form-group",{staticStyle:{color:"#fc8803"},attrs:{label:"Vendors（*）",description:"廠牌列表 請用 , 分隔",state:t.vendorState,"invalid-feedback":"*必填欄位"}},[a("b-form-textarea",{attrs:{rows:"6","max-rows":"12",state:t.vendorState,required:""},model:{value:t.detailObj.Vendors,callback:function(e){t.$set(t.detailObj,"Vendors",e)},expression:"detailObj.Vendors"}})],1),a("b-form-group",{staticStyle:{color:"#fc8803"},attrs:{label:"Models（*）",description:"型號列表 請用 , 分隔",state:t.modelState,"invalid-feedback":"*必填欄位"}},[a("b-form-textarea",{attrs:{rows:"6","max-rows":"12",state:t.modelState,required:""},model:{value:t.detailObj.Models,callback:function(e){t.$set(t.detailObj,"Models",e)},expression:"detailObj.Models"}})],1),a("b-form-group",{attrs:{label:"Devices",description:"裝置列表 請用 , 分隔"}},[a("b-form-textarea",{attrs:{rows:"6","max-rows":"12"},model:{value:t.detailObj.Devices,callback:function(e){t.$set(t.detailObj,"Devices",e)},expression:"detailObj.Devices"}})],1),a("b-row",[a("b-col",{attrs:{sm:"6"}},[a("b-form-group",{staticStyle:{color:"#fc8803"},attrs:{label:"Start Time（*）",description:"活動開始時間"}},[a("flat-pickr",{ref:"mystart",attrs:{config:t.flatpickrConfig},model:{value:t.StartTime,callback:function(e){t.StartTime=e},expression:"StartTime"}})],1)],1),a("b-col",{attrs:{sm:"6"}},[a("b-form-group",{staticStyle:{color:"#fc8803"},attrs:{label:"End Time（*）",description:"活動結束時間"}},[a("flat-pickr",{attrs:{config:t.flatpickrConfig},model:{value:t.EndTime,callback:function(e){t.EndTime=e},expression:"EndTime"}})],1)],1)],1),a("b-form-group",{staticStyle:{color:"#fc8803"},attrs:{label:"指定優惠碼（*）『請先產生優惠碼，活動開啟前要設定優惠碼來源』",description:"請指定要使用的優惠碼組",state:t.groupState,"invalid-feedback":"*必填欄位"}},[a("v-select",{attrs:{label:"description",options:t.couponGroupOptions,reduce:function(t){return t.id},selected:t.detailObj.RedeemGroupID,state:t.groupState,required:""},model:{value:t.detailObj.RedeemGroupID,callback:function(e){t.$set(t.detailObj,"RedeemGroupID",e)},expression:"detailObj.RedeemGroupID"}})],1)],1)])],1)},fr=[],hr={name:"TVEvents",props:{caption:{type:String,default:"TVEvents"},hover:{type:Boolean,default:!0},striped:{type:Boolean,default:!0},bordered:{type:Boolean,default:!1},small:{type:Boolean,default:!1},fixed:{type:Boolean,default:!1}},data:function(){return{q:"",items:[],showModal:!1,detailTitle:"",detailObj:{id:null},couponGroupOptions:[],StartTime:new Date,EndTime:new Date,displayDate:null,flatpickrConfig:{enableTime:!0,altFormat:"Y-m-d H:i",altInput:!0,dateFormat:"Z",allowInput:!0},nameState:null,summaryState:null,vendorState:null,modelState:null,groupState:null,uriState:null,alertCss:"",alertMsg:"",dismissCountDown:0,fields:[{key:"action",label:""},{key:"duration",label:"活動期間"},{key:"vendor",label:"Name / Vendors / Models / Devices"}],currentPage:1,perPage:0,totalRows:0}},computed:{filterItems:function(){var t=[];if(t=this.items,this.displayDate){var e=Date.parse(this.displayDate);t=t.filter(function(t){return Date.parse(t.StartTime)<=e&&Date.parse(t.EndTime)>=e})}if(""!==this.q){var a=this.q;t=t.filter(function(t){return t.Name&&t.Name.includes(a)||t.Vendors&&t.Vendors.includes(a)||t.Models&&t.Models.includes(a)})}return t}},mounted:function(){this.$route.query.q&&(this.q=this.$route.query.q),this.onFetch()},methods:{getBadge:function(t){var e;switch(t){case!0:e="success";break;case!1:e="warning";break}return e},getLocalTime:function(t){return new Date(t).toLocaleString("default",{hour12:!1,year:"numeric",month:"short",day:"2-digit",hour:"numeric",minute:"numeric"})},getCouponGroupsOptions:function(){var t=this;P.request("get","/v3/console/tvevents_coupon_groups").then(function(e){e.data&&e.data.data&&e.data.data.groups?(t.couponGroupOptions=e.data.data.groups,console.log("coupon groups",e.data.data.groups)):console.log("coupon groups not found")})},resetFormStates:function(){this.nameState=null,this.summaryState=null,this.vendorState=null,this.modelState=null,this.groupState=null},resetDetailObj:function(){this.detailObj={id:null}},countDownChanged:function(t){this.dismissCountDown=t},showAlert:function(){this.dismissCountDown=5},onFetch:function(){var t=this;P.request("get","/v3/console/tvevents").then(function(e){e.data&&e.data.data&&e.data.data.tvevents&&(t.items=e.data.data.tvevents,console.log("tvevents",e.data.data.tvevents))})},onModalShow:function(t){this.detailObj=Object.assign({},t),this.getCouponGroupsOptions(),t.ID?(this.detailTitle="修改 "+t.Name,this.StartTime=this.detailObj.StartTime,this.EndTime=this.detailObj.EndTime,this.detailObj.RedeemGroupID=parseInt(this.detailObj.RedeemGroupID,10)):(this.detailTitle="新增 TVEvent",this.StartTime=new Date,this.EndTime=new Date),this.showModal=!0},isValid:function(){var t=!0;return this.resetFormStates(),console.log("start validate"),this.detailObj.Name||(this.nameState="invalid",t=!1),this.detailObj.Summary||(this.summaryState="invalid",t=!1),this.detailObj.Vendors||(this.vendorState="invalid",t=!1),this.detailObj.Models||(this.modelState="invalid",t=!1),""===this.detailObj.RedeemGroupID&&(this.groupState="invalid",t=!1),t},handleSubmit:function(){var t=this,e=this,a=Date.parse(this.StartTime),i=Date.parse(this.EndTime);if(a>i&&(i=[a,a=i][0]),this.detailObj.StartTime=new Date(a).toISOString(),this.detailObj.EndTime=new Date(i).toISOString(),"undefined"===typeof this.detailObj.RedeemGroupID&&(this.detailObj.RedeemGroupID=""),this.detailObj.RedeemGroupID=this.detailObj.RedeemGroupID.toString(),this.isValid()){var n="/v3/console/tvevents";P.request("post",n,this.detailObj).then(function(a){console.log(a),t.detailObj.ID||(t.detailObj.ID=a.data.data.ID),e.alertCss="success",e.alertMsg="儲存成功",e.showAlert(),e.onFetch(),e.onCancel(),e.$refs.formModal.hide()}).catch(function(t){e.alertCss="warning",e.alertMsg="這位大大，您的權限不足",e.onCancel(),e.$refs.formModal.hide(),e.showAlert()})}},onOK:function(t){t.preventDefault(),this.handleSubmit()},onCancel:function(){this.resetFormStates(),this.resetDetailObj()}}},gr=hr,vr=(a("8bac"),Object(_["a"])(gr,br,fr,!1,null,"752442a5",null)),_r=vr.exports,yr=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("b-row",[a("b-col",{attrs:{cols:"12",xl:"12"}},[a("transition",{attrs:{name:"slide"}},[a("b-card",{attrs:{header:t.caption}},[a("b-row",[a("b-col",{attrs:{sm:"12"}},[a("form",{on:{submit:function(e){return e.preventDefault(),t.onFetch(e)}}},[a("b-form-group",{attrs:{description:"輸入要查詢的 coupon ID (序號) 或 GroupID (每組序號對應的群組 ID，也就是下面列表的 Id 按鈕，點即可進入列表)"}},[a("b-input-group",[a("b-form-input",{attrs:{type:"text",id:"name"},model:{value:t.q,callback:function(e){t.q=e},expression:"q"}}),a("b-input-group-append",[a("b-button",{attrs:{variant:"primary"},on:{click:t.onFetch}},[a("i",{staticClass:"fa",class:{"fa-refresh":t.loading,"fa-spin":t.loading}}),t._v("\n                Search\n                ")])],1),t._v("\n             \n            "),a("b-input-group-append",[a("b-button",{directives:[{name:"b-tooltip",rawName:"v-b-tooltip.hover",modifiers:{hover:!0}}],attrs:{variant:"warning",title:"您必須有對應的權限，否則無法新增修改"},on:{click:function(e){return t.onEdit({})}}},[t._v("新增序號")])],1)],1)],1)],1),a("b-alert",{attrs:{show:t.dismissCountDown,dismissible:"",variant:t.alertCss},on:{dismissed:function(e){t.dismissCountDown=0},"dismiss-count-down":t.countDownChanged}},[t._v("\n           "+t._s(t.alertMsg)+"\n        ")])],1),a("b-modal",{ref:"formModal",attrs:{size:"lg",title:t.detailTitle},on:{ok:t.onOK,cancel:t.onCancel},model:{value:t.showDetail,callback:function(e){t.showDetail=e},expression:"showDetail"}},[a("b-form-group",[a("b-row",[a("b-col",[a("b-form-group",{attrs:{label:"Prefix",description:"兩個大寫字母，全家販售序號固定為FM"}},[t.detailObj.id?a("b-form-input",{attrs:{readonly:"",type:"text",state:t.formState.prefix},model:{value:t.detailObj.prefix,callback:function(e){t.$set(t.detailObj,"prefix",e)},expression:"detailObj.prefix"}}):a("b-form-input",{attrs:{type:"text",state:t.formState.prefix},model:{value:t.detailObj.prefix,callback:function(e){t.$set(t.detailObj,"prefix",e)},expression:"detailObj.prefix"}}),a("b-form-invalid-feedback",{attrs:{state:t.formState.prefix}},[t._v("\n                      兩個大寫字母，全家販售序號專用固定為FM\n                    ")])],1)],1),a("b-col",[a("b-form-group",{attrs:{label:"價格"}},[a("b-form-input",{attrs:{type:"number",state:t.formState.price},model:{value:t.detailObj.price,callback:function(e){t.$set(t.detailObj,"price",e)},expression:"detailObj.price"}}),a("b-form-invalid-feedback",{attrs:{state:t.formState.price}},[t._v("\n                      不能小於0\n                    ")])],1)],1),a("b-col",[a("b-form-group",{attrs:{label:"未稅價格"}},[a("b-form-input",{attrs:{state:t.formState.price_no_tax,type:"number"},model:{value:t.detailObj.price_no_tax,callback:function(e){t.$set(t.detailObj,"price_no_tax",e)},expression:"detailObj.price_no_tax"}}),a("b-form-invalid-feedback",{attrs:{state:t.formState.price_no_tax}},[t._v("\n                      不能小於0\n                    ")])],1)],1),a("b-col",[a("b-form-group",{attrs:{label:"手續費"}},[a("b-form-input",{attrs:{type:"number"},model:{value:t.detailObj.fee,callback:function(e){t.$set(t.detailObj,"fee",e)},expression:"detailObj.fee"}}),a("b-form-invalid-feedback",{attrs:{state:t.formState.fee}},[t._v("\n                      不能小於0\n                    ")])],1)],1),a("b-col",[a("b-form-group",{attrs:{label:"疊加限制次數"}},[a("b-form-input",{attrs:{type:"number"},model:{value:t.detailObj.usage_limit_per_user,callback:function(e){t.$set(t.detailObj,"usage_limit_per_user",e)},expression:"detailObj.usage_limit_per_user"}})],1)],1)],1),a("b-form-group",{attrs:{label:"週期"}},[a("b-row",{staticClass:"my-1"},[a("b-col",{attrs:{sm:"6"}},[a("b-form-input",{attrs:{type:"number"},model:{value:t.detailObj.duration_value,callback:function(e){t.$set(t.detailObj,"duration_value",e)},expression:"detailObj.duration_value"}}),a("b-form-invalid-feedback",{attrs:{state:t.formState.duration_unit}},[t._v("\n                        沒有設定週期\n                      ")])],1),a("b-col",{attrs:{sm:"6"}},[a("b-form-select",{attrs:{state:t.formState.duration_unit,options:["","day","mon","year"]},model:{value:t.detailObj.duration_unit,callback:function(e){t.$set(t.detailObj,"duration_unit",e)},expression:"detailObj.duration_unit"}}),a("b-form-invalid-feedback",{attrs:{state:t.formState.duration_unit}},[t._v("\n                        沒有設定週期\n                      ")])],1)],1)],1),a("b-form-group",{attrs:{label:"免費週期",description:"免費週期"}},[a("b-row",{staticClass:"my-1"},[a("b-col",{attrs:{sm:"6"}},[a("b-form-input",{attrs:{type:"number"},model:{value:t.detailObj.free_duration_value,callback:function(e){t.$set(t.detailObj,"free_duration_value",e)},expression:"detailObj.free_duration_value"}})],1),a("b-col",{attrs:{sm:"6"}},[a("b-form-select",{attrs:{state:t.formState.free_duration_unit,options:["","day","mon","year"]},model:{value:t.detailObj.free_duration_unit,callback:function(e){t.$set(t.detailObj,"free_duration_unit",e)},expression:"detailObj.free_duration_unit"}}),a("b-form-invalid-feedback",{attrs:{state:t.formState.free_duration_unit}},[t._v("\n                        請設定免費週期的時間及正確的數值\n                      ")])],1)],1)],1),a("b-row",[a("b-col",{attrs:{sm:"6"}},[a("b-form-group",{attrs:{label:"生效日期"}},[a("flat-pickr",{attrs:{config:t.flatpickrConfig},model:{value:t.detailObj.valid_since,callback:function(e){t.$set(t.detailObj,"valid_since",e)},expression:"detailObj.valid_since"}})],1)],1),a("b-col",{attrs:{sm:"6"}},[a("b-form-group",{attrs:{label:"有效期限"}},[a("flat-pickr",{attrs:{config:t.flatpickrConfig},model:{value:t.detailObj.expires_at,callback:function(e){t.$set(t.detailObj,"expires_at",e)},expression:"detailObj.expires_at"}})],1)],1)],1),a("b-form-group",{attrs:{label:"說明",description:"JIRA票號連結前請加一個半形分號"}},[a("b-form-textarea",{attrs:{placeholder:"請輸入敘述;JIRA 票 URL 連結",rows:"3","max-rows":"6"},model:{value:t.detailObj.description,callback:function(e){t.$set(t.detailObj,"description",e)},expression:"detailObj.description"}}),a("b-form-invalid-feedback",{attrs:{state:t.formState.description}},[t._v("\n                  必須說明敘述\n                ")])],1),a("b-form-group",{attrs:{label:"產品ID",description:"有價序號，及序號是綁定某特定產品ID的銷售序號才需要設定，有價序號，只能設定 6，其他綁定產品，請設定產品ID (不能為保留的 4 及 6)"}},[a("b-form-input",{attrs:{state:t.formState.product_id,type:"number"},on:{change:t.onChangeProductID},model:{value:t.detailObj.product_id,callback:function(e){t.$set(t.detailObj,"product_id",e)},expression:"detailObj.product_id"}}),a("b-form-invalid-feedback",{attrs:{state:t.formState.product_id}},[t._v("\n                  免費序號請設定產品 ID 4 價格為 0，有價序號請設定產品 ID 6 價格大於 0 ，其他產品 ID 為綁定某特定產品 ID的銷售序號，週期，及免費週期留白，不需要設定\n                ")])],1),a("b-form-checkbox",{model:{value:t.detailObj.allow_reuse,callback:function(e){t.$set(t.detailObj,"allow_reuse",e)},expression:"detailObj.allow_reuse"}},[t._v("序號可以重複被使用")]),t.detailObj.id?t._e():a("b-row",[a("b-col",[a("b-form-checkbox",{on:{change:t.onChangeFami},model:{value:t.detailObj.is_for_fami_port,callback:function(e){t.$set(t.detailObj,"is_for_fami_port",e)},expression:"detailObj.is_for_fami_port"}},[t._v("是全家超商銷售序號")]),a("b-form-group",{attrs:{label:"序號數量"}},[a("b-form-input",{attrs:{state:t.formState.count,type:"number"},model:{value:t.detailObj.count,callback:function(e){t.$set(t.detailObj,"count",e)},expression:"detailObj.count"}}),a("b-form-invalid-feedback",{attrs:{state:t.formState.count}},[t._v("\n                      不能小於0\n                    ")])],1)],1)],1),a("b-form-group",{attrs:{label:"活動群組",description:"同一個活動群組的redeem不可被相同user使用，留空則無限制"}},[a("b-form-input",{attrs:{type:"text"},model:{value:t.detailObj.campaign_group,callback:function(e){t.$set(t.detailObj,"campaign_group",e)},expression:"detailObj.campaign_group"}})],1)],1)],1)],1),t.items?a("b-table",{attrs:{hover:t.hover,striped:t.striped,bordered:t.bordered,small:t.small,fixed:t.fixed,responsive:"sm",items:t.filterItems,fields:t.fields,"current-page":t.currentPage,"per-page":t.perPage},scopedSlots:t._u([{key:"is_active",fn:function(e){return[a("b-button",{on:{click:function(a){return t.onEdit(e.item)}}},[a("i",{staticClass:"fa fa-edit"})]),t._v(" \n          "),e.item.is_active?a("strong",[t._v("✔")]):t._e()]}},{key:"id",fn:function(e){return[a("b-button",{directives:[{name:"b-tooltip",rawName:"v-b-tooltip.hover",modifiers:{hover:!0}}],attrs:{variant:"primary",title:"點擊進入此 GroupID 詳細資料頁面，及所有序號列表"},on:{click:function(a){return t.rowClicked(e.item.id)}}},[t._v(t._s(e.item.id))])]}},{key:"description",fn:function(e){return[t._v("\n          "+t._s(e.item.description)+"\n          "),e.item.jira_link?a("span",[a("br"),a("a",{attrs:{href:e.item.jira_link,_target:"blank"}},[t._v("Jira 需求票")])]):t._e(),e.item.trello_link?a("span",[a("br"),a("a",{attrs:{href:e.item.trello_link,_target:"blank"}},[t._v("Trello 需求票")])]):t._e()]}},{key:"apply",fn:function(e){return[t._v("\n          "+t._s(e.item.applyStr)+"\n        ")]}},{key:"duration",fn:function(e){return[t._v("\n          "+t._s(e.item.durationStr)+"\n        ")]}},{key:"start_date",fn:function(e){return[t._v("\n          "+t._s(new Date(e.item.start_date).toISOString().split("T")[0])+"\n        ")]}},{key:"end_date",fn:function(e){return[t._v("\n          "+t._s(new Date(e.item.end_date).toISOString().split("T")[0])+"\n        ")]}},{key:"is_free",fn:function(e){return[e.item.is_free?a("strong",[t._v("免費")]):a("strong",[t._v("有價")])]}},{key:"usage_limit_per_user",fn:function(e){return[e.item.usage_limit_per_user?a("strong",[t._v(t._s(e.item.usage_limit_per_user))]):a("strong",[t._v("無")])]}}],null,!1,1909747800)}):t._e()],1)],1)],1)],1)},wr=[];function Or(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),a.push.apply(a,i)}return a}function kr(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?Or(a,!0).forEach(function(e){Object(B["a"])(t,e,a[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):Or(a).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))})}return t}var jr={name:"Redeem",props:{caption:{type:String,default:"Coupon Status"},hover:{type:Boolean,default:!0},striped:{type:Boolean,default:!0},bordered:{type:Boolean,default:!1},small:{type:Boolean,default:!1},fixed:{type:Boolean,default:!1}},data:function(){return{items:[],showDetail:!1,detailTitle:"Hi detail",detailObj:{id:null,prefix:""},flatpickrConfig:{enableTime:!0,altFormat:"Y-m-d H:i",altInput:!0,dateFormat:"Z",time_24hr:!0,allowInput:!0},alertCss:"",alertMsg:"",dismissCountDown:0,formState:{description:null,price:null,price_no_tax:null,fee:null,is_for_fami_port:null,prefix:null,count:null,duration:null,duration_unit:null,free_duration_unit:null},q:"",fields:[{key:"is_active",label:"有效"},{key:"id"},{key:"description",label:"說明"},{key:"used_count",label:"兌換數量"},{key:"total_count",label:"總數"},{key:"apply",label:"兌換率"},{key:"duration",label:"兌換天數"},{key:"start_date",label:"生效日期"},{key:"end_date",label:"有效期限"},{key:"is_free",label:"價格"},{key:"usage_limit_per_user",label:"疊加限制"}],currentPage:1,perPage:0,totalRows:0}},computed:kr({filterItems:function(){this.q;for(var t=0;t<this.items.length;t++)this.items[t].durationStr=this.items[t].duration,this.items[t].durationStr.includes("days")&&(this.items[t].durationStr=this.items[t].durationStr.replace("days","天")),(this.items[t].durationStr.includes("mons")||this.items[t].durationStr.includes("mon"))&&(this.items[t].durationStr=this.items[t].durationStr.replace("mons","月"),this.items[t].durationStr=this.items[t].durationStr.replace("mon","月")),this.items[t].durationStr.includes("year")&&(this.items[t].durationStr=this.items[t].durationStr.replace("year","年")),this.items[t].is_active&&(this.items[t]._rowVariant="success"),this.items[t].allow_reuse?this.items[t].applyStr="NA":this.items[t].applyStr=(this.items[t].used_count/this.items[t].total_count*100).toFixed(1)+"%";return this.items}},Object(F["b"])(["loading"])),mounted:function(){console.log("Redeem Mounted"),this.$route.query.q&&(this.q=this.$route.query.q),this.onFetch()},methods:{getRowCount:function(t){return t.length},forwardLink:function(t){return"/redeem/detail?q=".concat(t.toString())},rowClicked:function(t){var e=this.forwardLink(t);this.$router.push({path:e})},onCancel:function(){this.detailObj={id:null}},onEdit:function(t){if(this.resetFormStates(),this.detailObj=Object.assign({id:null,prefix:""},t),t.id){this.detailTitle="修改 "+t.description;var e=this.detailObj.duration.split(" "),a=this.detailObj.free_duration.split(" ");2===e.length&&(this.detailObj.duration_value=parseInt(e[0]),this.detailObj.duration_unit=e[1].replace("s","")),2===a.length&&(this.detailObj.free_duration_value=parseInt(a[0]),this.detailObj.free_duration_unit=a[1].replace("s","")),this.detailObj.description=t.description+";"+t.jira_link,this.detailObj.valid_since=t.start_date,this.detailObj.expires_at=t.end_date}else this.detailTitle="新增序號",this.detailObj.price=0,this.detailObj.price_no_tax=0,this.detailObj.fee=0,this.detailObj.count=0,this.detailObj.is_for_fami_port=!1;this.showDetail=!0},onChangeFami:function(t){t&&(this.detailObj.prefix="FM")},onChangeProductID:function(t){4!=t&&6!=t&&(this.detailObj.duration_value=null,this.detailObj.free_duration_value=null)},isValid:function(){var t=!0;return this.resetFormStates(),console.log("start validate"),this.detailObj.description||(this.formState.description="invalid",t=!1),this.detailObj.price&&this.detailObj.price<0&&(this.formState.price="invalid",t=!1),this.detailObj.is_for_fami_port&&this.detailObj.price<=0&&(this.formState.price="invalid",t=!1),/^[A-Z]{2}/.test(this.detailObj.prefix)||(this.formState.prefix="invalid",t=!1),this.detailObj.is_for_fami_port||"FM"!=this.detailObj.prefix||(this.formState.prefix="invalid",t=!1),this.detailObj.is_for_fami_port&&"FM"!=this.detailObj.prefix&&(this.formState.prefix="invalid",t=!1),this.detailObj.price_no_tax&&this.detailObj.price_no_tax<0&&(this.formState.price_no_tax="invalid",t=!1),this.detailObj.id||this.detailObj.count&&!(this.detailObj.count<=0)||(this.formState.count="invalid",t=!1),this.detailObj.duration_value&&(this.detailObj.duration_value<=0||!this.detailObj.duration_unit)&&(this.formState.duration_unit="invalid",t=!1),this.detailObj.free_duration_value&&(this.detailObj.free_duration_value<=0||!this.detailObj.free_duration_unit)&&(this.formState.free_duration_unit="invalid",t=!1),this.detailObj.product_id&&(4==this.detailObj.product_id&&this.detailObj.price>0&&(this.formState.product_id="invalid",t=!1),6==this.detailObj.product_id&&this.detailObj.price<=0&&(this.formState.product_id="invalid",t=!1)),t},onOK:function(t){if(t.preventDefault(),this.isValid()){var e=this;this.detailObj.duration_value&&""!=this.detailObj.duration_unit?this.detailObj.duration=this.detailObj.duration_value+" "+this.detailObj.duration_unit+"s":this.detailObj.duration="00:00:00",this.detailObj.free_duration_value&&""!=this.detailObj.free_duration_unit&&this.detailObj.free_duration_value>0?this.detailObj.free_duration=this.detailObj.free_duration_value+" "+this.detailObj.free_duration_unit+"s":this.detailObj.free_duration="00:00:00",this.detailObj.price||(this.detailObj.price=0),this.detailObj.price_no_tax||(this.detailObj.price_no_tax=0),this.detailObj.fee||(this.detailObj.fee=0),this.detailObj.usage_limit_per_user||(this.detailObj.usage_limit_per_user=null),this.detailObj.price=parseInt(this.detailObj.price),this.detailObj.price_no_tax=parseInt(this.detailObj.price_no_tax),this.detailObj.fee=parseInt(this.detailObj.fee),this.detailObj.count=parseInt(this.detailObj.count);var a="/v3/console/redeem";P.request("post",a,this.detailObj).then(function(t){e.onCancel(),e.$refs.formModal.hide(),e.onFetch()}).catch(function(){e.alertCss="warning",e.alertMsg="儲存失敗",e.showAlert(),e.$refs.formModal.hide(),e.onFetch()})}},resetFormStates:function(){this.formState={description:null,price:null,price_no_tax:null,fee:null,is_for_fami_port:null,prefix:null,count:null,duration:null,duration_unit:null,free_duration_unit:null}},countDownChanged:function(t){this.dismissCountDown=t},showAlert:function(){this.dismissCountDown=5},onFetch:function(){var t=this;if(""!==this.q)return console.log("To Detail"),void this.$router.push({path:"/redeem/detail",query:{q:this.q}});P.request("get","/v3/console/redeem?q="+this.q).then(function(e){e.data&&e.data.data&&e.data.data.coupons&&(t.items=e.data.data.coupons)})}}},xr=jr,Sr=(a("ff76"),Object(_["a"])(xr,yr,wr,!1,null,"4a689866",null)),Dr=Sr.exports,Cr=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("b-row",[a("b-col",{attrs:{cols:"12",xl:"12"}},[a("transition",{attrs:{name:"slide"}},[a("b-card",{attrs:{header:t.caption}},[a("b-row",[a("b-col",{attrs:{sm:"12"}},[a("form",{on:{submit:function(e){return e.preventDefault(),t.onFetch(e)}}},[a("b-form-group",{attrs:{description:"輸入 Coupon Group ID (序號)"}},[a("b-input-group",[a("b-form-input",{attrs:{type:"text",id:"name"},model:{value:t.q,callback:function(e){t.q=e},expression:"q"}}),a("b-input-group-append",[a("b-button",{attrs:{variant:"primary"},on:{click:t.onFetch}},[t._v("Submit")])],1),a("a",{attrs:{id:"download",href:"",download:"code.csv"}},[a("b-button",{directives:[{name:"b-tooltip",rawName:"v-b-tooltip.hover",modifiers:{hover:!0}}],attrs:{title:"下載"}},[a("span",[a("i",{staticClass:"fa fa-download"})])])],1)],1)],1)],1)])],1),t.coupon_group?a("b-table",{attrs:{hover:t.hover,striped:t.striped,bordered:t.bordered,small:t.small,fixed:t.fixed,responsive:"sm",items:t.filterItems,fields:t.fields},scopedSlots:t._u([{key:"is_active",fn:function(e){return[e.item.is_active?a("strong",[t._v("✔")]):t._e()]}},{key:"id",fn:function(e){return[t._v("\n          "+t._s(e.item.id)+"\n        ")]}},{key:"duration",fn:function(e){return[t._v("\n          "+t._s(e.item.durationStr)+"\n        ")]}},{key:"redeem_rate",fn:function(e){return[t._v("\n          "+t._s(e.item.redeemRateStr)+"\n        ")]}},{key:"is_free",fn:function(e){return[e.item.is_free?a("strong",[t._v("免費")]):a("strong",[t._v("有價")])]}},{key:"usage_limit_per_user",fn:function(e){return[e.item.usage_limit_per_user?a("strong",[t._v(t._s(e.item.usage_limit_per_user))]):a("strong",[t._v("無")])]}}],null,!1,968356066)},[a("template",{slot:"action"},[a("b-button",{directives:[{name:"b-tooltip",rawName:"v-b-tooltip.hover",modifiers:{hover:!0}}],attrs:{title:"註銷"},on:{click:function(e){return t.showBatchUpdateModal("revoke")}}},[a("span",[a("i",{staticClass:"fa fa-ban"})])]),t._v("\n           \n          "),a("b-button",{directives:[{name:"b-tooltip",rawName:"v-b-tooltip.hover",modifiers:{hover:!0}}],attrs:{title:"啟用"},on:{click:function(e){return t.showBatchUpdateModal("active")}}},[a("span",[a("i",{staticClass:"fa fa-check-circle"})])])],1)],2):t._e(),t.items?a("b-table",{attrs:{hover:t.hover,striped:t.striped,bordered:t.bordered,small:t.small,fixed:t.fixed,responsive:"sm",items:t.filterCodes,fields:t.codefields,"current-page":t.currentPage,"per-page":t.perPage},scopedSlots:t._u([{key:"code",fn:function(e){return[a("span",{class:{badge:!0,"badge-warning":e.item.code==t.q}},[t._v(" "+t._s(e.item.code))])]}},{key:"user_id",fn:function(e){return[e.item.user_id?a("strong",{on:{click:function(a){return t.rowClicked(e.item.user_id)}}},[t._v(t._s(e.item.user_id))]):a("strong",[t._v("未使用")])]}},{key:"updated_at",fn:function(e){return[e.item.user_id&&e.item.updated_at?a("span",[t._v(t._s(e.item.updated_at))]):t._e()]}},{key:"revoked_at",fn:function(e){return[e.item.revoked_at?a("span",[t._v(t._s(e.item.revoked_at))]):t._e()]}},{key:"action",fn:function(e){return[e.item.user_id?t._e():a("span",[a("b-button",{directives:[{name:"b-tooltip",rawName:"v-b-tooltip.hover",modifiers:{hover:!0}}],attrs:{title:e.item.action_btn_title},on:{click:function(a){return t.showSingleUpdateModal(e.item)}}},[e.item.revoked_at?a("span",[a("i",{staticClass:"fa fa-check-circle"})]):a("span",[a("i",{staticClass:"fa fa-ban"})])])],1)]}}],null,!1,2001537764)}):t._e(),a("nav",[a("b-pagination",{attrs:{size:"sm","total-rows":t.getRowCount(t.filterCodes),"per-page":t.perPage,"prev-text":"Prev","next-text":"Next","hide-goto-end-buttons":""},model:{value:t.currentPage,callback:function(e){t.currentPage=e},expression:"currentPage"}})],1)],1)],1)],1),a("b-modal",{ref:"formModal",attrs:{title:t.modalTitle},on:{ok:t.onOK,cancel:t.onCancel},model:{value:t.showModal,callback:function(e){t.showModal=e},expression:"showModal"}},[a("form",{ref:"form",on:{submit:function(e){return e.stopPropagation(),e.preventDefault(),t.onOK(e)}}},[a("b-form-group",{staticStyle:{color:"#fc8803"},attrs:{label:"原因（*）",state:t.reasonState,"invalid-feedback":"*必填欄位"}},[a("b-form-input",{attrs:{type:"text",state:t.reasonState,required:""},model:{value:t.detailObj.reason,callback:function(e){t.$set(t.detailObj,"reason",e)},expression:"detailObj.reason"}})],1)],1)])],1)},Pr=[],qr={name:"RedeemDetail",props:{caption:{type:String,default:"Redeem Detail"},hover:{type:Boolean,default:!0},striped:{type:Boolean,default:!0},bordered:{type:Boolean,default:!1},small:{type:Boolean,default:!1},fixed:{type:Boolean,default:!1}},data:function(){return{items:[],coupon_group:null,showModal:!1,detailObj:{id:null},reasonState:null,modalTitle:"",updateMode:"",batchAction:"",q:"",fields:[{key:"is_active",label:"有效"},{key:"id",label:"Group"},{key:"description",label:"說明"},{key:"used_and_total_count",label:"兌換數量/總數"},{key:"redeem_rate",label:"兌換率"},{key:"duration",label:"兌換天數"},{key:"start_date",label:"生效日期"},{key:"end_date",label:"有效期限"},{key:"is_free",label:"價格"},{key:"usage_limit_per_user",label:"疊加限制"},{key:"action",label:"註銷/啟用"}],codefields:[{key:"code",label:"兌換碼"},{key:"user_id"},{key:"updated_at",label:"使用時間"},{key:"revoked_at",label:"註銷時間"},{key:"action",label:"註銷/啟用"}],currentPage:1,perPage:100,totalRows:0}},computed:{filterItems:function(){this.q;if(this.coupon_group){this.coupon_group.duration.includes("day")?this.coupon_group.durationStr=this.coupon_group.duration.split(" ")[0]+"天":this.coupon_group.duration.includes("mon")?this.coupon_group.durationStr=this.coupon_group.duration.split(" ")[0]+"月":this.coupon_group.duration.includes("year")&&(this.coupon_group.durationStr=this.coupon_group.duration.split(" ")[0]+"年"),this.coupon_group.is_active&&(this.coupon_group._rowVariant="success"),this.items.length>0&&(this.coupon_group.total_count=this.items.length);for(var t=0;t<this.items.length;t++){var e=this.items[t];e.is_active=!e.revoked_at,e.action_btn_title=e.is_active?"註銷":"啟用",e.user_id&&this.coupon_group.used_count++}this.coupon_group.used_and_total_count=this.coupon_group.used_count+"/"+this.coupon_group.total_count,this.coupon_group.allow_reuse?this.coupon_group.redeemRateStr="NA":this.coupon_group.redeemRateStr=(this.coupon_group.used_count/this.coupon_group.total_count*100).toFixed(1)+"%"}return[this.coupon_group]},filterCodes:function(){var t=this.q;return parseInt(t)?this.items:this.items.filter(function(e){return e.code==t})}},mounted:function(){console.log("RedeemDetail Mounted"),this.$route.query.q&&(this.q=this.$route.query.q),this.onFetch()},methods:{getBadge:function(t){var e;switch(t){case"premium":e="success";break;case"freetrial":e="primary";break;case"expired":e="warning";break;case"general":e="success";break;case"test":e="warning";break;case"pr":e="secondary";break;case"classmate":e="secondary";break;case"prime":e="success";break}return e},getRowCount:function(t){return t.length},userOrderLink:function(t){return"/user/order?q=".concat(t.toString())},rowClicked:function(t){var e=this.userOrderLink(t);console.log(e),this.$router.push({path:e})},onFetch:function(){var t=this;P.request("get","/v3/console/redeemdetail?q="+this.q).then(function(e){e.data&&e.data.data&&(e.data.data.coupon&&(t.coupon_group=e.data.data.coupon),e.data.data.items&&(t.items=e.data.data.items,t.cookDownload()))})},showSingleUpdateModal:function(t){this.detailObj=Object.assign({},t),this.modalTitle=(t.is_active?"註銷":"啟用")+"兌換碼 "+t.code,this.updateMode="single",this.showModal=!0},showBatchUpdateModal:function(t){this.modalTitle=("revoke"===t?"註銷":"啟用")+"所有兌換碼",this.updateMode="batch",this.batchAction=t,this.showModal=!0},cookDownload:function(){if(this.items){var t=!1,e=document.getElementById("download");t=/^FM/.test(this.items[0].code);var a="";if(t)for(var i=new Date(this.coupon_group.end_date),n=i.getFullYear()+("00"+(i.getMonth()+1)).slice(-2)+("00"+i.getDate()).slice(-2),r=0;r<this.items.length;r++){var s=("00000000000000000000"+this.items[r].id).slice(-20)+","+this.items[r].code+","+n+"\n";a+=s}else for(r=0;r<this.items.length;r++){s=this.items[r].code+"\n";a+=s}if(a.length>0){var o=window.URL.createObjectURL(new Blob([a],{type:"application/octet-binary"}));e.href=o}}},toggleCouponCodeStatus:function(){var t=this;if(this.isValid()){var e=this.detailObj,a=e.is_active?"revoke":"active",i="/v3/console/redeemdetail/code/"+e.id+"/"+a;P.request("put",i,{reason:e.reason}).then(function(e){200===e.status?(t.onFetch(),t.onCancel(),t.$refs.formModal.hide()):(t.onCancel(),t.$refs.formModal.hide(),alert("更新失敗"))})}},updateCouponCodesStatusByGroup:function(t,e){var a=this;if(this.isValid()){var i=this.detailObj.reason,n="/v3/console/redeemdetail/group/"+t+"/"+e;P.request("put",n,{reason:i}).then(function(t){200===t.status?(a.onFetch(),a.onCancel(),a.$refs.formModal.hide()):(a.onCancel(),a.$refs.formModal.hide(),alert("更新失敗"))})}},onOK:function(t){switch(t.preventDefault(),console.log(this.updateMode),this.updateMode){case"single":this.toggleCouponCodeStatus();break;case"batch":this.updateCouponCodesStatusByGroup(this.coupon_group.id,this.batchAction);break;default:console.log("Invalid update mode: ",this.updateMode)}},onCancel:function(){this.resetFormStates(),this.resetDetailObj()},resetDetailObj:function(){this.detailObj={id:null}},resetFormStates:function(){this.reasonState=null},isValid:function(){var t=!0;return this.resetFormStates(),console.log("start validate"),this.detailObj.reason||(this.reasonState="invalid",t=!1),t}}},Mr=qr,Ar=(a("5d59"),Object(_["a"])(Mr,Cr,Pr,!1,null,"208fe993",null)),Tr=Ar.exports,Ir=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("b-row",[a("b-col",{attrs:{cols:"12",xl:"12"}},[a("transition",{attrs:{name:"slide"}},[a("b-card",{attrs:{header:t.caption}},[a("b-row",[a("b-col",{attrs:{sm:"12"}},[a("b-form-group",{attrs:{description:"編輯確定後，直接按 Save All ，會立刻生效"}},[a("b-input-group",[a("b-button",{attrs:{variant:"primary"},on:{click:t.onFetch}},[a("i",{staticClass:"fa",class:{"fa-refresh":t.loading,"fa-spin":t.loading}}),t._v("\n                Reload\n                ")]),t._v(" \n              "),a("b-button",{attrs:{variant:"primary"},on:{click:t.onSave}},[a("i",{staticClass:"fa",class:{"fa-refresh":t.loading,"fa-spin":t.loading}}),t._v("\n                Save All\n                ")])],1)],1),a("b-alert",{attrs:{show:t.dismissCountDown,dismissible:"",variant:"warning"},on:{dismissed:function(e){t.dismissCountDown=0},"dismiss-count-down":t.countDownChanged}},[t._v("\n                 "+t._s(t.alertMsg)+"\n          ")])],1)],1),a("table",{staticClass:"table b-table table-striped",attrs:{"aria-colcount":"1"}},[a("thead",{},[a("tr",[a("th",{attrs:{"aria-colindex":"1"}},[t._v("平台")]),a("th",[t._v("Current Version")]),a("th",[t._v("Update Message")]),a("th",[t._v("Review Version")]),a("th",[t._v("Must Update")])])]),a("tbody",{},[a("tr",[a("td",[t._v(" Android ")]),a("td",[a("b-form-input",{attrs:{type:"text"},model:{value:t.android.current_version,callback:function(e){t.$set(t.android,"current_version",e)},expression:"android.current_version"}})],1),a("td",[a("b-form-input",{attrs:{type:"text"},model:{value:t.android.update_message,callback:function(e){t.$set(t.android,"update_message",e)},expression:"android.update_message"}})],1),a("td",[a("b-form-input",{attrs:{type:"text"},model:{value:t.android.review_version,callback:function(e){t.$set(t.android,"review_version",e)},expression:"android.review_version"}})],1),a("td",[a("b-form-checkbox",{attrs:{checked:!0},model:{value:t.android.must_update,callback:function(e){t.$set(t.android,"must_update",e)},expression:"android.must_update"}},[t._v("\n                    Must Update\n                    ")])],1)]),a("tr",[a("td",[t._v("\n                  iOS\n                ")]),a("td",[a("b-form-input",{attrs:{type:"text"},model:{value:t.ios.current_version,callback:function(e){t.$set(t.ios,"current_version",e)},expression:"ios.current_version"}})],1),a("td",[a("b-form-input",{attrs:{type:"text"},model:{value:t.ios.update_message,callback:function(e){t.$set(t.ios,"update_message",e)},expression:"ios.update_message"}})],1),a("td",[a("b-form-input",{attrs:{type:"text"},model:{value:t.ios.review_version,callback:function(e){t.$set(t.ios,"review_version",e)},expression:"ios.review_version"}})],1),a("td",[a("b-form-checkbox",{attrs:{checked:!0},model:{value:t.ios.must_update,callback:function(e){t.$set(t.ios,"must_update",e)},expression:"ios.must_update"}},[t._v("\n                    Must Update\n                    ")])],1)]),a("tr",[a("td",[t._v("\n                  AppleTV\n                ")]),a("td",[a("b-form-input",{attrs:{type:"text"},model:{value:t.appletv.current_version,callback:function(e){t.$set(t.appletv,"current_version",e)},expression:"appletv.current_version"}})],1),a("td",[a("b-form-input",{attrs:{type:"text"},model:{value:t.appletv.update_message,callback:function(e){t.$set(t.appletv,"update_message",e)},expression:"appletv.update_message"}})],1),a("td",[a("b-form-input",{attrs:{type:"text"},model:{value:t.appletv.review_version,callback:function(e){t.$set(t.appletv,"review_version",e)},expression:"appletv.review_version"}})],1),a("td",[a("b-form-checkbox",{attrs:{checked:!0},model:{value:t.appletv.must_update,callback:function(e){t.$set(t.appletv,"must_update",e)},expression:"appletv.must_update"}},[t._v("\n                    Must Update\n                    ")])],1)])])])],1)],1)],1)],1)},$r=[];function Br(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),a.push.apply(a,i)}return a}function Fr(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?Br(a,!0).forEach(function(e){Object(B["a"])(t,e,a[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):Br(a).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))})}return t}var Er={name:"ServiceStatus",props:{caption:{type:String,default:"Service Status"},hover:{type:Boolean,default:!0},striped:{type:Boolean,default:!0},bordered:{type:Boolean,default:!1},small:{type:Boolean,default:!1},fixed:{type:Boolean,default:!1}},data:function(){return{items:[],dismissCountDown:0,alertMsg:"",android:{current_version:null,must_update:null,review_version:null,update_message:null},ios:{current_version:null,must_update:null,review_version:null,update_message:null},appletv:{current_version:null,must_update:null,review_version:null,update_message:null}}},computed:Fr({},Object(F["b"])(["loading"])),mounted:function(){console.log("ServiceStatus Mounted"),this.$route.query.q&&(this.q=this.$route.query.q),this.onFetch()},methods:{countDownChanged:function(t){this.dismissCountDown=t},showAlert:function(){this.dismissCountDown=5},onSave:function(){var t=this,e="/v3/console/service_status";P.request("put",e,{app_version:{android:this.android,ios:this.ios,appletv:this.appletv}}).then(function(e){t.alertCss="success",t.alertMsg="儲存成功",t.showAlert()}).catch(function(e){t.alertCss="warning",t.alertMsg="這位大大，您的權限不足",t.showAlert()})},onFetch:function(){var t=this;P.request("get","/v3/service_status").then(function(e){if(e.data&&e.data.data&&e.data.data.app_version){var a=e.data.data.app_version;a.android&&(t.android=a.android),a.ios&&(t.ios=a.ios),a.appletv&&(t.appletv=a.appletv),console.log(e.data.data)}})}}},Lr=Er,Ur=(a("0d4e"),Object(_["a"])(Lr,Ir,$r,!1,null,"435a88ac",null)),Rr=Ur.exports,Nr=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("b-row",[a("b-col",{attrs:{cols:"12",xl:"12"}},[a("transition",{attrs:{name:"slide"}},[a("b-card",{attrs:{header:t.caption}},[a("b-row",[a("b-col",{attrs:{sm:"12"}},[a("form",{on:{submit:function(e){return e.preventDefault(),t.onFetch(e)}}},[a("b-form-group",{attrs:{description:"輸入西元年月 ex: 2019-10 ， 每月 2 日，自動產生上個月的 income report"}},[a("b-input-group",[a("b-form-input",{attrs:{type:"text",id:"name"},model:{value:t.q,callback:function(e){t.q=e},expression:"q"}}),a("b-input-group-append",[a("b-button",{attrs:{variant:"primary"},on:{click:t.onFetch}},[a("i",{staticClass:"fa",class:{"fa-refresh":t.loading,"fa-spin":t.loading}}),t._v("\n                  Submit\n                  ")])],1)],1)],1)],1),a("b-alert",{attrs:{show:t.dismissCountDown,dismissible:"",variant:"warning"},on:{dismissed:function(e){t.dismissCountDown=0},"dismiss-count-down":t.countDownChanged}},[t._v("\n           這位大大，您的權限不足\n          ")])],1)],1),t.items?a("b-table",{attrs:{hover:t.hover,"sort-by":"filename","sort-desc":!0,striped:t.striped,bordered:t.bordered,small:t.small,fixed:t.fixed,responsive:"sm",items:t.filterItems,fields:t.fields,"current-page":t.currentPage,"per-page":t.perPage},scopedSlots:t._u([{key:"filename",fn:function(e){return[t._v("\n          "+t._s(e.item.filename)+"\n          "),a("b-button",{on:{click:function(a){return t.onDownload(e.item.filename)}}},[a("i",{staticClass:"fa fa-cloud-download"}),t._v(" Download")])]}}],null,!1,4064498744)}):t._e()],1)],1)],1)],1)},zr=[];function Hr(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),a.push.apply(a,i)}return a}function Kr(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?Hr(a,!0).forEach(function(e){Object(B["a"])(t,e,a[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):Hr(a).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))})}return t}var Jr={name:"Income",props:{caption:{type:String,default:"Income Report"},hover:{type:Boolean,default:!0},striped:{type:Boolean,default:!0},bordered:{type:Boolean,default:!1},small:{type:Boolean,default:!1},fixed:{type:Boolean,default:!1}},data:function(){return{items:[],q:"",fields:[{key:"filename",sortable:!0}],dismissCountDown:0,currentPage:1,perPage:0,totalRows:0}},mounted:function(){this.onFetch()},computed:Kr({filterItems:function(){if(""===this.q)return this.items;var t=this.q;return this.items.filter(function(e){return e.filename.includes(t)})}},Object(F["b"])(["loading"])),methods:{getBadge:function(t){var e;switch(t){case"COMPLETED":case"CLOSED":e="success";break;case"FAILED":e="danger";break;case"OPEN":e="warning";break}return e},getRowCount:function(t){return t.length},cookItem:function(){},countDownChanged:function(t){this.dismissCountDown=t},showAlert:function(){this.dismissCountDown=5},onDownload:function(t){var e=this;console.log(t),P.request("get","/v3/console/finance/income?filename="+t).then(function(t){t.data.data.url&&(console.log(t.data.data),window.open(t.data.data.url,"_blank"))}).catch(function(t){e.showAlert()})},onFetch:function(){var t=this;P.request("get","/v3/console/finance/income").then(function(e){e.data&&e.data.data&&e.data.data.files&&(t.items=e.data.data.files)}).catch(function(e){t.showAlert()})}}},Vr=Jr,Wr=(a("6ebd"),Object(_["a"])(Vr,Nr,zr,!1,null,"825409d2",null)),Gr=Wr.exports,Yr=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("b-row",[a("b-col",{attrs:{cols:"12",xl:"12"}},[a("transition",{attrs:{name:"slide"}},[a("b-card",{attrs:{header:t.caption}},[a("b-row",[a("b-col",{attrs:{sm:"12"}},[a("form",{on:{submit:function(e){return e.preventDefault(),t.onFetch(e)}}},[a("b-form-group",[a("b-input-group",[a("b-input-group-append",[a("b-button",{attrs:{variant:"primary"},on:{click:t.onFetch}},[a("i",{staticClass:"fa",class:{"fa-refresh":t.loading,"fa-spin":t.loading}}),t._v("\n                  重新載入現在 android devices 清單\n                  ")]),t._v("\n                 \n                "),a("b-button",{attrs:{variant:"warning"},on:{click:t.onSave}},[a("i",{staticClass:"fa",class:{"fa-refresh":t.loading,"fa-spin":t.loading}}),t._v("\n                  更新寫入現在 android devices 清單\n                  ")])],1)],1)],1)],1),a("b-alert",{attrs:{show:t.dismissCountDown,dismissible:"",variant:"warning"},on:{dismissed:function(e){t.dismissCountDown=0},"dismiss-count-down":t.countDownChanged}},[t._v("\n            "+t._s(t.alertMsg)+"\n          ")])],1)],1),a("b-form-textarea",{attrs:{id:"textarea",placeholder:"Enter Android devices",rows:"30"},model:{value:t.devices,callback:function(e){t.devices=e},expression:"devices"}})],1)],1)],1)],1)},Xr=[];function Zr(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),a.push.apply(a,i)}return a}function Qr(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?Zr(a,!0).forEach(function(e){Object(B["a"])(t,e,a[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):Zr(a).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))})}return t}var ts={name:"AndroidDevices",props:{caption:{type:String,default:"Android Devices"}},data:function(){return{devices:"",alertPermission:"這位大大，您的權限不足",alertJSON:"json 格式錯誤",alertMsg:"",q:"",dismissCountDown:0}},mounted:function(){this.onFetch()},computed:Qr({},Object(F["b"])(["loading"])),methods:{countDownChanged:function(t){this.dismissCountDown=t},showAlert:function(){this.dismissCountDown=5},onSave:function(){var t=this;try{JSON.parse(this.devices)}catch(e){return this.alertMsg=this.alertJSON,void this.showAlert()}P.request("put","/v3/console/android/devices",{android_devices:this.devices}).then(function(e){e.data&&e.data.data&&e.data.data.android_devices&&(t.devices=e.data.data.android_devices)}).catch(function(e){t.alertMsg=t.alertPermission,t.showAlert()})},onFetch:function(){var t=this;P.request("get","/v3/console/android/devices").then(function(e){e.data&&e.data.data&&e.data.data.android_devices&&(t.devices=e.data.data.android_devices)}).catch(function(e){t.alertMsg=t.alertPermission,t.showAlert()})}}},es=ts,as=(a("5e10"),Object(_["a"])(es,Yr,Xr,!1,null,"3244c602",null)),is=as.exports,ns=O["a"].prototype.push;O["a"].prototype.push=function(t){return ns.call(this,t).catch(function(t){return t})},r["default"].use(O["a"]);var rs=new O["a"]({mode:"hash",linkActiveClass:"open active",scrollBehavior:function(){return{y:0}},routes:[{path:"/",redirect:"/dashboard",beforeEnter:h.requireAuth,name:"Home",component:et,children:[{path:"dashboard",name:"Dashboard",component:Kt},{path:"user",meta:{label:"Users"},component:{render:function(t){return t("router-view")}},children:[{path:"",component:re},{path:"userchangelog",meta:{label:"User 修改紀錄查詢"},name:"UserChangeLog",component:we},{path:"order",meta:{label:"Order"},name:"Order",component:Pe},{path:"order/:order_id",meta:{label:"OrderDetail"},name:"OrderDetail",component:Fe},{path:"order/audit_log/:order_id",meta:{label:"OrderAuditLog"},name:"OrderAuditLog",component:Je},{path:"token",meta:{label:"Token"},name:"Token",component:ta},{path:"family",meta:{label:"Family"},name:"Family",component:la},{path:"mod",meta:{label:"MOD"},name:"MOD",component:ha},{path:"favorite/:id",meta:{label:"Favorite"},name:"Favorite",component:ja},{path:"watch_history/:id",meta:{label:"WatchHistory"},name:"WatchHistory",component:Aa},{path:"audit_log/:id",meta:{label:"UserAuditLog"},name:"UserAuditLog",component:Ra},{path:":id",meta:{label:"User Details"},name:"User",component:me}]},{path:"redeem",meta:{label:"Redeem"},component:{render:function(t){return t("router-view")}},children:[{path:"",component:Dr},{path:"detail",meta:{label:"Redeem Detail"},name:"RedeemDetail",component:Tr}]},{path:"servicestatus",name:"ServiceStatus",component:Rr},{path:"product",name:"Products",component:Va},{path:"package",name:"Packages",component:Qa},{path:"content",meta:{label:"Content"},component:{render:function(t){return t("router-view")}},children:[{path:"title",meta:{label:"Title"},name:"Title",component:ki},{path:"series",meta:{label:"Series"},name:"Series",component:Mi},{path:"publish",meta:{label:"Publish"},name:"Publish",component:Li},{path:"extra",meta:{label:"Extra"},name:"Extra",component:Vi},{path:"browse",meta:{label:"Browse"},name:"Browse",component:nn},{path:"titlelist",meta:{label:"TitleList"},name:"TitleList",component:pn},{path:"hotkeyword",meta:{label:"HotKeyWord"},name:"HotKeyWord",component:Cn},{path:"titlelist/:titlekey",meta:{label:"TitleListDetail"},name:"TitleListDetail",component:yn},{path:"announce",meta:{label:"Announce"},name:"Announce",component:Bn},{path:"event",meta:{label:"Event"},name:"Event",component:Hn},{path:"metatitlelist/:list_type",meta:{label:"TitleList"},name:"MetaTitleList",component:Xn},{path:"encoder",meta:{label:"Encoder"},name:"Encoder",component:oi},{path:"encoderbv",meta:{label:"Encoder BV"},name:"Encoder BV",component:fi},{path:"ads",meta:{label:"Ads"},name:"Ads",component:rr}]},{path:"remoteconfig",name:"RemoteConfig",component:mr},{path:"tvevents",name:"TVEvents",component:_r},{path:"finance/income",name:"Income Report",component:Gr},{path:"android/devices",name:"Android Devices",component:is}]},{path:"/login",name:"Login",component:Xt}]}),ss={serverURI:"/api/v1"},os={loading:!1,searching:"",serverURI:"",user:null,token:null,payment:null,ws:null,userInfo:{messages:[{1:"test",2:"test"}],notifications:[],tasks:[]}},ls={},cs={TOGGLE_LOADING:function(t){t.loading=!t.loading},SET_LOADING:function(t,e){t.loading=e},TOGGLE_SEARCHING:function(t){t.searching=""===t.searching?"loading":""},SET_USER:function(t,e){t.user=e},SET_TOKEN:function(t,e){t.token=e},SET_WEBSOCKET:function(t,e){t.ws=e}};r["default"].use(F["a"]);var us=new F["a"].Store({state:os,actions:ls,mutations:cs}),ds=a("31bd"),ps=a("c38f"),ms=a.n(ps),bs=(a("0952"),a("4a7a")),fs=a.n(bs);a("6dfc");r["default"].use(s["a"]),r["default"].use(ms.a),r["default"].component("v-select",fs.a),r["default"].prototype.$http=n.a,rs.beforeEach(function(t,e,a){t.auth&&"null"===t.router.app.$store.state.token?(window.console.log("Not authenticated"),a({path:"/login",query:{redirect:t.fullPath}})):a()}),n.a.interceptors.request.use(function(t){return us.commit("SET_LOADING",!0),t}),n.a.interceptors.response.use(function(t){return us.commit("SET_LOADING",!1),t},function(t){return us.commit("SET_LOADING",!1),401===t.response.status&&(localStorage.removeItem("token"),localStorage.removeItem("user"),rs.replace("/login")),Promise.reject(t)}),Object(ds["sync"])(us,rs),new r["default"]({el:"#app",router:rs,store:us,data:{config:ss},template:"<App/>",components:{App:w}})},"5c0b":function(t,e,a){"use strict";var i=a("9ed3"),n=a.n(i);n.a},"5d59":function(t,e,a){"use strict";var i=a("2cb7"),n=a.n(i);n.a},"5e10":function(t,e,a){"use strict";var i=a("7e38"),n=a.n(i);n.a},6400:function(t,e,a){"use strict";var i=a("ab2d"),n=a.n(i);n.a},6508:function(t,e,a){"use strict";var i=a("c5c1"),n=a.n(i);n.a},6798:function(t,e,a){"use strict";var i=a("d3f8"),n=a.n(i);n.a},"6b43":function(t,e,a){"use strict";var i=a("6d0a"),n=a.n(i);n.a},"6c09":function(t,e,a){"use strict";var i=a("186c"),n=a.n(i);n.a},"6c80":function(t,e,a){},"6d0a":function(t,e,a){},"6ebd":function(t,e,a){"use strict";var i=a("558c"),n=a.n(i);n.a},7372:function(t,e,a){},"7a99":function(t,e,a){},"7dc7":function(t,e,a){},"7e38":function(t,e,a){},8069:function(t,e,a){},8513:function(t,e,a){},"860e":function(t,e,a){},"86db":function(t,e,a){"use strict";var i=a("26c3"),n=a.n(i);n.a},"8bac":function(t,e,a){"use strict";var i=a("7372"),n=a.n(i);n.a},"8bca":function(t,e,a){"use strict";var i=a("e8a8"),n=a.n(i);n.a},"8cfb":function(t,e,a){},"92bb":function(t,e,a){"use strict";var i=a("7dc7"),n=a.n(i);n.a},"9adf":function(t,e,a){"use strict";var i=a("26da"),n=a.n(i);n.a},"9aeb":function(t,e,a){},"9ed3":function(t,e,a){},"9f31":function(t,e,a){"use strict";var i=a("f863"),n=a.n(i);n.a},"9fff":function(t,e,a){"use strict";var i=a("b57e"),n=a.n(i);n.a},a094:function(t,e,a){},a0ce:function(t,e,a){"use strict";var i=a("45a5"),n=a.n(i);n.a},a8da:function(t,e,a){"use strict";var i=a("7a99"),n=a.n(i);n.a},ab2d:function(t,e,a){},b0fa:function(t,e,a){"use strict";var i=a("fe5d"),n=a.n(i);n.a},b27a:function(t,e,a){"use strict";var i=a("860e"),n=a.n(i);n.a},b45c:function(t,e,a){"use strict";var i=a("1a9a"),n=a.n(i);n.a},b57e:function(t,e,a){},b7e9:function(t,e,a){},b878:function(t,e,a){},b90f:function(t,e,a){"use strict";var i=a("fa69"),n=a.n(i);n.a},bb1a:function(t,e,a){"use strict";var i=a("0328"),n=a.n(i);n.a},c389:function(t,e,a){"use strict";var i=a("a094"),n=a.n(i);n.a},c3ab:function(t,e,a){"use strict";var i=a("b7e9"),n=a.n(i);n.a},c5c1:function(t,e,a){},d0f3:function(t,e,a){"use strict";var i=a("9aeb"),n=a.n(i);n.a},d2f7:function(t,e,a){"use strict";var i=a("8513"),n=a.n(i);n.a},d3f8:function(t,e,a){},d420:function(t,e,a){},da97:function(t,e,a){"use strict";var i=a("d420"),n=a.n(i);n.a},e8a8:function(t,e,a){},eb76:function(t,e,a){"use strict";var i=a("ff8a"),n=a.n(i);n.a},ee53:function(t,e,a){"use strict";var i=a("fa3f"),n=a.n(i);n.a},f151:function(t,e,a){},f2b4:function(t,e,a){},f863:function(t,e,a){},fa3f:function(t,e,a){},fa69:function(t,e,a){},fe5d:function(t,e,a){},ff76:function(t,e,a){"use strict";var i=a("1e75"),n=a.n(i);n.a},ff8a:function(t,e,a){}});