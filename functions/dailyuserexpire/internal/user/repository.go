package user

import (
	"time"

	"github.com/KKTV/kktv-api-v3/pkg/database"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"gopkg.in/guregu/null.v3"
)

type Repository interface {
	BulkExpireUsers(expiredAt time.Time, traceBackFrom time.Time, limit int) ([]*ExpiringUser, error)
}

type repository struct {
	db database.DB
}

func NewRepository(db database.DB) Repository {
	return &repository{db: db}
}

type ExpiringUser struct {
	dbuser.User
	PaymentStatus null.String `db:"payment_status"`
	PaymentType   null.String `db:"payment_type"`
}

func (r *repository) BulkExpireUsers(expiredAt time.Time, traceBackFrom time.Time, limit int) ([]*ExpiringUser, error) {
	/**
	Explain: WHERE clauses
	  - status IS NOT NULL -> 要不就是已經取消訂閱，要不就是單次訂閱沒有續訂，即無未實現訂單
	  - order_date > $2 -> 回溯特定天數內該被處理的訂單，因為有可能前一次expire執行時失敗了，要補執行。 60天前的訂單則不管
	*/
	sql := `UPDATE users AS outer_users SET
 				role = 'expired', type = 'general', membership = $1, auto_renew = false, updated_at = NOW() FROM
 				(SELECT u.*, o.payment_type, o.status AS payment_status 
					FROM users AS u LEFT JOIN
				   (SELECT DISTINCT ON (user_id) user_id, status, payment_type FROM orders 
				   	WHERE status IS NOT NULL AND order_date > $2 ORDER BY user_id, order_date DESC 
			   		) AS o ON o.user_id = u.id WHERE u.role != 'expired' AND u.expired_at <= $3 LIMIT $4
			 	) AS result WHERE outer_users.id=result.id RETURNING result.*;`

	expiredMembership := dbuser.Membership{{Role: dbuser.MemberRoleExpired}}
	rows, err := r.db.Queryx(sql, expiredMembership, traceBackFrom, expiredAt, limit)
	if err != nil {
		return nil, err
	}
	users := make([]*ExpiringUser, 0)
	for rows.Next() {
		u := new(ExpiringUser)
		err = rows.StructScan(u)
		if err != nil {
			return nil, err
		}
		users = append(users, u)
	}
	return users, nil
}
