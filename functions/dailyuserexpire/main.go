package main

import (
	"context"
	"errors"
	"strings"
	"time"

	"github.com/KKTV/kktv-api-v3/functions/dailyuserexpire/internal/user"
	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	"github.com/KKTV/kktv-api-v3/kktvapi/pkg/auditing"
	amplitude "github.com/KKTV/kktv-api-v3/pkg/amplitudelib"
	"github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/KKTV/kktv-api-v3/pkg/model/dbuser"
	"github.com/KKTV/kktv-api-v3/pkg/secret"

	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-lambda-go/lambdacontext"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	lambdasvc "github.com/aws/aws-sdk-go/service/lambda"
	"gopkg.in/guregu/null.v3"
)

func init() {
	if err := config.Init(); err != nil {
		log.Fatal("main: init config fail").Err(err).Send()
	}
	secret.Init(config.Env)
	kkapp.ContextInit()
}

const (
	batchNumber   = 500
	daysBeforeNow = 60 // only look back no longer than two month (60 days) orders
)

// Handler for main
func Handler(ctx context.Context) error {
	db := kkapp.App.DbUser.Master().Unsafe()
	repo := user.NewRepository(db)
	amplitudeClient := amplitude.NewClient(kkapp.App.Env)
	// ematicClient := kkapp.App.Ematic
	auditingService := auditing.NewRepository(db)

	lc, ok := lambdacontext.FromContext(ctx)
	if !ok {
		return errors.New("Cannot find Lambda Function name")
	}
	arn := lc.InvokedFunctionArn
	arnSlice := strings.Split(arn, ":function:")
	if len(arnSlice) != 2 && len(strings.Split(arnSlice[1], ":")) != 2 {
		return errors.New("Cannot find Lambda Function and Alias name")
	}

	fnSlice := strings.Split(arnSlice[1], ":")
	funcName := fnSlice[0]
	funcAlias := fnSlice[1]

	now := time.Now()
	traceBackDate := now.AddDate(0, 0, -daysBeforeNow)
	expiringUsers, err := repo.BulkExpireUsers(now, traceBackDate, batchNumber)
	if err != nil {
		log.Error("DailyUserExpire: UserRepository: fail to BulkExpireUsers").Err(err).Send()
		return err
	}
	log.Info("DailyUserExpire: expired users processed").Int("expiring_users", len(expiringUsers)).Send()
	logs := make([]*dbuser.AuditLog, 0)
	for _, expiringUser := range expiringUsers {
		sendEventToAmplitude(amplitudeClient, expiringUser)
		// sendEventToEmatic(expiringUser, ematicClient)
		fulfillIfUserPrime(expiringUser.ID)
		logs = append(logs, genAuditLog(expiringUser))
	}

	if len(logs) > 0 {
		if err := auditingService.Insert(logs...); err != nil {
			log.Warn("DailyUserExpire: fail to insert audit logs").Err(err).Interface("audit_logs", logs).Send()
		}
	}

	if hasNextBatch := len(expiringUsers) == batchNumber; hasNextBatch {
		sess, err := session.NewSession(&aws.Config{
			Region: aws.String("ap-northeast-1"),
		})
		if err != nil {
			log.Fatal("DailyUserExpire: fail to create aws session").Err(err).Send()
		}
		svc := lambdasvc.New(sess)
		_, err = svc.Invoke(
			&lambdasvc.InvokeInput{
				FunctionName:   aws.String(funcName),
				InvocationType: aws.String("Event"),
				Qualifier:      aws.String(funcAlias),
			})
		if err != nil {
			log.Error("DailyUserExpire: fail to trigger next batch").Str("func_name", funcName).Str("func_alias", funcAlias).Err(err).Send()
			return err
		}
		log.Info("DailyUserExpire: trigger next batch").Str("func_name", funcName).Str("func_alias", funcAlias).Send()

	}
	return nil

}

func genAuditLog(expiringUser *user.ExpiringUser) *dbuser.AuditLog {
	changes := []dbuser.AuditLogDifference{
		{Field: "role", Old: expiringUser.Role, New: dbuser.RoleExpired.String()},
		{Field: "membership",
			Old: expiringUser.Membership, New: dbuser.Membership{{Role: dbuser.MemberRoleExpired}}},
	}
	if expiringUser.AutoRenew {
		changes = append(changes, dbuser.AuditLogDifference{Field: "auto_renew", Old: true, New: false})
	}
	return &dbuser.AuditLog{
		Detail:       &dbuser.AuditLogDetail{Diff: changes},
		TargetType:   "user",
		TargetID:     expiringUser.ID,
		ModifierType: dbuser.AuditModifierTypeSystem,
		ModAction:    dbuser.AuditModActionUpdate,
		Note:         null.StringFrom("daily user expire"),
	}
}

// func sendEventToEmatic(expiringUser *user.ExpiringUser, ematicClient *ematicagent.AgentAPI) {
// 	if email := expiringUser.Email.ValueOrZero(); email != "" {
// 		if err := ematicClient.Expired(email); err != nil {
// 			log.Warn("DailyUserExpire: fail to send expired event to Ematic").Err(err).Str("email", email).Send()
// 		}
// 	}
// }

func sendEventToAmplitude(amplitudeClient amplitude.Client, expiringUser *user.ExpiringUser) {
	evt := &amplitude.AccountDowngradedEvent{}
	evt.UserID = expiringUser.ID
	if expiringUser.PaymentType.Valid {
		evt.CancelledPaymentType = expiringUser.PaymentType.String
	}
	evt.TriggerCondition = "expired"
	evt.SetCancelReasonByPaymentStatus(expiringUser.PaymentStatus.String)
	if err := amplitudeClient.SendEvent(evt); err != nil {
		log.Warn("DailyUserExpire: fail to send account downgraded event to Amplitude").Err(err).Interface("event", evt).Send()
	}
}

func fulfillIfUserPrime(userID string) {
	billingClient := kkapp.App.BillingClient
	if billingResp, err := billingClient.FulFillOrder(userID); err != nil {
		log.Warn("DailyUserExpire: fulfill prime failed").Str("user_id", userID).Err(err).Send()
	} else {
		log.Info("DailyUserExpire: fulfill prime").Str("user_id", userID).
			Interface("billing response", billingResp).Send()
	}
}

func main() {
	// AWS lambda
	log.Info("DailyUserExpire: start").Send()
	lambda.Start(Handler)
	//ctx := context.TODO()
	//lc := new(lambdacontext.LambdaContext)
	//lc.InvokedFunctionArn = "aws:lambda:ap-northeast-1:************:function:kktv-api-v3_dailyuserexpire:test"
	//Handler(lambdacontext.NewContext(ctx, lc))
	log.Info("DailyUserExpire: end").Send()
}
