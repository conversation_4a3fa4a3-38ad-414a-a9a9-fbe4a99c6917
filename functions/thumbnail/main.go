package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"strings"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kkapp/model/dbmeta"
	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	plog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/dynamodb"
	"github.com/aws/aws-sdk-go/service/dynamodb/dynamodbattribute"
)

var (
	sess = session.Must(session.NewSession())
)

func init() {
	log.Println("Lambda thumbnail init")
	if err := config.Init(); err != nil {
		plog.Fatal("main: init config fail").Err(err).Send()
	}
	kkapp.ContextInit()

}

// refer https://stackoverflow.com/questions/49129534/unmarshal-mapstringdynamodbattributevalue-into-a-struct
// UnmarshalStreamImage converts events.DynamoDBAttributeValue to struct
func UnmarshalStreamImage(attribute map[string]events.DynamoDBAttributeValue, out interface{}) error {

	dbAttrMap := make(map[string]*dynamodb.AttributeValue)

	for k, v := range attribute {

		var dbAttr dynamodb.AttributeValue

		bytes, marshalErr := v.MarshalJSON()
		if marshalErr != nil {
			return marshalErr
		}

		json.Unmarshal(bytes, &dbAttr)
		dbAttrMap[k] = &dbAttr
	}

	return dynamodbattribute.UnmarshalMap(dbAttrMap, out)
	// return dynamo.UnmarshalItem(dbAttrMap, out)

}

func HandlerThumbnail(record events.DynamoDBEventRecord) {
	//  meta_episode
	// id        | character varying(14) |           | not null |
	var episode_id string

	newOne := record.Change.NewImage

	if _, ok := newOne["episode_id"]; ok {
		episode_id = newOne["episode_id"].String()
	}

	// validate episode id
	if _, err := strconv.Atoi(episode_id); err != nil {
		log.Println("[ERROR] not validate episode_id")
		return
	}

	dbmeta.NewThumbnail(episode_id)
}

func Handler(ctx context.Context, e events.DynamoDBEvent) {
	// "eventSourceARN": "arn:aws:dynamodb:us-west-2:account-id:table/ExampleTableWithStream/stream/2015-06-27T00:48:05.899",
	// EventName
	//    * INSERT - a new item was added to the table.
	//    * MODIFY - one or more of an existing item's attributes were modified.
	//    * REMOVE - the item was deleted from the table
	for _, record := range e.Records {
		fmt.Printf("Processing request data for event ID %s, type %s.\n", record.EventID, record.EventName)

		// FIXME current not implement DELETE, because, that control via publish-data table
		if record.EventName == "REMOVE" {
			continue
		}

		table := strings.Split(record.EventSourceArn, "/")[1]

		switch {
		case strings.Contains(table, "data-episodes"):
			// build thumbnail
			HandlerThumbnail(record)
		}
		log.Println("DynamoDB Table:", table, "Event:", record.EventName)
	}
}

func main() {
	// AWS lambda
	lambda.Start(Handler)
}
