package main

import (
	"bufio"
	"database/sql"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/KKTV/kktv-api-v3/kkapp"
	"github.com/KKTV/kktv-api-v3/kktvapi/config"
	plog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3/s3manager"
	sf "github.com/snowflakedb/gosnowflake"
)

var (
	sf_account   = "amplitude"
	sf_user      = "org_12671"
	sf_password  = "PLRDoUD4OesvIjeRvkvgYJsG"
	sf_host      = "amplitude.snowflakecomputing.com"
	sf_protocol  = "https"
	sf_database  = "DB_12671"
	sf_warehouse = "QUERY_WH"
	sf_schema    = "SCHEMA_148242"

	cfg = &sf.Config{
		Account:   sf_account,
		User:      sf_user,
		Password:  sf_password,
		Host:      sf_host,
		Protocol:  sf_protocol,
		Database:  sf_database,
		Warehouse: sf_warehouse,
		Schema:    sf_schema,
	}

	testBucket = "kktv-test-recommendation-source/play-history/"
	prodBucket = "kktv-prod-recommendation-source/play-history/"
	s3Bucket   = ""

	end          time.Time
	jsonFileName string
	jsonTemplate = "(json,{\"play-history:v2:%s:json\":{\"type\":\"zset\",\"value\":[%s]}})"

	sqlsf = map[string]string{
		"playhistory": `
WITH ute AS (
SELECT user_id, REPLACE(EVENT_PROPERTIES:"title id",'"','') AS e_title_id, EXTRACT(EPOCH FROM MAX(event_time)) AS event_time
FROM DB_12671.SCHEMA_148242.EVENTS_148242
WHERE event_time >= '2016-08-10'
AND EVENT_TYPE='Video Playing Stopped'
AND regexp_like(EVENT_PROPERTIES:"video played percentage",'^(-?[0-9]+[.,]?[0-9]*)$$')
AND CAST(REPLACE(EVENT_PROPERTIES:"video played percentage", ',', '.') AS FLOAT) >= 0.1
AND user_id IS NOT NULL
AND EVENT_PROPERTIES:"title id" IS NOT NULL
GROUP BY user_id, EVENT_PROPERTIES:"title id"
)
SELECT user_id,
       LISTAGG(e_title_id, ',') WITHIN GROUP (ORDER BY e_title_id) AS title_ids,
       LISTAGG(event_time, ',') WITHIN GROUP (ORDER BY e_title_id) AS event_times
FROM ute GROUP BY user_id;
`,
	}
)

// getDSN constructs a DSN based on the test connection parameters
func getDSN() (string, *sf.Config, error) {
	dsn, err := sf.DSN(cfg)
	return dsn, cfg, err
}

func dumpPlayHistory(dsn string) {

	if kkapp.App.Debug {
		s3Bucket = testBucket
	} else {
		s3Bucket = prodBucket
	}

	log.Println(time.Now())
	var err error

	file, err := os.Create(jsonFileName)
	if err != nil {
		log.Fatalf("failed creating file: %s", err)
	}
	defer file.Close()

	writer := bufio.NewWriter(file)
	defer writer.Flush()

	db, err := sql.Open("snowflake", dsn)
	if err != nil {
		log.Printf("Failed to connect Snowflake. %v, err: %v", dsn, err)
	}
	defer db.Close()

	rows, err := db.Query(sqlsf["playhistory"])
	if err != nil {
		log.Printf("failed to run a query. %v, err: %v", sqlsf["playhistory"], err)
	}
	defer rows.Close()

	var userid, titleid, eventtime string

	log.Println(time.Now())
	for rows.Next() {
		err := rows.Scan(&userid, &titleid, &eventtime)
		if err != nil {
			log.Printf("failed to get result. err: %v", err)
		}
		arrtitleid := strings.Split(titleid, ",")
		arreventtime := strings.Split(eventtime, ",")
		strValue := ""
		for i := range arrtitleid {
			strValue += fmt.Sprintf("[\"%s\",%s],", arrtitleid[i], arreventtime[i])
		}
		bindValue := strValue[:len(strValue)-1]
		strLine := fmt.Sprintf(jsonTemplate, userid, bindValue)

		// write string to file
		fmt.Fprintln(writer, strLine)
	}

	log.Println(time.Now())
	if rows.Err() != nil {
		log.Printf("ERROR: %v\n", rows.Err())
	}

	log.Printf("\nFile Name: %s", file.Name())

	uploadFiletoS3(file.Name())

}

func uploadFiletoS3(filename string) {

	log.Println("Preparing upload file to S3...", time.Now())
	sess, _ := session.NewSessionWithOptions(session.Options{Config: aws.Config{Region: aws.String("ap-northeast-1")}})
	uploader := s3manager.NewUploader(sess)

	file, err := os.Open(filename)

	if err != nil {
		log.Println(err.Error())
	}
	key := filepath.Base(file.Name())

	_, err = uploader.Upload(&s3manager.UploadInput{
		Bucket: aws.String(s3Bucket),
		Key:    aws.String(key),
		Body:   file,
	})

	if err != nil {
		log.Println("!!! Upload File to S3 Failed !!!", err)
	}
	log.Println("Upload file to S3 finish. ", time.Now())
}

func init() {
	log.Println("Lambda kktv-api-v3 dumpPlayHistory init")
	if err := config.Init(); err != nil {
		plog.Fatal("main: init config fail").Err(err).Send()
	}
	kkapp.ContextInit()

	now := time.Now()
	zone, err := time.LoadLocation("Asia/Taipei")
	if err != nil {
		log.Println("Load timezone error:", err)
	}

	end = now.In(zone)
	jsonFileName = fmt.Sprintf("/tmp/v2.%d-%02d-%02d.json", end.Year(), end.Month(), end.Day())
}

func Handler() {

	dsn, cfg, err := getDSN()
	if err != nil {
		log.Printf("failed to create DSN from Config: %v, err: %v", cfg, err)
	}

	dumpPlayHistory(dsn)

	log.Println("Finish Dump Play History")
}

func main() {
	// AWS lambda
	lambda.Start(Handler)
}
