package config

import (
	"fmt"

	"github.com/caarlos0/env/v7"
	"github.com/joho/godotenv"
)

var (
	Env       string
	RedisMeta []string
	DBMeta    []string
)

type Config struct {
	Env       string   `env:"ENV,notEmpty"`
	RedisMeta []string `env:"REDISMETA,notEmpty" envSeparator:","`
	DBMeta    []string `env:"DBMETA,notEmpty" envSeparator:","`
}

func Init() error {
	_ = godotenv.Load()

	var cfg Config
	if err := env.Parse(&cfg); err != nil {
		return err
	}
	Env = cfg.Env
	RedisMeta = cfg.RedisMeta

	DBMeta = toDBDSN(cfg.DBMeta)

	return nil
}

func toDBDSN(rawDSNs []string) []string {
	var dsns []string
	for _, dbURI := range rawDSNs {
		dsns = append(dsns, fmt.Sprintf("%s?sslmode=disable", dbURI))
	}
	return dsns
}
