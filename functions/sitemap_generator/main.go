package main

import (
	"bytes"
	"encoding/json"
	"encoding/xml"
	"fmt"
	"os"
	"time"

	"github.com/KKTV/kktv-api-v3/functions/sitemap_generator/internal/config"
	"github.com/KKTV/kktv-api-v3/functions/sitemap_generator/internal/meta"
	"github.com/KKTV/kktv-api-v3/kkapp/datastore"
	"github.com/KKTV/kktv-api-v3/kkapp/model/kkcontent"
	"github.com/KKTV/kktv-api-v3/pkg/campaign"

	plog "github.com/KKTV/kktv-api-v3/pkg/log"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
)

var (
	countryMap = map[string]string{
		"韓國":   "Korea",
		"日本":   "Japan",
		"中國":   "China",
		"台灣":   "Taiwan",
		"泰國":   "Thailand",
		"香港":   "Hongkong",
		"美國":   "America",
		"西班牙":  "Spain",
		"法國":   "France",
		"加拿大":  "Canada",
		"瑞士":   "Switzerland",
		"阿根廷":  "Argentina",
		"德國":   "Germany",
		"澳洲":   "Australia",
		"巴西":   "Brazil",
		"智利":   "Chile",
		"英格蘭":  "England",
		"芬蘭":   "Finland",
		"伊朗":   "Iran",
		"以色列":  "Israel",
		"義大利":  "Italy",
		"秘魯":   "Peru",
		"菲律賓":  "Philippines",
		"俄羅斯":  "Russia",
		"馬來西亞": "Malaysia",
		"丹麥":   "Denmark",
		"歐美":   "Western",
		"其它":   "Other",
	}

	countryMapEN = reverseMap(countryMap)
)

const xmlNamespace = "http://www.sitemaps.org/schemas/sitemap/0.9"

func reverseMap(m map[string]string) (nm map[string]string) {
	nm = make(map[string]string)
	for k, v := range m {
		nm[v] = k
	}
	return
}

type SitemapIndex struct {
	XMLName      xml.Name  `xml:"sitemapindex"`
	XMLNamespace string    `xml:"xmlns,attr"`
	Sitemap      []Sitemap `xml:"sitemap`
}

type Sitemap struct {
	XMLName  xml.Name `xml:"sitemap"`
	Location string   `xml:"loc"`
}

type UrlSet struct {
	XMLName      xml.Name `xml:"urlset"`
	XMLNamespace string   `xml:"xmlns,attr"`
	Urls         []Url    `xml:"url"`
}

type Url struct {
	XMLName         xml.Name `xml:"url"`
	Location        string   `xml:"loc"`
	LastModify      string   `xml:"lastmod"`
	ChangeFrequency string   `xml:"changefreq"`
}

func init() {
	plog.Info("Lambda thumbnail init")
	if err := config.Init(); err != nil {
		plog.Fatal("main: init config fail").Err(err).Send()
	}
}

func getBrowseFromRedis() (items kkcontent.Browses, err error) {
	redisPool := datastore.NewRedisPool(config.RedisMeta).Slave()
	browseBytes, err := redisPool.Cmd("GET", "browse:v3:json").Bytes()

	if err != nil {
		plog.Error("redis: GET browse:v3:json fail").Err(err).Send()
		return
	}

	err = json.Unmarshal(browseBytes, &items)
	if err != nil {
		plog.Fatal("json: Unmarshal browse to json fail").Err(err).Send()
		return
	}

	for idx := range items {
		items[idx].Parse()
	}

	return items, err
}

func newUrl(url string) Url {
	return Url{
		Location:        url,
		LastModify:      time.Now().Format("2006-01-02"),
		ChangeFrequency: "daily",
	}
}

func getAllTitleUrls(titleRepo meta.TitleRepo) (urls []Url, err error) {
	allID, err := titleRepo.ListAllTitleID()
	if err != nil {
		return
	}
	for _, id := range allID {
		urls = append(urls, newUrl(fmt.Sprintf("https://www.kktv.me/titles/%v", id)))
	}
	return
}

func getAllUrlsFromCollectionType(collectionType string) (urls []Url, err error) {
	browse, err := getBrowseFromRedis()
	if err != nil {
		return
	}
	for _, collection := range browse.CollectionType(collectionType).Platform("web").GetCollections() {
		urls = append(urls, newUrl(fmt.Sprintf("https://www.kktv.me/browse/%v/%v", collectionType, collection.CollectionName)))
	}
	return
}

func getallUrlsFromTitleMeta(titleRepo meta.TitleRepo) (urls []Url, err error) {
	isFigureExists, isCountriesExists, isThemesExists := make(map[string]bool), make(map[string]bool), make(map[string]bool)

	allMetas, err := titleRepo.ListAllTitleMeta()
	if err != nil {
		return
	}

	for _, m := range allMetas {
		if !m.Available {
			continue
		}

		for _, c := range m.Casts {
			if exists := isFigureExists[c.CollectionName]; !exists {
				urls = append(urls, newUrl(fmt.Sprintf("https://www.kktv.me/browse/figure/%v", c.CollectionName)))
				isFigureExists[c.CollectionName] = true
			}
		}

		if m.Country != nil && m.Country.CollectionName != "不使用" {
			if countriesExists := isCountriesExists[m.Country.CollectionName]; !countriesExists {
				urls = append(urls, newUrl(fmt.Sprintf("https://www.kktv.me/browse/country/%v", m.Country.CollectionName)))
				if countryMapEN[m.Country.CollectionName] != "" {
					urls = append(urls, newUrl(fmt.Sprintf("https://www.kktv.me/browse/country/%v", countryMapEN[m.Country.CollectionName])))
				}
				isCountriesExists[m.Country.CollectionName] = true
			}
		}

		if m.Themes != nil {
			for _, t := range m.Themes {
				if t.CollectionName == "不使用" {
					continue
				}
				if themesExists := isThemesExists[t.CollectionName]; !themesExists {
					urls = append(urls, newUrl(fmt.Sprintf("https://www.kktv.me/browse/theme/%v", t.CollectionName)))
					isThemesExists[t.CollectionName] = true
				}
			}
		}
	}
	return
}

func getAllTitleListUrls(titleRepo meta.TitleRepo) (urls []Url, err error) {
	allList, err := titleRepo.ListAllTitleList()
	if err != nil {
		return
	}
	for _, id := range allList {
		urls = append(urls, newUrl(fmt.Sprintf("https://www.kktv.me/titleList/%v", id)))
	}
	return
}

func getAllCampaignUrls() (urls []Url, err error) {
	allCamapigns, err := campaign.GetAllCampaignName()
	if err != nil {
		return
	}

	for _, id := range allCamapigns {
		urls = append(urls, newUrl(fmt.Sprintf("https://www.kktv.me/campaign/%v", id)))
	}
	return
}

func marshalData(structData any) (xmlData []byte, err error) {
	return xml.Marshal(structData)
}

func handleRequest() error {
	titleRepo := meta.NewTitleRepo(datastore.NewDBPool(config.DBMeta).Slave())
	urls := []Url{}
	urls = append(urls, newUrl("https://www.kktv.me/"))
	urls = append(urls, newUrl("https://www.kktv.me/about-kktv/"))

	// Generate all titles urls
	allTitleUrls, err := getAllTitleUrls(titleRepo)
	if err != nil {
		return err
	}
	urls = append(urls, allTitleUrls...)

	// Generate all content_agent urls
	allAgentUrls, err := getAllUrlsFromCollectionType("content_agent")
	if err != nil {
		return err
	}
	urls = append(urls, allAgentUrls...)

	// Generate all genre urls
	allGenreUrls, err := getAllUrlsFromCollectionType("genre")
	if err != nil {
		return err
	}
	urls = append(urls, allGenreUrls...)

	// Generate all figure urls
	allUrlsFromTitleMeta, err := getallUrlsFromTitleMeta(titleRepo)
	if err != nil {
		return err
	}
	urls = append(urls, allUrlsFromTitleMeta...)

	// Generate all titleList urls
	allTitleListUrls, err := getAllTitleListUrls(titleRepo)
	if err != nil {
		return err
	}
	urls = append(urls, allTitleListUrls...)

	// Generate all campaign urls
	allCampaignUrls, err := getAllCampaignUrls()
	if err != nil {
		return err
	}
	urls = append(urls, allCampaignUrls...)

	chuckUrls := [][]Url{}
	chunkSize := 20000

	for i := 0; i < len(urls); i += chunkSize {
		end := i + chunkSize

		if end > len(urls) {
			end = len(urls)
		}

		chuckUrls = append(chuckUrls, urls[i:end])
	}

	sitemapIndex := SitemapIndex{
		XMLNamespace: xmlNamespace,
	}

	for index, urls := range chuckUrls {
		urlset := UrlSet{
			XMLNamespace: xmlNamespace,
		}
		urlset.Urls = urls
		key := fmt.Sprintf("sitemap_part%d.xml", index+1)
		xmlData, err := marshalData(urlset)
		if err != nil {
			return err
		}
		err = uploadResultToS3(xmlData, key)
		if err != nil {
			return err
		}
		sitemap := Sitemap{
			Location: fmt.Sprintf("https://www.kktv.me/%v", key),
		}
		sitemapIndex.Sitemap = append(sitemapIndex.Sitemap, sitemap)
	}

	xmlData, err := marshalData(sitemapIndex)
	if err != nil {
		return err
	}
	err = uploadResultToS3(xmlData, "sitemap.xml")
	if err != nil {
		return err
	}

	return nil
}

func uploadResultToS3(xmlData []byte, key string) (err error) {
	// Upload xmlData to S3
	s3Client := s3.New(session.New(), &aws.Config{Region: aws.String("ap-northeast-1")})
	_, err = s3Client.PutObject(&s3.PutObjectInput{
		Bucket:      aws.String("kktv-prod-web-app"),
		ContentType: aws.String("text/xml; charset=utf-8"),
		Key:         aws.String(fmt.Sprintf("app.kktv.me-sitemap/%v", key)),
		Body:        bytes.NewReader(xmlData),
	})
	if err != nil {
		return
	} else {
		fmt.Printf("%v uploaded to S3 successfully!\n", key)
	}
	return
}

func main() {
	if os.Getenv("LAMBDA_RUNTIME_DIR") != "" {
		lambda.Start(handleRequest)
	} else {
		// local environment
		handleRequest()
	}
}
