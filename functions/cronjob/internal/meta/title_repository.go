//go:generate mockgen -source title_repository.go -destination title_repository_mock.go -package meta
package meta

import (
	"sync"

	"github.com/KKTV/kktv-api-v3/pkg/database"
)

var (
	titleRepo     Repository
	onceTitleRepo sync.Once
)

type Repository interface {
	ListAllAvailableTitleIDs() ([]string, error)
}

type titleRepository struct {
	dbReader database.DB
}

func NewTitleRepository(dbReader database.DB) Repository {
	onceTitleRepo.Do(func() {
		titleRepo = &titleRepository{
			dbReader: dbReader,
		}
	})
	return titleRepo
}

func (t *titleRepository) ListAllAvailableTitleIDs() ([]string, error) {
	sql := `select distinct id from meta_title where meta #>> '{available}' = 'true';`
	ids := make([]string, 0)
	if err := t.dbReader.Select(&ids, sql); err != nil {
		return nil, err
	}
	return ids, nil
}
