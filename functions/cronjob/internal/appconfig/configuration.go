package appconfig

type freeFormCommon struct {
	Enabled bool `json:"enabled"`
}

type bvIntegration struct {
	freeFormCommon
	Config struct {
		PlatformVersions map[string]string `json:"platform_versions"`
	} `json:"config"`
}
type browseEntrySupportProtect struct {
	freeFormCommon
	Config struct {
		PlatformVersions map[string]string `json:"platform_versions"`
	} `json:"config"`
}

type v4WatchHistoryAPI struct {
	freeFormCommon
	Config struct {
		PlatformVersions map[string]string `json:"platform_versions"`
	} `json:"config"`
}

type triggerCondition[T any] struct {
	TriggerType string `json:"trigger_type"`
	Args        T      `json:"args"`
}

type enablingMembership struct {
	freeFormCommon
	Config struct {
		Condition triggerCondition[[]string] `json:"condition"`
	} `json:"config"`
}

type animeAiringSchedule struct {
	freeFormCommon
	Config struct {
		PlatformVersions map[string]string `json:"platform_versions"`
	} `json:"config"`
}

type enablingAnimeAiring struct {
	freeFormCommon
	Config struct {
		Condition triggerCondition[[]string] `json:"condition"`
	} `json:"config"`
}

// apiConf is the configuration for the API.
// every property in the first level is a feature flag struct and should compose freeFormCommon.
type apiConf struct {
	BvIntegration             bvIntegration             `json:"bv_integration"`
	BrowseEntrySupportProtect browseEntrySupportProtect `json:"browse_entry_support_protect"`
	V4WatchHistoryAPI         v4WatchHistoryAPI         `json:"v4_watch_history_api"`
	EnablingMembership        enablingMembership        `json:"enabling_membership"`
	AnimeAiringSchedule       animeAiringSchedule       `json:"anime_airing_schedule"`
	EnablingAnimeAiring       enablingAnimeAiring       `json:"enabling_anime_airing"`
}
