--
-- PostgreSQL database dump
--

-- Dumped from database version 9.6.15
-- Dumped by pg_dump version 9.6.15

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Data for Name: coupon_groups; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.coupon_groups (id, prefix, price, duration, usage_limit_per_user, allow_reuse, valid_since, expires_at, created_at, updated_at, price_no_tax, description, free_duration, fee, channel, product_id) FROM stdin;
1	KK	0	1 mon	5	f	2019-06-20 17:00:00+01	2019-08-31 17:00:00+01	2019-06-13 04:58:20.554+01	2019-06-13 04:58:20.554+01	0	測試 - SONY TV 1個月 序號 2228組;\nhttps://kkvideo.atlassian.net/browse/KKTV-5537;	00:00:00	0		\N
\.


--
-- Data for Name: coupon_codes; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.coupon_codes (id, code, group_id, user_id, created_at, updated_at, price, price_no_tax, issue_user_id, device_id, revoked_at) FROM stdin;
1	KKTEST3JLM5	1	\N	2019-06-13 04:58:21.125+01	2019-06-13 04:58:21.125+01	\N	\N	\N	\N	\N
2	KKTESTNJK31	1	\N	2019-06-13 04:58:21.125+01	2019-06-13 04:58:21.125+01	\N	\N	\N	\N	\N
3	KKTESTVT487	1	\N	2019-06-13 04:58:21.125+01	2019-06-13 04:58:21.125+01	\N	\N	\N	\N	\N
4	KKTESTKI8NR	1	\N	2019-06-13 04:58:21.125+01	2019-06-13 04:58:21.125+01	\N	\N	\N	\N	\N
5	KKTESTPA471	1	\N	2019-06-13 04:58:21.125+01	2019-06-13 04:58:21.125+01	\N	\N	\N	\N	\N
6	KKTESTPA374	1	\N	2019-06-13 04:58:21.125+01	2019-06-13 04:58:21.125+01	\N	\N	\N	\N	\N
7	KKTESTPA95V	1	\N	2019-06-13 04:58:21.125+01	2019-06-13 04:58:21.125+01	\N	\N	\N	\N	\N
8	KKTESTKFP9P	1	\N	2019-06-13 04:58:21.125+01	2019-06-13 04:58:21.125+01	\N	\N	\N	\N	\N
\.


--
-- Name: coupon_codes_id_seq; Type: SEQUENCE SET; Schema: public; Owner: -
--

SELECT pg_catalog.setval('public.coupon_codes_id_seq', 1, false);


--
-- Name: coupon_groups_id_seq; Type: SEQUENCE SET; Schema: public; Owner: -
--

SELECT pg_catalog.setval('public.coupon_groups_id_seq', 1, false);


--
-- Data for Name: events; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.events (id, name, summary, start_time, end_time, redeem_group_id, vendors, models, devices, created_at, updated_at, active) FROM stdin;
1	SONY TV 合作案	KKTV 結合 SONY TV 打造高品質追劇體驗	2019-07-01 00:00:00+01	2020-01-01 00:00:00+00	1	SONY	KD-43X7500F,KD-43X8000G,KD-49X7500F,KD-49X8000E,KD-49X8000G,KD-49X8500F,KD-49X8500G,KD-49X9000E,KD-55A1,KD-55A8F,KD-55A8G,KD-55A9F,KD-55A9G,KD-55X7500F,KD-55X8000G,KD-55X8500E,KD-55X8500F,KD-55X8500G,KD-55X9000E,KD-55X9000F,KD-55X9300E,KD-55X9500G,KD-60X8300F,KD-65A1,KD-65A8F,KD-65A8G,KD-65A9F,KD-65A9G,KD-65X7500F,KD-65X8000G,KD-65X8500E,KD-65X8500F,KD-65X8500G,KD-65X9000E,KD-65X9000F,KD-65X9300E,KD-65X9500G,KD-65Z9F,KD-70X8300F,KD-75X8000G,KD-75X8500E,KD-75X8500F,KD-75X8500G,KD-75X9400E,KD-75X9500G,KD-75Z9F,KD-77A1,KD-77A9G,KD-85X8500F,KD-85X9500G	\N	2019-06-27 18:12:40.458+01	\N	t
\.


--
-- Name: events_id_seq; Type: SEQUENCE SET; Schema: public; Owner: -
--

SELECT pg_catalog.setval('public.events_id_seq', 1, false);


--
-- PostgreSQL database dump complete
--

